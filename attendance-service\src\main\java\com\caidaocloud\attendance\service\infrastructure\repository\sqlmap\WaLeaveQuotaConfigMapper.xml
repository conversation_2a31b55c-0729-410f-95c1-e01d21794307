<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaLeaveQuotaConfigMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveQuotaConfigPo">
    <id column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="leave_type_id" jdbcType="INTEGER" property="leaveTypeId" />
    <result column="distribution_cycle" jdbcType="INTEGER" property="distributionCycle" />
    <result column="dis_cycle_start" jdbcType="BIGINT" property="disCycleStart" />
    <result column="dis_cycle_end" jdbcType="BIGINT" property="disCycleEnd" />
    <result column="validity_period_type" jdbcType="INTEGER" property="validityPeriodType" />
    <result column="validity_duration" jdbcType="REAL" property="validityDuration" />
    <result column="validity_unit" jdbcType="INTEGER" property="validityUnit" />
    <result column="validity_start_type" jdbcType="INTEGER" property="validityStartType" />
    <result column="quota_distribute_rule" jdbcType="INTEGER" property="quotaDistributeRule" />
    <result column="quota_rounding_rule" jdbcType="INTEGER" property="quotaRoundingRule" />
    <result column="now_distribute_rule" jdbcType="INTEGER" property="nowDistributeRule" />
    <result column="now_rounding_rule" jdbcType="INTEGER" property="nowRoundingRule" />
    <result column="if_advance" jdbcType="INTEGER" property="ifAdvance" />
    <result column="expiration_rule" jdbcType="INTEGER" property="expirationRule" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="carry_over_to" jdbcType="BIGINT" property="carryOverTo" />
    <result column="carry_over_start_type" jdbcType="INTEGER" property="carryOverStartType" />
    <result column="carry_over_validity_duration" jdbcType="REAL" property="carryOverValidityDuration" />
    <result column="carry_over_validity_unit" jdbcType="INTEGER" property="carryOverValidityUnit" />
    <result column="convert_rule" jdbcType="INTEGER" property="convertRule" />
    <result column="child_rule" jdbcType="INTEGER" property="childRule" />
    <result column="validity_extension" jdbcType="BIT" property="validityExtension" />
    <result column="invalid_type" jdbcType="INTEGER" property="invalidType" />
    <result column="invalid_date" jdbcType="VARCHAR" property="invalidDate" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_start_date" jdbcType="BIGINT" property="ruleStartDate" />
    <result column="rule_end_date" jdbcType="BIGINT" property="ruleEndDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="contain_prenatal_leave" jdbcType="INTEGER" property="containPrenatalLeave" />
    <result column="apply_compensatory_cash" jdbcType="INTEGER" property="applyCompensatoryCash" />
    <result column="expire_handle" jdbcType="INTEGER" property="expireHandle" />
    <result column="apply_types" jdbcType="VARCHAR" property="applyTypes" />
    <result column="valid_quota_limit" jdbcType="REAL" property="validQuotaLimit" />
    <result column="leave_extension" jdbcType="BIT" property="leaveExtension" />
    <result column="extension_unit" jdbcType="INTEGER" property="extensionUnit" />
    <result column="max_extension" jdbcType="INTEGER" property="maxExtension" />
    <result column="extension_time" jdbcType="INTEGER" property="extensionTime" />
    <result column="i18n_rule_name" jdbcType="VARCHAR" property="i18nRuleName" />
    <result column="day_of_hire_month_dist" jdbcType="INTEGER" property="dayOfHireMonthDist" />
    <result column="carry_to_type" jdbcType="INTEGER" property="carryToType" />
    <result column="group_exp_condition" jdbcType="VARCHAR" property="groupExpCondition" />
    <result column="transfer_type" jdbcType="INTEGER" property="transferType" />
    <result column="max_transfer_quota" jdbcType="REAL" property="maxTransferQuota" />
  </resultMap>
  <sql id="Base_Column_List">
    config_id, tenant_id, leave_type_id, distribution_cycle, dis_cycle_start, dis_cycle_end, 
    validity_period_type, validity_duration, validity_unit, validity_start_type, quota_distribute_rule, 
    quota_rounding_rule, now_distribute_rule, now_rounding_rule, if_advance, expiration_rule, 
    deleted, create_by, create_time, update_by, update_time, carry_over_to, carry_over_start_type, 
    carry_over_validity_duration, carry_over_validity_unit, convert_rule,child_rule,invalid_type,
    validity_extension,invalid_date,description,rule_name,rule_start_date,rule_end_date,remark,sort,
    contain_prenatal_leave,apply_compensatory_cash,expire_handle,apply_types,valid_quota_limit,
    leave_extension,extension_unit,max_extension,extension_time, i18n_rule_name, day_of_hire_month_dist,
    carry_to_type, group_exp_condition, transfer_type, max_transfer_quota
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_leave_quota_config
    where config_id = #{configId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_leave_quota_config
    where config_id = #{configId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveQuotaConfigPo">
    insert into wa_leave_quota_config (config_id, tenant_id, leave_type_id, 
      distribution_cycle, dis_cycle_start, dis_cycle_end, 
      validity_period_type, validity_duration, validity_unit, 
      validity_start_type, quota_distribute_rule, 
      quota_rounding_rule, now_distribute_rule, now_rounding_rule, 
      if_advance, expiration_rule, deleted, 
      create_by, create_time, update_by, 
      update_time, carry_over_to, carry_over_start_type, 
      carry_over_validity_duration, carry_over_validity_unit, 
      convert_rule,child_rule,invalid_type,validity_extension,invalid_date,description,
      rule_name,rule_start_date,rule_end_date,remark,sort,contain_prenatal_leave,
      apply_compensatory_cash,expire_handle,apply_types,valid_quota_limit,
      leave_extension,extension_unit,max_extension,extension_time, i18n_rule_name,
      day_of_hire_month_dist,carry_to_type, group_exp_condition, transfer_type, max_transfer_quota)
    values (#{configId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{leaveTypeId,jdbcType=INTEGER},
      #{distributionCycle,jdbcType=INTEGER}, #{disCycleStart,jdbcType=BIGINT}, #{disCycleEnd,jdbcType=BIGINT}, 
      #{validityPeriodType,jdbcType=INTEGER}, #{validityDuration,jdbcType=REAL}, #{validityUnit,jdbcType=INTEGER}, 
      #{validityStartType,jdbcType=INTEGER}, #{quotaDistributeRule,jdbcType=INTEGER}, 
      #{quotaRoundingRule,jdbcType=INTEGER}, #{nowDistributeRule,jdbcType=INTEGER}, #{nowRoundingRule,jdbcType=INTEGER}, 
      #{ifAdvance,jdbcType=INTEGER}, #{expirationRule,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{carryOverTo,jdbcType=BIGINT}, #{carryOverStartType,jdbcType=INTEGER},
      #{carryOverValidityDuration,jdbcType=REAL}, #{carryOverValidityUnit,jdbcType=INTEGER}, 
      #{convertRule,jdbcType=INTEGER},#{childRule,jdbcType=INTEGER},#{invalidType,jdbcType=INTEGER},
      #{validityExtension,jdbcType=BIT},#{invalidDate,jdbcType=VARCHAR},#{description,jdbcType=VARCHAR},
      #{ruleName,jdbcType=VARCHAR},#{rule_start_date,jdbcType=BIGINT}, #{rule_end_date,jdbcType=BIGINT},#{remark,jdbcType=VARCHAR},
      #{sort,jdbcType=INTEGER},#{containPrenatalLeave,jdbcType=INTEGER},#{applyCompensatoryCash,jdbcType=INTEGER},
      #{expireHandle,jdbcType=INTEGER},#{applyTypes,jdbcType=VARCHAR},#{validQuotaLimit,jdbcType=REAL},
      #{leaveExtension,jdbcType=BIT},#{extensionUnit,jdbcType=INTEGER},#{maxExtension,jdbcType=INTEGER},#{extensionTime,jdbcType=INTEGER},
      #{i18nRuleName,jdbcType=VARCHAR}, #{dayOfHireMonthDist,jdbcType=INTEGER}, #{carryToType,jdbcType=INTEGER},
      #{groupExpCondition,jdbcType=VARCHAR},#{transferType,jdbcType=INTEGER},#{maxTransferQuota,jdbcType=REAL})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveQuotaConfigPo">
    insert into wa_leave_quota_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        config_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="leaveTypeId != null">
        leave_type_id,
      </if>
      <if test="distributionCycle != null">
        distribution_cycle,
      </if>
      <if test="disCycleStart != null">
        dis_cycle_start,
      </if>
      <if test="disCycleEnd != null">
        dis_cycle_end,
      </if>
      <if test="validityPeriodType != null">
        validity_period_type,
      </if>
      <if test="validityDuration != null">
        validity_duration,
      </if>
      <if test="validityUnit != null">
        validity_unit,
      </if>
      <if test="validityStartType != null">
        validity_start_type,
      </if>
      <if test="quotaDistributeRule != null">
        quota_distribute_rule,
      </if>
      <if test="quotaRoundingRule != null">
        quota_rounding_rule,
      </if>
      <if test="nowDistributeRule != null">
        now_distribute_rule,
      </if>
      <if test="nowRoundingRule != null">
        now_rounding_rule,
      </if>
      <if test="ifAdvance != null">
        if_advance,
      </if>
      <if test="expirationRule != null">
        expiration_rule,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="carryOverTo != null">
        carry_over_to,
      </if>
      <if test="carryOverStartType != null">
        carry_over_start_type,
      </if>
      <if test="carryOverValidityDuration != null">
        carry_over_validity_duration,
      </if>
      <if test="carryOverValidityUnit != null">
        carry_over_validity_unit,
      </if>
      <if test="convertRule != null">
        convert_rule,
      </if>
      <if test="childRule != null">
        child_rule,
      </if>
      <if test="invalidType != null">
        invalid_type,
      </if>
      <if test="validityExtension != null">
        validity_extension,
      </if>
      <if test="invalidDate != null">
        invalid_date,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="ruleStartDate != null">
        rule_start_date,
      </if>
      <if test="ruleEndDate != null">
        rule_end_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="containPrenatalLeave != null">
        contain_prenatal_leave,
      </if>
      <if test="applyCompensatoryCash != null">
        apply_compensatory_cash,
      </if>
      <if test="expireHandle != null">
        expire_handle,
      </if>
      <if test="applyTypes != null">
        apply_types,
      </if>
      <if test="validQuotaLimit != null">
        valid_quota_limit,
      </if>
      <if test="leaveExtension != null">
        leave_extension,
      </if>
      <if test="extensionUnit != null">
        extension_unit,
      </if>
      <if test="maxExtension != null">
        max_extension,
      </if>
      <if test="extensionTime != null">
        extension_time,
      </if>
      <if test="i18nRuleName != null">
        i18n_rule_name,
      </if>
      <if test="dayOfHireMonthDist != null">
        day_of_hire_month_dist,
      </if>
      <if test="carryToType != null">
        carry_to_type,
      </if>
      <if test="groupExpCondition != null">
        group_exp_condition,
      </if>
      <if test="transferType != null">
        transfer_type,
      </if>
      <if test="maxTransferQuota != null">
        max_transfer_quota,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="leaveTypeId != null">
        #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="distributionCycle != null">
        #{distributionCycle,jdbcType=INTEGER},
      </if>
      <if test="disCycleStart != null">
        #{disCycleStart,jdbcType=BIGINT},
      </if>
      <if test="disCycleEnd != null">
        #{disCycleEnd,jdbcType=BIGINT},
      </if>
      <if test="validityPeriodType != null">
        #{validityPeriodType,jdbcType=INTEGER},
      </if>
      <if test="validityDuration != null">
        #{validityDuration,jdbcType=REAL},
      </if>
      <if test="validityUnit != null">
        #{validityUnit,jdbcType=INTEGER},
      </if>
      <if test="validityStartType != null">
        #{validityStartType,jdbcType=INTEGER},
      </if>
      <if test="quotaDistributeRule != null">
        #{quotaDistributeRule,jdbcType=INTEGER},
      </if>
      <if test="quotaRoundingRule != null">
        #{quotaRoundingRule,jdbcType=INTEGER},
      </if>
      <if test="nowDistributeRule != null">
        #{nowDistributeRule,jdbcType=INTEGER},
      </if>
      <if test="nowRoundingRule != null">
        #{nowRoundingRule,jdbcType=INTEGER},
      </if>
      <if test="ifAdvance != null">
        #{ifAdvance,jdbcType=INTEGER},
      </if>
      <if test="expirationRule != null">
        #{expirationRule,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="carryOverTo != null">
        #{carryOverTo,jdbcType=BIGINT},
      </if>
      <if test="carryOverStartType != null">
        #{carryOverStartType,jdbcType=INTEGER},
      </if>
      <if test="carryOverValidityDuration != null">
        #{carryOverValidityDuration,jdbcType=REAL},
      </if>
      <if test="carryOverValidityUnit != null">
        #{carryOverValidityUnit,jdbcType=INTEGER},
      </if>
      <if test="convertRule != null">
        #{convertRule,jdbcType=INTEGER},
      </if>
      <if test="childRule != null">
        #{childRule,jdbcType=INTEGER},
      </if>
      <if test="invalidType != null">
        #{invalidType,jdbcType=INTEGER},
      </if>
      <if test="validityExtension != null">
        #{validityExtension,jdbcType=BIT},
      </if>
      <if test="invalidDate != null">
        #{invalidDate,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleStartDate != null">
        #{ruleStartDate,jdbcType=BIGINT},
      </if>
      <if test="ruleEndDate != null">
        #{ruleEndDate,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="containPrenatalLeave != null">
        #{containPrenatalLeave,jdbcType=INTEGER},
      </if>
      <if test="applyCompensatoryCash != null">
        #{applyCompensatoryCash,jdbcType=INTEGER},
      </if>
      <if test="expireHandle != null">
        #{expireHandle,jdbcType=INTEGER},
      </if>
      <if test="applyTypes != null">
        #{applyTypes,jdbcType=VARCHAR},
      </if>
      <if test="validQuotaLimit != null">
        #{validQuotaLimit,jdbcType=REAL},
      </if>
      <if test="leaveExtension != null">
        #{leaveExtension,jdbcType=BIT},
      </if>
      <if test="extensionUnit != null">
        #{extensionUnit,jdbcType=INTEGER},
      </if>
      <if test="maxExtension != null">
        #{maxExtension,jdbcType=INTEGER},
      </if>
      <if test="extensionTime != null">
        #{extensionTime,jdbcType=INTEGER},
      </if>
      <if test="i18nRuleName != null">
        #{i18nRuleName,jdbcType=VARCHAR},
      </if>
      <if test="dayOfHireMonthDist != null">
        #{dayOfHireMonthDist,jdbcType=INTEGER},
      </if>
      <if test="carryToType != null">
        #{carryToType,jdbcType=INTEGER},
      </if>
      <if test="groupExpCondition != null">
        #{groupExpCondition,jdbcType=VARCHAR},
      </if>
      <if test="transferType != null">
        #{transferType,jdbcType=INTEGER},
      </if>
      <if test="maxTransferQuota != null">
        #{maxTransferQuota,jdbcType=REAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveQuotaConfigPo">
    update wa_leave_quota_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="leaveTypeId != null">
        leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="distributionCycle != null">
        distribution_cycle = #{distributionCycle,jdbcType=INTEGER},
      </if>
      <if test="disCycleStart != null">
        dis_cycle_start = #{disCycleStart,jdbcType=BIGINT},
      </if>
      <if test="disCycleEnd != null">
        dis_cycle_end = #{disCycleEnd,jdbcType=BIGINT},
      </if>
      <if test="validityPeriodType != null">
        validity_period_type = #{validityPeriodType,jdbcType=INTEGER},
      </if>
      <if test="validityDuration != null">
        validity_duration = #{validityDuration,jdbcType=REAL},
      </if>
      <if test="validityUnit != null">
        validity_unit = #{validityUnit,jdbcType=INTEGER},
      </if>
      <if test="validityStartType != null">
        validity_start_type = #{validityStartType,jdbcType=INTEGER},
      </if>
      <if test="quotaDistributeRule != null">
        quota_distribute_rule = #{quotaDistributeRule,jdbcType=INTEGER},
      </if>
      <if test="quotaRoundingRule != null">
        quota_rounding_rule = #{quotaRoundingRule,jdbcType=INTEGER},
      </if>
      <if test="nowDistributeRule != null">
        now_distribute_rule = #{nowDistributeRule,jdbcType=INTEGER},
      </if>
      <if test="nowRoundingRule != null">
        now_rounding_rule = #{nowRoundingRule,jdbcType=INTEGER},
      </if>
      <if test="ifAdvance != null">
        if_advance = #{ifAdvance,jdbcType=INTEGER},
      </if>
      <if test="expirationRule != null">
        expiration_rule = #{expirationRule,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="carryOverTo != null">
        carry_over_to = #{carryOverTo,jdbcType=BIGINT},
      </if>
      <if test="carryOverStartType != null">
        carry_over_start_type = #{carryOverStartType,jdbcType=INTEGER},
      </if>
      <if test="carryOverValidityDuration != null">
        carry_over_validity_duration = #{carryOverValidityDuration,jdbcType=REAL},
      </if>
      <if test="carryOverValidityUnit != null">
        carry_over_validity_unit = #{carryOverValidityUnit,jdbcType=INTEGER},
      </if>
      <if test="convertRule != null">
        convert_rule = #{convertRule,jdbcType=INTEGER},
      </if>
      <if test="childRule != null">
        child_rule = #{childRule,jdbcType=INTEGER},
      </if>
      <if test="invalidType != null">
        invalid_type = #{invalidType,jdbcType=INTEGER},
      </if>
      <if test="validityExtension != null">
        validity_extension = #{validityExtension,jdbcType=BIT},
      </if>
      <if test="invalidDate != null">
        invalid_date = #{invalidDate,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleStartDate != null">
        rule_start_date = #{ruleStartDate,jdbcType=BIGINT},
      </if>
      <if test="ruleEndDate != null">
        rule_end_date = #{ruleEndDate,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="containPrenatalLeave != null">
        contain_prenatal_leave = #{containPrenatalLeave,jdbcType=INTEGER},
      </if>
      <if test="applyCompensatoryCash != null">
        apply_compensatory_cash = #{applyCompensatoryCash,jdbcType=INTEGER},
      </if>
      <if test="expireHandle != null">
        expire_handle = #{expireHandle,jdbcType=INTEGER},
      </if>
      <if test="applyTypes != null">
        apply_types = #{applyTypes,jdbcType=VARCHAR},
      </if>
      <if test="validQuotaLimit != null">
        valid_quota_limit = #{validQuotaLimit,jdbcType=REAL},
      </if>
      <if test="leaveExtension != null">
        leave_extension = #{leaveExtension,jdbcType=BIT},
      </if>
      <if test="extensionUnit != null">
        extension_unit = #{extensionUnit,jdbcType=INTEGER},
      </if>
      <if test="maxExtension != null">
        max_extension = #{maxExtension,jdbcType=INTEGER},
      </if>
      <if test="extensionTime != null">
        extension_time = #{extensionTime,jdbcType=INTEGER},
      </if>
      <if test="i18nRuleName != null">
        i18n_rule_name = #{i18nRuleName,jdbcType=VARCHAR},
      </if>
      <if test="dayOfHireMonthDist != null">
        day_of_hire_month_dist = #{dayOfHireMonthDist,jdbcType=INTEGER},
      </if>
      <if test="carryToType != null">
        carry_to_type = #{carryToType,jdbcType=INTEGER},
      </if>
      <if test="groupExpCondition != null">
        group_exp_condition = #{groupExpCondition,jdbcType=VARCHAR},
      </if>
      <if test="transferType != null">
        transfer_type = #{transferType,jdbcType=INTEGER},
      </if>
      <if test="maxTransferQuota != null">
        max_transfer_quota = #{maxTransferQuota,jdbcType=REAL},
      </if>
    </set>
    where config_id = #{configId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveQuotaConfigPo">
    update wa_leave_quota_config
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      distribution_cycle = #{distributionCycle,jdbcType=INTEGER},
      dis_cycle_start = #{disCycleStart,jdbcType=BIGINT},
      dis_cycle_end = #{disCycleEnd,jdbcType=BIGINT},
      validity_period_type = #{validityPeriodType,jdbcType=INTEGER},
      validity_duration = #{validityDuration,jdbcType=REAL},
      validity_unit = #{validityUnit,jdbcType=INTEGER},
      validity_start_type = #{validityStartType,jdbcType=INTEGER},
      quota_distribute_rule = #{quotaDistributeRule,jdbcType=INTEGER},
      quota_rounding_rule = #{quotaRoundingRule,jdbcType=INTEGER},
      now_distribute_rule = #{nowDistributeRule,jdbcType=INTEGER},
      now_rounding_rule = #{nowRoundingRule,jdbcType=INTEGER},
      if_advance = #{ifAdvance,jdbcType=INTEGER},
      expiration_rule = #{expirationRule,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      carry_over_to = #{carryOverTo,jdbcType=BIGINT},
      carry_over_start_type = #{carryOverStartType,jdbcType=INTEGER},
      carry_over_validity_duration = #{carryOverValidityDuration,jdbcType=REAL},
      carry_over_validity_unit = #{carryOverValidityUnit,jdbcType=INTEGER},
      convert_rule = #{convertRule,jdbcType=INTEGER},
      child_rule = #{childRule,jdbcType=INTEGER},
      invalid_type = #{invalidType,jdbcType=INTEGER},
      validity_extension = #{validityExtension,jdbcType=BIT},
      invalid_date = #{invalidDate,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      rule_start_date = #{ruleStartDate,jdbcType=BIGINT},
      rule_end_date = #{ruleEndDate,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      contain_prenatal_leave = #{containPrenatalLeave,jdbcType=INTEGER},
      apply_compensatory_cash = #{applyCompensatoryCash,jdbcType=INTEGER},
      expire_handle = #{expireHandle,jdbcType=INTEGER},
      valid_quota_limit = #{validQuotaLimit,jdbcType=REAL},
      leave_extension = #{leaveExtension,jdbcType=BIT},
      extension_unit = #{extensionUnit,jdbcType=INTEGER},
      max_extension = #{maxExtension,jdbcType=INTEGER},
      extension_time = #{extensionTime,jdbcType=INTEGER},
      i18n_rule_name = #{i18nRuleName,jdbcType=VARCHAR},
      day_of_hire_month_dist = #{dayOfHireMonthDist,jdbcType=INTEGER},
      carry_to_type = #{carryToType,jdbcType=INTEGER},
      group_exp_condition = #{groupExpCondition,jdbcType=VARCHAR},
      transfer_type = #{transferType,jdbcType=INTEGER},
      max_transfer_quota = #{maxTransferQuota,jdbcType=REAL}
    where config_id = #{configId,jdbcType=BIGINT}
  </update>

  <select id="getEmpFixedQuotaConfigList" resultType="java.util.Map">
    select ei.empid,
    ei.hire_date       as "hireDate",
    ei.gender,
    wg.wa_group_id     as "waGroupId",
    wlt.leave_type_id  as "leaveTypeId",
    wlt.leave_type     as "leaveType",
    wlt.gender_type    as "genderType",
    wlt.acct_time_type as "acctTimeType",
    wlqc.config_id     as "configId"
    from sys_emp_info ei
    join wa_emp_group_view wegv on wegv.empid = ei.empid
    join wa_group wg on wg.wa_group_id = wegv.wa_group_id
    join wa_leave_type wlt on wlt.leave_type_id = any (wg.leave_type_ids)
    join wa_leave_quota_config wlqc on wlt.leave_type_id = wlqc.leave_type_id
    where ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    and ei.deleted = 0
    and ((ei.stats <![CDATA[<>]]> 1 and hire_date <![CDATA[<=]]> #{curDate}) or (ei.stats = 1 AND ei.termination_date <![CDATA[>=]]> #{curDate}))
    and wlt.quota_restriction_type = 1
    and wlt.quota_type = 3
    <if test="datafilter != null and datafilter != ''">
      ${datafilter}
    </if>
  </select>
  <select id="loadStatus" resultType="java.util.Map">
    select config_id, status from quota_config_status
    where config_id in
    <foreach collection="configIds" item="item" open="(" separator="," close=")">#{item}</foreach>
  </select>

  <insert id="enableQuotaConfig">
    delete from quota_config_status where config_id = #{configId}
  </insert>

  <delete id="disableQuotaConfig">
    insert into quota_config_status(config_id, status) select #{configId}, 'DISABLED' from quota_config_status
    where not exists(select config_id from quota_config_status where config_id = #{configId})
    limit 1
  </delete>

  <select id="selectLeaveQuotaConfigList" resultMap="BaseResultMap">
    select *
    from wa_leave_quota_config
    where deleted=0 and tenant_id=#{tenantId};
  </select>

  <select id="selectListByTenantId" resultMap="BaseResultMap">
    select wlqc.config_id, COALESCE(wlqc.rule_name, wlt.leave_name) as rule_name,
           wlqc.leave_type_id,carry_over_to,carry_to_type,carry_over_validity_duration,
           carry_over_validity_unit
    from attendance.wa_leave_quota_config wlqc
           join attendance.wa_leave_type wlt on wlt.leave_type_id = wlqc.leave_type_id
    where wlqc.deleted = 0
      and wlqc.tenant_id = #{tenantId}
  </select>

  <update id="updateGroupExpCondition">
    update wa_leave_quota_config set group_exp_condition = #{groupExpCondition}
    where config_id = #{configId}
  </update>
</mapper>