package com.caidaocloud.attendance.service.interfaces.vo.overtime;

import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 加班申请VO
 */
@Data
@ApiModel("加班申请VO")
public class OvertimeApplyTimeVo {
    @ApiModelProperty("申请时长分钟数")
    private Double originalOtDuration;
    @ApiModelProperty("申请时长文本，返回小时数+单位")
    private String otDuration;
    @ApiModelProperty("工作流业务流程Key")
    private String wfBusKey;

    @ApiModelProperty("是否开启工作流")
    private boolean openWf;
    @ApiModelProperty("工作流开启信息")
    private WfBeginWorkflowDto beginWorkflowDto;
    private Integer preOtId;
}
