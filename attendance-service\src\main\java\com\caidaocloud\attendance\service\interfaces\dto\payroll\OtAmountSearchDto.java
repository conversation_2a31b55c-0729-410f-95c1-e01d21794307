package com.caidaocloud.attendance.service.interfaces.dto.payroll;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OtAmountSearchDto {
    @ApiModelProperty(value = "加班类型")
    private Integer overtimeType;

    @ApiModelProperty(value = "加班补偿类型")
    private Integer compensateType;

    @ApiModelProperty("员工ID集合：不能超过 2000")
    private List empids;

    @ApiModelProperty("考勤周期开始日期")
    private Long tmStartDay;

    @ApiModelProperty("考勤周期结束日期")
    private Long tmEndDay;

    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
}
