package com.caidaocloud.attendance.service.infrastructure.util;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/5/2021 2:11 PM
 * 4
 */
@Deprecated
public class ObjectConverter<F, T> {
    public T objectConvert(F from, Class<T> toClazz) {
        T to = null;
        try {
            to = toClazz.newInstance();
            if (from == null) {
                return to;
            }
            org.springframework.beans.BeanUtils.copyProperties(from, to);
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return to;
    }

    public List<T> objectConvertCollection(List<F> modelList, Class<T> toClazz) {
        if (modelList == null) {
            return new ArrayList<>();
        }
        return modelList.stream().map(i -> {
            return objectConvert(i, toClazz);
        }).collect(Collectors.toList());
    }
}
