package com.caidaocloud.attendance.service.interfaces.dto.leave;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveApplySaveDto;
import com.caidaocloud.dto.UserInfo;
import com.qcloud.cos.utils.Md5Utils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量休假申请DTO
 *
 * <AUTHOR>
 * @Date 2024/6/17
 */
@Data
public class BatchLeaveApplyDto {
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;
    @ApiModelProperty("紧急联系人")
    private String emergencyContact;
    @ApiModelProperty("附件地址")
    private String filePath;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("休假集合")
    private List<LeaveApplySaveDto> leaveList;

    public static String getIdempotentString(BatchLeaveApplyDto model) {
        UserInfo userInfo = UserContext.preCheckUser();
        return Md5Utils.md5Hex(String.format("%s_%s_%s_%s_%s", "BATCHLEAVE", userInfo.getTenantId(), userInfo.getUserId(),
                model.getEmpid(), model.getLeaveTypeId()));
    }

    public List<LeaveApplySaveDto> getLeaveList() {
        this.leaveList.forEach(leaveApplySaveDto -> {
            leaveApplySaveDto.setEmpid(this.empid);
            leaveApplySaveDto.setLeaveTypeId(this.leaveTypeId);
        });
        return this.leaveList;
    }

}
