package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.dto.TimeSlot;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ShiftDto implements Serializable {

    @ApiModelProperty("班次Id")
    private Integer shiftDefId;
    @ApiModelProperty("日期类型：1、工作日，2休息日，3法定假日")
    private Integer dateType;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("上班时间")
    private Integer startTime;
    @ApiModelProperty("下班时间")
    private Integer endTime;
    @ApiModelProperty("是否有午休时间：0否，1是")
    private Boolean isNoonRest;
    @ApiModelProperty("午休开始时间")
    private Integer noonRestStart;
    @ApiModelProperty("午休结束时间")
    private Integer noonRestEnd;
    @ApiModelProperty("总休息时长")
    private Integer restTotalTime;
    @ApiModelProperty("总工作时长")
    private Integer workTotalTime;
    @ApiModelProperty("上班打卡开始时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("上班打卡截止时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("下班打卡开始时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("下班打卡截止时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("加班打卡开始时间")
    private Integer overtimeStartTime;
    @ApiModelProperty("加班打卡截止时间")
    private Integer overtimeEndTime;
    @ApiModelProperty("加班休息时间段")
    private Object overtimeRestPeriods;
    @ApiModelProperty("休息时间段")
    private Object restPeriods;
    @ApiModelProperty("弹性规则 1:晚到晚走，早到早走 2:下班晚走，第二天早到")
    private Integer flexibleWorkRule;
    @ApiModelProperty("上班最多可晚到多少小时")
    private BigDecimal flexibleWorkLate;
    @ApiModelProperty("下班最多可早走多少小时")
    private BigDecimal flexibleWorkEarly;
    @ApiModelProperty("下班晚走,第二天晚到")
    private FlexibleDto [] flexibleRules ;
    private String flexibleOffWorkRule;
    @ApiModelProperty("弹性分析开关 1:关闭 2:开启")
    private Integer flexibleShiftSwitch;
    @ApiModelProperty("补卡时间限制")
    private List<TimeSlot> repairRestPeriods;
    @ApiModelProperty("半天时间")
    private Integer halfdayTime;
    @ApiModelProperty("中途打卡时间段")
    private List<TimeSlot> midwayClockTimes;

    @ApiModelProperty("代替班次ID, 非工作日出差等业务使用")
    private Integer substituteShift;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nShiftDefName;

    public void initShiftDefName() {
        if (StringUtils.isNotBlank(this.shiftDefName)) {
            return;
        }
        if (null == this.i18nShiftDefName || this.i18nShiftDefName.isEmpty() || null == this.i18nShiftDefName.get("default")) {
            return;
        }
        this.setShiftDefName(this.i18nShiftDefName.get("default"));
    }

    public void initI18nShiftDefName() {
        if (null != this.i18nShiftDefName && !this.i18nShiftDefName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.shiftDefName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.shiftDefName);
        this.setI18nShiftDefName(i18nName);
    }
}
