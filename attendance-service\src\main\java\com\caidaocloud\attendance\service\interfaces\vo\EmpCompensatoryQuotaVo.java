package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpCompensatoryQuotaVo {

    private Long quotaId;
    @ApiModelProperty("workno")
    private String workno;

    @ApiModelProperty("员工ID")
    private Long empId;

    @ApiModelProperty("员工姓名")
    private String empName;

    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;

    @ApiModelProperty("假期类型")
    private String leaveTypeName;

    @ApiModelProperty("加班日期")
    private Long overtimeDate;

    @ApiModelProperty("日期类型 1 工作日加班、2 休息日加班、3 法定假日加班")
    private Integer overtimeType;

    @ApiModelProperty("加班额度")
    private Float overtimeDuration;

    @ApiModelProperty("加班单位 1 天 2 小时")
    private Integer overtimeUnit;

    @ApiModelProperty("标准时长")
    private Float workingTime;

    @ApiModelProperty("调休单位 1 天 2 小时")
    private Integer quotaUnit;

    @ApiModelProperty("调休额度")
    private Float quotaDay;

    @ApiModelProperty("调整额度")
    private Float adjustQuotaDay;

    @ApiModelProperty("已用")
    private Float usedDay;

    @ApiModelProperty("流程中")
    private Float inTransitQuota;

    @ApiModelProperty("余额")
    private Float leftQuota;

    @ApiModelProperty("生效日期")
    private Long startDate;

    @ApiModelProperty("失效日期")
    private Long lastDate;

    @ApiModelProperty("状态 0失效 、1 生效 、2 撤销")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("数据源，AUTO:系统自动生成，MANUAL手动新增/导入")
    private String dataSource;
}
