package com.caidaocloud.attendance.service.interfaces.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WaClockPlanDto {
    @ApiModelProperty("id")
    private Long id;

    private Long corpId;

    private String belongOrgId;

    private String planName;

    private String clockWay;

    private Integer supplementCount;

    private Boolean isSupplement;

    private String gps;

    private String wifi;

    private String bluetooth;

    private Long creator;

    private Long createTime;

    private Long updater;

    private Long updateTime;

    private Long empId;

    private String empName;

    private Boolean reasonMust;

    @ApiModelProperty("补卡条数每次")
    private Integer supplementNumber;

    private Integer reasonWordNum;

    private Boolean enclosureRequired;
}
