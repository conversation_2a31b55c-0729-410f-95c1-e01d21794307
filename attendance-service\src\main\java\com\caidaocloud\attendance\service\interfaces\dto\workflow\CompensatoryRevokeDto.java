package com.caidaocloud.attendance.service.interfaces.dto.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompensatoryRevokeDto {
    @ApiModelProperty("businessKey")
    private String businessKey;
    @ApiModelProperty("撤销原因")
    private String revokeReason;
}
