package com.caidaocloud.attendance.service.interfaces.dto.overtime;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/7/15 15:23
 * @Description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OverTimeInfoDto {
    @ApiModelProperty("主键id")
    private Integer overtimeTypeId;
    @ApiModelProperty(value = "加班类型")
    private String typeName;
    @ApiModelProperty(value = "日期类型")
    private String overtimeType;
    @ApiModelProperty(value = "补偿类型")
    private String compensateType;
    @ApiModelProperty(value = "最小加班单位")
    private Float minOvertimeUnit;
    @ApiModelProperty(value = "最小申请时长")
    private Float minApplyNum;
    @ApiModelProperty("最大申请时长")
    private Float maxValidTime;
    @ApiModelProperty("生效时间")
    private Long startDate;
    @ApiModelProperty("失效时间")
    private Long endDate;
    @ApiModelProperty("状态 0 已失效 1:待生效 2:生效中")
    private Integer status;
    @ApiModelProperty("状态 0 停用 1 启用")
    private Integer enableState;
}
