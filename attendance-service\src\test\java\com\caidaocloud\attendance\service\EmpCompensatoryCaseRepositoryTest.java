package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseDo;
import com.caidaocloud.attendance.service.infrastructure.repository.impl.EmpCompensatoryCaseRepository;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class EmpCompensatoryCaseRepositoryTest {
    @Autowired
    private EmpCompensatoryCaseRepository repository;

    @Test
    public void test1() {
        List<EmpCompensatoryCaseDo> empCompensatoryCaseDos = repository.getApprovedOfCompensatoryCase(Lists.newArrayList(), 1706520650L, 1709256650L);
        System.out.println(empCompensatoryCaseDos);
    }
}
