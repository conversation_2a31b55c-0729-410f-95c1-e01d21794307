package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/11 15:41
 * @Version 1.0
 */
@Data
public class CityTreeVo implements Serializable {

    @ApiModelProperty("城市id")
    private Long cityId;
    @ApiModelProperty("城市code")
    private String code;
    @ApiModelProperty("城市名称中文")
    private String chnName;
    @ApiModelProperty("城市名称英文")
    private String engName;
    @ApiModelProperty("countryId")
    private Long countryId;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("城市类型")
    private Integer type;
    @ApiModelProperty("父级ID")
    private Long cityPid;
    @ApiModelProperty("是否是父级")
    private Boolean isParent;
    @ApiModelProperty("子节点")
    private List<CityTreeVo> children;
}
