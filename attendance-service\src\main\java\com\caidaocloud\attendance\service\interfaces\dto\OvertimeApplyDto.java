package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OvertimeApplyDto extends ExportBasePage {
    @ApiModelProperty("开始日期")
    private Long startDate;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束日期")
    private Long endDate;

    @ApiModelProperty("结束时间")
    private Long endTime;
}
