package com.caidaocloud.attendance.service.interfaces.vo.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GroupVo {

    @ApiModelProperty("id")
    private Integer waGroupId;

    @ApiModelProperty("方案名称")
    private String waGroupName;

    @ApiModelProperty("休假类型名称，多个以逗号分隔")
    private String leaveTypeNames;

    @ApiModelProperty("休假类型id")
    private String leaveTypeIds;

    @ApiModelProperty("加班类型id")
    private String otTypeIds;

    @ApiModelProperty("加班类型名称，多个以逗号分隔")
    private String otTypeNames;

    @ApiModelProperty("考勤周期：上月1，本月2")
    private Integer cyleMonth;

    @ApiModelProperty("考勤周期：起始日")
    private Integer cyleStartdate;

    @ApiModelProperty("分析规则名称")
    private String parseGroupIdName;

    @ApiModelProperty("分析规则id")
    private Integer parseGroupId;

    @ApiModelProperty("true适用全部员工/false适用部分员工")
    private Boolean isDefault;
}
