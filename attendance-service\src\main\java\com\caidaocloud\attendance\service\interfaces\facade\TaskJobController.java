package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.cron.ClockTaskService;
import com.caidaocloud.attendance.service.application.service.ITravelCompensatoryService;
import com.caidaocloud.attendance.service.application.service.impl.CheckWorkAttendanceService;
import com.caidaocloud.attendance.service.application.service.impl.ClockTaskWorkService;
import com.caidaocloud.attendance.service.application.service.msg.DailyWeeklyMsgService;
import com.caidaocloud.attendance.service.interfaces.dto.AutoExeTaskClockInMsgDto;
import com.caidaocloud.attendance.service.interfaces.dto.AutoGenQuotaDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @Date 2021/4/22
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/taskjob/v1")
public class TaskJobController {
    @Autowired
    private ClockTaskService clockTaskService;
    @Autowired
    private ClockTaskWorkService clockTaskWorkService;
    @Autowired
    private CheckWorkAttendanceService checkWorkAttendanceService;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private DailyWeeklyMsgService dailyWeeklyMsgService;
    @Autowired
    private ITravelCompensatoryService travelCompensatoryService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("考勤分析")
    @PostMapping(value = "/analyzeRegister")
    public void analyzeRegister() {
        log.info("定时任务【考勤分析】------------------------开始执行");
        try {
            checkWorkAttendanceService.analyzeRegister();
        } catch (Exception e) {
            log.error("【考勤分析】定时任务执行异常，{}", e.getMessage(), e);
        }
        log.info("定时任务 【考勤分析】------------------------结束执行");
    }

    @ApiOperation("排班同步")
    @PostMapping(value = "/workCalendarToMongo")
    public void workCalendarToMongo() {
        clockTaskService.workCalendarToMongo();
    }

    private String getBelongOrgId(AutoGenQuotaDto autoGenQuotaDto) {
        if (autoGenQuotaDto.getBelongOrgId() != null) {
            return autoGenQuotaDto.getBelongOrgId();
        }
        UserInfo userInfo = getUserInfo();
        if (userInfo != null) {
            return userInfo.getTenantId();
        }
        return null;
    }

    @ApiOperation("自动计算年假配额")
    @PostMapping("/autoGenEmpQuotaForIssuedAnnually")
    @Deprecated
    public Result<Boolean> autoGenEmpQuotaForIssuedAnnually(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoGenEmpQuotaForIssuedAnnually(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("自动计算育儿假配额")
    @PostMapping("/autoGenEmpQuotaForParRent")
    @Deprecated
    public Result<Boolean> autoGenEmpQuotaForParRent(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoGenEmpQuotaForParRent(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("自动计算年假当前配额")
    @PostMapping("/autoGenEmpNowQuotaForIssuedAnnually")
    @Deprecated
    public Result<Boolean> autoGenEmpNowQuotaForIssuedAnnually(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        if (StringUtils.isNotBlank(autoGenQuotaDto.getDate())) {
            // 指定当前配额折算日期
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Long curtime = format.parse(autoGenQuotaDto.getDate()).getTime() / 1000;
            String belongOrgId = getBelongOrgId(autoGenQuotaDto);
            clockTaskWorkService.autoGenEmpNowQuotaForIssuedAnnuallyByDate(belongOrgId, curtime, autoGenQuotaDto.getEmpId());
        } else {
            clockTaskWorkService.autoGenEmpNowQuotaForIssuedAnnually();
        }
        return Result.ok(true);
    }

    @ApiOperation("自动计算固定额度")
    @PostMapping("/autoGenEmpQuotaForFixedQuota")
    @Deprecated
    public Result<Boolean> autoGenEmpQuotaForFixedQuota(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoGenEmpQuotaForFixedQuota(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("自动计算调休额度")
    @PostMapping("/autoGenEmpQuotaForCompensatoryLeave")
    @Deprecated
    public Result<Boolean> autoGenEmpQuotaForCompensatoryLeave(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoGenEmpQuotaForCompensatoryLeave(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("按年发放的配额自动结转")
    @PostMapping("/autoCarryForwardQuota")
    @Deprecated
    public Result<Boolean> autoCarryForwardQuota(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoCarryForwardQuota(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("日报消息发送")
    @PostMapping("/autoSendDailyMsg")
    @Deprecated
    public Result<Boolean> autoSendDailyMsg(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        if (null == belongOrgId) {
            return Result.ok(true);
        }
        if (autoGenQuotaDto.getManual()) {
            dailyWeeklyMsgService.manualSendDailyTtlMsg(belongOrgId, autoGenQuotaDto.getDate(), autoGenQuotaDto.getDailyNotifyAbnormal());
        } else {
            dailyWeeklyMsgService.sendDailyTtlMsg(belongOrgId, autoGenQuotaDto.getDate());
        }
        return Result.ok(true);
    }

    @ApiOperation("周报消息发送")
    @PostMapping("/autoWeeklyDailyMsg")
    @Deprecated
    public Result<Boolean> autoWeeklyDailyMsg(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        if (null == belongOrgId) {
            return Result.ok(true);
        }

        dailyWeeklyMsgService.sendWeeklyTtlMsg(belongOrgId, autoGenQuotaDto.getDate());
        return Result.ok(true);
    }


    @ApiOperation("打卡消息提醒发送")
    @PostMapping("/autoClockInMsg")
    @Deprecated
    public Result<Boolean> autoClockInMsg(@RequestBody AutoExeTaskClockInMsgDto clockInMsgDto) throws Exception {
        UserInfo userInfo = getUserInfo();
        String belongOrgId = clockInMsgDto.getBelongOrgId() == null ? userInfo.getTenantId() : clockInMsgDto.getBelongOrgId();
        if (null == belongOrgId) {
            return Result.ok(true);
        }
        clockTaskWorkService.synchronizeEmpClockShiftInfo(clockInMsgDto);
        return Result.ok(true);
    }

    @ApiOperation("销假提醒发送")
    @PostMapping("/autoLeaveCancelMsg")
    @Deprecated
    public Result<Boolean> autoLeaveCancelMsg(@RequestBody AutoExeTaskClockInMsgDto clockInMsgDto) throws Exception {
        UserInfo userInfo = getUserInfo();
        String belongOrgId = clockInMsgDto.getBelongOrgId() == null ? userInfo.getTenantId() : clockInMsgDto.getBelongOrgId();
        if (null == belongOrgId) {
            return Result.ok(true);
        }
        clockTaskWorkService.synchronizeEmpLeaveCancelInfo(clockInMsgDto);
        return Result.ok(true);
    }

    @ApiOperation("自动销假")
    @PostMapping("/autoLeaveCancel")
    @Deprecated
    public Result<Boolean> autoLeaveCancel(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoLeaveCancel(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("调休付现")
    @PostMapping("/autoCompensatoryToCase")
    public Result<Boolean> autoCompensatoryToCase(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoCompensatoryToCase(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("考勤异常汇总提醒")
    @PostMapping("/attendanceAbnormalReminder")
    @Deprecated
    public Result<Boolean> attendanceAbnormalReminder(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        dailyWeeklyMsgService.attendanceAbnormalReminder(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("考勤异常汇总任务刷新")
    @PostMapping("/refreshAttendanceAbnormalReminderJob")
    @Deprecated
    public Result<Boolean> refreshAttendanceAbnormalReminderJob(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.attendanceAbnormalJobRefresh(belongOrgId);
        return Result.ok(true);
    }

    @ApiOperation("出差转调休")
    @PostMapping("/autoTravelToCompensatory")
    public Result<Boolean> autoTravelToCompensatory(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String tenantId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.autoTravelToCompensatory(tenantId);
        return Result.ok(true);
    }

    @ApiOperation("出差转调休")
    @GetMapping("/autoManualTravelToCompensatory")
    public Result<Boolean> autoManualTravelToCompensatory(@RequestParam("travelId") Long travelId) throws Exception {
        travelCompensatoryService.generateCompensatoryQuota(travelId);
        return Result.ok(true);
    }

    @ApiOperation("考勤明细任务刷新")
    @PostMapping("/attendanceDetailJobRefresh")
    @Deprecated
    public Result<Boolean> attendanceDetailJobRefresh(@RequestBody AutoGenQuotaDto autoGenQuotaDto) throws Exception {
        String belongOrgId = getBelongOrgId(autoGenQuotaDto);
        clockTaskWorkService.attendanceDetailJobRefresh(belongOrgId);
        return Result.ok(true);
    }
}
