package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 出差记录
 */
@Data
public class EmpTravelVo {

    @ApiModelProperty("出差记录ID")
    private Long travelId;

    @ApiModelProperty("员工工号")
    private String workNo;

    @ApiModelProperty("姓名")
    private String empName;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("部门全称")
    private String fullPath;

    @ApiModelProperty("出差类型")
    private String travelType;

    @ApiModelProperty("申请时长")
    private Float timeDuration;

    @ApiModelProperty("时间单位")
    private String timeUnitName;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("出行方式")
    private String travelModeName;

    @ApiModelProperty("省")
    private String provinceName;

    @ApiModelProperty("市")
    private String cityName;

    @ApiModelProperty("区")
    private String countyName;

    @ApiModelProperty("访问地")
    private String visitedPlace;

    @ApiModelProperty("申请理由")
    private String reason;

    @ApiModelProperty("申请时间")
    private Long createTime;

    @ApiModelProperty("审批状态")
    private String statusName;

    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;

    private Integer status;
    private Integer funType;
    private String businessKey;

    @ApiModelProperty("调休配额")
    private Float quotaDay;
    @ApiModelProperty("调休单位")
    private String quotaUnit;
    private String revokeReason;
}
