package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class HolidayVo implements Serializable {

    @ApiModelProperty("日期id")
    private Integer holidayCalendarId;
    @ApiModelProperty("日期名称")
    private String calendarName;
    @ApiModelProperty("公司Id")
    private String belongOrgid;
    @ApiModelProperty("日期名称扩展")
    private Object calendarNameLang;
    @ApiModelProperty("创建人")
    private Long crtuser;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nCalendarName;
}
