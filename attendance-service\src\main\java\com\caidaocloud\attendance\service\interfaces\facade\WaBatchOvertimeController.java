package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchOvertimeService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeApplyDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.RevokeBatchOvertimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.BatchOvertimePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeApplyTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeDateVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeStatisticsVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 批量加班
 *
 * <AUTHOR>
 * @Date 2024/6/18
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/overtime/v1")
@Api(value = "/api/attendance/batch/overtime/v1", description = "批量加班")
public class WaBatchOvertimeController {
    @Autowired
    private WaBatchOvertimeService waBatchOvertimeService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation(value = "加班统计")
    @GetMapping(value = "/statistics")
    public Result<OvertimeStatisticsVo> statistics(@RequestParam("empid") Long empid,
                                                   @RequestParam("startDate") Long startDate,
                                                   @RequestParam("endDate") Long endDate) {
        return Result.ok(waBatchOvertimeService.statistics(empid, startDate, endDate));
    }

    @ApiOperation(value = "获取加班日期")
    @GetMapping(value = "/listDate")
    public Result<List<OvertimeDateVo>> listDate(@RequestParam("empid") Long empid,
                                                 @RequestParam("startDate") Long startDate,
                                                 @RequestParam("endDate") Long endDate) {
        return Result.ok(waBatchOvertimeService.listOtDate(empid, startDate, endDate));
    }

    @ApiOperation(value = "计算加班时长")
    @PostMapping(value = "/calTime")
    public Result<OvertimeApplyTimeVo> calTime(@RequestBody BatchOvertimeApplyDto applyDto) {
        String lockKey = BatchOvertimeApplyDto.getIdempotentString(applyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            return Result.ok(waBatchOvertimeService.calTime(applyDto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.GET_OVERTIME_DURATION_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "保存加班单据")
    @PostMapping(value = "/save")
    public Result<OvertimeApplyTimeVo> save(@RequestBody BatchOvertimeApplyDto applyDto) {
        String lockKey = BatchOvertimeApplyDto.getIdempotentString(applyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            return Result.ok(waBatchOvertimeService.save(applyDto, AppTypeEnum.BATCH_PC));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_OVERTIME_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "分页列表")
    @PostMapping(value = "/page")
    public Result<AttendancePageResult<BatchOvertimePageListVo>> getPageList(@RequestBody BatchOvertimeQueryDto dto,
                                                                             HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.BATCH_LEAVE_APPLY_RECORD_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("批量加班申请列表 DataScope = {}", dto.getDataScope());

        PageBean pageBean = PageUtil.getPageBean(dto);
        PageList<BatchOvertimePageListVo> pageList = waBatchOvertimeService.getPageList(pageBean, dto, UserContext.getAndCheckUser());
        AttendancePageResult<BatchOvertimePageListVo> pageResult = new AttendancePageResult<>(pageList, pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revoke(@RequestBody RevokeBatchOvertimeDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            waBatchOvertimeService.revoke(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_FAILED, Boolean.FALSE);
        }
    }
}
