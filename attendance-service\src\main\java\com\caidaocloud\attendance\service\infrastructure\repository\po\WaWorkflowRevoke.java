package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "wa_workflow_revoke")
public class WaWorkflowRevoke {
    private Long id;
    private String tenantId;
    private Long entityId;
    private String moduleName;
    private Integer status;
    private String reason;
    private Long lastApprovalTime;
    private String revokeReason;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private String processCode;
}