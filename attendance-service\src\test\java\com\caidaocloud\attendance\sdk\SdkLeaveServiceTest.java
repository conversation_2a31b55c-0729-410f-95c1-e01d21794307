package com.caidaocloud.attendance.sdk;

import com.caidaocloud.attendance.sdk.dto.SdkLeaveApplySaveDTO;
import com.caidaocloud.attendance.sdk.service.SdkLeaveService;
import com.caidaocloud.attendance.service.AttendanceApplication;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2023/6/25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class SdkLeaveServiceTest {
    @Autowired
    private SdkLeaveService sdkLeaveService;

    @Test
    public void testGetLeaveTotalTime(){
        String body = "{\n" +
                "    \"empid\":1699793729935361,\n" +
                "    \"endTime\":1687708800,\n" +
                "    \"etime\":1080,\n" +
                "    \"leaveTypeId\":2,\n" +
                "    \"showDay\":0,\n" +
                "    \"showMin\":1,\n" +
                "    \"startTime\":1687708800,\n" +
                "    \"stime\":540\n" +
                "}";
        SdkLeaveApplySaveDTO leaveApplySaveDto = FastjsonUtil.convertObject(body,SdkLeaveApplySaveDTO.class);
        sdkLeaveService.getLeaveTotalTime(leaveApplySaveDto);
    }
}
