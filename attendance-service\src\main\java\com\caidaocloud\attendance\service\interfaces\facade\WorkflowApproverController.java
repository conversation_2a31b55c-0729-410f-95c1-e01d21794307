package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.service.workflow.WorkflowApproverService;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.annotation.WfApprover;
import com.caidaocloud.workflow.enums.WfApproverFetchType;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/attendance/workflowApprover/v1")
public class WorkflowApproverController {
    @Autowired
    private WorkflowApproverService workflowApproverService;

    @GetMapping("/batch/travel/costcenter")
    @ApiOperation(value = "批量出差申请成本中心负责人")
    @WfApprover(name = "成本中心负责人", code = "BATCH_TRAVEL_COST_CENTER_APPROVER", fetchType = WfApproverFetchType.RELATIVE_PATH,
            address = "/api/attendance/workflowApprover/v1/batch/travel/costcenter", funCode = "ATTENDANCE-BATCH-TRAVEL",
            component = WfValueComponentEnum.NONE, serviceId = "caidaocloud-attendance-service")
    public Result<String> getBatchTravelCostCenter(@RequestParam("businessKey") String businessKey) {
        return Result.ok(workflowApproverService.getBatchTravelCostCenter(businessKey));
    }
}