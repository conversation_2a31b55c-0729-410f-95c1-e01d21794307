package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidaocloud.attendance.service.application.enums.ApplyQuotaEnum;
import com.caidaocloud.attendance.service.application.enums.DataSourceEnum;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveQuotaConfigDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;

import java.util.Objects;

@Data
public class CompensotaryDto {
    @ApiModelProperty("日期类型")
    private Integer overTimeType;
    @ApiModelProperty("日期类型文本")
    private String overTimeTypeTxt;
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("生效日期")
    private Long lastDate;
    @ApiModelProperty("调休额度")
    private Float quotaDay;
    @ApiModelProperty("已使用")
    private Float usedDay;
    @ApiModelProperty("流程中")
    private Float inTransitQuota;
    @ApiModelProperty("调整额度")
    private Float adjustQuota;
    @ApiModelProperty("剩余")
    private Float leftDay;
    @ApiModelProperty("单位 1:天 2:小时")
    private Integer unit;
    @ApiModelProperty("加班日期")
    private Long overtimeDate;
    @ApiModelProperty("当前余额")
    private Float currentQuota;
    private Long configId;
    @ApiModelProperty("是否可申请调休转付现：true(是)/false(否)")
    private boolean applyCompensatoryCash = false;
    private Long empQuotaId;
    private String dataSource;

    /**
     * 检查是否可申请调休转付现
     *
     * @param leaveQuotaConfig
     */
    public void checkIsApplyCompensatoryCash(LeaveQuotaConfigDto leaveQuotaConfig) {
        if (leaveQuotaConfig == null || !Objects.equals(leaveQuotaConfig.getApplyCompensatoryCash(), 1) || CollectionUtils.isEmpty(leaveQuotaConfig.getApplyTypes())) {
            return;
        } else if (leaveQuotaConfig.getApplyTypes().size() > 1) {
            this.applyCompensatoryCash = true;
            return;
        }
        var currentTime = System.currentTimeMillis() / 1000;
        switch (ApplyQuotaEnum.valueOf(String.valueOf(leaveQuotaConfig.getApplyTypes().iterator().next()))) {
            case INVALID:
                this.applyCompensatoryCash = this.lastDate != null && currentTime >= this.lastDate.longValue();
                break;
            case EFFICIENT:
                this.applyCompensatoryCash = this.startDate != null && this.lastDate != null &&
                        this.startDate.longValue() <= currentTime && currentTime <= this.lastDate.longValue();
                break;
        }
    }
}