package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpFixQuotaVo {
    @ApiModelProperty("主键")
    private Integer empQuotaId;
    @ApiModelProperty("员工ID")
    private Long empid;
    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("workno")
    private String workno;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("假期类型ID")
    private Long leaveTypeId;//假期类型
    @ApiModelProperty("假期类型")
    private String leaveTypeName;//假期类型
    @ApiModelProperty("入职时间")
    private Long hireDate;
    @ApiModelProperty("离职时间")
    private Long terminationDate;
    @ApiModelProperty("婚姻状态ID")
    private Long marriage;
    @ApiModelProperty("婚姻状态")
    private String marriageName;
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("可用")
    private Float quotaDay;
    @ApiModelProperty("已用")
    private Float usedDay;
    @ApiModelProperty("在途")
    private Float inTransitQuota;
    @ApiModelProperty("调整")
    private Float adjustQuota;
    @ApiModelProperty("余额")
    private Float leftQuota;
    @ApiModelProperty("状态 0 生效 1 失效")
    private Integer status;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("工作地ID")
    private Long workplace;
    @ApiModelProperty("工作地")
    private String workplaceName;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("社保缴纳地")
    private String socialProvinceCityName;
}
