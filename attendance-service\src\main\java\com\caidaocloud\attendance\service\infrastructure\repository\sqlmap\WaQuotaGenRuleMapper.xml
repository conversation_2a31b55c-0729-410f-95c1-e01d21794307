<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaQuotaGenRuleMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaQuotaGenRulePo">
    <id column="quota_rule_id" jdbcType="BIGINT" property="quotaRuleId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="leave_type_id" jdbcType="INTEGER" property="leaveTypeId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="condition_exp" jdbcType="VARCHAR" property="conditionExp" />
    <result column="condition_note" jdbcType="VARCHAR" property="conditionNote" />
    <result column="quota_val" jdbcType="INTEGER" property="quotaVal" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    quota_rule_id, tenant_id, leave_type_id, config_id, condition_exp, condition_note, 
    quota_val, deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_quota_gen_rule
    where quota_rule_id = #{quotaRuleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_quota_gen_rule
    where quota_rule_id = #{quotaRuleId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaQuotaGenRulePo">
    insert into wa_quota_gen_rule (quota_rule_id, tenant_id, leave_type_id, 
      config_id, condition_exp, condition_note, 
      quota_val, deleted, create_by, 
      create_time, update_by, update_time
      )
    values (#{quotaRuleId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{leaveTypeId,jdbcType=INTEGER},
      #{configId,jdbcType=BIGINT}, #{conditionExp,jdbcType=VARCHAR}, #{conditionNote,jdbcType=VARCHAR}, 
      #{quotaVal,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaQuotaGenRulePo">
    insert into wa_quota_gen_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quotaRuleId != null">
        quota_rule_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="leaveTypeId != null">
        leave_type_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="conditionExp != null">
        condition_exp,
      </if>
      <if test="conditionNote != null">
        condition_note,
      </if>
      <if test="quotaVal != null">
        quota_val,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quotaRuleId != null">
        #{quotaRuleId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="leaveTypeId != null">
        #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="conditionExp != null">
        #{conditionExp,jdbcType=VARCHAR},
      </if>
      <if test="conditionNote != null">
        #{conditionNote,jdbcType=VARCHAR},
      </if>
      <if test="quotaVal != null">
        #{quotaVal,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaQuotaGenRulePo">
    update wa_quota_gen_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="leaveTypeId != null">
        leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="conditionExp != null">
        condition_exp = #{conditionExp,jdbcType=VARCHAR},
      </if>
      <if test="conditionNote != null">
        condition_note = #{conditionNote,jdbcType=VARCHAR},
      </if>
      <if test="quotaVal != null">
        quota_val = #{quotaVal,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where quota_rule_id = #{quotaRuleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaQuotaGenRulePo">
    update wa_quota_gen_rule
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      config_id = #{configId,jdbcType=BIGINT},
      condition_exp = #{conditionExp,jdbcType=VARCHAR},
      condition_note = #{conditionNote,jdbcType=VARCHAR},
      quota_val = #{quotaVal,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where quota_rule_id = #{quotaRuleId,jdbcType=BIGINT}
  </update>
</mapper>