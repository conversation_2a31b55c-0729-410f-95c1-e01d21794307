package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DayAnalyseAbnormalPageDto extends ExportBasePage implements Serializable {

    @ApiModelProperty("开始日期")
    private Integer startDate;
    @ApiModelProperty("结束日期")
    private Integer endDate;
    @ApiModelProperty("考勤方案")
    private List<Integer> waGroupIds;
}