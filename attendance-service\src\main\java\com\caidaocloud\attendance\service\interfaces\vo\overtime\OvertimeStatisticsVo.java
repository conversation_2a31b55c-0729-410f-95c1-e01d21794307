package com.caidaocloud.attendance.service.interfaces.vo.overtime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 加班统计VO
 *
 * <AUTHOR>
 * @Date 2024/6/26
 */
@Data
public class OvertimeStatisticsVo {
    @ApiModelProperty("已申请加班结薪")
    private Float appliedWorkPaid;

    @ApiModelProperty("已申请加班调休")
    private Float appliedCompensatoryLeave;

    @ApiModelProperty("申请中加班结薪")
    private Float applyingWorkPaid;

    @ApiModelProperty("申请中加班调休")
    private Float applyingCompensatoryLeave;

    @ApiModelProperty("本月加班结薪合计")
    private Float workPaid;

    @ApiModelProperty("本月加班调休合计")
    private Float compensatoryLeave;

    @ApiModelProperty("本月加班合计")
    private Float totalDuration;

    @ApiModelProperty("时长单位 1 天 2 小时")
    private Integer unit;

    @ApiModelProperty("时长单位名称")
    private String unitTxt;

    @ApiModelProperty("本次加班结薪合计")
    private Float currentWorkPaid;

    @ApiModelProperty("本次加班调休合计")
    private Float currentCompensatoryLeave;

    @ApiModelProperty("本次加班合计")
    private Float currentTotalDuration;
}
