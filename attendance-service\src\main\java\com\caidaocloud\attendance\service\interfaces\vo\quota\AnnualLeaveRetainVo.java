package com.caidaocloud.attendance.service.interfaces.vo.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AnnualLeaveRetainVo {
    @ApiModelProperty("余额id")
    private Integer empQuotaId;
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("员工工号")
    private String workno;
    @ApiModelProperty("时间单位|单位")
    private String timeUnitName;
    @ApiModelProperty("留存配额")
    private Float quotaDay;
    @ApiModelProperty("留存配额已使用")
    private Float usedDay;
    @ApiModelProperty("留存配额流程中")
    private Float inTransitQuota;
    @ApiModelProperty("留存有效期")
    private Long lastDate;

    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("上年留存来源假期额度主键")
    private Long configId;
    @ApiModelProperty("上年留存来源假期额度名称")
    private String quotaName;
    @ApiModelProperty("上年留存来源假期主键")
    private Integer leaveTypeId;
    @ApiModelProperty("上年留存来源假期名称")
    private String leaveTypeName;
    @ApiModelProperty("年份")
    private Short periodYear;
}
