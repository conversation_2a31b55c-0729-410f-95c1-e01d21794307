package com.caidaocloud.attendance.service.application.dto.travel;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

@Accessors(chain = true)
@Data
public class BatchTravelImportResult {
    private boolean success = true;

    private List<String> errorMessages;

    private Set<String> successList;

    private Set<String> failList;

    public void addError(String errorMessage) {
        if (this.errorMessages == null) {
            this.errorMessages = Lists.newArrayList();
        }
        this.errorMessages.add(errorMessage);
    }

    public void addSuccess(String dataId) {
        if (this.successList == null) {
            this.successList = Sets.newHashSet();
        }
        this.successList.add(dataId);
    }

    public void addFail(String dataId) {
        if (this.failList == null) {
            this.failList = Sets.newHashSet();
        }
        this.failList.add(dataId);
    }
}
