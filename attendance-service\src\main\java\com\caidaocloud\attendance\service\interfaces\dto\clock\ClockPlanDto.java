package com.caidaocloud.attendance.service.interfaces.dto.clock;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ClockPlanDto {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("方案名称")
    private String planName;
    @ApiModelProperty("打卡方式：1、GPS打卡，4、蓝牙，5、WIFI，多个打卡方式以逗号分隔")
    private String clockWay;
    @ApiModelProperty("支持补卡次数")
    private Integer supplementCount;
    @ApiModelProperty("是否支持补卡")
    private Boolean isSupplement;
    @ApiModelProperty("打卡地点id，多个以逗号分隔")
    private String gps;
    @ApiModelProperty("wifi id，多个以逗号分隔")
    private String wifi;
    @ApiModelProperty("蓝牙id，多个以逗号分隔")
    private String bluetooth;
    @ApiModelProperty("适用对象id")
    private List<Long> empIds;
    @ApiModelProperty("已分配方案的员工id")
    private List<Long> conflictEmpIds;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    //20210830新增字段
    @ApiModelProperty("是否允许外勤打卡:true/false，默认false")
    private Boolean allowFieldClockIn = false;
    @ApiModelProperty("外勤打卡需填写备注:true/false，默认false")
    private Boolean fieldClockInNote = false;
    @ApiModelProperty("外勤打卡需拍照:true/false，默认false")
    private Boolean fieldClockInEnclosure = false;
    //20210907新增字段
    @ApiModelProperty("打卡时间间隔")
    private Float timeInterval;
    //20210923新增字段
    @ApiModelProperty("补卡说明")
    private String description;
    @ApiModelProperty("补卡原因必填开关：true/false")
    private Boolean reasonMust;
    @ApiModelProperty(name = "是否允许范围外打卡：true(允许)/false(false)", allowableValues = "true,false")
    private Boolean clockInAllowed;
    @ApiModelProperty("补卡条数每次")
    private Integer supplementNumber;
    @ApiModelProperty("补卡原因字数")
    private Integer reasonWordNum;
    @ApiModelProperty("补卡附件必须开关：默认false，true必须，false非必须")
    private Boolean enclosureRequired;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nPlanName;

    public void initPlanName() {
        if (StringUtils.isNotBlank(this.planName)) {
            return;
        }
        if (null == this.i18nPlanName || this.i18nPlanName.isEmpty() || null == this.i18nPlanName.get("default")) {
            return;
        }
        this.setPlanName(this.i18nPlanName.get("default"));
    }

    public void initI18nPlanName() {
        if (null != this.i18nPlanName && !this.i18nPlanName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.planName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.planName);
        this.setI18nPlanName(i18nName);
    }
}
