<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftChangeMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo">
    <id column="rec_id" jdbcType="INTEGER" property="recId" />
    <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId" />
    <result column="empid" jdbcType="BIGINT" property="empid" />
    <result column="work_date" jdbcType="BIGINT" property="workDate" />
    <result column="old_shift_def_id" jdbcType="INTEGER" property="oldShiftDefId" />
    <result column="new_shift_def_id" jdbcType="INTEGER" property="newShiftDefId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="crtuser" jdbcType="BIGINT" property="crtuser" />
    <result column="crttime" jdbcType="BIGINT" property="crttime" />
    <result column="upduser" jdbcType="BIGINT" property="upduser" />
    <result column="updtime" jdbcType="BIGINT" property="updtime" />
  </resultMap>
  <sql id="Base_Column_List">
    rec_id, belong_org_id, empid, work_date, old_shift_def_id, new_shift_def_id, remark, 
    status, crtuser, crttime, upduser, updtime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_emp_shift_change
    where rec_id = #{recId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from wa_emp_shift_change
    where rec_id = #{recId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo">
    insert into wa_emp_shift_change (rec_id, belong_org_id, empid, 
      work_date, old_shift_def_id, new_shift_def_id, 
      remark, status, crtuser, 
      crttime, upduser, updtime
      )
    values (#{recId,jdbcType=INTEGER}, #{belongOrgId,jdbcType=VARCHAR}, #{empid,jdbcType=BIGINT},
      #{workDate,jdbcType=BIGINT}, #{oldShiftDefId,jdbcType=INTEGER}, #{newShiftDefId,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{crtuser,jdbcType=BIGINT},
      #{crttime,jdbcType=BIGINT}, #{upduser,jdbcType=BIGINT}, #{updtime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo">
    insert into wa_emp_shift_change
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recId != null">
        rec_id,
      </if>
      <if test="belongOrgId != null">
        belong_org_id,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="workDate != null">
        work_date,
      </if>
      <if test="oldShiftDefId != null">
        old_shift_def_id,
      </if>
      <if test="newShiftDefId != null">
        new_shift_def_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="crtuser != null">
        crtuser,
      </if>
      <if test="crttime != null">
        crttime,
      </if>
      <if test="upduser != null">
        upduser,
      </if>
      <if test="updtime != null">
        updtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recId != null">
        #{recId,jdbcType=INTEGER},
      </if>
      <if test="belongOrgId != null">
        #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="workDate != null">
        #{workDate,jdbcType=BIGINT},
      </if>
      <if test="oldShiftDefId != null">
        #{oldShiftDefId,jdbcType=INTEGER},
      </if>
      <if test="newShiftDefId != null">
        #{newShiftDefId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="crtuser != null">
        #{crtuser,jdbcType=BIGINT},
      </if>
      <if test="crttime != null">
        #{crttime,jdbcType=BIGINT},
      </if>
      <if test="upduser != null">
        #{upduser,jdbcType=BIGINT},
      </if>
      <if test="updtime != null">
        #{updtime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo">
    update wa_emp_shift_change
    <set>
      <if test="belongOrgId != null">
        belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="empid != null">
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="workDate != null">
        work_date = #{workDate,jdbcType=BIGINT},
      </if>
      <if test="oldShiftDefId != null">
        old_shift_def_id = #{oldShiftDefId,jdbcType=INTEGER},
      </if>
      <if test="newShiftDefId != null">
        new_shift_def_id = #{newShiftDefId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="crtuser != null">
        crtuser = #{crtuser,jdbcType=BIGINT},
      </if>
      <if test="crttime != null">
        crttime = #{crttime,jdbcType=BIGINT},
      </if>
      <if test="upduser != null">
        upduser = #{upduser,jdbcType=BIGINT},
      </if>
      <if test="updtime != null">
        updtime = #{updtime,jdbcType=BIGINT},
      </if>
    </set>
    where rec_id = #{recId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftChangePo">
    update wa_emp_shift_change
    set belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      empid = #{empid,jdbcType=BIGINT},
      work_date = #{workDate,jdbcType=BIGINT},
      old_shift_def_id = #{oldShiftDefId,jdbcType=INTEGER},
      new_shift_def_id = #{newShiftDefId,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      crtuser = #{crtuser,jdbcType=BIGINT},
      crttime = #{crttime,jdbcType=BIGINT},
      upduser = #{upduser,jdbcType=BIGINT},
      updtime = #{updtime,jdbcType=BIGINT}
    where rec_id = #{recId,jdbcType=INTEGER}
  </update>

  <select id="queryByParams" resultMap="BaseResultMap">
    select * from wa_emp_shift_change
    where belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and status = #{status}
    and work_date between #{startDate} and #{endDate}
    <if test="empIds != null and empIds.size() > 0">
      and empid in
      <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
        #{empId}
      </foreach>
    </if>
  </select>

  <insert id="saveEmpShiftChanges">
    <if test="changes != null and changes.size() > 0">
      <foreach collection="changes" item="change" separator=";">
        insert into wa_emp_shift_change (belong_org_id, empid, work_date, old_shift_def_id, new_shift_def_id, remark, status, crtuser, crttime, upduser, updtime)
        values (#{change.belongOrgId}, #{change.empid}, #{change.workDate}, #{change.oldShiftDefId}, #{change.newShiftDefId}, #{change.remark},
        #{change.status}, #{change.crtuser}, #{change.crttime}, #{change.upduser}, #{change.updtime})
      </foreach>
    </if>
  </insert>

  <update id="updateEmpShiftChanges">
    <if test="changes != null and changes.size() > 0">
      <foreach collection="changes" item="change" separator=";">
        update wa_emp_shift_change
        set status = #{change.status}, upduser = #{change.upduser}, updtime = #{change.updtime}
        where rec_id = #{change.recId}
      </foreach>
    </if>
  </update>
</mapper>