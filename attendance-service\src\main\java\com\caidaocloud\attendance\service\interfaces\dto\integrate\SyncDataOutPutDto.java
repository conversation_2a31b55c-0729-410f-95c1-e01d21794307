package com.caidaocloud.attendance.service.interfaces.dto.integrate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;


@Data
public class SyncDataOutPutDto implements Serializable {
    private static final long serialVersionUID = -6179280905707357176L;

    @ApiModelProperty("数据输出ID")
    private Integer sysDataOutputId;

    @ApiModelProperty("params 参数")
    private Map params;
}
