package com.caidaocloud.attendance.service;

import com.caidaocloud.util.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.TimeUnit;

@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class SmsCodeTest {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void sendSmsCode(){
        String phoneNumber = "15121098888";
        Long expTime = stringRedisTemplate.getExpire("msgCode" + "15121098888", TimeUnit.SECONDS);
        System.out.println(expTime);

        String redisCode = stringRedisTemplate.opsForValue().get("msgCode" + phoneNumber);
        if(StringUtil.isEmpty(redisCode)){
            stringRedisTemplate.opsForValue().set("msgCode" + phoneNumber, "6666", 60 * 5, TimeUnit.SECONDS);
        }else {
            System.out.println("存在");
        }


    }
}
