package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.service.IWorkflowRevokeService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.RevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.attendance.service.interfaces.vo.EmpTravelRevokeVo;
import com.caidaocloud.attendance.service.interfaces.vo.OvertimeRevokeApplyVo;
import com.caidaocloud.attendance.service.interfaces.vo.leave.EmpLeaveRevokeVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/attendance/workflowRevoke/v1")
public class WorkflowRevokeController {

    @Autowired
    private IWorkflowRevokeService workflowRevokeService;
    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @PostMapping("/overtimeRevokeList")
    @ApiOperation(value = "加班流程撤销列表")
    public Result<AttendancePageResult<OvertimeRevokeApplyVo>> getOvertimeWorkflowRevokeList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OVERTIME_WORKFLOW_REVOKE_LIST, "b");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("加班流程撤销列表 DataScope = {}", dataScope);
            PageList<Map> pageList = workflowRevokeService.getOvertimeWorkflowRevokeList(dto, pageBean, getUserInfo());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<OvertimeRevokeApplyVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(OvertimeRevokeApplyVo.class);
            AttendancePageResult<OvertimeRevokeApplyVo> pageResult = new AttendancePageResult<OvertimeRevokeApplyVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.getOvertimeWorkflowRevokeList execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @PostMapping("/overtimeAbolishList")
    @ApiOperation(value = "加班流程废止列表")
    public Result<AttendancePageResult<OvertimeRevokeApplyVo>> getOvertimeWorkflowAbolishList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OVERTIME_WORKFLOW_ABOLISH_LIST, "b");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("加班流程废止列表 DataScope = {}", dataScope);
            PageList<Map> pageList = workflowRevokeService.getOvertimeWorkflowAbolishList(dto, pageBean, getUserInfo());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<OvertimeRevokeApplyVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(OvertimeRevokeApplyVo.class);
            AttendancePageResult<OvertimeRevokeApplyVo> pageResult = new AttendancePageResult<OvertimeRevokeApplyVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.getOvertimeWorkflowAbolishList execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @PostMapping("/travelRevokeList")
    @ApiOperation(value = "出差流程撤销列表")
    public Result<AttendancePageResult<EmpTravelRevokeVo>> getTravelWorkflowRevokeList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        try {
            UserInfo userInfo = this.getUserInfo();
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.TRAVEL_WORKFLOW_REVOKE_LIST, "ei");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                        .replaceAll("empid", "ei.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("出差流程撤销列表 DataScope = {}", dataScope);
            PageList<Map> pageList = workflowRevokeService.getTravelWorkflowRevokeList(dto, userInfo);
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageBean pageBean = PageUtil.getNewPageBean(dto);
            List<EmpTravelRevokeVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(EmpTravelRevokeVo.class);
            AttendancePageResult<EmpTravelRevokeVo> pageResult = new AttendancePageResult<>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.getTravelWorkflowRevokeList execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @PostMapping("/travelAbolishList")
    @ApiOperation(value = "出差流程废止列表")
    public Result<AttendancePageResult<EmpTravelRevokeVo>> getTravelWorkflowAbolishList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        try {
            UserInfo userInfo = this.getUserInfo();
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.TRAVEL_WORKFLOW_ABOLISH_LIST, "ei");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                        .replaceAll("empid", "ei.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("出差流程废止列表 DataScope = {}", dataScope);
            PageList<Map> pageList = workflowRevokeService.getTravelWorkflowAbolishList(dto, userInfo);
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageBean pageBean = PageUtil.getNewPageBean(dto);
            List<EmpTravelRevokeVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(EmpTravelRevokeVo.class);
            AttendancePageResult<EmpTravelRevokeVo> pageResult = new AttendancePageResult<>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.getTravelWorkflowAbolishList execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销发起的审批流撤销申请")
    public Result<Boolean> revoke(@RequestBody RevokeDto dto) {
        try {
            UserInfo userInfo = this.getUserInfo();
            return workflowRevokeService.revoke(dto, userInfo);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.revoke failed {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, Boolean.FALSE);
        }
    }

    @PostMapping("/leaveRevokeList")
    @ApiOperation(value = "休假流程撤销列表")
    public Result<AttendancePageResult<EmpLeaveRevokeVo>> getLeaveWorkflowRevokeList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_REVOKE_LIST, "ei");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                        .replaceAll("empid", "ei.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("休假流程撤销列表 DataScope = {}", dataScope);
            PageList<Map> pageList = workflowRevokeService.getLeaveWorkflowRevokeList(dto, getUserInfo());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageBean pageBean = PageUtil.getNewPageBean(dto);
            List<EmpLeaveRevokeVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(EmpLeaveRevokeVo.class);
            AttendancePageResult<EmpLeaveRevokeVo> pageResult = new AttendancePageResult<>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.getLeaveWorkflowRevokeList execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @PostMapping("/leaveAbolishList")
    @ApiOperation(value = "休假流程废止列表")
    public Result<AttendancePageResult<EmpLeaveRevokeVo>> getLeaveWorkflowAbolishList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_ABOLISH_LIST, "ei");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                        .replaceAll("empid", "ei.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("休假流程废止列表 DataScope = {}", dataScope);
            PageList<Map> pageList = workflowRevokeService.getLeaveWorkflowAbolishList(dto, getUserInfo());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageBean pageBean = PageUtil.getNewPageBean(dto);
            List<EmpLeaveRevokeVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(EmpLeaveRevokeVo.class);
            AttendancePageResult<EmpLeaveRevokeVo> pageResult = new AttendancePageResult<>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("WorkflowRevokeController.getLeaveWorkflowAbolishList execute exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }
}
