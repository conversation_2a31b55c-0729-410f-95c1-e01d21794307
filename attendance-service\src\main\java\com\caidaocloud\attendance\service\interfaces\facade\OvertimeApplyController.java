package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.OvertimeCarryForwardDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OtLeftDurationDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OvertimeLeftDurationDto;
import com.caidaocloud.attendance.service.application.service.IOvertimeApplyService;
import com.caidaocloud.attendance.service.application.service.impl.WorkOvertimeService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplyRevokeDto;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplySaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.OvertimeApplyDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OtLeftDurationRequestDto;
import com.caidaocloud.attendance.service.interfaces.vo.OtCompensateTypeVo;
import com.caidaocloud.attendance.service.interfaces.vo.OverReApplyVo;
import com.caidaocloud.attendance.service.interfaces.vo.OvertimeApplyVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OtLeftDurationVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeApplyTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeCarryForwardVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeLeftDurationVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 加班申请
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/overtimeapply/v1")
@Api(value = "/api/attendance/overtimeapply/v1", description = "加班记录接口")
public class OvertimeApplyController {
    @Resource
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private WorkOvertimeService workOvertimeService;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private CacheService cacheService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("加班记录列表")
    @PostMapping(value = "/getEmpOtList")
    @Security(code = "OvertimeApplyRecordList")
    public Result<AttendancePageResult<OvertimeApplyVo>> getEmpOtList(@RequestBody OvertimeApplyDto dto, HttpServletRequest request) {
        PageBean pageBean = PageUtil.getPageBean(dto);
        String belongId = getUserInfo().getTenantId();
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("getOvertimeList");
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OVERTIME_APPLY_RECORD_LIST, "b");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
                dataScope = dto.getDataScope();
            }
            log.info("加班记录列表 dataScope = {}", dataScope);
            List<Map> list = workOvertimeService.getOvertimeList(belongId, pageBean, null, dto.getStartDate(), dto.getStartTime(), dto.getEndDate(), dto.getEndTime(), dataScope);
            stopWatch.stop();
            log.info("-----------------------------,{}", stopWatch.prettyPrint());
            if (CollectionUtils.isEmpty(list)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageList pageList = (PageList) list;
            List<OvertimeApplyVo> overtimeApplyVos = JSON.parseArray(JSON.toJSONString(list), OvertimeApplyVo.class);
            overtimeApplyVos.forEach(row -> {
                row.setFuncType(BaseConst.WF_FUNC_TYPE_2);
                if (StringUtils.isBlank(row.getBusinessKey())) {
                    row.setBusinessKey(String.valueOf(row.getWaid()));
                }
            });
            AttendancePageResult<OvertimeApplyVo> pageResult = new AttendancePageResult<>(overtimeApplyVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("OvertimeApplyController.getEmpOtList executes exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("获取补偿方式数据，1加班费 2调休 其他")
    @GetMapping(value = "/getCompensateTypeList")
    public Result<List<OtCompensateTypeVo>> getCompensateTypeList(@RequestParam(value = "empid", required = false) Long empid,
                                                                  @RequestParam("start") Long start,
                                                                  @RequestParam("end") Long end) {
        if (null == empid) {
            empid = getUserInfo().getStaffId();
        }
        return overtimeApplyService.getCompensateTypeList(empid, start, end);
    }

    @ApiOperation("获取加班时长")
    @GetMapping(value = "/getOtTotaltime")
    public Result getOtTotaltime(@RequestParam(value = "empId", required = false) Long empId,
                                 @RequestParam("overtimeTypeId") Integer overtimeTypeId,
                                 @RequestParam("startTime") Integer startTime,
                                 @RequestParam("endTime") Integer endTime,
                                 @RequestParam("stime") Integer stime,
                                 @RequestParam("etime") Integer etime) {
        try {
            UserInfo userInfo = getUserInfo();
            empId = empId == null ? userInfo.getStaffId() : empId;
            OverApplySaveDto overtimeSaveDto = new OverApplySaveDto();
            overtimeSaveDto.setEmpId(empId);
            overtimeSaveDto.setOvertimeTypeId(overtimeTypeId);
            overtimeSaveDto.setStartTime(startTime);
            overtimeSaveDto.setEndTime(endTime);
            overtimeSaveDto.setStime(stime);
            overtimeSaveDto.setEtime(etime);
            Map map = overtimeApplyService.getOtTotalTime(overtimeSaveDto);
            if (Integer.parseInt(map.get("status").toString()) == -1) {
                return Result.fail(String.valueOf(map.get("message")));
            }
            return ResponseWrap.wrapResult(map.get("data"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_OVERTIME_DURATION_FAILED, null);
        }
    }

    @ApiOperation("选人加班申请保存")
    @PostMapping(value = "/saveOverApply")
    @LogRecordAnnotation(success = "申请了{empName{#empId}}的{{#content}}", category = "申请", menu = "加班管理-加班记录")
    public Result<?> saveOverApply(@RequestBody OverApplySaveDto dto) {
        LogRecordContext.putVariable("empId",dto.getEmpId());
        AppTypeEnum appType = dto.getAppType();
        if (null == appType) {
            if (null == dto.getEmpId()) {
                dto.setEmpId(getUserInfo().getStaffId());
                appType = AppTypeEnum.APP_H5;
            } else {
                appType = AppTypeEnum.APP_BACK;
            }
        }
        if (dto.getReason().length() >= 500) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_REASON_TOO_LONG, Boolean.FALSE);
        }
        UserInfo userInfo = getUserInfo();
        String lockKey = MessageFormat.format("OTAPPLY_LOCK_{0}_{1}_{2}_{3}", userInfo.getTenantId(), userInfo.getUserId(), dto.getStartTime(), dto.getEndTime());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, null);
        }
        cacheService.cacheSet(lockKey, "1", 60);
        try {
            Result<?> result = overtimeApplyService.saveOtData(dto, appType);
            if (!result.isSuccess()) {
                return result;
            }
            OvertimeApplyTimeVo data = (OvertimeApplyTimeVo) result.getData();
            return Result.ok(data.getWfBusKey());
        } catch (Exception e) {
            log.error("Failed to apply for overtime, reason[{}]", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("选人加班申请重新发起")
    @PostMapping(value = "/saveOvertimeReApply")
    public Result saveOvertimeReApply(@RequestBody OverApplySaveDto dto) {
        if (dto.getReason().length() >= 500) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_REASON_TOO_LONG, Boolean.FALSE);
        }
        try {
            AppTypeEnum appType = dto.getAppType();
            if (null == appType) {
                appType = AppTypeEnum.APP_BACK;
            }
            Result<?> result = overtimeApplyService.saveOtData(dto, appType);
            if (!result.isSuccess()) {
                return result;
            }
            OvertimeApplyTimeVo data = (OvertimeApplyTimeVo) result.getData();
            return Result.ok(data.getWfBusKey());
        } catch (Exception e) {
            log.error("Failed to reapply for overtime, reason[{}]", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        }
    }

    @ApiOperation("加班申请重新发起获取数据")
    @GetMapping(value = "/getEmpOtById")
    public Result<OverReApplyVo> getEmpOtById(@RequestParam("waid") Integer waid) {
        Map map = overtimeApplyService.getOvertimeInfoById(waid);
        OverReApplyVo overReApplyVo = JSON.parseObject(JSON.toJSONString(map)).toJavaObject(OverReApplyVo.class);
        return ResponseWrap.wrapResult(overReApplyVo);
    }

    @ApiOperation("撤销加班申请")
    @PostMapping(value = "/revokeEmpOt")
    @LogRecordAnnotation(success = "撤销了数据", category = "撤销", menu = "加班管理-加班记录")
    public Result<Boolean> revokeEmpOt(@RequestBody OverApplyRevokeDto dto) {
        if (StringUtils.isEmpty(dto.getRecokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRecokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        UserInfo userInfo = this.getUserInfo();
        try {
            Result<Boolean> result = overtimeApplyService.revokeEmpOt(dto, userInfo);

            return result;
        } catch (Exception e) {
            log.error("revokeEmpOt error msg {}", e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("加班转调休记录列表")
    @GetMapping(value = "/getEmpOtCompensatoryList")
    public Result<List<OvertimeCarryForwardVo>> getEmpOtCompensatoryList(@RequestParam("quotaId") Long quotaId) {
        String tenantId = getUserInfo().getTenantId();
        try {
            List<OvertimeCarryForwardDto> list = overtimeApplyService.getEmpOtCompensatoryList(tenantId, quotaId);
            if (CollectionUtils.isEmpty(list)) {
                return ResponseWrap.wrapResult(new ArrayList<>());
            }
            return ResponseWrap.wrapResult(ObjectConverter.convertList(list, OvertimeCarryForwardVo.class));
        } catch (Exception e) {
            log.error("OvertimeApplyController.getEmpOtCompensatoryList executes exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_OVERTIME_TO_COMPENSATORY_DETAIL_FAILED, new ArrayList<>());
        }
    }

    @ApiOperation("加班结余时长")
    @GetMapping("/getEmpOvertimeLeftDuration")
    public Result<OvertimeLeftDurationVo> getEmpOvertimeLeftDuration(@RequestParam(value = "empId", required = false) Long empId) {
        try {
            if (null == empId) {
                UserInfo userInfo = getUserInfo();
                empId = userInfo.getStaffId();
            }
            OvertimeLeftDurationDto dto = overtimeApplyService.getEmpOvertimeLeftDuration(empId);
            return Result.ok(ObjectConverter.convert(dto, OvertimeLeftDurationVo.class));
        } catch (Exception e) {
            log.error("getEmpOvertimeLeftDuration exception:{}", e.getMessage(), e);
            return Result.fail();
        }
    }

    @ApiOperation("加班结余时长列表")
    @PostMapping(value = "/getEmpOtLeftDurationList")
    public Result<AttendancePageResult<OtLeftDurationVo>> getEmpOtLeftDurationList(@RequestBody OtLeftDurationRequestDto dto, HttpServletRequest request) {
        try {
            String dataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(dataScope)) {
                dto.setDataScope(dataScope);
            }
            log.info("加班结余时长列表 dataScope = {}", dataScope);
            AttendancePageResult<OtLeftDurationDto> pageResult = overtimeApplyService.getEmpOvertimeLeftDurationPageList(dto, getUserInfo());
            List<OtLeftDurationVo> items = JSON.parseArray(JSON.toJSONString(pageResult.getItems()), OtLeftDurationVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
        } catch (Exception e) {
            log.error("OvertimeApplyController.getEmpOtLeftDurationList executes exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }
}
