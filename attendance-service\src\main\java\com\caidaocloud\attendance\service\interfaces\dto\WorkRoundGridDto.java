package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/2/9
 */
@Data
public class WorkRoundGridDto implements Serializable {
    private static final long serialVersionUID = 7026287618315435192L;

    @ApiModelProperty("排班计划ID")
    private Integer workRoundId;

    @ApiModelProperty("排班名称")
    private String roundName;

    @ApiModelProperty("排班周期")
    private String schedulingCycle;
}
