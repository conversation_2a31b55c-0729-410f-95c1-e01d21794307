package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.interfaces.dto.WorkflowCallBackHandleLeaveDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流回掉2.0
 *
 * <AUTHOR>
 * @Date 2022/8/4 17:14
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/workflow/v1")
public class WorkFlowCallBackController {
    @Autowired
    private WorkflowCallBackService workflowCallBackService;

    /**
     * 请假回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/leave")
    @ApiOperation(value = "休假回调接口")
    public Result<Boolean> leaveCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveWfLeaveApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back leave error: {}", e.getMessage());
            return Result.fail("请假回调失败！");
        }
    }

    /**
     * 批量休假回调接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchLeave")
    @ApiOperation(value = "批量休假回调接口")
    public Result<Boolean> batchLeaveCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.batchLeaveCallback(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back batch-leave error: {}", e.getMessage());
            return Result.fail("批量请假回调失败！");
        }
    }

    /**
     * 批量加班回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchOvertime")
    @ApiOperation(value = "批量加班回调接口")
    public Result<Boolean> batchOvertimeCallBack(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveBatchOvertimeApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back batch-overtime error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("批量加班回调失败！");
        }
    }

    /**
     * 批量考勤异常申请回调接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchAnalyseAdjust")
    @ApiOperation(value = "批量考勤异常申请回调接口")
    public Result<Boolean> batchAnalyseAdjustCallBack(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveBatchAnalyseAdjustApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back batch analyse adjust error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("批量考勤异常申请回调失败！");
        }
    }

    /**
     * 加班回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/overtime")
    @ApiOperation(value = "加班回调接口")
    public Result<Boolean> overTimeCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveWfOtApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back overtime error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("加班回调失败！");
        }
    }

    @PostMapping("/register")
    @ApiOperation(value = "补卡回调接口")
    public Result<Boolean> registerCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveWfBdkApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back register error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("补卡回调失败！");
        }
    }

    @PostMapping("/travel")
    @ApiOperation(value = "出差回调接口")
    public Result<Boolean> travelCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveTravelApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call travel travel error:{}", e.getMessage(), e);
            return Result.fail("出差回调失败！");
        }
    }

    @PostMapping("/batchTravel")
    @ApiOperation(value = "批量出差回调接口")
    public Result<Boolean> batchTravelCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveBatchTravelApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call batch travel error:{}", e.getMessage(), e);
            return Result.fail("批量出差回调失败！");
        }
    }

    @PostMapping("/shiftChange")
    @ApiOperation(value = "调班回调接口")
    public Result<Boolean> shiftChange(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveShiftApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call shiftChange error:{}", e.getMessage(), e);
            return Result.fail("调班回调失败！");
        }
    }

    @PostMapping("/leaveCancel")
    @ApiOperation(value = "销假回调接口")
    public Result<Boolean> leaveCancel(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveLeaveCancelApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call leave cancel error:{}", e.getMessage(), e);
            return Result.fail("销假回调失败！");
        }
    }

    @PostMapping("/handleLeave")
    @ApiOperation(value = "撤销销假休假单处理接口")
    public Result<Boolean> handleLeave(@RequestBody WorkflowCallBackHandleLeaveDto dto) {
        try {
            workflowCallBackService.handleLeave(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call handleLeave error:{}", e.getMessage(), e);
            return Result.fail("撤销销假休假单处理失败！");
        }
    }

    @PostMapping("/compensatory")
    @ApiOperation(value = "调休付现回调接口")
    public Result<Boolean> compensatoryCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.compensatoryCallback(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call handleLeave error:{}", e.getMessage(), e);
            return Result.fail("调休付现回调失败！");
        }
    }

    /**
     * 加班撤销审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/overtimeRevoke")
    @ApiOperation(value = "加班撤销审批回调接口")
    public Result<Boolean> overTimeRevokeCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveWfOtRevokeApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back overTimeRevokeCallback error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("加班撤销审批回调失败！");
        }
    }

    /**
     * 加班废止审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/overtimeAbolish")
    @ApiOperation(value = "加班废止审批回调接口")
    public Result<Boolean> overTimeAbolishCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveWfOtAbolishApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back overTimeAbolishCallback error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("加班撤销审批回调失败！");
        }
    }

    /**
     * 出差撤销审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/travelRevoke")
    @ApiOperation(value = "出差撤销审批回调接口")
    public Result<Boolean> travelRevokeCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveTravelRevokeApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call travel travelRevokeCallback error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("出差撤销审批回调失败！");
        }
    }

    /**
     * 出差废止审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/travelAbolish")
    @ApiOperation(value = "出差废止审批回调接口")
    public Result<Boolean> travelAbolishCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveTravelAbolishApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call travel travelAbolishCallback error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("出差废止审批回调失败！");
        }
    }

    /**
     * 假期延期审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/leaveExtension")
    @ApiOperation(value = "假期延期审批回调")
    public Result<Boolean> leaveExtensionCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveWfLeaveExtensionApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back leaveExtensionCallback error: {}", e.getMessage());
            return Result.fail("假期延期审批回调失败！");
        }
    }

    /**
     * 批量出差撤销审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchTravelRevoke")
    @ApiOperation(value = "批量出差撤销审批回调接口")
    public Result<Boolean> batchTravelRevokeCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveBatchTravelRevokeApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call travel batchTravelRevokeCallback error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("出差撤销审批回调失败！");
        }
    }

    /**
     * 批量出差废止审批回调
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchTravelAbolish")
    @ApiOperation(value = "批量出差废止审批回调接口")
    public Result<Boolean> batchTravelAbolishCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.saveBatchTravelAbolishApproval(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call travel batchTravelAbolishCallback error:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("批量出差废止审批回调失败！");
        }
    }

    /**
     * 批量休假撤销回调接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchLeaveRevoke")
    @ApiOperation(value = "批量休假撤销回调接口")
    public Result<Boolean> batchLeaveRevokeCallback(@RequestBody WfCallbackResultDto dto) {
        log.info("批量休假撤销回调接口,params:{}", FastjsonUtil.toJsonStr(dto));
        try {
            workflowCallBackService.batchLeaveRevokeCallback(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back batch-leave revoke error: {}", e.getMessage());
            return Result.fail("批量请假撤销回调失败！");
        }
    }

    /**
     * 批量休假废止回调接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchLeaveAbolish")
    @ApiOperation(value = "批量休假废止回调接口")
    public Result<Boolean> batchLeaveAbolishCallback(@RequestBody WfCallbackResultDto dto) {
        try {
            workflowCallBackService.batchLeaveAbolishCallback(dto);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("workflow call back batch-leave abolish error: {}", e.getMessage());
            return Result.fail("批量请假废止回调失败！");
        }
    }
}
