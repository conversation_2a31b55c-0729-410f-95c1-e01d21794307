org.quartz.scheduler.instanceName=quartzScheduler
org.quartz.scheduler.instanceId=AUTO

org.quartz.jobStore.useProperties=true
org.quartz.jobStore.misfireThreshold=60000
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
org.quartz.jobStore.selectWithLockSQL=SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?

org.quartz.jobStore.tablePrefix=QRTZ_
org.quartz.jobStore.isClustered=false
org.quartz.threadPool.threadCount=3

#org.quartz.jobStore.dataSource=qzDS
#org.quartz.dataSource.qzDS.connectionProvider.class = org.quartz.utils.HikariCpPoolingConnectionProvider
#org.quartz.dataSource.qzDS.driver = org.postgresql.Driver
#org.quartz.dataSource.qzDS.URL = *******************************************
#org.quartz.dataSource.qzDS.user = admin
#org.quartz.dataSource.qzDS.password = AU3hO4wtXYcBkXBu
#org.quartz.dataSource.qzDS.maxConnections = 10