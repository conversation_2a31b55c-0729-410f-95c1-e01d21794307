package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShiftPageVo {

    @ApiModelProperty("日期类型：1、工作日，2休息日，3法定假日")
    private Object dateType;

    @ApiModelProperty("是否跨夜")
    private String isNight;

    @ApiModelProperty("是否有中午休息时间")
    private Boolean isNoonRest;

    @ApiModelProperty("休息结束时间")
    private Integer noonRestEnd;

    @ApiModelProperty("休息开始时间")
    private Integer noonRestStart;

    @ApiModelProperty("下班打卡截止时间")
    private Integer offDutyEndTime;

    @ApiModelProperty("下班打卡开始时间")
    private Integer offDutyStartTime;

    @ApiModelProperty("上班打卡截止时间")
    private Integer onDutyEndTime;

    @ApiModelProperty("上班打卡开始时间")
    private Integer onDutyStartTime;

    @ApiModelProperty("加班打卡开始时间")
    private Integer overtimeStartTime;

    @ApiModelProperty("加班打卡结束时间")
    private Integer overtimeEndTime;

    @ApiModelProperty("加班休息时间")
    private Object overtimeRestPeriods;

    @ApiModelProperty("总休息时长")
    private Integer restTotalTime;

    @ApiModelProperty("班次代码")
    private String shiftDefCode;

    @ApiModelProperty("班次Id")
    private Integer shiftDefId;

    @ApiModelProperty("班次名称")
    private String shiftDefName;

    @ApiModelProperty("上班时间")
    private Integer startTime;

    @ApiModelProperty("下班时间")
    private Integer endTime;

    @ApiModelProperty("总工作时长")
    private Integer workTotalTime;

    private Object restPeriods;
}
