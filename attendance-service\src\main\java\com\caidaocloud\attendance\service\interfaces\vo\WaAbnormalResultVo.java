package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidao1.wa.mybatis.model.WaAnalyze;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤异常结果VO
 *
 * <AUTHOR>
 * @Date 2024/6/24
 */
@Data
@ApiModel("考勤异常结果VO")
public class WaAbnormalResultVo {
    @ApiModelProperty("考勤日期")
    private Long belongDate;

    @ApiModelProperty("迟到时长")
    private Float originalLateTime;

    @ApiModelProperty("早退时长")
    private Float originalEarlyTime;

    @ApiModelProperty("旷工时长")
    private Integer originalKgWorkTime;

    @ApiModelProperty("原签到ID")
    private Integer originalSigninId;

    @ApiModelProperty("原签退ID")
    private Integer originalSignoffId;

    @ApiModelProperty("原签到时间")
    private Long originalSigninTime;

    @ApiModelProperty("原签退时间")
    private Long originalSignoffTime;

    public static WaAbnormalResultVo convert2Vo(WaAnalyze waAnalyze) {
        WaAbnormalResultVo vo = new WaAbnormalResultVo();
        vo.setBelongDate(waAnalyze.getBelongDate());
        vo.setOriginalLateTime(waAnalyze.getLateTime());
        vo.setOriginalEarlyTime(waAnalyze.getEarlyTime());
        vo.setOriginalKgWorkTime(waAnalyze.getKgWorkTime());
        vo.setOriginalSigninId(waAnalyze.getSigninId());
        vo.setOriginalSignoffId(waAnalyze.getSignoffId());
        vo.setOriginalSigninTime(waAnalyze.getRegSigninTime());
        vo.setOriginalSignoffTime(waAnalyze.getRegSignoffTime());
        return vo;
    }
}
