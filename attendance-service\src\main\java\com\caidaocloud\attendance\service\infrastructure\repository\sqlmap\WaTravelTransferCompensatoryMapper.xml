<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaTravelTransferCompensatoryMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="quota_id" jdbcType="BIGINT" property="quotaId" />
    <result column="sob_id" jdbcType="INTEGER" property="sobId" />
    <result column="quota_unit" jdbcType="INTEGER" property="quotaUnit" />
    <result column="quota_day" jdbcType="REAL" property="quotaDay" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, emp_id, quota_id, sob_id, quota_unit, quota_day, status, deleted, 
    create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_travel_transfer_compensatory
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_travel_transfer_compensatory
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    insert into wa_travel_transfer_compensatory (id, tenant_id, emp_id, 
      quota_id, sob_id, quota_unit, 
      quota_day, status, deleted, 
      create_by, create_time, update_by, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{empId,jdbcType=BIGINT}, 
      #{quotaId,jdbcType=BIGINT}, #{sobId,jdbcType=INTEGER}, #{quotaUnit,jdbcType=INTEGER}, 
      #{quotaDay,jdbcType=REAL}, #{status,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    insert into wa_travel_transfer_compensatory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="sobId != null">
        sob_id,
      </if>
      <if test="quotaUnit != null">
        quota_unit,
      </if>
      <if test="quotaDay != null">
        quota_day,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=BIGINT},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="sobId != null">
        #{sobId,jdbcType=INTEGER},
      </if>
      <if test="quotaUnit != null">
        #{quotaUnit,jdbcType=INTEGER},
      </if>
      <if test="quotaDay != null">
        #{quotaDay,jdbcType=REAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    update wa_travel_transfer_compensatory
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=BIGINT},
      </if>
      <if test="quotaId != null">
        quota_id = #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="sobId != null">
        sob_id = #{sobId,jdbcType=INTEGER},
      </if>
      <if test="quotaUnit != null">
        quota_unit = #{quotaUnit,jdbcType=INTEGER},
      </if>
      <if test="quotaDay != null">
        quota_day = #{quotaDay,jdbcType=REAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    update wa_travel_transfer_compensatory
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=BIGINT},
      quota_id = #{quotaId,jdbcType=BIGINT},
      sob_id = #{sobId,jdbcType=INTEGER},
      quota_unit = #{quotaUnit,jdbcType=INTEGER},
      quota_day = #{quotaDay,jdbcType=REAL},
      status = #{status,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryTravelCompensatoryRecordList" parameterType="hashmap" resultType="com.caidaocloud.attendance.service.domain.entity.TravelCompensatoryDo">
    select * from(
    SELECT a.id,
    b.workno workNo,
    b.emp_name empName,
    b.orgid,
    a.quota_day quotaDay,
    a.quota_unit quotaUnit,
    a.create_time createTime,
    a.status,
    lqc.rule_name quotaName,
    ws.sys_period_month periodMonth
    FROM wa_travel_transfer_compensatory a
    JOIN sys_emp_info b ON a.emp_id = b.empid AND b.deleted = 0
    JOIN wa_emp_compensatory_quota c ON c.quota_id=a.quota_id
    LEFT JOIN sys_corp_org sco on b.orgid = sco.orgid AND sco.deleted = 0
    LEFT JOIN wa_leave_quota_config lqc ON lqc.config_id = c.config_id
    LEFT JOIN wa_sob ws on ws.wa_sob_id=a.sob_id
    <where>
      AND a.tenant_id=#{tenantId} AND a.deleted=0
      <if test="dataFilter != null and dataFilter != ''">
        ${dataFilter}
      </if>
      <if test="status != null">
        AND a.status = #{status}
      </if>
      <if test="empId != null">
        AND a.emp_id = #{empId}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (b.workno like concat('%', #{keywords}, '%') or b.emp_name like concat('%', #{keywords}, '%'))
      </if>
    </where>) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
    order by createTime desc
  </select>

  <select id="batchInsert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    <foreach collection="records" item="record" separator=";">
      insert into wa_travel_transfer_compensatory (id, tenant_id, emp_id, quota_id, sob_id, quota_unit, quota_day, status, deleted,
      create_by, create_time, update_by, update_time)
      values (#{record.id,jdbcType=BIGINT}, #{record.tenantId,jdbcType=VARCHAR}, #{record.empId,jdbcType=BIGINT},
      #{record.quotaId,jdbcType=BIGINT}, #{record.sobId,jdbcType=INTEGER}, #{record.quotaUnit,jdbcType=INTEGER},
      #{record.quotaDay,jdbcType=REAL}, #{record.status,jdbcType=INTEGER}, #{record.deleted,jdbcType=INTEGER},
      #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT}, #{record.updateBy,jdbcType=BIGINT},
      #{record.updateTime,jdbcType=BIGINT})
    </foreach>
  </select>

  <select id="getDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.TravelCompensatoryDo">
    select ttc.*,
           sei.workno workNo,
           sei.emp_name empName,
           coalesce(sei2.emp_name, sui.empname) initiator,
           sei2.workno initiatorWorkNo
    from wa_travel_transfer_compensatory ttc
           left join sys_emp_info sei on sei.empid=ttc.emp_id
           left join sys_user_info sui on ttc.create_by=sui.userid
           left join sys_emp_info sei2 on sui.empid=sei2.empid
    where ttc.tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id}
  </select>

  <select id="queryTravelCompensatoryRecords" resultType="com.caidaocloud.attendance.service.domain.entity.TravelCompensatoryDo">
    select * from wa_travel_transfer_compensatory
    where tenant_id = #{tenantId} and deleted=0
    <if test="sobIds != null and sobIds.size() > 0">
    and sob_id in
      <foreach collection="sobIds" item="sobId" open="(" close=")" separator=",">
        #{sobId}
      </foreach>
    </if>
  </select>

  <update id="batchUpdate" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelTransferCompensatory">
    <if test="records != null and records.size() > 0">
      <foreach collection="records" item="record" separator=";">
        update wa_travel_transfer_compensatory
        <set>
          <if test="record.tenantId != null">
            tenant_id = #{record.tenantId,jdbcType=VARCHAR},
          </if>
          <if test="record.empId != null">
            emp_id = #{record.empId,jdbcType=BIGINT},
          </if>
          <if test="record.quotaId != null">
            quota_id = #{record.quotaId,jdbcType=BIGINT},
          </if>
          <if test="record.sobId != null">
            sob_id = #{record.sobId,jdbcType=INTEGER},
          </if>
          <if test="record.quotaUnit != null">
            quota_unit = #{record.quotaUnit,jdbcType=INTEGER},
          </if>
          <if test="record.quotaDay != null">
            quota_day = #{record.quotaDay,jdbcType=REAL},
          </if>
          <if test="record.status != null">
            status = #{record.status,jdbcType=INTEGER},
          </if>
          <if test="record.deleted != null">
            deleted = #{record.deleted,jdbcType=INTEGER},
          </if>
          <if test="record.createBy != null">
            create_by = #{record.createBy,jdbcType=BIGINT},
          </if>
          <if test="record.createTime != null">
            create_time = #{record.createTime,jdbcType=BIGINT},
          </if>
          <if test="record.updateBy != null">
            update_by = #{record.updateBy,jdbcType=BIGINT},
          </if>
          <if test="record.updateTime != null">
            update_time = #{record.updateTime,jdbcType=BIGINT},
          </if>
        </set>
        where id = #{record.id,jdbcType=BIGINT}
      </foreach>
    </if>
  </update>
</mapper>