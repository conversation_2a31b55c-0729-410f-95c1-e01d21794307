package com.caidaocloud.attendance.service;

import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.wa.mybatis.model.WaEmpLeave;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ApplyTest {
    @Autowired
    private RemoteImportService importService;

    @Test
    public void getOvertimeList() {
        List<WaEmpLeave> list = new ArrayList<>();

        WaEmpLeave empLeave = new WaEmpLeave();
        empLeave.setEmpid(123456L);
        empLeave.setStatus((short) 1);
        empLeave.setApprovalNum((short) 2);
        empLeave.setCrtuser(123456L);
        empLeave.setCrttime(System.currentTimeMillis());
        list.add(empLeave);
//        waEmpLeaveMapper.insertSelective(empLeave);
        WaEmpLeave empLeave2 = new WaEmpLeave();
        empLeave2.setEmpid(123456L);
        empLeave2.setStatus((short) 1);
        empLeave2.setApprovalNum((short) 2);
        empLeave2.setCrtuser(123456L);
        empLeave2.setCrttime(System.currentTimeMillis());
        list.add(empLeave2);
//        waEmpLeaveMapper.insertSelective(empLeave2);
//        waRegisterRecordMapper.deleteByPrimaryKey(121322456);

        importService.fastInsertList(WaEmpLeave.class, "leaveId", list);
    }
}
