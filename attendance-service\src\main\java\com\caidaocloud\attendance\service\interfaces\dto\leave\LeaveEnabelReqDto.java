package com.caidaocloud.attendance.service.interfaces.dto.leave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LeaveEnabelReqDto implements Serializable {

    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;
    @ApiModelProperty("考勤方案id")
    private Integer waGroupId;
    @ApiModelProperty("假期类型")
    private Integer leaveType;
    @ApiModelProperty("假期类型名称")
    private String leaveName;
    @ApiModelProperty("休假单位")
    private Integer acctTimeType;


}
