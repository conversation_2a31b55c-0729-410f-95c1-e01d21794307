<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpShiftMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo" >
        <id column="emp_shift_id" property="empShiftId" jdbcType="INTEGER" />
        <result column="work_calendar_id" property="workCalendarId" jdbcType="INTEGER" />
        <result column="empid" property="empid" jdbcType="BIGINT" />
        <result column="belong_orgid" property="belongOrgid" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="BIGINT" />
        <result column="end_time" property="endTime" jdbcType="BIGINT" />
        <result column="crttime" property="crttime" jdbcType="BIGINT" />
        <result column="crtuser" property="crtuser" jdbcType="BIGINT" />
        <result column="updtime" property="updtime" jdbcType="BIGINT" />
        <result column="upduser" property="upduser" jdbcType="BIGINT" />
    </resultMap>
    <select id="queryEmpShiftByPeriod" resultMap="BaseResultMap">
        select * from wa_emp_shift
        where belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="empId != null">
            and empid = #{empId}
        </if>
        <if test="startTime != null and endTime != null">
            and ((#{startTime} between start_time and end_time) or (#{endTime} between start_time and end_time)
            or (start_time <![CDATA[>=]]> #{startTime} and end_time <![CDATA[<=]]> #{endTime}))
        </if>
        <if test="empShiftId != null">
            and emp_shift_id <![CDATA[<>]]> #{empShiftId}
        </if>
    </select>

    <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo" >
        insert into wa_emp_shift
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="empShiftId != null" >
                emp_shift_id,
            </if>
            <if test="workCalendarId != null" >
                work_calendar_id,
            </if>
            <if test="empid != null" >
                empid,
            </if>
            <if test="belongOrgid != null" >
                belong_orgid,
            </if>
            <if test="startTime != null" >
                start_time,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="crttime != null" >
                crttime,
            </if>
            <if test="crtuser != null" >
                crtuser,
            </if>
            <if test="updtime != null" >
                updtime,
            </if>
            <if test="upduser != null" >
                upduser,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="empShiftId != null" >
                #{empShiftId},
            </if>
            <if test="workCalendarId != null" >
                #{workCalendarId},
            </if>
            <if test="empid != null" >
                #{empid},
            </if>
            <if test="belongOrgid != null" >
                #{belongOrgid,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                #{startTime},
            </if>
            <if test="endTime != null" >
                #{endTime},
            </if>
            <if test="crttime != null" >
                #{crttime},
            </if>
            <if test="crtuser != null" >
                #{crtuser},
            </if>
            <if test="updtime != null" >
                #{updtime},
            </if>
            <if test="upduser != null" >
                #{upduser},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo" >
        update wa_emp_shift
        <set >
            <if test="workCalendarId != null" >
                work_calendar_id = #{workCalendarId},
            </if>
            <if test="empid != null" >
                empid = #{empid},
            </if>
            <if test="belongOrgid != null" >
                belong_orgid = #{belongOrgid,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                start_time = #{startTime},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime},
            </if>
            <if test="crttime != null" >
                crttime = #{crttime},
            </if>
            <if test="crtuser != null" >
                crtuser = #{crtuser},
            </if>
            <if test="updtime != null" >
                updtime = #{updtime},
            </if>
            <if test="upduser != null" >
                upduser = #{upduser},
            </if>
        </set>
        where emp_shift_id = #{empShiftId}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from wa_emp_shift
        where emp_shift_id = #{empShiftId}
    </delete>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select *
        from wa_emp_shift
        where emp_shift_id = #{empShiftId}
    </select>

    <insert id="batchSave" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpShiftPo">
        <foreach collection="records" item="record" index="index" separator=";">
            insert into wa_emp_shift (work_calendar_id, empid, belong_orgid, start_time, end_time, crttime, crtuser, updtime, upduser)
            values(#{record.workCalendarId}, #{record.empid},#{record.belongOrgid},
            #{record.startTime}, #{record.endTime}, #{record.crttime}, #{record.crtuser}, #{record.updtime}, #{record.upduser})
        </foreach>
    </insert>

    <delete id="deleteByIds">
        delete from wa_emp_shift
        where emp_shift_id in
        <foreach collection="empShiftIds" item="empShiftId" open="(" close=")" separator=",">
            #{empShiftId}
        </foreach>
    </delete>

    <select id="queryEmpShiftByIds" resultMap="BaseResultMap">
        select * from wa_emp_shift
        where emp_shift_id in
        <foreach collection="empShiftIds" item="empShiftId" open="(" close=")" separator=",">
            #{empShiftId}
        </foreach>
    </select>
</mapper>