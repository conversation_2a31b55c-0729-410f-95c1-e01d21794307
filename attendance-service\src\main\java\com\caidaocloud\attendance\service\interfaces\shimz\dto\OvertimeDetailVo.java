package com.caidaocloud.attendance.service.interfaces.shimz.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OvertimeDetailVo {
    @ApiModelProperty("主键id")
    private Integer overtimeId;
    private Integer detailId;
    @ApiModelProperty("员工工号 = 账号")
    private String workno;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("加班类型")
    private String typeName;
    @ApiModelProperty("申请时长")
    private Float timeDuration;
    @ApiModelProperty("有效时长")
    private Float relTimeDuration;
    @ApiModelProperty("审批状态 0暂存 1审批中 2审批通过 3审批不通过 4作废 5已退回 8撤销中 9已撤销")
    private Integer status;
    @ApiModelProperty("审批状态")
    private String statusName;
    @ApiModelProperty("加班事件时间")
    private String timeSlot;
    @ApiModelProperty("申请加班开始时间")
    private String startTime;
    @ApiModelProperty("申请加班结束时间")
    private String endTime;
    @ApiModelProperty("审批日期")
    private String lastApprovalTime;
    @ApiModelProperty("申请日期")
    private String applyTime;
}
