package com.caidaocloud.attendance.service.infrastructure.repository.impl;

import com.caidao1.wa.mybatis.mapper.WaLeaveTypeDefMapper;
import com.caidao1.wa.mybatis.model.WaLeaveTypeDef;
import com.caidao1.wa.mybatis.model.WaLeaveTypeDefExample;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDefDo;
import com.caidaocloud.attendance.service.domain.repository.IWaLeaveTypeDefRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.LeaveTypeDefMapper;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import com.weibo.api.motan.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class WaLeaveTypeDefRepository implements IWaLeaveTypeDefRepository {

    @Autowired
    private LeaveTypeDefMapper leaveTypeDefMapper;

    @Autowired
    private WaLeaveTypeDefMapper waLeaveTypeDefMapper;

    @Override
    public List<WaLeaveTypeDefDo> getWaLeaveTypeDefList(String belongOrgid) {
        return leaveTypeDefMapper.getWaLeaveTypeDefList(belongOrgid);
    }

    @Override
    public void delete(Integer id, String belongOrgId) {
        WaLeaveTypeDef waLeaveTypeDef = new WaLeaveTypeDef();
        waLeaveTypeDef.setDeleted((short) 1);
        waLeaveTypeDef.setLeaveTypeDefId(id);
        waLeaveTypeDefMapper.updateByPrimaryKeySelective(waLeaveTypeDef);
    }

    @Override
    public void save(WaLeaveTypeDefDo defDo, String belongId, Long userId) {
        WaLeaveTypeDef waLeaveTypeDef = ObjectConverter.convert(defDo, WaLeaveTypeDef.class);
        LogRecordContext.putVariable("name", waLeaveTypeDef.getLeaveTypeDefName());
        if (waLeaveTypeDef.getLeaveTypeDefId() != null) {
            WaLeaveTypeDefExample example = new WaLeaveTypeDefExample();
            WaLeaveTypeDefExample.Criteria criteria = example.createCriteria();
            criteria.andLeaveTypeDefIdEqualTo(defDo.getLeaveTypeDefId()).andBelongOrgidEqualTo(defDo.getBelongOrgid());
            List<WaLeaveTypeDef> waLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(example);
            if (CollectionUtil.isEmpty(waLeaveTypeDefList)) {
                return;
            }
            waLeaveTypeDef.setLeaveTypeDefCode(waLeaveTypeDef.getLeaveTypeDefName());
            waLeaveTypeDef.setUpduser(userId);
            waLeaveTypeDef.setUpdtime(System.currentTimeMillis() / 1000L);
            LogRecordContext.putVariable("operate", "编辑");
            waLeaveTypeDefMapper.updateByPrimaryKeySelective(waLeaveTypeDef);
        } else {
            waLeaveTypeDef.setLeaveTypeDefCode(waLeaveTypeDef.getLeaveTypeDefName());
            waLeaveTypeDef.setDeleted((short) 0);
            waLeaveTypeDef.setCrttime(System.currentTimeMillis() / 1000L);
            waLeaveTypeDef.setCrtuser(userId);
            waLeaveTypeDef.setBelongOrgid(belongId);
            LogRecordContext.putVariable("operate", "新增");
            waLeaveTypeDefMapper.insertSelective(waLeaveTypeDef);
        }
    }

    @Override
    public WaLeaveTypeDefDo getDetailList(Integer id) {
        WaLeaveTypeDefExample example = new WaLeaveTypeDefExample();
        WaLeaveTypeDefExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveTypeDefIdEqualTo(id).andBelongOrgidEqualTo("0");
        List<WaLeaveTypeDef> waLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(example);
        return CollectionUtil.isEmpty(waLeaveTypeDefList) ? null : ObjectConverter.convert(waLeaveTypeDefList.get(0), WaLeaveTypeDefDo.class);
    }

    @Override
    public WaLeaveTypeDefDo selectById(String belongOrgid, Integer leaveTypeDefId) {
        WaLeaveTypeDefExample example = new WaLeaveTypeDefExample();
        WaLeaveTypeDefExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveTypeDefIdEqualTo(leaveTypeDefId)
                .andBelongOrgidIn(Lists.newArrayList(belongOrgid, "0"))
                .andStatusEqualTo(0)
                .andDeletedEqualTo((short) 0);
        List<WaLeaveTypeDef> waLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(example);
        return CollectionUtil.isEmpty(waLeaveTypeDefList) ? null :
                ObjectConverter.convert(waLeaveTypeDefList.get(0), WaLeaveTypeDefDo.class);
    }

    @Override
    public List<WaLeaveTypeDefDo> selectByIds(String belongOrgid, List<Integer> leaveTypeDefIds) {
        WaLeaveTypeDefExample example = new WaLeaveTypeDefExample();
        WaLeaveTypeDefExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveTypeDefIdIn(leaveTypeDefIds)
                .andBelongOrgidIn(Lists.newArrayList(belongOrgid, "0"))
                .andStatusEqualTo(0)
                .andDeletedEqualTo((short) 0);
        List<WaLeaveTypeDef> waLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(example);
        return CollectionUtil.isEmpty(waLeaveTypeDefList) ? null :
                ObjectConverter.convertList(waLeaveTypeDefList, WaLeaveTypeDefDo.class);
    }

    @Override
    public WaLeaveTypeDefDo selectByCode(String belongOrgid, String leaveTypeDefCode) {
        WaLeaveTypeDefExample example = new WaLeaveTypeDefExample();
        WaLeaveTypeDefExample.Criteria criteria = example.createCriteria();
        criteria.andLeaveTypeDefCodeEqualTo(leaveTypeDefCode)
                .andBelongOrgidEqualTo(belongOrgid)
                .andStatusEqualTo(0)
                .andDeletedEqualTo((short) 0);
        List<WaLeaveTypeDef> waLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(example);
        return CollectionUtil.isEmpty(waLeaveTypeDefList) ? null :
                ObjectConverter.convert(waLeaveTypeDefList.get(0), WaLeaveTypeDefDo.class);
    }

    @Override
    public WaLeaveTypeDefDo getWaLeaveTypeDefByData(WaLeaveTypeDefDo defDo, String belongId) {
        List<WaLeaveTypeDefDo> waLeaveTypeDefList = leaveTypeDefMapper.getWaLeaveTypeDefByData(belongId, defDo.getLeaveTypeDefName().trim(), defDo.getLeaveTypeDefId());
        return CollectionUtil.isEmpty(waLeaveTypeDefList) ? null : waLeaveTypeDefList.get(0);
    }

    @Override
    public List<WaLeaveTypeDefDo> getAllLeaveTypeDefList(String tenantId, Boolean includeSystem) {
        WaLeaveTypeDefExample example = new WaLeaveTypeDefExample();
        example.createCriteria().andBelongOrgidEqualTo(tenantId);
        List<WaLeaveTypeDef> waLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(example);
        if (includeSystem) {
            WaLeaveTypeDefExample sysExample = new WaLeaveTypeDefExample();
            sysExample.createCriteria().andBelongOrgidEqualTo("0");
            List<WaLeaveTypeDef> sysLeaveTypeDefList = waLeaveTypeDefMapper.selectByExample(sysExample);
            if (!CollectionUtil.isEmpty(waLeaveTypeDefList) && !CollectionUtil.isEmpty(sysLeaveTypeDefList)) {
                waLeaveTypeDefList.addAll(sysLeaveTypeDefList);
            }
        }
        return ObjectConverter.convertList(waLeaveTypeDefList, WaLeaveTypeDefDo.class);
    }
}