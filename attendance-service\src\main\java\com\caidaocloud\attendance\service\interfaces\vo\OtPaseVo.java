package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 加班分析规则
 *
 * <AUTHOR>
 * @Date 2021/3/13
 */
@Data
public class OtPaseVo {
    @ApiModelProperty("加班类型id,多个用逗号隔开")
    private String otTypeIds;
    @ApiModelProperty("加班类型名称,多个用逗号隔开")
    private String otTypeIdsTxt;
    @ApiModelProperty("规则 1 无 2 向上取整15分钟 3 向下取整15分钟 4 向上取整 30分钟 5 向下取整30 分钟")
    private Integer rule;
    @ApiModelProperty("单位 1 天 2 小时 3 分钟")
    private Integer unit;
    @ApiModelProperty("最小有效时长")
    private Double minValidTime;
}
