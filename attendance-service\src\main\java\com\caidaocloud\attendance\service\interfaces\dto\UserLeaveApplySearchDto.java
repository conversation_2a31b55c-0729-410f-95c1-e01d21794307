package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserLeaveApplySearchDto extends AttendanceBasePage {

    @ApiModelProperty("审批状态 0暂存 1审批中 2审批通过 3审批不通过 4作废 5已退回 8撤销中 9已撤销")
    private Integer status;
    @ApiModelProperty("假期类型")
    private String leaveName;
    @ApiModelProperty("开始日期")
    private Long applyStartTime;
    @ApiModelProperty("结束日期")
    private Long applyEndTime;
}
