package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.service.IEmpCompensatoryCaseApplyService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.infrastructure.util.UserInfoHolder;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.EmpCompensatoryCaseDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.CompensatoryRevokeDto;
import com.caidaocloud.attendance.service.interfaces.vo.CompensatoryCaseVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/attendance/empCompensatory/v1")
@Api(value = "/api/attendance/empCompensatory/v1", description = "调休转付现")
public class EmpCompensatoryCaseController {

    @Autowired
    private IEmpCompensatoryCaseApplyService empCompensatoryCaseApplyService;
    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("调休转付现申请列表")
    @PostMapping(value = "/list")
    @Security(code = "CompensatoryApplyRecordList")
    public Result<AttendancePageResult<CompensatoryCaseVo>> getEmpCompensatoryCaseList(@RequestBody CompensatoryCaseReqDto dto, HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.COMPENSATORY_CASH_APPLY_RECORD_LIST, "b");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("调休转付现申请列表 DataScope = {}", dataScope);
            SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
            PageList<CompensatoryCaseItemDto> pageList = empCompensatoryCaseApplyService.getEmpCompensatoryCaseList(dto, pageBean, userInfo.getTenantId());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<CompensatoryCaseVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(CompensatoryCaseVo.class);
            AttendancePageResult<CompensatoryCaseVo> pageResult = new AttendancePageResult<CompensatoryCaseVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("EmpCompensatoryCaseController.getEmpCompensatoryCaseList has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("保存调休转付现申请")
    @PostMapping(value = "/saveApply")
    @LogRecordAnnotation(success = "调整了结转调休明细", category = "调休付现明细", menu = "休假管理-假期余额-调休明细")
    public Result<String> saveCompensatoryCaseApply(@RequestBody EmpCompensatoryCaseDto dto) {
        if (null == dto.getEmpId()) {
            UserInfo userInfo = getUserInfo();
            dto.setEmpId(userInfo.getStaffId());
        }
        if (Optional.ofNullable(dto.getApplyDuration()).orElse(0f) <= 0 && Optional.ofNullable(dto.getValidDuration()).orElse(0f) <= 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_DURATION_EMPTY, "");
        }
        try {
            Result<String> result = empCompensatoryCaseApplyService.saveApply(dto);

            return result;
        } catch (Exception e) {
            log.error("EmpCompensatoryCaseController.saveCompensatoryCaseApply has exception:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, "");
        }
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销调休转付现单据")
    public Result<Boolean> revokeCompensatoryCaseApply(@RequestBody CompensatoryRevokeDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.EXIST_NUMBER_VALUE_EXCEEDS_UPPERLIMIT, Boolean.FALSE);
        }
        UserInfo userInfo = UserInfoHolder.getUserInfo();
        try {
            return empCompensatoryCaseApplyService.revokeCompensatoryApply(dto, userInfo);
        } catch (Exception e) {
            log.error("EmpCompensatoryCaseController.revokeCompensatoryCaseApply has exception, {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, Boolean.FALSE);
        }
    }
}
