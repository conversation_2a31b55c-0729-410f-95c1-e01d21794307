<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaDataBackupMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaDataBackup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="data_table" jdbcType="VARCHAR" property="dataTable" />
    <result column="api" jdbcType="VARCHAR" property="api" />
    <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, module, data_table, api, ip_address, status, create_by, create_time, 
    update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_data_backup
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_data_backup
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaDataBackup">
    insert into wa_data_backup (id, tenant_id, module, 
      data_table, api, ip_address, 
      status, create_by, create_time, 
      update_by, update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{module,jdbcType=VARCHAR}, 
      #{dataTable,jdbcType=VARCHAR}, #{api,jdbcType=VARCHAR}, #{ipAddress,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaDataBackup">
    insert into wa_data_backup
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="module != null">
        module,
      </if>
      <if test="dataTable != null">
        data_table,
      </if>
      <if test="api != null">
        api,
      </if>
      <if test="ipAddress != null">
        ip_address,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="dataTable != null">
        #{dataTable,jdbcType=VARCHAR},
      </if>
      <if test="api != null">
        #{api,jdbcType=VARCHAR},
      </if>
      <if test="ipAddress != null">
        #{ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaDataBackup">
    update wa_data_backup
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="dataTable != null">
        data_table = #{dataTable,jdbcType=VARCHAR},
      </if>
      <if test="api != null">
        api = #{api,jdbcType=VARCHAR},
      </if>
      <if test="ipAddress != null">
        ip_address = #{ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaDataBackup">
    update wa_data_backup
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      module = #{module,jdbcType=VARCHAR},
      data_table = #{dataTable,jdbcType=VARCHAR},
      api = #{api,jdbcType=VARCHAR},
      ip_address = #{ipAddress,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>