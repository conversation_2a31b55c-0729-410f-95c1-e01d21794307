package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("销假时间")
@NoArgsConstructor
@AllArgsConstructor
public class LeaveCancelPeriod {

    @ApiModelProperty("开始时间")
    private String shiftStartTime;
    @ApiModelProperty("结束时间")
    private String shiftEndTime;
}
