package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("销假单详情DTO")
public class LeaveCancelInfoDto {
    @ApiModelProperty("开始时间，unix时间戳，精确到秒")
    private Long startTime;
    @ApiModelProperty("结束时间，unix时间戳，精确到秒")
    private Long endTime;
    @ApiModelProperty("半天开始：A 上半天 P 下半天")
    private String shalfDay;
    @ApiModelProperty("半天结束：A 上半天 P 下半天")
    private String ehalfDay;
}
