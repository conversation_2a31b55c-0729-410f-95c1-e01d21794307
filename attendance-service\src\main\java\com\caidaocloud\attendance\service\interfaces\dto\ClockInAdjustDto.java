package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("打卡记录调整DTO")
public class ClockInAdjustDto {
    @ApiModelProperty("打卡记录ID")
    private Integer recordId;

    @ApiModelProperty("归属日期，单位秒")
    private Long belongDate;

    @ApiModelProperty("打卡日期，年月日，单位秒")
    private Long clockInDate;

    @ApiModelProperty("打卡时间，时分秒，单位秒")
    private Long clockInTime;

    @ApiModelProperty("调整原因")
    private String reason;

    @ApiModelProperty(value = "打卡状态：0无效,1有效", required = true, allowableValues = "0,1")
    private Integer clockSiteStatus;
}
