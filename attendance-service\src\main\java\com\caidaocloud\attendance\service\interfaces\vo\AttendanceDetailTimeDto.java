package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AttendanceDetailTimeDto {
    @ApiModelProperty("1(每月)/2(每周)")
    private Integer type;
    @ApiModelProperty("type=1代表日期，type=2代表1周日2周一3周二4周三5周四6周五7周六")
    private Integer day;
    @ApiModelProperty("时间点：单位分钟")
    private Integer time;
    @ApiModelProperty("开始日期类型：1当月，2上月")
    private Integer startType;
    @ApiModelProperty("开始日期")
    private Integer startDay;
    @ApiModelProperty("结束日期类型：1当月，2上月")
    private Integer endType;
    @ApiModelProperty("结束日期")
    private Integer endDay;
    @ApiModelProperty("考勤方案")
    private Integer waGroupId;
}
