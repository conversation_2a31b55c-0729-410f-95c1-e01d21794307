<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.OtRecordMapper">

    <select id="getOtDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        SELECT ei.workno,
               ei.emp_name                                                AS "empName",
               eo.start_time                                              AS "startTime",
               eo.end_time                                                AS "endTime",
               eo.ot_duration / 60 || '小时' || eo.ot_duration % 60 || '分钟' AS "otDurationDesc",
               case
                   when compensate_type = 0 then '不补偿'
                   when compensate_type = 1 then '加班费'
                   when compensate_type = 2 then '调休'
                   else '其他' end                                             "compensateTypeName",
                compensate_type AS "compensateType",
                eo.status,
               CASE
                   WHEN eo.status = 0
                       THEN '暂存'
                   WHEN eo.status = 1
                       THEN '审批中'
                   WHEN eo.status = 2
                       THEN '已通过'
                   WHEN eo.status = 3
                       THEN '已拒绝'
                   WHEN eo.status = 4
                       THEN '已作废'
                   WHEN eo.status = 9
                       THEN '已撤销' END                                     AS "statusName",
               eo.reason,
               eo.revoke_reason                                           as  revokeReason,
               wof.file_name                                              as "fileNames",
               wof.url                                                    as "files",
               ei.hire_date                                               as "hireDate",
               ei.workplace                                               as "workCity",
               co.shortname                                               as "orgName",
                case
                   when co.full_path is not null and co.full_path != ''
                then concat_ws('/', co.full_path, co.shortname)
                   else co.shortname
                end  as   "fullPath",
               eo.overtime_type_id                                        as "overtimeTypeId",
               eo.crttime,
               eo.crtuser,
               eo.process_code                                                   as "processCode"
        FROM wa_emp_overtime eo
                 JOIN sys_emp_info ei ON eo.empid = ei.empid and ei.deleted = 0
                 LEFT JOIN wa_overtime_file wof on eo.ot_id = wof.ot_id
                 LEFT JOIN sys_corp_org co ON co.orgid = ei.orgid
        WHERE eo.ot_id = #{otId}
            <if test="corpid != null">
                AND ei.corpid = #{corpid}
            </if>
    </select>

    <select id="getEmpOvertimeListByDayTime"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        SELECT
        ot.ot_id        as "otId",
        ot.status,
        ot.crttime      as "applyTime",
        eod.start_time as "startTime",
        eod.end_time   as "endTime",
        ot.ot_duration  as "duration"
        FROM wa_emp_overtime ot
        JOIN wa_emp_overtime_detail eod on ot.ot_id=eod.overtime_id and
        case when eod.real_date is not null then eod.real_date between #{start} and #{end}
        else
            (
            (#{start} <![CDATA[>=]]> eod.start_time AND #{start} <![CDATA[<]]> eod.end_time)
            OR (#{end} <![CDATA[>]]> eod.start_time AND #{end} <![CDATA[<=]]> eod.end_time)
            OR (#{start} <![CDATA[<=]]> eod.start_time AND #{end} <![CDATA[>=]]> eod.end_time)
            )
        end
        WHERE ot.empid = #{empid} AND ot.status in (1, 2, 8)
    </select>

    <select id="getEmpOvertimeList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        SELECT
                weo.ot_id AS "otId",
                weo.crttime AS "applyTime",
                weo.start_time AS "startTime",
                weo.end_time   AS "endTime",
                weo.ot_duration as "otDuration",
                weo.date_type AS "dateType",
                CASE weo.date_type
                WHEN 1 then '工作日加班'
                WHEN 2 then '休息日加班'
                WHEN 3 then '法定假日加班'
                WHEN 5 then '法定休日加班'
                WHEN 4 then '特殊休日加班' END AS "dateTypeName",
                        weo.compensate_type AS "compensateType",
                CASE weo.compensate_type WHEN 0 then '不补偿' WHEN 1 then '加班费' WHEN 2 then '调休' END AS "compensateTypeName",
                weo.status,
                CASE
                WHEN weo.status = 0
                THEN '暂存'
                WHEN weo.status = 1
                THEN '审批中'
                WHEN weo.status = 2
                THEN '已通过'
                WHEN weo.status = 3
                THEN '已拒绝'
                WHEN weo.status = 4
                THEN '已作废'
                WHEN weo.status = 5
                THEN '已退回'
                WHEN weo.status = 9
                THEN '已撤销' END AS "statusName",weod.overtime_type_id overtimeTypeId,weod.detail_id detailId
            FROM wa_emp_overtime weo
            JOIN wa_emp_overtime_detail weod ON weod.overtime_id=weo.ot_id
            where weo.empid = #{empid}
            <if test=" applyTime != null">
                AND weo.crttime >= #{applyTime}
            </if>
            <if test=" endApplyTime != null">
                AND weo.crttime &lt;= #{endApplyTime}
            </if>
            <if test=" status != null">
                AND weo.status = #{status}
            </if>
        order by weo.crttime desc
    </select>

    <select id="queryEmpOvertimeListByBelongDate" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        SELECT
        ot.ot_id        as "otId",
        ot.status,
        ot.crttime      as "applyTime",
        ot.start_time as "startTime",
        ot.end_time   as "endTime",
        ot.ot_duration  as "duration"
        FROM wa_emp_overtime ot
        WHERE ot.empid = #{empId} AND ot.status in (1, 2, 8)
        <if test="startDate != null and endDate != null">
            and start_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="countOvertimeTypeUsed" resultType="int">
        select count(1) from wa_emp_overtime weo
        join wa_overtime_type wot on weo.overtime_type_id = wot.overtime_type_id
        where weo.overtime_type_id=#{overtimeTypeId} and wot.belong_orgid=#{tenantId}
    </select>

    <select id="getEmpOverTimeByEmpid" parameterType="hashmap" resultType="map">
        SELECT a.empid,
        a.ot_id,
        a.ot_duration,
        b.start_time,
        b.end_time,
        b.start_time - ((b.start_time + 28800) % 86400) AS "belongDate",
        b.time_duration,
        b.real_date,
        wot.date_type,
        wot.compensate_type,
        a.start_time - ((a.start_time + 28800) % 86400) AS "regdate",
        b.detail_id                                     as "ot_detail_id",
        COALESCE(b.overtime_type_id,0)                  as "overTypeId"
        from wa_emp_overtime a
        JOIN wa_emp_overtime_detail b on a.ot_id = b.overtime_id
        left join sys_emp_info e on e.empid = a.empid and e.deleted = 0
        left join wa_overtime_type wot on wot.overtime_type_id=b.overtime_type_id
        where a.status in <foreach collection="approvalStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>
        and e.belong_org_id=#{belongid} and b.real_date <![CDATA[>]]> 0
        <if test="anyEmpids2 != null ">
            and a.empid = any(${anyEmpids2})
        </if>
        <if test="startDate != null and endDate != null">
            and ((b.real_date BETWEEN  #{startDate} AND #{endDate}) or (b.start_time BETWEEN  #{startDate} AND #{endDate}))
        </if>
        order by b.start_time
    </select>

    <select id="queryEmpOvertimes" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        select * from wa_emp_overtime weo
        join wa_overtime_type wot on weo.overtime_type_id = wot.overtime_type_id
        where weo.overtime_type_id=#{overtimeTypeId} and wot.belong_orgid=#{tenantId}
          and weo.empid=#{empId} and weo.status in (1,2)
        <if test="startDate != null and endDate != null">
            and start_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="queryOtRevokeDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        SELECT ei.workno,
        ei.emp_name                                                AS "empName",
        eo.start_time                                              AS "startTime",
        eo.end_time                                                AS "endTime",
        eo.ot_duration / 60 || '小时' || eo.ot_duration % 60 || '分钟' AS "otDurationDesc",
        compensate_type AS "compensateType",
        case
        when compensate_type = 0 then '不补偿'
        when compensate_type = 1 then '加班费'
        when compensate_type = 2 then '调休'
        else '其他' end                                             "compensateTypeName",
        wwr.status,
        CASE
        WHEN wwr.status = 0
        THEN '暂存'
        WHEN wwr.status = 1
        THEN '审批中'
        WHEN wwr.status = 2
        THEN '已通过'
        WHEN wwr.status = 3
        THEN '已拒绝'
        WHEN wwr.status = 4
        THEN '已作废'
        WHEN wwr.status = 9
        THEN '已撤销' END                                     AS "statusName",
        wwr.reason,
        eo.revoke_reason                                           as  revokeReason,
        wof.file_name                                              as "fileNames",
        wof.url                                                    as "files",
        ei.hire_date                                               as "hireDate",
        ei.workplace                                               as "workCity",
        co.shortname                                               as "orgName",
        case
        when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
        else co.shortname
        end  as   "fullPath",
        eo.overtime_type_id                                        as "overtimeTypeId",
        wwr. create_time crttime,
        wwr.create_by crtuser,
        wwr.process_code
        FROM wa_emp_overtime eo
        JOIN wa_workflow_revoke wwr on wwr.entity_id=eo.ot_id
        JOIN sys_emp_info ei ON eo.empid = ei.empid and ei.deleted = 0
        LEFT JOIN wa_overtime_file wof on eo.ot_id = wof.ot_id
        LEFT JOIN sys_corp_org co ON co.orgid = ei.orgid
        WHERE wwr.id = #{id}
        <if test="tenantId != null">
            AND ei.belong_org_id = #{tenantId}
        </if>
    </select>

    <select id="queryEmpOtCompensatoryList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
        select t.detail_id detailId,
               t.overtime_id otId,
               t.start_time startTime,
               t.end_time endTime,
               t.time_duration otDuration,
               t.carry_duration carryDuration,
               weo.status,
               weo.crttime applyTime,
               weo.last_approval_time lastApprovalTime,
               weo.reason,
               ot.type_name overtimeTypeName,
               t.overtime_type_id overtimeTypeId
        from wa_emp_overtime weo
        join (select min(wcqr.detail_id) detail_id,
                     weod.overtime_id overtime_id,
                     min(weod.start_time) start_time,
                     max(weod.end_time) end_time,
                     COALESCE(SUM(weod.time_duration), 0) time_duration,
                     COALESCE(SUM(wcqr.carry_duration), 0) carry_duration,
                     weod.overtime_type_id
              from wa_emp_overtime_detail weod
              join wa_compensatory_quota_record wcqr on wcqr.detail_id = weod.detail_id
              where wcqr.deleted = 0 and wcqr.status = 2 and wcqr.tenant_id=#{tenantId} and wcqr.quota_id=#{quotaId}
              group by weod.overtime_id,weod.overtime_type_id) t on t.overtime_id=weo.ot_id
        left join wa_overtime_type ot on ot.overtime_type_id = t.overtime_type_id
        where weo.tenant_id=#{tenantId}
    </select>
</mapper>