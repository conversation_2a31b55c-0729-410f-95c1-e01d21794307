package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("撤销销假Dto")
public class RevokeLeaveCancelDto {
    @ApiModelProperty("销假id")
    private Long leaveCancelId;
    @ApiModelProperty("撤销原因")
    private String revokeReason;
    @ApiModelProperty("是否关闭撤销校验")
    private boolean closeCheck;
}
