package com.caidaocloud.attendance.service;

import com.caidao1.mobile.service.MobileV16Service;
import com.caidaocloud.attendance.service.application.service.user.WorkFlowApprovalProcessorService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class WorkFlowinvalidTest {

    public static final Integer leaveId = 5301220;

    public static final Integer otId = 27305297;

    public static final Integer businiessKey = 4657518;

    @Resource
    private MobileV16Service mobileV16Service;

    @Resource
    private WorkFlowApprovalProcessorService workFlowApprovalProcessorService;

    /**
     * 请假
     */
    @Test
    public void testEmpLeave() {
        try {
            mobileV16Service.saveWfLeaveApproval(leaveId, "revoke", "");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 加班
     */
    @Test
    public void testEmpOt() {
        try {
            mobileV16Service.saveWfOtApproval(otId, "revoke", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 补卡
     */
    @Test
    public void testEmpPunchIn() {
        try {
            workFlowApprovalProcessorService.finishBdkApproval(businiessKey, "revoke");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
