package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.report.common.ExportBaseController;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.impl.EmpWorkCalendarService;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.EmpShiftChangeDto;
import com.caidaocloud.attendance.service.interfaces.dto.EmpShiftSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.EmpWorkCalendarSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.DownloadShiftTemplateDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 员工日历
 *
 * <AUTHOR>
 * @Date 2021/8/11
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/empworkcalendar/v1")
public class EmpWorkCalendarController extends ExportBaseController {
    @Autowired
    private EmpWorkCalendarService empWorkCalendarService;
    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "查询员工排班数据")
    @PostMapping(value = "/searchEmpWorkTimeDetailsByMonth")
    public Result searchWorkTimeDetailsByMonth(@RequestBody EmpWorkCalendarSearchDto searchDto, HttpServletRequest request) {
        log.info("searchEmpWorkTimeDetailsByMonth params:{}", JSONUtils.ObjectToJson(searchDto));
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            searchDto.setDataScope(orgDataScope);
        }
        try {
            PageList pageList = empWorkCalendarService.searchEmpWorkTimeDetailsByMonth(searchDto, this.getUserInfo());
            if (CollectionUtils.isNotEmpty(pageList)) {
                PageBean pageBean = PageUtil.getPageBean(searchDto);
                return Result.ok(new AttendancePageResult(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
            }
        } catch (Exception e) {
            log.error("searchEmpWorkTimeDetailsByMonth error msg {}", e.getMessage(), e);
        }
        return Result.ok(new AttendancePageResult<>());
    }

    @ApiOperation(value = "调整员工日历排班")
    @PostMapping(value = "/updateEmpWorkCalendarShift")
    @LogRecordAnnotation(success = "编辑了{empName{#empId}}班次", category = "编辑", menu = "排班管理-排班明细-排班明细")
    public Result updateEmpWorkCalendarShift(@RequestBody EmpShiftChangeDto changeDto) {
        try {
            LogRecordContext.putVariable("empId", changeDto.getEmpId());
            empWorkCalendarService.updateEmpWorkCalendarShift(changeDto, this.getUserInfo());

            return Result.ok(true);
        } catch (Exception e) {
            log.error("updateEmpWorkCalendarShift error msg {}", e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, "修改失败");
        }
    }

    @ApiOperation(value = "员工班次调整记录列表查询")
    @PostMapping(value = "/getEmpShiftChangeList")
    public Result getEmpShiftChangeList(@RequestBody EmpShiftSearchDto searchDto, HttpServletRequest request) {
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_SHIFT_CHANGE_LIST, "sei");
            searchDto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                        .replaceAll("empid", "sei.empid");
                searchDto.setDataScope(searchDto.getDataScope() + orgDataScope);
            }
            PageList pageList = empWorkCalendarService.getEmpShiftChangeList(searchDto, this.getUserInfo());
            if (CollectionUtils.isNotEmpty(pageList)) {
                PageBean pageBean = PageUtil.getPageBean(searchDto);
                return Result.ok(new AttendancePageResult(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
            }
        } catch (Exception e) {
            log.error("getEmpShiftChangeList error msg {}", e.getMessage(), e);
        }
        return Result.ok(new AttendancePageResult<>());
    }

    @ApiOperation(value = "班次下拉列表")
    @GetMapping(value = "/getShiftOptions")
    public Result getShiftOptions(@RequestParam(value = "dateType", required = false) Integer dateType) {
        UserInfo userInfo = this.getUserInfo();
        return Result.ok(empWorkCalendarService.getShiftOptions(userInfo.getTenantId(), dateType));
    }

    @ApiOperation("查看员工个人工作日历")
    @GetMapping(value = "/getEmpCalendarShiftListByYm")
    public Result getEmpCalendarShiftListByYm(@RequestParam("empId") Long empId, @RequestParam("start") String start,
                                              @RequestParam("end") String end) throws Exception {
        return ResponseWrap.wrapResult(empWorkCalendarService.getEmpCalendarShiftListByYm(this.getUserInfo().getTenantId(), empId, start, end, null));
    }

    @ApiOperation("下载员工排班模板")
    @PostMapping(value = "/downloadEmpShiftTemplate")
    @LogRecordAnnotation(success = "模版下载", category = "模版下载", menu = "排班管理-排班明细-排班明细")
    public void downloadEmpShiftTemplate(HttpServletResponse response, HttpServletRequest request, @RequestBody DownloadShiftTemplateDto dto) {
        try {
            empWorkCalendarService.downloadEmpShiftTemplate(response, request, dto);

        } catch (Exception e) {
            log.error("EmpWorkCalendarController.downloadEmpShiftTemplate has exception {}", e.getMessage(), e);
        }
    }

    @ApiOperation("导入员工排班数据")
    @PostMapping(value = "/importEmpShift")
    @LogRecordAnnotation(success = "导入了排班明细", category = "导入", menu = "排班管理-排班明细-排班明细")
    public Result<String> importEmpShift(MultipartFile file) {
        if (null == file) {
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_EMPTY, "");
        }
        try {
            return empWorkCalendarService.importEmpShift(file);
        } catch (Exception e) {
            log.error("EmpWorkCalendarController.importEmpShift has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, "");
        }
    }

    @ApiOperation("导出员工排班数据")
    @PostMapping(value = "/exportEmpShift")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "排班管理-排班明细-排班明细")
    public void exportEmpShift(HttpServletResponse response, HttpServletRequest request, @RequestBody EmpWorkCalendarSearchDto dto) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            dto.setDataScope(orgDataScope);
        }
        try {
            empWorkCalendarService.exportEmpShift(response, request, dto);

        } catch (Exception e) {
            log.error("EmpWorkCalendarController.exportEmpShift has exception {}", e.getMessage(), e);
        }
    }
}
