package com.caidaocloud.attendance.service.interfaces.dto.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GenQuotaDto {

    @ApiModelProperty("考勤分组id")
    private Integer groupId;

    @ApiModelProperty("员工id")
    private Long empId;

    @ApiModelProperty("生成周期:1本年，2次年")
    private Short year;

    @ApiModelProperty("本年未生成员工：true 只生成对应员工无假期配额的数据（已生成的不做任何处理，不会重新生成）、false 生成对应员工所有的假期配额（已生成的做覆盖处理，使用最新数据） ")
    private boolean onlyNotGen;

    @ApiModelProperty("上年结转")
    private boolean isCarryForward;

    @ApiModelProperty("是否重新扣减假期")
    private boolean isReCalQuota;

    @ApiModelProperty("进程唯一标识")
    private String progress;

    @ApiModelProperty("考勤周期ID")
    private Integer sobId;

    @ApiModelProperty("1:生成新员工配额、2:更新离职人员配额、3:更新全员配额")
    private Integer genQuotaMode;
}
