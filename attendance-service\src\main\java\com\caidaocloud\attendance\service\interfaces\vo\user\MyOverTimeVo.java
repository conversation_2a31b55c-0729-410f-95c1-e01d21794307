package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyOverTimeVo {
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("状态")
    private Short status;
    @ApiModelProperty("状态文本")
    private String statusName;
    @ApiModelProperty("审批流程key")
    private String businessKey;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    private Integer funcType;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("申请类型")
    private String applyName;
}
