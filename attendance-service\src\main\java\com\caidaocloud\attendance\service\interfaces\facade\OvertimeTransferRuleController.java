package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.TransferRulePeriod;
import com.caidaocloud.attendance.service.application.enums.CompensateTypeEnum;
import com.caidaocloud.attendance.service.application.service.IOvertimeTransferRuleService;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OvertimeTransferRuleDto;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeTransferRuleVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Api(description = "加班转换规则接口", value = "/api/attendance/overtime/transferRule/v1")
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/overtime/transferRule/v1")
public class OvertimeTransferRuleController {
    @Autowired
    private IOvertimeTransferRuleService overtimeTransferRuleService;

    @ApiOperation(value = "保存加班转换规则")
    @PostMapping(value = "/save")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-加班设置-转换规则")
    public Result<Boolean> saveOvertimeTransferRule(@RequestBody OvertimeTransferRuleDto dto) {
        if (StringUtil.isBlank(dto.getRuleName())) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_NAME_EMPTY, Boolean.FALSE);
        }
        if (dto.getRuleName().length() > 50) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_NAME_TOO_LONG, Boolean.FALSE);
        }
        if (overtimeTransferRuleService.checkOvertimeTransferRuleName(dto)) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_NAME_EXISTED, Boolean.FALSE);
        }
        if (null == dto.getCompensateType()) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_COMPENSATE_TYPE_EMPTY, Boolean.FALSE);
        }
        if (dto.getCompensateType().equals(CompensateTypeEnum.COMPENSATORY_LEAVE.ordinal()) && null == dto.getLeaveTypeId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_LEAVE_EMPTY, Boolean.FALSE);
        }
        if (StringUtil.isNotBlank(dto.getNote()) && dto.getNote().length() > 1000) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_NOTE_TOO_LONG, Boolean.FALSE);
        }
        try {
            overtimeTransferRuleService.saveOvertimeTransferRule(dto);
        } catch (Exception e) {
            log.error("OvertimeTransferRuleController.saveOvertimeTransferRule exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
        return Result.ok(Boolean.TRUE);
    }

    @ApiOperation(value = "加班转换规则详情")
    @GetMapping(value = "/detail")
    public Result<OvertimeTransferRuleVo> getOvertimeTransferRule(@RequestParam("ruleId") Long ruleId) {
        try {
            OvertimeTransferRuleDto result = overtimeTransferRuleService.getOvertimeTransferRule(ruleId);
            if (null == result) {
                return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_RULE_NOT_EXIST, null);
            }
            if (null != result.getTransferPeriods()) {
                PGobject pGobject = (PGobject) result.getTransferPeriods();
                List<TransferRulePeriod> periods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<TransferRulePeriod>>() {
                });
                if (CollectionUtils.isNotEmpty(periods)) {
                    result.setTransferPeriods(periods);
                }
            }
            return Result.ok(ObjectConverter.convert(result, OvertimeTransferRuleVo.class));
        } catch (Exception e) {
            log.error("OvertimeTransferRuleController.getOvertimeTransferRule exception:{}", e.getMessage(), e);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
    }

    @ApiOperation(value = "删除加班转换规则")
    @DeleteMapping(value = "/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-加班设置-转换规则")
    public Result<Boolean> deleteOvertimeTransferRule(@RequestParam("ruleId") Long ruleId) {
        if (overtimeTransferRuleService.checkTransferRuleReferenced(ruleId)) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TRANSFER_RULE_REFERENCED, Boolean.FALSE);
        }
        try {
            overtimeTransferRuleService.deleteOvertimeTransferRule(ruleId);
        } catch (Exception e) {
            log.error("OvertimeTransferRuleController.deleteOvertimeTransferRule exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
        return Result.ok();
    }

    @ApiOperation(value = "加班转换规则列表")
    @PostMapping(value = "/list")
    public Result<List<OvertimeTransferRuleVo>> getOvertimeTransferRules() {
        try {
            List<OvertimeTransferRuleDto> list = overtimeTransferRuleService.getOvertimeTransferRuleList();
            if (CollectionUtils.isNotEmpty(list)) {
                return Result.ok(ObjectConverter.convertList(list, OvertimeTransferRuleVo.class));
            }
        } catch (Exception e) {
            log.error("OvertimeTransferRuleController.getOvertimeTransferRules exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new ArrayList<>());
        }
        return Result.ok(new ArrayList<>());
    }

    @ApiOperation(value = "加班转换规则下拉选择项")
    @GetMapping(value = "/selectOptions")
    public Result<List<KeyValue>> getOvertimeTransferRuleOption(@RequestParam("compensateType") Integer compensateType) {
        try {
            return Result.ok(overtimeTransferRuleService.getOvertimeTransferRuleList(compensateType));
        } catch (Exception e) {
            log.error("OvertimeTransferRuleController.getOvertimeTransferRules exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SELECT_OPTION_FAILED, new ArrayList<>());
        }
    }
}
