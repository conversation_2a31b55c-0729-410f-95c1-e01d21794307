<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpLeaveCancelMapper" >
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancel" >
    <id column="leave_cancel_id" property="leaveCancelId" jdbcType="BIGINT" />
    <result column="leave_id" property="leaveId" jdbcType="INTEGER" />
    <result column="leave_type_id" property="leaveTypeId" jdbcType="INTEGER" />
    <result column="empid" property="empid" jdbcType="BIGINT" />
    <result column="type_id" property="typeId" jdbcType="BIGINT" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="time_unit" property="timeUnit" jdbcType="INTEGER" />
    <result column="time_duration" property="timeDuration" jdbcType="REAL" />
    <result column="period_type" property="periodType" jdbcType="SMALLINT" />
    <result column="status" property="status" jdbcType="SMALLINT" />
    <result column="last_approval_time" property="lastApprovalTime" jdbcType="BIGINT" />
    <result column="last_empid" property="lastEmpid" jdbcType="BIGINT" />
    <result column="revoke_status" property="revokeStatus" jdbcType="INTEGER" />
    <result column="revoke_reason" property="revokeReason" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="file_id" property="fileId" jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="BIGINT" />
    <result column="update_by" property="updateBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="BIGINT" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>

    <result column="original_leave_id" property="originalLeaveId" jdbcType="INTEGER" />
    <result column="adjust_time_slot" property="adjustTimeSlot" jdbcType="VARCHAR" />
    <result column="adjust_duration" property="adjustDuration" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    leave_cancel_id, leave_id, leave_type_id, empid, type_id, reason, time_unit, time_duration, 
    period_type, status, last_approval_time, last_empid, revoke_status, revoke_reason, 
    file_name, file_id, deleted, create_by, create_time, update_by, update_time, tenant_id,original_leave_id,
    adjust_time_slot,adjust_duration
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wa_emp_leave_cancel
    where leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wa_emp_leave_cancel
    where leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancel" >
    insert into wa_emp_leave_cancel (leave_cancel_id, leave_id, leave_type_id, 
      empid, type_id, reason, 
      time_unit, time_duration, period_type, 
      status, last_approval_time, last_empid, 
      revoke_status, revoke_reason, file_name, 
      file_id, deleted, create_by, 
      create_time, update_by, update_time, 
      tenant_id,original_leave_id,adjust_time_slot,
      adjust_duration)
    values (#{leaveCancelId,jdbcType=BIGINT}, #{leaveId,jdbcType=INTEGER}, #{leaveTypeId,jdbcType=INTEGER}, 
      #{empid,jdbcType=BIGINT}, #{typeId,jdbcType=BIGINT}, #{reason,jdbcType=VARCHAR},
      #{timeUnit,jdbcType=INTEGER}, #{timeDuration,jdbcType=REAL}, #{periodType,jdbcType=SMALLINT}, 
      #{status,jdbcType=SMALLINT}, #{lastApprovalTime,jdbcType=BIGINT}, #{lastEmpid,jdbcType=BIGINT},
      #{revokeStatus,jdbcType=INTEGER}, #{revokeReason,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileId,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{tenantId,jdbcType=VARCHAR}, #{originalLeaveId,jdbcType=INTEGER}, #{adjustTimeSlot,jdbcType=VARCHAR},
      #{adjustDuration,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancel" >
    insert into wa_emp_leave_cancel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="leaveCancelId != null" >
        leave_cancel_id,
      </if>
      <if test="leaveId != null" >
        leave_id,
      </if>
      <if test="leaveTypeId != null" >
        leave_type_id,
      </if>
      <if test="empid != null" >
        empid,
      </if>
      <if test="typeId != null" >
        type_id,
      </if>
      <if test="reason != null" >
        reason,
      </if>
      <if test="timeUnit != null" >
        time_unit,
      </if>
      <if test="timeDuration != null" >
        time_duration,
      </if>
      <if test="periodType != null" >
        period_type,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="lastApprovalTime != null" >
        last_approval_time,
      </if>
      <if test="lastEmpid != null" >
        last_empid,
      </if>
      <if test="revokeStatus != null" >
        revoke_status,
      </if>
      <if test="revokeReason != null" >
        revoke_reason,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="fileId != null" >
        file_id,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="createBy != null" >
        create_by,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateBy != null" >
        update_by,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
      <if test="originalLeaveId != null" >
        original_leave_id,
      </if>
      <if test="adjustTimeSlot != null" >
        adjust_time_slot,
      </if>
      <if test="adjustDuration != null" >
        adjust_duration,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="leaveCancelId != null" >
        #{leaveCancelId,jdbcType=BIGINT},
      </if>
      <if test="leaveId != null" >
        #{leaveId,jdbcType=INTEGER},
      </if>
      <if test="leaveTypeId != null" >
        #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="empid != null" >
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="typeId != null" >
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="timeUnit != null" >
        #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="timeDuration != null" >
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="periodType != null" >
        #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="lastApprovalTime != null" >
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null" >
        #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeStatus != null" >
        #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="revokeReason != null" >
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null" >
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="originalLeaveId != null" >
        #{originalLeaveId,jdbcType=INTEGER},
      </if>
      <if test="adjustTimeSlot != null" >
        #{adjustTimeSlot,jdbcType=VARCHAR},
      </if>
      <if test="adjustDuration != null" >
        #{adjustDuration,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancel" >
    update wa_emp_leave_cancel
    <set >
      <if test="leaveId != null" >
        leave_id = #{leaveId,jdbcType=INTEGER},
      </if>
      <if test="leaveTypeId != null" >
        leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="empid != null" >
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="typeId != null" >
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="reason != null" >
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="timeUnit != null" >
        time_unit = #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="timeDuration != null" >
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="periodType != null" >
        period_type = #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="lastApprovalTime != null" >
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null" >
        last_empid = #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeStatus != null" >
        revoke_status = #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="revokeReason != null" >
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="originalLeaveId != null" >
        original_leave_id = #{originalLeaveId,jdbcType=INTEGER},
      </if>
      <if test="adjustTimeSlot != null" >
        adjust_time_slot = #{adjustTimeSlot,jdbcType=VARCHAR},
      </if>
      <if test="adjustDuration != null" >
        adjust_duration = #{adjustDuration,jdbcType=VARCHAR},
      </if>
    </set>
    where leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancel" >
    update wa_emp_leave_cancel
    set leave_id = #{leaveId,jdbcType=INTEGER},
      leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      empid = #{empid,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=BIGINT},
      reason = #{reason,jdbcType=VARCHAR},
      time_unit = #{timeUnit,jdbcType=INTEGER},
      time_duration = #{timeDuration,jdbcType=REAL},
      period_type = #{periodType,jdbcType=SMALLINT},
      status = #{status,jdbcType=SMALLINT},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      revoke_status = #{revokeStatus,jdbcType=INTEGER},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      original_leave_id = #{originalLeaveId,jdbcType=INTEGER},
      adjust_time_slot = #{adjustTimeSlot,jdbcType=VARCHAR},
      adjust_duration = #{adjustDuration,jdbcType=VARCHAR}
    where leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT}
  </update>

  <select id="getInfoById" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDo">
    select ei.workno                                                            as "workNo",
           ei.emp_name                                                          as "empName",
           co.shortname                                                         as "orgName",
           /*co.full_path                                                         as "fullPath",*/
           case
             when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
             else co.shortname
             end  as   "fullPath",
           ei.hire_date                                                         as "hireDate",
           wet.leave_cancel_id                                                  as  "leaveCancelId",
           wet.leave_id                                                         as  "leaveId",
           wet.leave_type_id                                                    as  "leaveTypeId",
           wet.type_id                                                          as  "typeId",
           wet.create_time                                                      as "createTime",
           wet.create_by                                                        as "createBy",
           wtt.leave_name                                                       as "leaveTypeName",
           wet.status,
           case
             when wet.status = 0
               then '暂存'
             when wet.status = 1
               then '审批中'
             when wet.status = 2
               then '已通过'
             when wet.status = 3
               then '已拒绝'
             when wet.status = 4
               then '已作废'
             when wet.status = 5
               then '已退回'
             when wet.status = 9
               then '已撤销' END                                               as "statusName",
           wet.reason,
           wet.file_id                                                          as "fileId",
           wet.file_name                                                        as "fileName",
           wet.time_duration                                                    as "timeDuration",
           wet.time_unit                                                        as "timeUnit",
           ei.employ_type as "employType",
           ei.workplace as "workCity",
           wet.original_leave_id as "originalLeaveId",
           wet.adjust_time_slot as "adjustTimeSlot",
           wet.adjust_duration as "adjustDuration",
           wet.process_code as "processCode"
    from wa_emp_leave_cancel wet
           join wa_leave_type wtt ON wtt.leave_type_id = wet.leave_type_id
           join sys_emp_info ei on ei.empid = wet.empid and ei.deleted = 0
           left join sys_corp_org co ON co.orgid = ei.orgid
    where wet.leave_cancel_id = #{leaveCancelId}
    <if test="tenantId != null">
      and wet.tenant_id = #{tenantId,jdbcType=VARCHAR}
    </if>
  </select>

   <select id="selectPageList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDo">
       select * from (
       select
       distinct
       a.leave_id           as "leaveId",
       c.workno             as "workNo",
       c.emp_name           as "empName",
       d.shortname          as "orgName",
       case
       when d.full_path is not null and d.full_path != ''
           then concat_ws('/', d.full_path, d.shortname)
       else d.shortname
           end      as "fullPath",
       a.leave_cancel_id    as "leaveCancelId",
       e.leave_name         as "leaveTypeName",
       a.time_duration      as "timeDuration",
       a.time_unit          as "timeUnit",
       a.reason             as "reason",
       a.create_time        as "createTime",
       a.status             as "status",
       a.last_approval_time as "lastApprovalTime",
       a.update_time,
       e.leave_type         as "leaveType",
       a.type_id            as "typeId",
       a.process_code       as "processCode",
       e.i18n_leave_name    as "i18nLeaveTypeName"
     from wa_emp_leave_cancel a
       left join wa_emp_leave_cancel_info b on a.leave_cancel_id=b.leave_cancel_id
       join sys_emp_info c on a.empid = c.empid and c.deleted = 0
       left join sys_corp_org d on d.orgid = c.orgid
       left join wa_leave_type e on e.leave_type_id = a.leave_type_id
       <where>
         and a.deleted=0
         and c.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
         <if test="datafilter != null and datafilter != ''">
           ${datafilter}
         </if>
         <if test="keywords != null and keywords != ''">
             and (c.emp_name like concat('%', #{keywords}, '%') or c.workno like concat('%', #{keywords}, '%') or a.process_code like concat(#{keywords}, '%'))
         </if>
         <if test="empId !=null">
             and a.empid = #{empId}
         </if>
         <if test="startDateTime != null">
           AND (
           (b.shift_start_time &gt;= #{startDateTime} AND b.shift_start_time &lt;= #{endDateTime})
           OR (b.shift_end_time &gt;= #{startDateTime} AND b.shift_end_time &lt;= #{endDateTime})
           OR (b.shift_start_time &lt;= #{startDateTime} AND b.shift_end_time &gt;= #{endDateTime})
           )
         </if>
       </where>
       order by a.update_time desc
      ) as t
      <where>
         <if test="filter != null">
           ${filter}
         </if>
      </where>
   </select>
</mapper>