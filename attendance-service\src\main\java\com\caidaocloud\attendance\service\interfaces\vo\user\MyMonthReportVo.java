package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyMonthReportVo {

    @ApiModelProperty("排班工时")
    private Float workTimeHour;
    @ApiModelProperty("出勤时长")
    private Float actualWorkTimeHour;
    @ApiModelProperty("迟到")
    private Float lateTime;
    @ApiModelProperty("早退")
    private Float earlyTime;
    @ApiModelProperty("旷工")
    private Integer kgWorkTime;
    @ApiModelProperty("补卡")
    private Integer bdkCount;
    @ApiModelProperty("休假")
    private Integer leaveCount;
    @ApiModelProperty("加班")
    private Float otTimeHour;
    @ApiModelProperty("出差")
    private Integer travelCount;
}
