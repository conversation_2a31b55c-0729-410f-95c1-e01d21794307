package com.caidaocloud.attendance.service.infrastructure.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;

public abstract class DateUtil {

    public static Long getZeroTodayDate(){
        //当天日期
        long now = System.currentTimeMillis() / 1000L;
        long daySecond = 60 * 60 * 24;
        long startDate = (now - (now + 8 * 3600) % daySecond);
        return startDate;
    }

    public static Long getEndDate(){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + 8);
        long endDate = calendar.getTimeInMillis() / 1000L;
        return endDate;
    }

    public static Long getNextDate(int nextDay){
        // 当前日期 + nextDay 天
        return getZeroTodayDate() + nextDay * 24 * 3600;
    }

    public static LocalDateTime getTodayStartOfDay() {
        return LocalDate.now().atStartOfDay();
    }

    public static LocalDateTime getYesterdayStartOfDay() {
        return LocalDate.now().minusDays(1).atStartOfDay();
    }

    public static LocalDateTime getThisWeekStartOfWeek() {
        return LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay();
    }

    public static LocalDateTime getThisWeekEndOfWeek() {
        return LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay()
                .plusWeeks(1).minusDays(1);
    }

    public static LocalDateTime getLastWeekStartOfWeek() {
        return LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).minusWeeks(1).atStartOfDay();
    }

    public static LocalDateTime getLastWeekEndOfWeek() {
        return LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).minusWeeks(1).atStartOfDay()
                .plusWeeks(1).minusDays(1);
    }

    public static LocalDateTime getThisMonthStartOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
    }

    public static LocalDateTime getThisMonthEndOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay().plusMonths(1)
                .minusDays(1);
    }

    public static LocalDateTime getLastMonthStartOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1).atStartOfDay();
    }

    public static LocalDateTime getLastMonthEndOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1).atStartOfDay().plusMonths(1)
                .minusDays(1);
    }

    public static LocalDateTime getNextDay(Long timeStamp) {
        Instant instant = Instant.ofEpochSecond(timeStamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return localDateTime.plusDays(1);
    }

    public static Long getNextDayInstant(Long timeStamp) {
        return toInstant(getNextDay(timeStamp), true);
    }

    public static Integer getDayValue(Long timeStamp) {
        Instant instant = Instant.ofEpochSecond(timeStamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formatStr = localDateTime.format(formatter);
        return Integer.parseInt(formatStr);
    }

    public static Long getNextDayInstant(Long timeStamp, Boolean unix) {
       return toInstant(getNextDay(timeStamp), unix);
    }

    public static Long toInstant(LocalDateTime localDateTime) {
        return toInstant(localDateTime, true);
    }

    public static Long toInstant(LocalDateTime localDateTime, Boolean unix) {
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        long unixTimestamp = instant.toEpochMilli();
        return unix == null || unix ? unixTimestamp / 1000L  : unixTimestamp;
    }

}
