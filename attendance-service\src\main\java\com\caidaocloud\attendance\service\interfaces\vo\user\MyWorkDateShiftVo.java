package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyWorkDateShiftVo {
    @ApiModelProperty("班次开始时间点，单位分钟")
    private Integer startTime;
    @ApiModelProperty("班次结束时间点，单位分钟")
    private Integer endTime;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次开始时间点，格式HH:mm")
    private String startTimeTxt;
    @ApiModelProperty("班次结束时间点，格式HH:mm")
    private String endTimeTxt;

    @ApiModelProperty("班次休息开始时间点，格式HH:mm")
    private String restStartTimeTxt;
    @ApiModelProperty("班次休息结束时间点，格式HH:mm")
    private String restEndTimeTxt;
    @ApiModelProperty("工作时长")
    private String workTotalTimeTxt;
}
