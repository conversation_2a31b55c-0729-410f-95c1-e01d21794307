package com.caidaocloud.attendance.service.interfaces.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyWorkDateShiftDto {
    @ApiModelProperty("日期类型")
    private Integer dateType;
    @ApiModelProperty("开始时间")
    private Integer startTime;
    @ApiModelProperty("开始时间")
    private Integer endTime;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次开始时间点，格式HH:mm")
    private String startTimeTxt;
    @ApiModelProperty("班次结束时间点，格式HH:mm")
    private String endTimeTxt;
    @ApiModelProperty("上班打卡开始时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("上班打卡结束时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("下班打卡开始时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("下班打卡结束时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("班次编码")
    private String shiftCode;
    private String i18nShiftDefName;

    private Boolean isNoonRest;
    private Integer noonRestStart;
    private Integer noonRestEnd;
    private Integer restTotalTime;
    private Integer workTotalTime;

    @ApiModelProperty("班次休息开始时间点，格式HH:mm")
    private String restStartTimeTxt;
    @ApiModelProperty("班次休息结束时间点，格式HH:mm")
    private String restEndTimeTxt;
    @ApiModelProperty("工作时长")
    private String workTotalTimeTxt;
}
