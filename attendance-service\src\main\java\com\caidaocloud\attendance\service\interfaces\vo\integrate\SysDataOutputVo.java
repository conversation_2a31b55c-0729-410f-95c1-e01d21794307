package com.caidaocloud.attendance.service.interfaces.vo.integrate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class SysDataOutputVo implements Serializable{
    private static final long serialVersionUID = -3988935495112786666L;
    @ApiModelProperty("数据流入ID")
    private Integer sysDataOutputId;
    @ApiModelProperty("数据源类型")
    private String sourceType;
    @ApiModelProperty("数据源列")
    private String sourceCol;
    @ApiModelProperty("数据源表达式")
    private String sourceExp;
    @ApiModelProperty("输出类型")
    private String targetType;
    @ApiModelProperty("输出数据源配置")
    private Object targetConfig;
    @ApiModelProperty("输出表达式")
    private String targetExp;
    @ApiModelProperty("定时器")
    private String trigger;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("最后执行时间")
    private Long workTime;
    @ApiModelProperty("成功判断")
    private String targetGreen;
    @ApiModelProperty("说明")
    private String note;
    @ApiModelProperty("前置触发")
    private String beforeTrigger;
    @ApiModelProperty("后置触发")
    private String afterTrigger;
    @ApiModelProperty("用工单位")
    private Integer belongOrgId;
    @ApiModelProperty("创建人")
    private Integer crtuser;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("修改人")
    private Integer upduser;
    @ApiModelProperty("修改时间")
    private Long updtime;
    @ApiModelProperty("异常后置回调")
    private String errorTrigger;
}
