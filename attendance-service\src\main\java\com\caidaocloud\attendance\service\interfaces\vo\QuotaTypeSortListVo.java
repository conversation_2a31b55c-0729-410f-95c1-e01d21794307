package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Optional;

@Data
@Accessors(chain = true)
public class QuotaTypeSortListVo {
    @ApiModelProperty("业务主键")
    private String bid;
    @ApiModelProperty("额度规则ID")
    private Long configId;
    @ApiModelProperty("额度规则名称")
    private String ruleName;
    @ApiModelProperty("扣减顺序")
    private Integer sort;

    public Integer getSort() {
        return Optional.ofNullable(sort).orElse(0);
    }
}
