package com.caidaocloud.attendance.service.interfaces.dto.compensatory;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CompensatoryCaseReqDto  extends ExportBasePage implements Serializable {
    private Long empId;

    private List<Long> empIds;

    private Integer status;
}
