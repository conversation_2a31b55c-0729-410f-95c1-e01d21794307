package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/12/28 17:45
 * @Description:
 **/
@Slf4j
@RestController
@RequestMapping("/api/attendance/apply/v1")
public class EmpApplyController {

    @Deprecated
    @ApiOperation("数据迁移，将休假，加班，出差，补卡数据同步到聚合表")
    @PostMapping("/dataMigration")
    public Result<Boolean> dataMigration(@RequestBody ItemsResult<Integer> dto) {
        try {
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("EmpApplyController exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "EmpApplyController exception", Boolean.TRUE);
        }
    }

    @ApiOperation("经纬度转换")
    @GetMapping("/transformPosition")
    public Result<Boolean> transformPosition() {
        try {
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("EmpApplyController.transformPosition exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "EmpApplyController.transformPosition exception", Boolean.TRUE);
        }
    }
}
