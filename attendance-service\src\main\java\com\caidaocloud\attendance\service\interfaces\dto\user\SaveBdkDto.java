package com.caidaocloud.attendance.service.interfaces.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("补卡DTO")
public class SaveBdkDto {
    @ApiModelProperty("签到时间")
    private Long regDateTime;
    @ApiModelProperty("理由")
    private String reason;
    @ApiModelProperty("附件")
    private String file;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("签到时间")
    private List<Long> regDateTimes;

//    public String getBodyLockContent() {
//        String regDateTimeStr = null != regDateTime ? String.valueOf(regDateTime) : "0";
//        String regDateTimesStr = CollectionUtils.isNotEmpty(regDateTimes) ? StringUtils.join(regDateTimes, "#") : "0";
//        return String.format("%s#%s", regDateTimeStr, regDateTimesStr);
//    }
}
