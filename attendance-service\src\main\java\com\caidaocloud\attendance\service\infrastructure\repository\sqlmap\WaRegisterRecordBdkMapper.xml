<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaRegisterRecordBdkMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    <id column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="empid" jdbcType="BIGINT" property="empid" />
    <result column="register_type" jdbcType="INTEGER" property="registerType" />
    <result column="result_desc" jdbcType="VARCHAR" property="resultDesc" />
    <result column="result_type" jdbcType="INTEGER" property="resultType" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="reg_addr" jdbcType="VARCHAR" property="regAddr" />
    <result column="reg_date_time" jdbcType="BIGINT" property="regDateTime" />
    <result column="lng" jdbcType="NUMERIC" property="lng" />
    <result column="lat" jdbcType="NUMERIC" property="lat" />
    <result column="belong_date" jdbcType="BIGINT" property="belongDate" />
    <result column="mob_device_num" jdbcType="VARCHAR" property="mobDeviceNum" />
    <result column="crtuser" jdbcType="BIGINT" property="crtuser" />
    <result column="crttime" jdbcType="BIGINT" property="crttime" />
    <result column="upduser" jdbcType="BIGINT" property="upduser" />
    <result column="updtime" jdbcType="BIGINT" property="updtime" />
    <result column="normal_addr" jdbcType="VARCHAR" property="normalAddr" />
    <result column="normal_date" jdbcType="VARCHAR" property="normalDate" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="ow_rmk" jdbcType="VARCHAR" property="owRmk" />
    <result column="pic_list" jdbcType="VARCHAR" property="picList" />
    <result column="his_reg_time" jdbcType="BIGINT" property="hisRegTime" />
    <result column="shift_def_id" jdbcType="INTEGER" property="shiftDefId" />
    <result column="is_device_error" jdbcType="BIT" property="isDeviceError" />
    <result column="ori_mob_device_num" jdbcType="VARCHAR" property="oriMobDeviceNum" />
    <result column="is_workflow" jdbcType="BIT" property="isWorkflow" />
    <result column="approval_status" jdbcType="INTEGER" property="approvalStatus" />
    <result column="approval_reason" jdbcType="VARCHAR" property="approvalReason" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="site_id" jdbcType="BIGINT" property="siteId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="end_time" jdbcType="INTEGER" property="endTime" />
    <result column="corpid" jdbcType="BIGINT" property="corpid" />
    <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="if_valid" jdbcType="INTEGER" property="ifValid" />
    <result column="reg_date_times" jdbcType="VARCHAR" property="regDateTimes" />
  </resultMap>

  <sql id="Base_Column_List">
    record_id, empid, register_type, result_desc, result_type, reason, reg_addr, reg_date_time, 
    lng, lat, belong_date, mob_device_num, crtuser, crttime, upduser, updtime, normal_addr, 
    normal_date, type, ow_rmk, pic_list, his_reg_time, shift_def_id, is_device_error, 
    ori_mob_device_num, is_workflow, approval_status, approval_reason, file_path, revoke_reason, 
    site_id, province, city, start_time, end_time, corpid, belong_org_id, last_approval_time, 
    if_valid, reg_date_times
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_register_record_bdk
    where record_id = #{recordId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_register_record_bdk
    where record_id = #{recordId,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    insert into wa_register_record_bdk (record_id, empid, register_type, 
      result_desc, result_type, reason, 
      reg_addr, reg_date_time, lng, 
      lat, belong_date, mob_device_num, 
      crtuser, crttime, upduser, 
      updtime, normal_addr, normal_date, 
      type, ow_rmk, pic_list, 
      his_reg_time, shift_def_id, is_device_error, 
      ori_mob_device_num, is_workflow, approval_status, 
      approval_reason, file_path, revoke_reason, 
      site_id, province, city, 
      start_time, end_time, corpid, 
      belong_org_id, last_approval_time, if_valid, reg_date_times, reg_date_times
      )
    values (#{recordId,jdbcType=BIGINT}, #{empid,jdbcType=BIGINT}, #{registerType,jdbcType=INTEGER},
      #{resultDesc,jdbcType=VARCHAR}, #{resultType,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, 
      #{regAddr,jdbcType=VARCHAR}, #{regDateTime,jdbcType=BIGINT}, #{lng,jdbcType=NUMERIC}, 
      #{lat,jdbcType=NUMERIC}, #{belongDate,jdbcType=BIGINT}, #{mobDeviceNum,jdbcType=VARCHAR}, 
      #{crtuser,jdbcType=BIGINT}, #{crttime,jdbcType=BIGINT}, #{upduser,jdbcType=BIGINT},
      #{updtime,jdbcType=BIGINT}, #{normalAddr,jdbcType=VARCHAR}, #{normalDate,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{owRmk,jdbcType=VARCHAR}, #{picList,jdbcType=VARCHAR}, 
      #{hisRegTime,jdbcType=BIGINT}, #{shiftDefId,jdbcType=INTEGER}, #{isDeviceError,jdbcType=BIT}, 
      #{oriMobDeviceNum,jdbcType=VARCHAR}, #{isWorkflow,jdbcType=BIT}, #{approvalStatus,jdbcType=INTEGER}, 
      #{approvalReason,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR}, #{revokeReason,jdbcType=VARCHAR}, 
      #{siteId,jdbcType=BIGINT}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=INTEGER}, #{endTime,jdbcType=INTEGER}, #{corpid,jdbcType=BIGINT},
      #{belongOrgId,jdbcType=VARCHAR}, #{lastApprovalTime,jdbcType=BIGINT}, #{ifValid,jdbcType=INTEGER},
      #{regDateTimes,jdbcType=VARCHAR}
      )
  </insert>

  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    insert into wa_register_record_bdk
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        record_id,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="registerType != null">
        register_type,
      </if>
      <if test="resultDesc != null">
        result_desc,
      </if>
      <if test="resultType != null">
        result_type,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="regAddr != null">
        reg_addr,
      </if>
      <if test="regDateTime != null">
        reg_date_time,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="belongDate != null">
        belong_date,
      </if>
      <if test="mobDeviceNum != null">
        mob_device_num,
      </if>
      <if test="crtuser != null">
        crtuser,
      </if>
      <if test="crttime != null">
        crttime,
      </if>
      <if test="upduser != null">
        upduser,
      </if>
      <if test="updtime != null">
        updtime,
      </if>
      <if test="normalAddr != null">
        normal_addr,
      </if>
      <if test="normalDate != null">
        normal_date,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="owRmk != null">
        ow_rmk,
      </if>
      <if test="picList != null">
        pic_list,
      </if>
      <if test="hisRegTime != null">
        his_reg_time,
      </if>
      <if test="shiftDefId != null">
        shift_def_id,
      </if>
      <if test="isDeviceError != null">
        is_device_error,
      </if>
      <if test="oriMobDeviceNum != null">
        ori_mob_device_num,
      </if>
      <if test="isWorkflow != null">
        is_workflow,
      </if>
      <if test="approvalStatus != null">
        approval_status,
      </if>
      <if test="approvalReason != null">
        approval_reason,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="siteId != null">
        site_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="corpid != null">
        corpid,
      </if>
      <if test="belongOrgId != null">
        belong_org_id,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="ifValid != null">
        if_valid,
      </if>
      <if test="regDateTimes != null">
        reg_date_times,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        #{recordId,jdbcType=BIGINT},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=INTEGER},
      </if>
      <if test="resultDesc != null">
        #{resultDesc,jdbcType=VARCHAR},
      </if>
      <if test="resultType != null">
        #{resultType,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="regAddr != null">
        #{regAddr,jdbcType=VARCHAR},
      </if>
      <if test="regDateTime != null">
        #{regDateTime,jdbcType=BIGINT},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=NUMERIC},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=NUMERIC},
      </if>
      <if test="belongDate != null">
        #{belongDate,jdbcType=BIGINT},
      </if>
      <if test="mobDeviceNum != null">
        #{mobDeviceNum,jdbcType=VARCHAR},
      </if>
      <if test="crtuser != null">
        #{crtuser,jdbcType=BIGINT},
      </if>
      <if test="crttime != null">
        #{crttime,jdbcType=BIGINT},
      </if>
      <if test="upduser != null">
        #{upduser,jdbcType=BIGINT},
      </if>
      <if test="updtime != null">
        #{updtime,jdbcType=BIGINT},
      </if>
      <if test="normalAddr != null">
        #{normalAddr,jdbcType=VARCHAR},
      </if>
      <if test="normalDate != null">
        #{normalDate,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="owRmk != null">
        #{owRmk,jdbcType=VARCHAR},
      </if>
      <if test="picList != null">
        #{picList,jdbcType=VARCHAR},
      </if>
      <if test="hisRegTime != null">
        #{hisRegTime,jdbcType=BIGINT},
      </if>
      <if test="shiftDefId != null">
        #{shiftDefId,jdbcType=INTEGER},
      </if>
      <if test="isDeviceError != null">
        #{isDeviceError,jdbcType=BIT},
      </if>
      <if test="oriMobDeviceNum != null">
        #{oriMobDeviceNum,jdbcType=VARCHAR},
      </if>
      <if test="isWorkflow != null">
        #{isWorkflow,jdbcType=BIT},
      </if>
      <if test="approvalStatus != null">
        #{approvalStatus,jdbcType=INTEGER},
      </if>
      <if test="approvalReason != null">
        #{approvalReason,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="siteId != null">
        #{siteId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="corpid != null">
        #{corpid,jdbcType=BIGINT},
      </if>
      <if test="belongOrgId != null">
        #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="ifValid != null">
        #{ifValid,jdbcType=INTEGER},
      </if>
      <if test="regDateTimes != null">
        #{regDateTimes,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    update wa_register_record_bdk
    <set>
      <if test="empid != null">
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=INTEGER},
      </if>
      <if test="resultDesc != null">
        result_desc = #{resultDesc,jdbcType=VARCHAR},
      </if>
      <if test="resultType != null">
        result_type = #{resultType,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="regAddr != null">
        reg_addr = #{regAddr,jdbcType=VARCHAR},
      </if>
      <if test="regDateTime != null">
        reg_date_time = #{regDateTime,jdbcType=BIGINT},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=NUMERIC},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=NUMERIC},
      </if>
      <if test="belongDate != null">
        belong_date = #{belongDate,jdbcType=BIGINT},
      </if>
      <if test="mobDeviceNum != null">
        mob_device_num = #{mobDeviceNum,jdbcType=VARCHAR},
      </if>
      <if test="crtuser != null">
        crtuser = #{crtuser,jdbcType=BIGINT},
      </if>
      <if test="crttime != null">
        crttime = #{crttime,jdbcType=BIGINT},
      </if>
      <if test="upduser != null">
        upduser = #{upduser,jdbcType=BIGINT},
      </if>
      <if test="updtime != null">
        updtime = #{updtime,jdbcType=BIGINT},
      </if>
      <if test="normalAddr != null">
        normal_addr = #{normalAddr,jdbcType=VARCHAR},
      </if>
      <if test="normalDate != null">
        normal_date = #{normalDate,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="owRmk != null">
        ow_rmk = #{owRmk,jdbcType=VARCHAR},
      </if>
      <if test="picList != null">
        pic_list = #{picList,jdbcType=VARCHAR},
      </if>
      <if test="hisRegTime != null">
        his_reg_time = #{hisRegTime,jdbcType=BIGINT},
      </if>
      <if test="shiftDefId != null">
        shift_def_id = #{shiftDefId,jdbcType=INTEGER},
      </if>
      <if test="isDeviceError != null">
        is_device_error = #{isDeviceError,jdbcType=BIT},
      </if>
      <if test="oriMobDeviceNum != null">
        ori_mob_device_num = #{oriMobDeviceNum,jdbcType=VARCHAR},
      </if>
      <if test="isWorkflow != null">
        is_workflow = #{isWorkflow,jdbcType=BIT},
      </if>
      <if test="approvalStatus != null">
        approval_status = #{approvalStatus,jdbcType=INTEGER},
      </if>
      <if test="approvalReason != null">
        approval_reason = #{approvalReason,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="siteId != null">
        site_id = #{siteId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="corpid != null">
        corpid = #{corpid,jdbcType=BIGINT},
      </if>
      <if test="belongOrgId != null">
        belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="ifValid != null">
        if_valid = #{ifValid,jdbcType=INTEGER},
      </if>
      <if test="regDateTimes != null">
        reg_date_times = #{regDateTimes,jdbcType=VARCHAR},
      </if>
    </set>
    where record_id = #{recordId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    update wa_register_record_bdk
    set empid = #{empid,jdbcType=BIGINT},
      register_type = #{registerType,jdbcType=INTEGER},
      result_desc = #{resultDesc,jdbcType=VARCHAR},
      result_type = #{resultType,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      reg_addr = #{regAddr,jdbcType=VARCHAR},
      reg_date_time = #{regDateTime,jdbcType=BIGINT},
      lng = #{lng,jdbcType=NUMERIC},
      lat = #{lat,jdbcType=NUMERIC},
      belong_date = #{belongDate,jdbcType=BIGINT},
      mob_device_num = #{mobDeviceNum,jdbcType=VARCHAR},
      crtuser = #{crtuser,jdbcType=BIGINT},
      crttime = #{crttime,jdbcType=BIGINT},
      upduser = #{upduser,jdbcType=BIGINT},
      updtime = #{updtime,jdbcType=BIGINT},
      normal_addr = #{normalAddr,jdbcType=VARCHAR},
      normal_date = #{normalDate,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      ow_rmk = #{owRmk,jdbcType=VARCHAR},
      pic_list = #{picList,jdbcType=VARCHAR},
      his_reg_time = #{hisRegTime,jdbcType=BIGINT},
      shift_def_id = #{shiftDefId,jdbcType=INTEGER},
      is_device_error = #{isDeviceError,jdbcType=BIT},
      ori_mob_device_num = #{oriMobDeviceNum,jdbcType=VARCHAR},
      is_workflow = #{isWorkflow,jdbcType=BIT},
      approval_status = #{approvalStatus,jdbcType=INTEGER},
      approval_reason = #{approvalReason,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      site_id = #{siteId,jdbcType=BIGINT},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      corpid = #{corpid,jdbcType=BIGINT},
      belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      if_valid = #{ifValid,jdbcType=INTEGER},
      reg_date_times = #{regDateTimes,jdbcType=VARCHAR}
    where record_id = #{recordId,jdbcType=BIGINT}
  </update>

  <select id="queryEmpDkCountByType" resultType="java.lang.Integer">
    SELECT count(0) AS "count"
    FROM wa_register_record_bdk r
    WHERE r.type = #{type} AND r.empid = #{empId}
    AND r.reg_date_time <![CDATA[>=]]> #{startTime} AND r.reg_date_time <![CDATA[<=]]> #{endTime}
    AND r.shift_def_id IS NOT NULL AND (coalesce(r.is_workflow, FALSE) = FALSE OR (r.is_workflow = TRUE AND r.approval_status IN (1,2)))
  </select>

  <select id="getRegisterPageList" resultType="java.util.Map">
    select * from (
    select rd.record_id as "recordId",
    rd.type,
    rd.reg_date_time as "regDateTime",
    rd.reg_addr as "regAddr",
    rd.mob_device_num as "mobDeviceNum",
    rd.reason,
    rd.approval_status as "approvalStatus",
    rd.last_approval_time as "approvalTime",
    rd.belong_date as "belongDate",
    rd.register_type as "registerType",
    rd.result_type as "resultType",
    rd.ow_rmk as "owRmk",
    rd.file_path as "files",
    rd.pic_list as "fileNames",
    sei.workno,
    sui.empname AS "userName",
    rd.crttime,
    sei.emp_name as "empName",
    case
    when sco.full_path is not null and sco.full_path != ''
    then concat_ws('/', sco.full_path, sco.shortname)
    else sco.shortname
    end  as "fullPath",
    sco.shortname,
    sco.shortname as "orgName",
    sei.stats,
    sei.employ_type,
    sei.orgid,
    rd.empid,rd.crtuser,rd.upduser,rd.updtime,
    rd.reg_date_times as "regDateTimes"
    FROM wa_register_record_bdk rd
    JOIN sys_emp_info sei on rd.empid = sei.empid AND sei.belong_org_id=rd.belong_org_id AND sei.deleted = 0
    LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid AND sco.deleted = 0
    LEFT JOIN sys_user_info sui ON sui.userid = rd.crtuser
    WHERE rd.corpid = #{corpId} AND rd.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    <choose>
      <when test="startDate != null and endDate != null">
        <choose>
          <when test="isAnalyze">
            AND rd.belong_date BETWEEN #{startDate} and #{endDate}
          </when>
          <otherwise>
            AND rd.reg_date_time BETWEEN #{startDate} and #{endDate}
          </otherwise>
        </choose>
      </when>
    </choose>
    <if test="ifShowAll != null and ifShowAll == true">
      AND (rd.type in(1, 2, 3, 4, 5) OR (rd.type = 6 AND rd.approval_status = 2))
    </if>
    <if test="types != null and types.size > 0">
      AND rd.type in
      <foreach close=")" collection="types" item="listItem" open="(" separator=",">
        #{listItem}
      </foreach>
    </if>
    <if test="keywords != null and keywords != ''">
      AND (sei.emp_name like concat('%', #{keywords}, '%') OR sei.workno like CONCAT('%', #{keywords},'%'))
    </if>
    <if test="datafilter != null and datafilter != ''">
      ${datafilter}
    </if>
    ) t
    <where>
      <if test="filter != null and filter != ''">
        ${filter}
      </if>
    </where>
    order by "belongDate" desc
  </select>

  <select id="getEmpBdkRegisterList" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    select empid, belong_date as "belongDate"
    from wa_register_record_bdk r
    where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    and type = 6
    and (approval_status is null or approval_status = 2)
    and empid in <foreach collection="empIdList" open="(" separator="," item="item" close=")">#{item}</foreach>
    and belong_date BETWEEN #{startDate} AND #{endDate}
  </select>

  <select id="queryRegisterDetailById" resultType="java.util.Map">
      SELECT
            e.workno,
            e.emp_name                                                         AS "empName",
            co.shortname                                                       AS "shortName",
            case
              when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
              else co.shortname
              end                                                        AS "fullPath",
            e.hire_date                                                        AS "hireDate",
            r.belong_date                                                      AS "belongDate",
            CASE r.register_type WHEN 1 THEN '签到' WHEN 2 THEN '签退' ELSE '' END AS "registerTypeName",
            r.reg_date_time                                                    AS "regDateTime",
            r.reason                                                           AS "reason",
            r.file_path                                                        AS "files",
            r.approval_status AS "approvalStatus",
            CASE
              WHEN r.approval_status = 1 THEN '审批中'
              WHEN r.approval_status = 2 THEN '已通过'
              WHEN r.approval_status = 3 THEN '已拒绝'
              WHEN r.approval_status = 4 THEN '已作废'
              WHEN r.approval_status = 5 THEN '已退回'
              WHEN r.approval_status = 9 THEN '已撤销'   END                   AS "statusName",
            r.pic_list                                                        As "fileNames",
            r.reg_date_times as "regDateTimes",
            e.workplace as "workCity",
            r.crttime,r.empid,r.crtuser
      FROM wa_register_record_bdk r
      LEFT JOIN sys_emp_info e ON r.empid = e.empid and e.deleted = 0
      LEFT JOIN sys_corp_org co ON co.orgid = e.orgid
      where r.record_id = #{registerId} and r.corpid = #{corpId}
    </select>

  <select id="queryEmpRegisterRecordList" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaRegisterRecordBdkPo">
    select * from wa_register_record_bdk
    where empid = #{empId} and belong_date between #{startDate} and #{endDate}
    <if test="statusList != null and statusList.size() > 0">
      and approval_status in
      <foreach collection="statusList" item="status" separator="," open="(" close=")">
        #{status}
      </foreach>
    </if>
  </select>

  <select id="getEmpFillClockCount" resultType="map">
    select r.empid, count(0) as num
    from wa_register_record_bdk r
    where type = 6
    and (approval_status is null or approval_status = 2)
    <if test="anyEmpid != null">
      and empid = any(${anyEmpid})
    </if>
    and belong_date BETWEEN #{startDate} AND #{endDate}
    group by r.empid
  </select>
</mapper>