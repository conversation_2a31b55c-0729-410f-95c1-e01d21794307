package com.caidaocloud.attendance.service.interfaces.dto.leaveExtension;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LeaveExtensionDto {
    private Long id;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("员工工号")
    private String workNo;
    @ApiModelProperty("任职组织")
    private String orgName;
    @ApiModelProperty("延期额度")
    private Float timeDuration;
    @ApiModelProperty("延期额度单位：1、天，2、小时")
    private Integer timeUnit;
    @ApiModelProperty("延期额度单位：1、天，2、小时")
    private String timeUnitName;
    @ApiModelProperty("审批状态:1、审批中，2、已通过，9、已撤销")
    private Integer status;
    @ApiModelProperty("审批状态:1、审批中，2、已通过，9、已撤销")
    private String statusName;
    @ApiModelProperty("延期开始时间")
    private Long startDate;
    @ApiModelProperty("延期结束时间")
    private Long endDate;
    @ApiModelProperty("原失效日期")
    private Long originalEndDate;
    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;
    @ApiModelProperty("申请事由")
    private String reason;
    @ApiModelProperty("撤销原因")
    private String revokeReason;
    @ApiModelProperty("申请时间")
    private Long createTime;
    private String businessKey;
    @ApiModelProperty("延期假期类型")
    private String quotaName;
    private String i18nRuleName;
}