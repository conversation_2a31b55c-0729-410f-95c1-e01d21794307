package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.service.impl.RuleService;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/attendance/rule/v1")
@Api(value = "/api/attendance/rule/v1", description = "考勤规则接口")
public class RuleController {

    @Autowired
    private RuleService ruleService;

    @ApiOperation(value = "获取考勤规则列表", produces = "application/json")
    @GetMapping(value = "/list")
    public Result selectParseGroupList() {
        Map<String, Object> map = new HashMap<>();
        map.put("items", ruleService.selectParseGroupList());
        return ResponseWrap.wrapResult(map);
    }
}
