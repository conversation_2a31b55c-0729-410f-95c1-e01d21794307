package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工申请记录")
public class WaEmpApplyRecordVo {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("模块")
    private String module;
    @ApiModelProperty("模块名称")
    private String moduleName;
    @ApiModelProperty("员工id")
    private Long empId;
    @ApiModelProperty("申请时间")
    private String applyTime;
    @ApiModelProperty("流程key")
    private String businessKey;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("补卡时间")
    private String replaceTime;
    @ApiModelProperty("时长")
    private String duration;
    @ApiModelProperty("状态")
    private Integer status;
}