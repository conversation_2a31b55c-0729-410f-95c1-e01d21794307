package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WaAnalyzeDetailVo {

    @ApiModelProperty("考勤日期")
    private Long belongDate;
    @ApiModelProperty("所属班次")
    private String shiftDefName;
    @ApiModelProperty("打卡规则")
    private Integer clockType;
    @ApiModelProperty("应工作时长")
    private Integer workTime;
    @ApiModelProperty("实工作时长")
    private Float actualWorkTime;
    @ApiModelProperty("上班打卡时间")
    private Integer startTime;
    @ApiModelProperty("下班打卡时间")
    private Integer endTime;
    @ApiModelProperty("最早上班打卡时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("最晚上班打卡时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("最早下班打卡时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("最晚下班打卡时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("签到时间")
    private Long regSignInTime;
    @ApiModelProperty("签退时间")
    private Long regSignOffTime;
    @ApiModelProperty("上班打卡结果")
    private Integer regSignInResult;
    @ApiModelProperty("下班打卡结果")
    private Integer regSignOffResult;
    @ApiModelProperty("上班补卡结果")
    private Long makeUpSignTime;
    @ApiModelProperty("下班补卡结果")
    private Long makeOffSignTime;
    @ApiModelProperty("打卡记录")
    private List<RegisterRecordAnalyzeVo> recordList;

}
