package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.dto.TimeSlot;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class ShiftVo implements Serializable {

    private Integer shiftDefId;
    private Integer dateType;
    private Boolean isNight;
    private String shiftDefName;
    private String shiftDefCode;
    private Integer startTime;
    private Integer endTime;
    private Boolean isNoonRest;
    private Integer noonRestStart;
    private Integer noonRestEnd;
    private Integer restTotalTime;
    private Integer workTotalTime;
    private Integer onDutyStartTime;
    private Integer onDutyEndTime;
    private Integer offDutyStartTime;
    private Integer offDutyEndTime;
    private Integer overtimeStartTime;
    private Integer overtimeEndTime;
    private String belongOrgid;
    private Boolean isDefault;
    private Boolean isHalfdayTime;
    private Integer halfdayTime;
    private Boolean isFlexibleWork;
    private Integer flexibleOnDutyStartTime;
    private Integer flexibleOnDutyEndTime;
    private Integer flexibleOffDutyStartTime;
    private Integer flexibleOffDutyEndTime;
    private Integer flexibleWorkType;
    private Object restPeriods;
    private Object overtimeRestPeriods;
    private Boolean isAdjustWorkHour;
    private Object adjustWorkHourJson;
    private Boolean isSpecial;
    private Integer specialWorkTime;
    private Boolean isApplyOvertime;
    private Object multiCheckinTimes;
    private Object multiWorkTimes;
    private String restTimeDesc;
    private Long orgid;
    private Long effectStartTime;
    private Long effectEndTime;
    private Integer flexibleWorkRule;
    private BigDecimal flexibleWorkLate;
    private BigDecimal flexibleWorkEarly;
    private String flexibleOffWorkRule;
    private List<FlexibleVo> flexibleRules;
    private Integer flexibleShiftSwitch;
    @ApiModelProperty("补卡时间限制")
    private List<TimeSlot> repairRestPeriods;
    @ApiModelProperty("中途打卡时间段")
    private List<TimeSlot> midwayClockTimes;
    @ApiModelProperty("代替班次ID, 非工作日出差等业务使用")
    private Integer substituteShift;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nShiftDefName;
}
