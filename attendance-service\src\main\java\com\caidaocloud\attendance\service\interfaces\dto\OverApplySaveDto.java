package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 加班单保存DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("加班单保存DTO")
public class OverApplySaveDto {
    @ApiModelProperty("员工id")
    private Long empId;
    @ApiModelProperty("开始日期 日")
    private Integer startTime;
    @ApiModelProperty("结束日期 日")
    private Integer endTime;
    @ApiModelProperty("开始时间 分钟")
    private Integer stime;
    @ApiModelProperty("结束时间 分钟")
    private Integer etime;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("补偿类型")
    private Integer compensateType;
    @ApiModelProperty("附件")
    private String file;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("加班类型")
    private Integer overtimeTypeId;
    @ApiModelProperty("申请入口")
    private AppTypeEnum appType;
    @ApiModelProperty("重新发起的id，重新发起时使用")
    private Integer waid;
    @ApiModelProperty("批量单据ID，批量申请时使用")
    private Long batchId;
}
