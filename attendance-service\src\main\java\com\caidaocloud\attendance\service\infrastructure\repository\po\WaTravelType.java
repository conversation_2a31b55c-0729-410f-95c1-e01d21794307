package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName(value = "wa_travel_type")
@Data
public class WaTravelType {
    private Long travelTypeId;

    private String tenantId;

    private String travelTypeName;

    private Integer acctTimeType;

    private Float roundTimeUnit;

    private Integer ifIncludeNonWorkday;

    private Integer ifUploadFile;

    private Integer ifWriteRemark;

    private String remark;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    private Integer overtimeRule;

    private String autoTransferRule;

    private Boolean revokeWorkflow;

    private Boolean revokePassed;

    private String revokeAllowStatus;

    private String i18nTravelTypeName;

    private Integer travelTypeDef;

    private Integer sortNum;
}