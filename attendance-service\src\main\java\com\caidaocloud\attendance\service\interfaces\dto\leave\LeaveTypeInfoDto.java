package com.caidaocloud.attendance.service.interfaces.dto.leave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/7/8 19:08
 * @Description:
 **/
@Data
public class LeaveTypeInfoDto implements Serializable {
    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
    @ApiModelProperty("假期类型")
    private Integer leaveType;
    @ApiModelProperty("假期类型名称")
    private String leaveTypeName;
    @ApiModelProperty("假期名称")
    private String leaveName;
    @ApiModelProperty("假期编码")
    private String leaveCode;
    @ApiModelProperty("时间单位")
    private Object acctTimeType;
    @ApiModelProperty("最小单位")
    private Float roundTimeUnit;
    @ApiModelProperty("休息日顺延")
    private Object isRestDay;
    @ApiModelProperty("法定假日顺延")
    private Object isLegalHoliday;
    @ApiModelProperty("适用性别")
    private Object genderType;
    @ApiModelProperty("额度限制类型： 1 限额、2 不限额")
    private Integer quotaRestrictionType;
    private String quotaRestrictionTypeName;
    @ApiModelProperty("额度类型： 1 按年发放、2 调休、3 固定额度")
    private Integer quotaType;
    @ApiModelProperty("状态： 0 停用、1 启用")
    private Integer status;
    @ApiModelProperty("顺序")
    private Integer orders;
    //其他字段
    private String statusTxt;
    private String acctTimeTypeTxt;
    private String isRestDaytxt;
    private String isLegalHolidayTxt;
    private String genderTypeTxt;

    private String leaveTypeDefCode;

    @ApiModelProperty("调休复现申请数字输入整数限制：0开启，1关闭")
    private Integer applyNumberType;
    private String i18nLeaveName;
    private String i18nLeaveTypeName;
}
