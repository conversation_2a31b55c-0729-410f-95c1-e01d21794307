<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaClockPlanMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="corp_id" jdbcType="BIGINT" property="corpId" />
    <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="clock_way" jdbcType="VARCHAR" property="clockWay" />
    <result column="supplement_count" jdbcType="INTEGER" property="supplementCount" />
    <result column="is_supplement" jdbcType="BIT" property="isSupplement" />
    <result column="gps" jdbcType="VARCHAR" property="gps" />
    <result column="wifi" jdbcType="VARCHAR" property="wifi" />
    <result column="bluetooth" jdbcType="VARCHAR" property="bluetooth" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="group_exp" jdbcType="VARCHAR" property="groupExp" />
    <result column="group_note" jdbcType="VARCHAR" property="groupNote" />
    <!--20210830 by Aaron-->
    <result column="allow_field_clock_in" jdbcType="BIT" property="allowFieldClockIn" />
    <result column="field_clock_in_note" jdbcType="BIT" property="fieldClockInNote" />
    <result column="field_clock_in_enclosure" jdbcType="BIT" property="fieldClockInEnclosure" />
    <!--20210908 by Aaron-->
    <result column="time_interval" property="timeInterval" jdbcType="REAL" />
    <!--20210923 by Aaron-->
    <result column="description" jdbcType="VARCHAR" property="description" />
    <!--20211209 by Aaron-->
    <result column="reason_must" jdbcType="BIT" property="reasonMust" />
    <result column="clock_in_allowed" jdbcType="BIT" property="clockInAllowed" />
    <result column="supplement_number" jdbcType="INTEGER" property="supplementNumber" />
    <result column="reason_word_num" jdbcType="INTEGER" property="reasonWordNum" />
    <result column="enclosure_required" jdbcType="BIT" property="enclosureRequired" />
    <result column="i18n_plan_name" jdbcType="VARCHAR" property="i18nPlanName" />
  </resultMap>

  <sql id="Base_Column_List">
    id, corp_id, belong_org_id, plan_name, clock_way, supplement_count, is_supplement, 
    gps, wifi, bluetooth, creator, create_time, updater, update_time, group_exp, group_note,
    allow_field_clock_in, field_clock_in_note, field_clock_in_enclosure, time_interval, description,
    reason_must, clock_in_allowed, supplement_number, reason_word_num, enclosure_required, i18n_plan_name
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_clock_plan
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_clock_plan
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo">
    insert into wa_clock_plan (id, corp_id, belong_org_id, plan_name, clock_way, supplement_count,
      is_supplement, gps, wifi, bluetooth, creator, create_time, updater, update_time, group_exp, group_note,
      allow_field_clock_in, field_clock_in_note, field_clock_in_enclosure, time_interval, description, reason_must,
      clock_in_allowed, supplement_number, reason_word_num, enclosure_required, i18n_plan_name)
    values (#{id,jdbcType=BIGINT}, #{corpId,jdbcType=BIGINT}, #{belongOrgId,jdbcType=VARCHAR},
      #{planName,jdbcType=VARCHAR}, #{clockWay,jdbcType=VARCHAR}, #{supplementCount,jdbcType=INTEGER}, 
      #{isSupplement,jdbcType=BIT}, #{gps,jdbcType=VARCHAR}, #{wifi,jdbcType=VARCHAR}, 
      #{bluetooth,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
      #{updater,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{groupExp,jdbcType=VARCHAR}, #{groupNote,jdbcType=VARCHAR},
      #{allowFieldClockIn,jdbcType=BIT}, #{fieldClockInNote,jdbcType=BIT}, #{fieldClockInEnclosure,jdbcType=BIT}, #{timeInterval,jdbcType=REAL},
      #{description,jdbcType=VARCHAR}, #{reasonMust,jdbcType=BIT}, #{clockInAllowed,jdbcType=BIT}, #{supplementNumber,jdbcType=INTEGER},
      #{reasonWordNum,jdbcType=INTEGER}, #{enclosureRequired,jdbcType=BIT},#{i18nPlanName,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo">
    insert into wa_clock_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="belongOrgId != null">
        belong_org_id,
      </if>
      <if test="planName != null">
        plan_name,
      </if>
      <if test="clockWay != null">
        clock_way,
      </if>
      <if test="supplementCount != null">
        supplement_count,
      </if>
      <if test="isSupplement != null">
        is_supplement,
      </if>
      <if test="gps != null">
        gps,
      </if>
      <if test="wifi != null">
        wifi,
      </if>
      <if test="bluetooth != null">
        bluetooth,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="groupExp != null">
        group_exp,
      </if>
      <if test="groupNote != null">
        group_note,
      </if>
      <if test="allowFieldClockIn != null">
        allow_field_clock_in,
      </if>
      <if test="fieldClockInNote != null">
        field_clock_in_note,
      </if>
      <if test="fieldClockInEnclosure != null">
        field_clock_in_enclosure,
      </if>
      <if test="timeInterval != null">
        time_interval,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="reasonMust != null">
        reason_must,
      </if>
      <if test="clockInAllowed != null">
        clock_in_allowed,
      </if>
      <if test="supplementNumber != null">
        supplement_number,
      </if>
      <if test="reasonWordNum != null">
        reason_word_num,
      </if>
      <if test="enclosureRequired != null">
        enclosure_required,
      </if>
      <if test="i18nPlanName != null">
        i18n_plan_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=BIGINT},
      </if>
      <if test="belongOrgId != null">
        #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="clockWay != null">
        #{clockWay,jdbcType=VARCHAR},
      </if>
      <if test="supplementCount != null">
        #{supplementCount,jdbcType=INTEGER},
      </if>
      <if test="isSupplement != null">
        #{isSupplement,jdbcType=BIT},
      </if>
      <if test="gps != null">
        #{gps,jdbcType=VARCHAR},
      </if>
      <if test="wifi != null">
        #{wifi,jdbcType=VARCHAR},
      </if>
      <if test="bluetooth != null">
        #{bluetooth,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="groupExp != null">
        #{groupExp,jdbcType=VARCHAR},
      </if>
      <if test="groupNote != null">
        #{groupNote,jdbcType=VARCHAR},
      </if>
      <if test="allowFieldClockIn != null">
        #{allowFieldClockIn,jdbcType=BIT},
      </if>
      <if test="fieldClockInNote != null">
        #{fieldClockInNote,jdbcType=BIT},
      </if>
      <if test="fieldClockInEnclosure != null">
        #{fieldClockInEnclosure,jdbcType=BIT},
      </if>
      <if test="timeInterval != null">
        #{timeInterval,jdbcType=REAL},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="reasonMust != null">
        #{reasonMust,jdbcType=BIT},
      </if>
      <if test="clockInAllowed != null">
        #{clockInAllowed,jdbcType=BIT},
      </if>
      <if test="supplementNumber != null">
        #{supplementNumber,jdbcType=INTEGER},
      </if>
      <if test="reasonWordNum != null">
        #{reasonWordNum,jdbcType=INTEGER},
      </if>
      <if test="enclosureRequired != null">
        #{enclosureRequired,jdbcType=BIT},
      </if>
      <if test="i18nPlanName != null">
        #{i18nPlanName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo">
    update wa_clock_plan
    <set>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=BIGINT},
      </if>
      <if test="belongOrgId != null">
        belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        plan_name = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="clockWay != null">
        clock_way = #{clockWay,jdbcType=VARCHAR},
      </if>
      <if test="supplementCount != null">
        supplement_count = #{supplementCount,jdbcType=INTEGER},
      </if>
      <if test="isSupplement != null">
        is_supplement = #{isSupplement,jdbcType=BIT},
      </if>
      <if test="gps != null">
        gps = #{gps,jdbcType=VARCHAR},
      </if>
      <if test="wifi != null">
        wifi = #{wifi,jdbcType=VARCHAR},
      </if>
      <if test="bluetooth != null">
        bluetooth = #{bluetooth,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="groupExp != null">
        group_exp = #{groupExp,jdbcType=VARCHAR},
      </if>
      <if test="groupNote != null">
        group_note = #{groupNote,jdbcType=VARCHAR},
      </if>
      <if test="allowFieldClockIn != null">
        allow_field_clock_in = #{allowFieldClockIn,jdbcType=BIT},
      </if>
      <if test="fieldClockInNote != null">
        field_clock_in_note = #{fieldClockInNote,jdbcType=BIT},
      </if>
      <if test="fieldClockInEnclosure != null">
        field_clock_in_enclosure = #{fieldClockInEnclosure,jdbcType=BIT},
      </if>
      <if test="timeInterval != null">
        time_interval = #{timeInterval,jdbcType=REAL},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="reasonMust != null">
        reason_must = #{reasonMust,jdbcType=BIT},
      </if>
      <if test="clockInAllowed != null">
        clock_in_allowed = #{clockInAllowed,jdbcType=BIT},
      </if>
      <if test="supplementNumber != null">
        supplement_number = #{supplementNumber,jdbcType=INTEGER},
      </if>
      <if test="reasonWordNum != null">
        reason_word_num = #{reasonWordNum,jdbcType=INTEGER},
      </if>
      <if test="enclosureRequired != null">
        enclosure_required = #{enclosureRequired,jdbcType=BIT},
      </if>
      <if test="i18nPlanName != null">
        i18n_plan_name = #{i18nPlanName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockPlanPo">
    update wa_clock_plan
    set corp_id = #{corpId,jdbcType=BIGINT},
      belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      plan_name = #{planName,jdbcType=VARCHAR},
      clock_way = #{clockWay,jdbcType=VARCHAR},
      supplement_count = #{supplementCount,jdbcType=INTEGER},
      is_supplement = #{isSupplement,jdbcType=BIT},
      gps = #{gps,jdbcType=VARCHAR},
      wifi = #{wifi,jdbcType=VARCHAR},
      bluetooth = #{bluetooth,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      updater = #{updater,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      group_exp = #{groupExp,jdbcType=VARCHAR},
      group_note = #{groupNote,jdbcType=VARCHAR},
      allow_field_clock_in = #{allowFieldClockIn,jdbcType=BIT},
      field_clock_in_note = #{fieldClockInNote,jdbcType=BIT},
      field_clock_in_enclosure = #{fieldClockInEnclosure,jdbcType=BIT},
      time_interval = #{timeInterval,jdbcType=REAL},
      description = #{description,jdbcType=VARCHAR},
      reason_must = #{reasonMust,jdbcType=BIT},
      clock_in_allowed = #{clockInAllowed,jdbcType=BIT},
      supplement_number = #{supplementNumber,jdbcType=INTEGER},
      reason_word_num = #{reasonWordNum,jdbcType=INTEGER},
      enclosure_required = #{enclosureRequired,jdbcType=BIT},
      i18n_plan_name = #{i18nPlanName,jdbcType=VARCHAR},
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryClockPlanPageList" resultMap="BaseResultMap">
    select * from wa_clock_plan
    where corp_id = #{corpId,jdbcType=BIGINT}
    <if test="belongOrgId != null">
      and belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </if>
    <if test="keywords !=null and keywords != ''">
      and plan_name like concat('%', #{keywords}, '%')
    </if>
    order by update_time desc
  </select>

  <select id="queryPlanByParams" resultMap="BaseResultMap">
    select * from wa_clock_plan
    where corp_id = #{corpId,jdbcType=BIGINT}
    <if test="belongOrgId != null">
      and belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </if>
    <if test="planName != null and planName != ''">
      and plan_name = #{planName,jdbcType=VARCHAR}
    </if>
    <if test="planId != null">
      and id <![CDATA[<>]]> #{planId,jdbcType=BIGINT}
    </if>
  </select>

  <select id="queryPlanRelEmployeesByEmpIds" resultType="java.util.HashMap">
    select distinct p.id, p.plan_name as planName, r.emp_id as empId, e.emp_name as empName, e.workno as workNo
    from wa_clock_plan p
    left join wa_plan_emp_rel r on p.id = r.plan_id
    left join sys_emp_info e on e.empid = r.emp_id and e.deleted = 0
    where r.corp_id = #{corpId,jdbcType=BIGINT}
    <if test="belongOrgId != null">
      and r.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </if>
    <if test="planId != null">
      and r.plan_id <![CDATA[<>]]> #{planId,jdbcType=BIGINT}
    </if>
    <if test="empIds!=null and empIds.size()>0">
      and r.emp_id in
      <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
        #{empId,jdbcType=INTEGER}
      </foreach>
    </if>
  </select>

  <select id="getPlanListBySiteId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List" /> from wa_clock_plan where #{siteId} = any (string_to_array(gps,',') :: bigint[])
  </select>
</mapper>