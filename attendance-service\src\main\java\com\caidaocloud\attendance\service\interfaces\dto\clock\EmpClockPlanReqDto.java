package com.caidaocloud.attendance.service.interfaces.dto.clock;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 打卡方案列表查询参数
 *
 * <AUTHOR>
 * @Date 2021/9/9
 */
@Data
public class EmpClockPlanReqDto extends AttendanceBasePage {
    @ApiModelProperty("打卡方案id")
    private Long planId;
    private String dataScope;
    @ApiModelProperty("方案状态：1 未生效 、2 生效中 、3 已失效 ，三种状态可随意组合，中间用英文逗号隔开")
    private String effectiveStatus;

    public String getEffectiveStatusFilterValue() {
        if (StringUtils.isBlank(this.effectiveStatus)) {
            return null;
        }
        switch (this.effectiveStatus) {
            case "1":
                return "NotEffective";
            case "2":
                return "InEffect";
            case "3":
                return "Expired";
            case "1,2":
                return "NotEffective+InEffect";
            case "2,1":
                return "NotEffective+InEffect";
            case "1,3":
                return "NotEffective+Expired";
            case "3,1":
                return "NotEffective+Expired";
            case "2,3":
                return "InEffect+Expired";
            case "3,2":
                return "InEffect+Expired";
            default:
                return null;
        }
    }
}
