server:
  port: 8088
spring:
  application:
    name: caidaocloud-attendance-service
  cloud:
    nacos:
      discovery:
        server-addr: 106.52.150.12:8848
        namespace: herman

nacos:
  com:
    alibaba:
      nacos:
        naming:
          cache:
            dir: /tmp
  config:
    type: yaml
    server-addr: 106.52.150.12:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: herman
    bootstrap:
      enable: true
      log:
        enable: true
