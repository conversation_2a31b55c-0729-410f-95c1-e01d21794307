package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.ioc.util.ListsHelper;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IQuotaTypeService;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveQuotaPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveQuotaSortDto;
import com.caidaocloud.attendance.service.interfaces.vo.LeaveQuotaVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/24
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/quotatype/v1")
public class QuotaTypeController {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private IQuotaTypeService quotaTypeService;

    @ApiOperation(value = "查询配额类型列表")
    @PostMapping(value = "/list")
    public Result<AttendancePageResult<LeaveQuotaDto>> getLeaveSettingPageList(@RequestBody LeaveQuotaPageDto quotaPageDto) {
        AttendanceBasePage basePage = com.caidaocloud.util.ObjectConverter.convert(quotaPageDto, AttendanceBasePage.class);
        return ResponseWrap.wrapResult(quotaTypeService.getLeaveQuotaPageList(basePage, quotaPageDto.getLeaveTypeId()));
    }

    private Result<Boolean> checkParams(LeaveQuotaDto leaveQuotaDto) {
        if (leaveQuotaDto.getQuotaSettingName().length() > 50) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.NAME_LENGTH_LIMIT, null).getMsg(), 50));
        }
        if (StringUtils.isNotBlank(leaveQuotaDto.getRmk()) && leaveQuotaDto.getRmk().length() > 200) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.NOTE_LENGTH_LIMIT, null).getMsg(), 200));
        }
        return Result.ok(true);
    }

    @ApiOperation(value = "新增假期配额类型")
    @PostMapping(value = "/save")
    public Result<Boolean> saveLeaveSetting(@RequestBody LeaveQuotaDto leaveQuotaDto) {
        Result<Boolean> checkResult = checkParams(leaveQuotaDto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        quotaTypeService.saveOrUpdateLeaveQuota(leaveQuotaDto);
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation(value = "修改假期配额类型")
    @PostMapping(value = "/update")
    public Result<Boolean> updateLeaveSetting(@RequestBody LeaveQuotaDto leaveQuotaDto) {
        Result<Boolean> checkResult = checkParams(leaveQuotaDto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        quotaTypeService.saveOrUpdateLeaveQuota(leaveQuotaDto);
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation(value = "删除假期配额类型")
    @DeleteMapping(value = "/delete")
    public Result<Boolean> deleteLeaveSetting(@RequestParam("id") Integer id) {
        try {
            quotaTypeService.deleteLeaveQuota(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            if (e instanceof CDException) {
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, e.getMessage(), Boolean.FALSE);
            }
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "查询假期配额类型信息")
    @GetMapping(value = "/detail")
    public Result<LeaveQuotaVo> getLeaveSettingById(@RequestParam("id") Integer id) {
        LeaveQuotaDto leaveQuotaDto = quotaTypeService.getLeaveQuotaById(id);
        LeaveQuotaVo leaveQuotaVo = ObjectConverter.convert(leaveQuotaDto, LeaveQuotaVo.class);
        if (CollectionUtils.isNotEmpty(leaveQuotaDto.getQuotaGroups())) {
            leaveQuotaVo.setQuotaGroups(leaveQuotaDto.getQuotaGroups());
        }
        return ResponseWrap.wrapResult(leaveQuotaVo);
    }

    @ApiOperation(value = "查询假期配额类型下拉列表")
    @GetMapping(value = "/selectLeaveSettingList")
    public Result<ItemsResult> selectLeaveSettingList(Long empid) throws Exception {
        List<Map> mapList = waConfigService.getLeaveSettingByEmp(empid);
        List list = ListsHelper.convertBean2List(mapList, new ListsHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("quota_setting_id");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("quota_setting_name");
            }
        });
        return ResponseWrap.wrapResult(new ItemsResult<>(list));
    }

    @ApiOperation(value = "设置配额扣减顺序")
    @PostMapping(value = "/updteQuotaSort")
    public Result<Boolean> updateQuotaSort(@RequestBody LeaveQuotaSortDto quotaSortDto) {
        quotaTypeService.updateQuotaSort(quotaSortDto.getQuotaSettingId(), quotaSortDto.getQuotaSortNo());
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }
}