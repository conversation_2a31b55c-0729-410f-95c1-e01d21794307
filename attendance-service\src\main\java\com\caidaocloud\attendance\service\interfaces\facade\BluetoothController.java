package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.report.dto.PageBean;
import com.caidao1.system.service.dto.EquipmentMaintainingParam;
import com.caidao1.system.mybatis.model.SysIbeaconInfo;
import com.caidao1.system.service.EquipmentMaintainingService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.infrastructure.util.BeanMapUtils;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.BluetoothDto;
import com.caidaocloud.attendance.service.interfaces.dto.KeywordBasePageDto;
import com.caidaocloud.attendance.service.interfaces.vo.BluetoothVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Bluetooth 蓝牙设置相关
 *
 * <AUTHOR>
 * @date 2021-02-25
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/bluetooth/v1")
@Api(value = "/api/attendance/bluetooth/v1", description = "蓝牙设置接口")
public class BluetoothController {
    @Autowired
    private EquipmentMaintainingService equipmentMaintainingService;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "蓝牙设置列表")
    @PostMapping(value = "/list")
    @Security(code = "bluetoothlist")
    public Result list(@RequestBody KeywordBasePageDto search) throws Exception {
        EquipmentMaintainingParam param = new EquipmentMaintainingParam();
        param.setText_query(search.getKeyworks());
        param.setBelongOrgId(getUserInfo().getTenantId());
        PageBean pageBean = PageUtil.getPageBean(search);
        PageList<Map> data = (PageList<Map>) equipmentMaintainingService.querySysIbeaconInfo(param, pageBean);
        AttendancePageResult pageResult = null;
        if (null != data) {
            List<BluetoothVo> list = new ArrayList<>();
            BluetoothVo bluetoothVo;
            for (Map map : data) {
                bluetoothVo = BeanMapUtils.mapToBean(map, BluetoothVo.class);
                bluetoothVo.setIbeaconId((Integer) map.get("ibeacon_id"));
                bluetoothVo.setMinorName((String) map.get("minor_name"));
                bluetoothVo.setMajorName((String) map.get("major_name"));
                bluetoothVo.setBluetoothName((String) map.get("bluetooth_name"));
                list.add(bluetoothVo);
            }
            pageResult = new AttendancePageResult<>(list, data.getPaginator(), pageBean.getPage(), pageBean.getCount());
        }
        return Result.ok(pageResult);
    }

    @Security(code = "bluetoothsave")
    @ApiOperation(value = "保存/修改蓝牙设置")
    @PostMapping(value = "/save")
    public Result save(@RequestBody BluetoothDto bluetooth) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(bluetooth.getUuid()), ResponseWrap.wrapResult(AttendanceCodes.UUID_CAN_NOT_EMPTY, null).getMsg());
        PreCheck.preCheckArgument(StringUtil.isEmpty(bluetooth.getMajor()), ResponseWrap.wrapResult(AttendanceCodes.MAJOR_CAN_NOT_EMPTY, null).getMsg());
        PreCheck.preCheckArgument(StringUtil.isEmpty(bluetooth.getMajorName()), ResponseWrap.wrapResult(AttendanceCodes.MAJOR_NAME_CAN_NOT_EMPTY, null).getMsg());
        SysIbeaconInfo SysIbeaconInfo = ObjectConverter.convert(bluetooth, SysIbeaconInfo.class);
        SysIbeaconInfo.setBelongOrgId(getUserInfo().getTenantId());
        equipmentMaintainingService.saveIbeaconEquipment(SysIbeaconInfo);
        return Result.ok(true);
    }

    /**
     * 删除蓝牙设置
     */
    @ApiOperation(value = "删除蓝牙设置")
    @DeleteMapping("/delete")
    public Result delete(@RequestParam("ibeaconId") Integer ibeaconId) {
        PreCheck.preCheckArgument(null == ibeaconId, "Illegal parameter");
        SysIbeaconInfo sysIbeaconInfo = new SysIbeaconInfo();
        sysIbeaconInfo.setIbeaconId(ibeaconId);
        sysIbeaconInfo.setBelongOrgId(getUserInfo().getTenantId());
        equipmentMaintainingService.delIbeaconEquipment(sysIbeaconInfo);
        return Result.ok(true);
    }
}
