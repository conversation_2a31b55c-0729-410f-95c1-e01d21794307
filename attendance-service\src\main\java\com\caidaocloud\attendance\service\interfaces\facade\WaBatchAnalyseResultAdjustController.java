package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.exception.MobileException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.BatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchAnalyseResultAdjustService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.BatchAnalyseResultAdjustQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.RevokeBatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.interfaces.vo.BatchAnalyseResultAdjustPageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.WaAbnormalResultVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 批量考勤异常申请
 *
 * <AUTHOR>
 * @Date 2024/6/24
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/analyseResultAdjust/v1")
@Api(value = "/api/attendance/batch/analyseResultAdjust/v1", description = "批量考勤异常申请")
public class WaBatchAnalyseResultAdjustController {
    @Autowired
    private WaBatchAnalyseResultAdjustService waBatchAnalyseResultAdjustService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation(value = "指定日期范围查询员工考勤数据")
    @GetMapping(value = "/getWaResultList")
    public Result<List<WaAbnormalResultVo>> getWaResultList(@RequestParam("empid") Long empid,
                                                            @RequestParam("startDate") Long startDate,
                                                            @RequestParam("endDate") Long endDate) {
        return Result.ok(waBatchAnalyseResultAdjustService.getWaResultList(empid, startDate, endDate));
    }

    @ApiOperation(value = "指定日期查询员工考勤数据")
    @GetMapping(value = "/getWaResult")
    public Result<WaAbnormalResultVo> getWaResult(@RequestParam("empid") Long empid,
                                                  @RequestParam("date") Long date) {
        try {
            return Result.ok(waBatchAnalyseResultAdjustService.getWaResult(empid, date));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 获取考勤数据失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201910", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "检查考勤异常申请数据")
    @PostMapping(value = "/check")
    public Result<Boolean> check(@RequestBody BatchAnalyseResultAdjustDto adjustDto) {
        try {
            waBatchAnalyseResultAdjustService.check(adjustDto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException || e instanceof MobileException) {
                return Result.fail(e.getMessage());
            }
            // 申请失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201911", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "保存考勤异常申请数据")
    @PostMapping(value = "/save")
    public Result<Boolean> save(@RequestBody BatchAnalyseResultAdjustDto adjustDto) {
        try {
            return Result.ok(waBatchAnalyseResultAdjustService.save(adjustDto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 申请失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201911", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "分页列表")
    @PostMapping(value = "/page")
    public Result<AttendancePageResult<BatchAnalyseResultAdjustPageListVo>> getPageList(@RequestBody BatchAnalyseResultAdjustQueryDto dto,
                                                                                        HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.BATCH_ANALYSE_RESULT_ADJUST_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("批量考勤异常申请列表 DataScope = {}", dto.getDataScope());
        PageBean pageBean = PageUtil.getPageBean(dto);
        PageList<BatchAnalyseResultAdjustPageListVo> pageList = waBatchAnalyseResultAdjustService.getPageList(pageBean, dto, UserContext.getAndCheckUser());
        AttendancePageResult<BatchAnalyseResultAdjustPageListVo> pageResult = new AttendancePageResult<>(pageList, pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revoke(@RequestBody RevokeBatchAnalyseResultAdjustDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            waBatchAnalyseResultAdjustService.revoke(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 撤销失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201102", WebUtil.getRequest()));
        }
    }
}
