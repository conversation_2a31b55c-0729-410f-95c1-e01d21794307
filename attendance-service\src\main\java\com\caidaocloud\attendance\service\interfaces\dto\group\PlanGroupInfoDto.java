package com.caidaocloud.attendance.service.interfaces.dto.group;

import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("考勤方案设置-保存基本信息参数DTO")
public class PlanGroupInfoDto {
    @ApiModelProperty("方案id")
    private Integer waGroupId;
    @ApiModelProperty("方案名称")
    private String waGroupName;
    @ApiModelProperty("加班类型id，多个以逗号分隔")
    private Object otTypeIds;
    @ApiModelProperty("加班类型名称，多个以逗号分隔")
    private String otTypeNames;
    @ApiModelProperty("休假类型id，多个以逗号分隔")
    private Object leaveTypeIds;
    @ApiModelProperty("休假类型名称，多个以逗号分隔")
    private String leaveTypeNames;
    @ApiModelProperty("true适用全部员工/false适用部分员工")
    private Boolean isDefault;
    @ApiModelProperty("分析规则id")
    private Integer parseGroupId;
    @ApiModelProperty("考勤周期：上月1，本月2")
    private Integer cyleMonth;
    @ApiModelProperty("考勤周期：起始日")
    private Integer cyleStartdate;
    @ApiModelProperty("适用员工id")
    private List<Long> empIds;
    @ApiModelProperty("超出上限控制是否可申请:true 可申请/ false 不可申请")
    private Boolean outLimitControl = true;
    @ApiModelProperty("请输入加班上限（单位小时）")
    private Integer outLimit;
    @ApiModelProperty("标准时长字段，定义每天工作多长时间，单位为小时")
    private BigDecimal workingTime;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    @ApiModelProperty("自动生成考勤周期开关，默认false(关闭)， true(开启)")
    private Boolean autoPeriod;
    @ApiModelProperty("自动生成周期月份，默认值1，本月，2下月")
    private Integer periodCycleMonth;
    @ApiModelProperty("自动生成周期截止日，默认1号")
    private Integer periodStartDate;

    @ApiModelProperty("员工查看考勤范围:0 全部日期，1本月，2上月")
    private Integer calendarDataRange;
    @ApiModelProperty("当月计薪的休假数据:1审批通过的休假单据，2审批中及审批通过的休假单,默认1")
    private Integer leaveStatus;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nWaGroupName;
    @ApiModelProperty("自定义表达式条件")
    private ConditionTree groupExpCondition;

    public void initWaGroupName() {
        if (StringUtils.isNotBlank(this.waGroupName)) {
            return;
        }
        if (null == this.i18nWaGroupName || this.i18nWaGroupName.isEmpty() || null == this.i18nWaGroupName.get("default")) {
            return;
        }
        this.setWaGroupName(this.i18nWaGroupName.get("default"));
    }

    public void initI18nWaGroupName() {
        if (null != this.i18nWaGroupName && !this.i18nWaGroupName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.waGroupName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.waGroupName);
        this.setI18nWaGroupName(i18nName);
    }
}
