package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class HolidayEntryPageVo implements Serializable {

    @ApiModelProperty("特殊日期日期项id")
    private Integer calendarDetailId;

    @ApiModelProperty("日期类型:1、工作日，2休息日，3法定假日")
    private Integer dateType;

    @ApiModelProperty("日期类型名称")
    private String dateTypeName;

    @ApiModelProperty("特殊日期id")
    private Integer holidayCalendarId;

    @ApiModelProperty("是否适用于门店考勤：0否1是")
    private Boolean isApplyStore;

    @ApiModelProperty("特殊日期")
    private Long calendarDate;

    @ApiModelProperty("替换日期")
    private Long specDate;

    @ApiModelProperty("替换班次名称")
    private  String replaceShiftName;
}
