package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidao1.report.dto.FilterBean;
import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QuotaEmpDto extends ExportBasePage implements Serializable {

    @ApiModelProperty("筛选条件")
    private List<FilterBean> filterList;

}
