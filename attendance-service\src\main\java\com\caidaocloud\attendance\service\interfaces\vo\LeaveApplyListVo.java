package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.interfaces.dto.LeaveCancelPeriod;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LeaveApplyListVo implements Serializable {
    @ApiModelProperty("主键id")
    private Integer waid;

    @ApiModelProperty(value = "工号")
    private String workno;

    @ApiModelProperty("姓名")
    private String empName;

    @ApiModelProperty("任职组织")
    private String shortname;

    @ApiModelProperty("申请时长")
    private Float duration;

    @ApiModelProperty("销假时长")
    private Float cancelTimeDuration;

    @ApiModelProperty("实际时长")
    private Float actualTimeDuration;

    @ApiModelProperty("休假单位")
    private String timeUnitName;

    @ApiModelProperty("休假事由")
    private String reason;

    @ApiModelProperty("申请时间")
    private Long crttime;

    @ApiModelProperty("审批状态")
    private String statusName;

    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;

    @ApiModelProperty("附件名称")
    private String fileName;

    @ApiModelProperty("假期名称")
    private String waName;

    @ApiModelProperty("查看的工作流id")
    private String businessKey;

    @ApiModelProperty("事件时间")
    private String timeSlot;

    @ApiModelProperty("审批状态 0暂存 1审批中 2审批通过 3审批不通过 4作废 5已退回 8撤销中 9已撤销")
    private Integer status;

    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;

    private String fullPath;

    @ApiModelProperty("休假开始时间")
    private String startDate;

    @ApiModelProperty("休假结束时间")
    private String endDate;

    @ApiModelProperty("假期类型编码")
    private String leaveTypeCode;

    @ApiModelProperty("休假状态 1 销假中 2 已完成 3 未销假")
    private Integer leaveStatus;

    @ApiModelProperty("销假时间")
    private List<LeaveCancelPeriod> cancelPeriods;

    @ApiModelProperty("是否允许多次销假")
    private boolean allowManyLeaveCancel;

    private String leaveCancelTime;
}
