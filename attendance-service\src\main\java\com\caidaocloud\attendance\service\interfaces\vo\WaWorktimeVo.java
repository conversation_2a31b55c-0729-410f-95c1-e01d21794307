package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2021/2/18
 */
@Data
public class WaWorktimeVo implements Serializable{
    private static final long serialVersionUID = 4424450260700593072L;

    @ApiModelProperty("工作日历ID")
    private Integer workCalendarId;

    @ApiModelProperty("特殊日期分组ID")
    private Integer calendarGroupId;

    @ApiModelProperty("排班ID")
    private Integer workRoundId;

    @ApiModelProperty("工作日历名称")
    private String workCalendarName;

    @ApiModelProperty("开始日期")
    private Long startdate;

    @ApiModelProperty("结束日期")
    private Long enddate;

    @ApiModelProperty("适用范围 true 适用全部员工 false 适用部分员工")
    private Boolean isDefault;

    @ApiModelProperty("员工ID")
    private String empIdArray;

    @ApiModelProperty("员工姓名")
    private String empNameArray;

    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nWorkCalendarName;
}
