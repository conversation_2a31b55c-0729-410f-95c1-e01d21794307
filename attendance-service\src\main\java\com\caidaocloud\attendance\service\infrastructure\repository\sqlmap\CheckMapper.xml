<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.CheckMapper">

    <select id="checkLeaveRepeat" resultType="Integer">
        SELECT count(m.*)
        FROM (
           SELECT
              el.start_date,
              el.time_slot,
              lt.shift_start_time,
              CASE
              WHEN(lt.period_type = 1 OR lt.period_type = 4)
              THEN lt.shift_end_time + 86399
              ELSE
                  lt.shift_end_time
              END AS shift_end_time
          FROM
          wa_emp_leave el
          JOIN wa_emp_leave_time lt ON el.leave_id = lt.leave_id AND el.status != 3 AND el.status != 4 AND el.status != 9 AND el.status != 5
          WHERE el.empid = #{empid}
        ) m
        WHERE (
            (#{start} <![CDATA[>=]]> m.shift_start_time AND #{start} <![CDATA[<]]> m.shift_end_time)
            OR (#{end} <![CDATA[>]]> m.shift_start_time AND #{end} <![CDATA[<=]]> m.shift_end_time)
            OR (#{start} <![CDATA[<=]]> m.shift_start_time AND #{end} <![CDATA[>=]]> m.shift_end_time)
        )
    </select>

    <select id="checkOtRepeat" resultType="Integer">
        SELECT COUNT (*)
        FROM wa_emp_overtime_detail weod
        JOIN wa_emp_overtime weo on weod.overtime_id = weo.ot_id
        WHERE weo.empid = #{empid}
        AND weo.status in (1, 2, 8)
        AND (
            (#{start} <![CDATA[>=]]> weod.start_time AND #{start} <![CDATA[<]]> weod.end_time)
            OR (#{end} <![CDATA[>]]> weod.start_time AND #{end} <![CDATA[<=]]> weod.end_time)
            OR (#{start} <![CDATA[<=]]> weod.start_time AND #{end} <![CDATA[>=]]> weod.end_time)
        )
    </select>

    <select id="checkOtDateType" resultType="map">
        SELECT ot.overtime_type, ot.compensate_type, ot.min_apply_num, ot.min_unit
        FROM wa_emp_group_view egv
        JOIN wa_group eg ON egv.wa_group_id = eg.wa_group_id
        JOIN wa_overtime_type ot ON ot.overtime_type_id = ANY(eg.ot_type_ids)
        WHERE egv.empid = #{empid} AND ot.date_type = #{dateType}
        <if test="compensateType != null">
            AND compensate_type = #{compensateType}
        </if>
    </select>

    <select id="listEmpOvertimeCompensateTypes" resultType="map">
        select distinct to_char(to_timestamp(weod.start_time), 'yyyy-MM-dd') as "date", compensate_type as "compensateType"
        from wa_emp_overtime_detail weod
        join wa_emp_overtime weo on weod.overtime_id = weo.ot_id
        where weo.status in (1, 2, 8)
        and weo.empid = #{empid}
        and #{startTime} <![CDATA[<=]]> weo.end_time
        and #{endTime} <![CDATA[>=]]> weo.start_time
        <if test="compensateTypeIsNotEqualTo != null">
            and weo.compensate_type <![CDATA[<>]]> #{compensateTypeIsNotEqualTo}
        </if>
    </select>
</mapper>
