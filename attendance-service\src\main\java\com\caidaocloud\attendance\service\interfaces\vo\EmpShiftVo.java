package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/3/5
 */
@Data
public class EmpShiftVo {
    @ApiModelProperty("员工排班ID")
    private Integer empShiftId;

    @ApiModelProperty("员工ID")
    private Long empid;

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("姓名")
    private String empName;

    @ApiModelProperty("所属公司")
    private String corpName;

    @ApiModelProperty("所属部门")
    private String orgName;

    @ApiModelProperty("工作日历ID")
    private Integer workCalendarId;

    @ApiModelProperty("工作日历")
    private String workCalendarName;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;
}
