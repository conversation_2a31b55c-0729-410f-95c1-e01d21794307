<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaOvertimeTransferRuleMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo">
        <id column="rule_id" jdbcType="BIGINT" property="ruleId" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
        <result column="compensate_type" jdbcType="INTEGER" property="compensateType" />
        <result column="leave_type_id" jdbcType="INTEGER" property="leaveTypeId" />
        <result column="transfer_rule" jdbcType="INTEGER" property="transferRule" />
        <result column="transfer_periods" jdbcType="OTHER" property="transferPeriods" />
        <result column="transfer_time" jdbcType="REAL" property="transferTime" />
        <result column="note" jdbcType="VARCHAR" property="note" />
        <result column="deleted" jdbcType="INTEGER" property="deleted" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_time" jdbcType="BIGINT" property="createTime" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_time" jdbcType="BIGINT" property="updateTime" />
        <result column="time_duration" jdbcType="REAL" property="timeDuration" />
    </resultMap>
    <sql id="Base_Column_List">
        rule_id, tenant_id, rule_name, compensate_type, leave_type_id, transfer_rule, transfer_periods,
    transfer_time, note, deleted, create_by, create_time, update_by, update_time,time_duration
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wa_overtime_transfer_rule
        where rule_id = #{ruleId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from wa_overtime_transfer_rule
        where rule_id = #{ruleId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo">
        insert into wa_overtime_transfer_rule (rule_id, tenant_id, rule_name,
                                               compensate_type, leave_type_id, transfer_rule,
                                               transfer_periods, transfer_time, note,
                                               deleted, create_by, create_time,
                                               update_by, update_time, time_duration)
        values (#{ruleId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR},
                #{compensateType,jdbcType=INTEGER}, #{leaveTypeId,jdbcType=INTEGER}, #{transferRule,jdbcType=INTEGER},
                #{transferPeriods,jdbcType=OTHER,typeHandler=com.caidao1.commons.mybatis.handler.JsonTypeHandler},
                #{transferTime,jdbcType=REAL}, #{note,jdbcType=VARCHAR},
                #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
                #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{timeDuration,jdbcType=REAL})
    </insert>
    <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo">
        insert into wa_overtime_transfer_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="ruleName != null">
                rule_name,
            </if>
            <if test="compensateType != null">
                compensate_type,
            </if>
            <if test="leaveTypeId != null">
                leave_type_id,
            </if>
            <if test="transferRule != null">
                transfer_rule,
            </if>
            <if test="transferPeriods != null">
                transfer_periods,
            </if>
            <if test="transferTime != null">
                transfer_time,
            </if>
            <if test="note != null">
                note,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="timeDuration != null">
                time_duration,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null">
                #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="compensateType != null">
                #{compensateType,jdbcType=INTEGER},
            </if>
            <if test="leaveTypeId != null">
                #{leaveTypeId,jdbcType=INTEGER},
            </if>
            <if test="transferRule != null">
                #{transferRule,jdbcType=INTEGER},
            </if>
            <if test="transferPeriods != null">
                #{transferPeriods,jdbcType=OTHER,typeHandler=com.caidao1.commons.mybatis.handler.JsonTypeHandler},
            </if>
            <if test="transferTime != null">
                #{transferTime,jdbcType=REAL},
            </if>
            <if test="note != null">
                #{note,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="timeDuration != null">
                #{timeDuration,jdbcType=REAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo">
        update wa_overtime_transfer_rule
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null">
                rule_name = #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="compensateType != null">
                compensate_type = #{compensateType,jdbcType=INTEGER},
            </if>
            <if test="leaveTypeId != null">
                leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
            </if>
            <if test="transferRule != null">
                transfer_rule = #{transferRule,jdbcType=INTEGER},
            </if>
            <if test="transferPeriods != null">
                transfer_periods = #{transferPeriods,jdbcType=OTHER,typeHandler=com.caidao1.commons.mybatis.handler.JsonTypeHandler},
            </if>
            <if test="transferTime != null">
                transfer_time = #{transferTime,jdbcType=REAL},
            </if>
            <if test="note != null">
                note = #{note,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="timeDuration != null">
                time_duration = #{timeDuration,jdbcType=REAL},
            </if>
        </set>
        where rule_id = #{ruleId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo">
        update wa_overtime_transfer_rule
        set tenant_id = #{tenantId,jdbcType=VARCHAR},
            rule_name = #{ruleName,jdbcType=VARCHAR},
            compensate_type = #{compensateType,jdbcType=INTEGER},
            leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
            transfer_rule = #{transferRule,jdbcType=INTEGER},
            transfer_periods = #{transferPeriods,jdbcType=OTHER,typeHandler=com.caidao1.commons.mybatis.handler.JsonTypeHandler},
            transfer_time = #{transferTime,jdbcType=REAL},
            note = #{note,jdbcType=VARCHAR},
            deleted = #{deleted,jdbcType=INTEGER},
            create_by = #{createBy,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=BIGINT},
            update_by = #{updateBy,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            time_duration = #{timeDuration,jdbcType=REAL}
        where rule_id = #{ruleId,jdbcType=BIGINT}
    </update>
</mapper>