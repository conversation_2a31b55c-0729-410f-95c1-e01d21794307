package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.ioc.dto.CheckMessage;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.TravelTypeDefEnum;
import com.caidaocloud.attendance.service.application.service.IEmpTravelService;
import com.caidaocloud.attendance.service.application.service.ITravelTypeService;
import com.caidaocloud.attendance.service.application.service.impl.ImportEmpTravelService;
import com.caidaocloud.attendance.service.application.service.impl.WfService;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.travel.*;
import com.caidaocloud.attendance.service.interfaces.vo.EmpTravelVo;
import com.caidaocloud.attendance.service.interfaces.vo.TravelTypeVo;
import com.caidaocloud.attendance.service.interfaces.vo.WfDetailVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 出差申请接口
 *
 * <AUTHOR>
 * @Date 2021/9/8
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/emptravel/v1")
@Api(value = "/api/attendance/emptravel/v1", description = "出差申请")
public class EmpTravelController {
    @Autowired
    private IEmpTravelService empTravelService;
    @Autowired
    private WfService wfService;
    @Resource
    private ITravelTypeService travelTypeService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private ImportEmpTravelService importEmpTravelService;
    @Autowired
    private RedisTemplate redisTemplate;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "获取出差记录分页列表")
    @PostMapping("/list")
    public Result<PageResult<EmpTravelVo>> getTravelPageList(@RequestBody EmpTravelReqDto dto, HttpServletRequest request) {
        UserInfo userInfo = this.getUserInfo();
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        PageResult<EmpTravelDto> pageResult = empTravelService.getEmpTravelPageList(dto, userInfo);
        List<EmpTravelVo> voList = ObjectConverter.convertList(pageResult.getItems(), EmpTravelVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation(value = "查看明细")
    @GetMapping(value = "/getWfFuncDetail")
    @LogRecordAnnotation(success = "查看了明细", category = "查看", menu = "出差管理-出差规则")
    public Result<WfDetailVo> getWfFuncDetail(@RequestParam("businessKey") String businessKey,
                                              @RequestParam(value = "nodeId", required = false) String nodeId,
                                              @RequestParam(value = "funcType", required = false) Integer funcType) throws Exception {
        WfDetailDto detailDto = wfService.getWfFuncDetail(businessKey, nodeId, funcType);
        WfDetailVo vo = ObjectConverter.convert(detailDto, WfDetailVo.class);
        vo.setData(detailDto.getItems());

        return ResponseWrap.wrapResult(vo);
    }

    @PostMapping(value = "/revokeEmpTravel")
    @ApiOperation(value = "撤销员工出差单据")
    @LogRecordAnnotation(success = "撤销了{empName{#empId}}", category = "撤销", menu = "出差管理-出差规则")
    public Result revokeEmpTravel(@RequestBody RevokeEmpTraveDto dto) {
        if (StringUtils.isEmpty(dto.getRecokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_REVOKE_REASON_MUST, null);
        }
        if (dto.getRecokeReason().length() >= 100) {
            return Result.fail(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_REVOKE_REASON_NUM_LIMIT, null).getMsg());
        }
        UserInfo userInfo = this.getUserInfo();
        try {
            return empTravelService.revokeEmpTravel(dto, userInfo);
        } catch (Exception e) {
            log.error("EmpTravelController.revokeEmpTravel executes exception, {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, "撤销失败");
        }
    }

    @ApiOperation(value = "查询出差类型列表")
    @GetMapping(value = "/getTravelTypes")
    public Result<List<?>> getTravelTypes(@RequestParam(value = "dataType", required = false) String dataType) {
        TravelTypeListReqDto dto = new TravelTypeListReqDto();
        dto.setPageNo(1);
        dto.setPageSize(10000);
        PageResult<WaTravelTypeDo> pageResult = travelTypeService.getTravelTypePageResult(dto);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return Result.ok(new ArrayList<>());
        }
        pageResult.getItems().forEach(item -> {
            item.setTravelTypeName(LangParseUtil.getI18nLanguage(item.getI18nTravelTypeName(), item.getTravelTypeName()));
        });
        if (!"data".equals(dataType)) {
            List<WaTravelTypeDo> dataList = pageResult.getItems();
            List<TravelTypeVo> items = new ArrayList<>();
            for (WaTravelTypeDo item : dataList) {
                TravelTypeVo travelTypeVo = ObjectConverter.convert(item, TravelTypeVo.class);
                travelTypeVo.setTravelTypeName(LangParseUtil.getI18nLanguage(item.getI18nTravelTypeName(), item.getTravelTypeName()));
                travelTypeVo.setIfAbroad(TravelTypeDefEnum.ABROAD.getIndex().equals(item.getTravelTypeDef()));
                items.add(travelTypeVo);
            }
            return Result.ok(items);
        } else {
            List<Map> list = new ArrayList<>();
            pageResult.getItems().forEach(i -> {
                Map<String, Object> newMap = new HashMap<>();
                String i18n = LangParseUtil.getI18nLanguage(i.getI18nTravelTypeName(), i.getTravelTypeName());
                newMap.put("code", i18n);
                newMap.put("text", i18n);
                newMap.put("value", i.getTravelTypeId());
                newMap.put("ifAbroad", TravelTypeDefEnum.ABROAD.getIndex().equals(i.getTravelTypeDef()));
                list.add(newMap);
            });
            return Result.ok(list);
        }
    }

    @ApiOperation(value = "获取出差时长")
    @PostMapping(value = "/getTravelTime")
    public Result getTravelTime(@RequestBody EmpTravelSaveDto dto) {
        dto.setOpt(1);
        Result checkResult = checkLeaveApplyParams(dto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        UserInfo userInfo = this.getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_USER_TRAVEL_APPLY_LOCK_{0}_{1}", userInfo.getTenantId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return empTravelService.checkOrSaveTravelTime(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "保存出差申请单")
    @PostMapping(value = "/saveTravelTime")
    @LogRecordAnnotation(success = "申请了{empName{#empId}}的{{#name}}", category = "申请出差", menu = "出差管理-出差规则")
    public Result saveTravelTime(@RequestBody EmpTravelSaveDto dto) {
        dto.setOpt(2);
        Result checkResult = checkLeaveApplyParams(dto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        UserInfo userInfo = this.getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_USER_TRAVEL_APPLY_LOCK_{0}_{1}", userInfo.getTenantId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            Result result = empTravelService.checkOrSaveTravelTime(dto);
            LogRecordContext.putVariable("empId", dto.getEmpId());

            return result;
        } catch (Exception e) {
            log.error("Employee travel application has exception:{}", e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "门户-获取出差时长")
    @PostMapping(value = "/getTravelTimeByUser")
    public Result getTravelTimeByUser(@RequestBody EmpTravelSaveDto dto) {
        dto.setOpt(1);
        UserInfo userInfo = this.getUserInfo();
        dto.setEmpId(userInfo.getStaffId());
        Result checkResult = checkLeaveApplyParams(dto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_USER_TRAVEL_APPLY_LOCK_{0}_{1}", userInfo.getTenantId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return empTravelService.checkOrSaveTravelTime(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "门户-保存出差申请单")
    @PostMapping(value = "/saveTravelTimeByUser")
    public Result saveTravelTimeByUser(@RequestBody EmpTravelSaveDto dto) {
        dto.setOpt(2);
        UserInfo userInfo = this.getUserInfo();
        dto.setEmpId(userInfo.getStaffId());
        Result checkResult = checkLeaveApplyParams(dto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_USER_TRAVEL_APPLY_LOCK_{0}_{1}", userInfo.getTenantId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return empTravelService.checkOrSaveTravelTime(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    private Result checkLeaveApplyParams(EmpTravelSaveDto dto) {
        if (dto.getEmpId() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_EMPLOYEE_SELECTED, Boolean.FALSE);
        }
        if (dto.getStartTime() == null || dto.getEndTime() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_SELECT_TRAVEL_TIME, Boolean.FALSE);
        }
        if (dto.getStartTime().equals(dto.getEndTime()) && "P".equals(dto.getShalfDay()) && "A".equals(dto.getEhalfDay())) {
            return ResponseWrap.wrapResult(AttendanceCodes.STARTTIME_CANNOTBE_GE_ENDTIME, null);
        }
        if (dto.getStartTime() > dto.getEndTime()) {
            return ResponseWrap.wrapResult(AttendanceCodes.STARTTIME_CANNOTBE_GE_ENDTIME, Boolean.FALSE);
        }
        if (dto.getShowMin() == 1 && dto.getStartTime().equals(dto.getEndTime())
                && dto.getStime().equals(dto.getEtime())) {
            return ResponseWrap.wrapResult(AttendanceCodes.STARTTIME_CANNOTBE_EQ_ENDTIME, Boolean.FALSE);
        }
        return Result.ok(true);
    }

    @ApiOperation("导入出差记录")
    @PostMapping(value = "/import")
    @LogRecordAnnotation(success = "导入了出差记录", category = "导入", menu = "出差管理-出差记录-出差记录")
    public Result<String> importEmpTravel(MultipartFile file, String progress) {
        LinkedList<CheckMessage> checkMessageList = new LinkedList<>();
        try {
            if (null == file) {
                checkMessageList.add(new CheckMessage(0, -1, ResponseWrap.wrapResult(AttendanceCodes.IMPORT_TEMPLATE_FILE, null).getMsg()));
            } else {
                importEmpTravelService.importEmpTravel(checkMessageList, file, sessionService.getUserInfo());
            }
        } catch (Exception e) {
            log.error("importEmpTravel has exception {}", e.getMessage(), e);
            checkMessageList.add(new CheckMessage(0, -1, "导入出差记录失败"));
        } finally {
            log.info("importTravel progress:{} checkMsgList:{}", progress, FastjsonUtil.toJson(checkMessageList));
            importEmpTravelService.saveErrorCache(checkMessageList, progress);
            Map map = new HashMap<>(1);
            map.put("progress", 1);
            redisTemplate.opsForValue().set("importDataList_" + progress, map);
            redisTemplate.expire("importDataList_" + progress, 720, TimeUnit.SECONDS);
        }
        return Result.ok("success");
    }
}
