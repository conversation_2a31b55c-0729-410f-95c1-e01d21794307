package com.caidaocloud.attendance.service.interfaces.dto.common;

import com.caidaocloud.dto.KeyValue;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class WaKeyValue extends KeyValue {
    private String key;

    public WaKeyValue(String text, Object value) {
        super(text, value);
    }

    public WaKeyValue(String text, Object value, String key) {
        super(text, value);
        this.key = key;
    }
}
