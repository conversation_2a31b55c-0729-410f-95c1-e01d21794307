package com.caidaocloud.attendance.service.interfaces.dto.group;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("考勤方案设置-保存加班规则参数DTO")
public class GroupBaseInfoDto {
    @ApiModelProperty("方案id")
    private Integer waGroupId;
    @ApiModelProperty("超出上限控制是否可申请:true 可申请/ false 不可申请")
    private Boolean outLimitControl = true;
    @ApiModelProperty("请输入加班上限（单位小时）")
    private Integer outLimit;

    @ApiModelProperty("加班原因必填开关：true/false")
    private Boolean reasonMust;
    @ApiModelProperty("同一天可申请多条加班单：true/false")
    private Boolean overtimeControl;
    @ApiModelProperty("加班时效性控制")
    private Boolean isOpenTimeControl;
    @ApiModelProperty("加班时效性类型 0:提前 1:延后")
    private Integer timeControlType;
    @ApiModelProperty("加班时效性时长")
    private Float controlTimeDuration;
    @ApiModelProperty("加班时效性单位 1:天 2:小时")
    private Integer controlTimeUnit;
    @ApiModelProperty("跨夜加班归属：1归属至加班开始日期 2根据零点拆分，默认值1")
    private Integer overtimeBelong;
    @ApiModelProperty("同一天是否可申请多种加班类型:true/false,默认true")
    private Boolean overtimeTypeControl;
}
