package com.caidaocloud.attendance.service.application.enums;

public enum LeaveExemptionTypeEnum {

    NON_LEAVE(1, "正常上/下班享有"),
    DAY_LEAVE(2, "休半天假时享有"),
    HOUR_LEAVE(3, "休小时假时享有");
    private Integer index;
    private String desc;

    LeaveExemptionTypeEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
