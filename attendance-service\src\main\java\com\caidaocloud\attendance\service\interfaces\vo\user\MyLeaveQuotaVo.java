package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyLeaveQuotaVo {

    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;

    @ApiModelProperty("假期名称")
    private String leaveName;

    @ApiModelProperty("假期类型 1:年假 2:固额 3:调休")
    private Integer quotaType;

    @ApiModelProperty("配额id")
    private long empQuotaId;

    @ApiModelProperty("总配额")
    private Float TotalDay;

    @ApiModelProperty("在途配额")
    private Float inTransitQuota;

    @ApiModelProperty("已使用")
    private Float usedDay;

    @ApiModelProperty("剩余")
    private Float leftDay;

    @ApiModelProperty("单位")
    private Integer unit;

    @ApiModelProperty("单位文本")
    private String unitTxt;
}
