package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.NotifyConfigItemDto;
import com.caidaocloud.attendance.service.application.service.INotifyConfigService;
import com.caidaocloud.attendance.service.interfaces.dto.NotifyConfigDto;
import com.caidaocloud.attendance.service.interfaces.vo.NotifyConfigItemVo;
import com.caidaocloud.attendance.service.interfaces.vo.NotifyConfigVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Jiang
 * @date 2021/09/16
 */
@Slf4j
@RestController
@Api(value = "/api/attendance/notifyconfig/v1", description = "消息推送配置接口")
@RequestMapping("/api/attendance/notifyconfig/v1")
public class NotifyConfigController {

    @Resource
    private INotifyConfigService notifyConfigService;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "查询消息配置推送规则")
    @GetMapping(value = "/detail")
    public Result<NotifyConfigVo> getNotifyConfig() {
        try {
            UserInfo userInfo = getUserInfo();
            NotifyConfigDto configDto = notifyConfigService.getNotifyConfig(userInfo.getTenantId());
            NotifyConfigVo configVo = ObjectConverter.convert(configDto, NotifyConfigVo.class);
            return Result.ok(configVo);
        } catch (Exception e) {
            log.error("NotifyConfigController.getNotifyConfig has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, null);
        }
    }

    @ApiOperation(value = "保存消息推送规则")
    @PostMapping(value = "/save")
    public Result<Boolean> saveNotifyConfig(@RequestBody NotifyConfigDto dto) {
        try {
            UserInfo userInfo = getUserInfo();
            notifyConfigService.saveNotifyConfig(dto, userInfo);
            return Result.ok(CommonConstant.TRUE);
        } catch (Exception e) {
            log.error("NotifyConfigController.saveNotifyConfig has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "消息列表")
    @PostMapping(value = "/list")
    public Result<List<NotifyConfigItemVo>> getNotifyConfigs() {
        try {
            UserInfo userInfo = getUserInfo();
            String tenantId = userInfo.getTenantId();
            List<NotifyConfigItemDto> list = notifyConfigService.getNotifyConfigs(tenantId);
            return Result.ok(ObjectConverter.convertList(list, NotifyConfigItemVo.class));
        } catch (Exception e) {
            log.error("Get list has exception :{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new ArrayList<>());
        }
    }

    @ApiOperation(value = "启用")
    @GetMapping(value = "/enable")
    public Result<Boolean> enable(@RequestParam("num") Integer num) {
        try {
            UserInfo userInfo = getUserInfo();
            String result = notifyConfigService.updateNotifyConfigStatus(num, 1, userInfo);
            if (StringUtil.isNotBlank(result)) {
                return Result.fail(result);
            }
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Enable notify config has exception :{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ENABLE_FAIL, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "停用")
    @GetMapping(value = "/disable")
    public Result<Boolean> disable(@RequestParam("num") Integer num) {
        try {
            UserInfo userInfo = getUserInfo();
            String result = notifyConfigService.updateNotifyConfigStatus(num, 0, userInfo);
            if (StringUtil.isNotBlank(result)) {
                return Result.fail(result);
            }
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Disable notify config has exception :{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_UNENABLE_FAIL, Boolean.FALSE);
        }
    }
}
