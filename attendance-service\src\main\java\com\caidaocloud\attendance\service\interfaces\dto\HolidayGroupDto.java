package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class HolidayGroupDto implements Serializable {

    @ApiModelProperty("分组id")
    private Integer calendarGroupId;
    @ApiModelProperty("分组名")
    private String groupName;
    @ApiModelProperty("特殊日期id")
    private List<Integer> holidayCalendarIds;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nGroupName;

    public void initGroupName() {
        if (StringUtils.isNotBlank(this.groupName)) {
            return;
        }
        if (null == this.i18nGroupName || this.i18nGroupName.isEmpty() || null == this.i18nGroupName.get("default")) {
            return;
        }
        this.setGroupName(this.i18nGroupName.get("default"));
    }

    public void initI18nGroupName() {
        if (null != this.i18nGroupName && !this.i18nGroupName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.groupName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.groupName);
        this.setI18nGroupName(i18nName);
    }
}
