package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyCalendarDateVo {
    @ApiModelProperty("日期类型")
    private Integer dateType;
    @ApiModelProperty("日期")
    private String workDate;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("当日考勤分析结果 1 考勤异常 2 考勤正常 3存在加班记录 4存在休假记录 5存在出差记录")
    private Integer waResult;
}
