package com.caidaocloud.attendance.service.interfaces.vo.overtime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OvertimeTransferRuleVo {
    @ApiModelProperty("规则ID")
    private Long ruleId;
    @ApiModelProperty("规则名称，不可重复")
    private String ruleName;
    @ApiModelProperty(value = "补偿方式：0不补偿，1付现，2调休 3 已预付 4 自由选择", allowableValues = "0,1,2,3,4,")
    private Integer compensateType;
    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
    @ApiModelProperty("转换规则:1按比例转换2阶梯规则（天）3阶梯规则（小时）4、其他规则")
    private Integer transferRule;
    @ApiModelProperty("阶梯转换时间段：[{\"start\": 1,\"end\": 5,\"unit\": \"1 小时/ 2 天\",\"duration\": 4\"}]")
    private Object transferPeriods;
    @ApiModelProperty("转换时间")
    private Float transferTime;
    @ApiModelProperty("备注")
    private String note;
    @ApiModelProperty("加班时长")
    private Float timeDuration;
}
