package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DayAnalyseAbnormalDto implements Serializable {

    @ApiModelProperty("考勤分析主键")
    private Integer analyzeId;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("任职组织")
    private String orgName;
    @ApiModelProperty("任职组织全路径")
    private String fullPath;
    @ApiModelProperty("考勤状态")
    private Integer analyzeResult;
    @ApiModelProperty("考勤日期")
    private Long belongDate;
    @ApiModelProperty("考勤日期：周一")
    private String belongDateWeek;
    @ApiModelProperty("员工类型")
    private String employTypeName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工类型名称")
    private String empStatusName;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("出勤规则")
    private String clockType;
    @ApiModelProperty("休假类型")
    private String leaveName;
    @ApiModelProperty("申请日期")
    private Long applyDate;
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;
}
