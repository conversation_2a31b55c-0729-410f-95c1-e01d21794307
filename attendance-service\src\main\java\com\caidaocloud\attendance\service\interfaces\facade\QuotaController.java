package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidao1.ioc.util.GridUtil;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaLeaveSettingMapper;
import com.caidao1.wa.mybatis.mapper.WaSobMapper;
import com.caidao1.wa.mybatis.model.WaEmpQuota;
import com.caidao1.wa.mybatis.model.WaLeaveSetting;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.QuotaGenDto;
import com.caidaocloud.attendance.service.application.dto.ReDecEmpLeaveQuotaDto;
import com.caidaocloud.attendance.service.application.enums.DataSourceEnum;
import com.caidaocloud.attendance.service.application.service.IQuotaService;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveSettingDo;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.SelectListMapper;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.FixQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.quota.*;
import com.caidaocloud.attendance.service.interfaces.vo.EmpCompensatoryQuotaVo;
import com.caidaocloud.attendance.service.interfaces.vo.EmpFixQuotaVo;
import com.caidaocloud.attendance.service.interfaces.vo.KeyValueIntegerVo;
import com.caidaocloud.attendance.service.interfaces.vo.KeyValueVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.QuotaAdjustPageVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.QuotaPageVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.QuotaVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.*;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/attendance/quota/v1")
@Api(value = "/api/attendance/quota/v1", description = "假期配额")
public class QuotaController {

    @Resource
    private WaAttendanceConfigService waConfigService;
    @Resource
    private ISessionService sessionService;
    @Resource
    private SelectListMapper selectListMapper;
    @Resource
    private WaLeaveSettingMapper waLeaveSettingMapper;
    @Resource
    private IQuotaService quotaService;
    @Resource
    private WaLeaveSettingDo waLeaveSettingDo;
    @Autowired
    private AsyncExecService asyncService;
    @Autowired
    private CacheService cacheService;

    private static final String ASYN_CARRY_FORWARD_QUOTA_PROCESS = "ASYN_CARRY_FORWARD_QUOTA_PROCESS_";

    private static final String ASYN_CARRY_FORWARD_QUOTA_MSG_PROCESS = "ASYN_CARRY_FORWARD_QUOTA_MSG_PROCESS_";

    private static final String GEN_EMPQUOTA_PROCESS = "GEN_EMPQUOTA_PROCESS_";

    private static final String GEN_EMPQUOTA_MSG_PROCESS = "GEN_EMPQUOTA_MSG_PROCESS_";

    private static final String GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS = "GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS_";

    private static final String GEN_EMP_COMPENSATORY_LEAVEQUOTA_MSG_PROCESS = "GEN_EMP_COMPENSATORY_LEAVEQUOTA_MSG_PROCESS_";

    private static final String ASYNC_INITIALIZATION_FIXED_QUOTA_PROCESS = "ASYNC_INITIALIZATION_FIXED_QUOTA_PROCESS_";

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("新增或修改假期配额")
    @PostMapping(value = "/save")
    public Result<Boolean> saveEmpQuota(@RequestBody QuotaDto dto) {
        if (null == dto.getEmpid()) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_SELECTED_EMP, Boolean.FALSE);
        }
        if (null == dto.getPeriodYear()) {
            return ResponseWrap.wrapResult(AttendanceCodes.PERIOD_YEAR_EMPTY, Boolean.FALSE);
        }
        if (null == dto.getQuotaSettingId()) {
            return ResponseWrap.wrapResult(AttendanceCodes.BALANCE_TYPE_EMPTY, Boolean.FALSE);
        }
        if (null == dto.getStartDate() || null == dto.getLastDate()) {
            return ResponseWrap.wrapResult(AttendanceCodes.VALID_START_DATE_EMPTY, Boolean.FALSE);
        }
        if (null == dto.getQuotaDay()) {
            return ResponseWrap.wrapResult(AttendanceCodes.CURRENT_YEAR_QUOTA_EMPTY, Boolean.FALSE);
        }
        if (null == dto.getRemainValidDate()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LAST_YEAR_CARRY_TO_VALID_DATE_EMPTY, Boolean.FALSE);
        }
        int count = quotaService.getEmpQuotaCountByYearAndType(dto);
        if (count >= 1) {
            WaLeaveSettingDo leaveSetting = waLeaveSettingDo.getLeaveSettingById(dto.getQuotaSettingId());
            if (leaveSetting == null) {
                return ResponseWrap.wrapResult(AttendanceCodes.SELECTED_QUOTA_TYPE_NOT_EXIST, Boolean.FALSE);
            }
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_ONE_YEAR_ONE_QUOTA_EXISTED, Boolean.FALSE).getMsg(), dto.getPeriodYear() ,leaveSetting.getQuotaSettingName()));
        }
        WaEmpQuota record = ObjectConverter.convert(dto, WaEmpQuota.class);
        UserInfo userInfo = getUserInfo();
        waConfigService.saveEmpQuota(userInfo.getTenantId(), record);
        if (dto.getEmpQuotaId() == null) {
            Integer maxQuotaSortNo = selectListMapper.selectMaxQuotaSortNo(userInfo.getTenantId());
            if (maxQuotaSortNo == null) {
                maxQuotaSortNo = 0;
            }
            WaLeaveSetting waLeaveSetting = new WaLeaveSetting();
            waLeaveSetting.setQuotaSettingId(record.getQuotaSettingId());
            waLeaveSetting.setQuotaSortNo(maxQuotaSortNo + 1);
            waLeaveSettingMapper.updateByPrimaryKeySelective(waLeaveSetting);
        }
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation("假期配额分页列表")
    @PostMapping("/list")
    @Security(code = "QuotaList")
    public Result<AttendancePageResult<QuotaPageVo>> getEmpQuotaList(@RequestBody QuotaPageDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.QUOTA_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("假期配额分页列表 dataScope = {}", dataScope);
        //由于年份查询放到了更多筛选里面，因此需要从更多筛选条件中取出来作为单独参数
        List<FilterBean> filterList = dto.getFilterList();
        if (CollectionUtils.isNotEmpty(filterList)) {
            Optional<String> yearFilters = filterList.stream().filter(st -> "period_year".equals(st.getField()) && StringUtils.isNotEmpty(st.getMin())).map(FilterBean::getMin).findFirst();
            if (yearFilters.isPresent()) {
                dto.setYears(Arrays.stream(yearFilters.get().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                filterList.removeIf(st -> "period_year".equals(st.getField()) && StringUtils.isNotEmpty(st.getMin()));
            }
        }
        PageBean pageBean = PageUtil.getPageBean(dto);
        pageBean.setFilterList(filterList);
        UserInfo userInfo = getUserInfo();
        List<Map> empQuotaList = quotaService.getQuotaList(pageBean, dto.getYear(), dto.getQuotaSettingId(), userInfo.getTenantId(), dto.getYears(), dto.getDataScope());
        if (CollectionUtils.isEmpty(empQuotaList)) {
            return ResponseWrap.wrapResult(new AttendancePageResult<>());
        }
        PageList<Map> pageList = (PageList<Map>) empQuotaList;
        List<QuotaPageVo> items = JSON.parseArray(JSON.toJSONString(pageList), QuotaPageVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
    }

    @ApiOperation("取得假期配额信息")
    @GetMapping("/detail")
    public Result<QuotaVo> getEmpQuota(@RequestParam("id") Integer id) {
        WaEmpQuota quota = waConfigService.getEmpQuota(id);
        if (null == quota) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, null);
        }
        return ResponseWrap.wrapResult(ObjectConverter.convert(quota, QuotaVo.class));
    }

    @ApiOperation(value = "删除假期配额", tags = "v1.1")
    @DeleteMapping("/delete")
    public Result<Boolean> deleteEmpQuota(@RequestParam("id") Integer id) {
        UserInfo userInfo = getUserInfo();
        waConfigService.deleteEmpQuota(userInfo.getTenantId(), id);
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @PostMapping("/genQuota")
    @Deprecated
    public Result<Boolean> genEmpQuota(@RequestBody GenQuotaDto dto) throws Exception {
        UserInfo userInfo = getUserInfo();
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        if (dto.getYear() != null && dto.getYear() == 2) {
            //次年
            year = year + 1;
        }
        //重复生成校验
        String lockKey = MessageFormat.format("GENEMPQUOTA_LOCK_{0}_{1}_{2}", userInfo.getBelongOrgId(), dto.getGroupId(), year);
        if (cacheService.containsKey(lockKey)) {
            //return Result.fail("另一个配额生成进程正在运行中，请稍候再尝试!");
            return ResponseWrap.wrapResult(AttendanceCodes.ANOTHER_QUOTA_GEN_PROCESS_RUNNING, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.QUOTA_GEN, "ei");
            log.info("QuotaService.genEmpQuota dataScope = {}", dataScope);
            quotaService.genEmpQuota(userInfo.getTenantId(), dto.getGroupId(), dto.getEmpId(),
                    (short) year, !dto.isOnlyNotGen(), dto.isCarryForward(), dto.isReCalQuota(), userInfo.getUserId(), dataScope);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @PostMapping("/asynGenEmpQuota")
    @Security(code = "QuotaGen")
    @ApiOperation(value = "异步生成假期配额", tags = "v1.1")
    @LogRecordAnnotation(success = "计算了额度", category = "计算额度", menu = "休假管理-假期余额-年度假期明细")
    public Result<Boolean> asynGenEmpQuota(@RequestBody GenQuotaDto dto) {
        UserInfo userInfo = getUserInfo();
        //重复生成校验
        String lockKey = MessageFormat.format("GENEMPQUOTA_LOCK_{0}_{1}_{2}", userInfo.getBelongOrgId(), dto.getGroupId(), dto.getYear());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.ANOTHER_QUOTA_GEN_PROCESS_RUNNING, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            cacheService.cacheValue(GEN_EMPQUOTA_PROCESS + dto.getProgress(), "0.5");
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.QUOTA_GEN, "ei");
            log.info("QuotaService.genEmpQuota dataScope = {}", dataScope);
            String finalDataScope = dataScope;
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        log.info("asynGenEmpQuota params ... " + JSONUtils.ObjectToJson(dto) + ",user info = " + JSONUtils.ObjectToJson(userInfo));
                        QuotaGenDto genDto = new QuotaGenDto();
                        genDto.setAll(!dto.isOnlyNotGen());
                        genDto.setBelongId(userInfo.getTenantId());
                        genDto.setEmpid(dto.getEmpId());
                        genDto.setUserId(userInfo.getUserId());
                        genDto.setWaGroupId(dto.getGroupId());
                        genDto.setYear(dto.getYear());
                        genDto.setDataScope(finalDataScope);
                        genDto.setGenQuotaMode(dto.getGenQuotaMode());
                        genDto.setCorpId(ConvertHelper.longConvert(userInfo.getTenantId()));
                        quotaService.genEmpQuotaForIssuedAnnually(genDto);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_GEN_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }
                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(GEN_EMPQUOTA_PROCESS + dto.getProgress(), "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(GEN_EMPQUOTA_MSG_PROCESS + dto.getProgress(), JsonSerializeHelper.serialize(engineMessage), 5 * 60);
                    }
                }
            });

            return Result.ok(true);
        } catch (Exception ex) {
            log.error("QuotaController.asynGenEmpQuota executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Gen emp quota has exception", Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @PostMapping("/asynCarryForwardQuota")
    @Security(code = "AsynCarryForwardQuota")
    @ApiOperation(value = "异步结转假期配额", tags = "v1.1")
    public Result<Boolean> asynCarryForwardQuota(@RequestBody QuotaCarryForwardDto dto) {
        UserInfo userInfo = getUserInfo();
        //重复生成校验
        String lockKey = MessageFormat.format("ASYN_CARRY_FORWARD_QUOTA_LOCK_{0}_{1}_{2}", userInfo.getBelongOrgId(), dto.getWaGroupId(), dto.getYear());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.ANOTHER_QUOTA_CARRY_TO_PROCESS_RUNNING, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            cacheService.cacheValue(ASYN_CARRY_FORWARD_QUOTA_PROCESS + dto.getProgress(), "0.5");
            String dataScope = "";
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        log.info("asynCarryForwardQuota params ... " + JSONUtils.ObjectToJson(dto) + ",user info = " + JSONUtils.ObjectToJson(userInfo));
                        quotaService.carryForwardQuota(dto, userInfo, dataScope);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_GEN_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }
                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(ASYN_CARRY_FORWARD_QUOTA_PROCESS + dto.getProgress(), "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(ASYN_CARRY_FORWARD_QUOTA_MSG_PROCESS + dto.getProgress(), JsonSerializeHelper.serialize(engineMessage), 5 * 60);
                    }
                }
            });
            return Result.ok(true);
        } catch (Exception ex) {
            log.error("QuotaController.asynCarryForwardQuota executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Asyn carry forward quota exception", Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @PostMapping("/genEmpQuotaForCompensatoryLeave")
    @Security(code = "CompensatoryLeaveQuotaGen")
    @ApiOperation(value = "异步生成调休假期配额", tags = "v1.1")
    @LogRecordAnnotation(success = "核算了{{#content}}", category = "核算", menu = "休假管理-假期余额-调休明细")
    public Result<Boolean> genEmpQuotaForCompensatoryLeave(@RequestBody GenQuotaDto dto) {
        UserInfo userInfo = getUserInfo();
        //重复生成校验
        String lockKey = MessageFormat.format("GENECOMPENSATORYLEAVEMPQUOTA_LOCK_{0}_{1}_{2}", userInfo.getTenantId(), dto.getSobId());
        if (cacheService.containsKey(lockKey)) {//redisTemplate.hasKey(lockKey)
            return ResponseWrap.wrapResult(AttendanceCodes.ANOTHER_QUOTA_GEN_PROCESS_RUNNING, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        WaSobMapper waSobMapper = SpringUtil.getBean(WaSobMapper.class);
        WaSob waSob = waSobMapper.selectByPrimaryKey(dto.getSobId());
        LogRecordContext.putVariable("content", waSob.getWaSobName());
        try {
            cacheService.cacheValue(GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS + dto.getProgress(), "0.5");
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.COMPENSATORY_LEAVE_QUOTA_GEN, "emp");
            log.info("QuotaService.genEmpQuotaForCompensatoryLeave dataScope = {}", dataScope);

            String finalDataScope = dataScope;
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        log.info("genEmpQuotaForCompensatoryLeave params ... " + JSONUtils.ObjectToJson(dto) + ",user info = " + JSONUtils.ObjectToJson(userInfo));
                        quotaService.genEmpQuotaForCompensatoryLeave(userInfo.getTenantId(), userInfo.getUserId(), dto.getSobId(), dto.getEmpId(), finalDataScope, ConvertHelper.longConvert(userInfo.getTenantId()));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_GEN_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }
                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS + dto.getProgress(), "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(GEN_EMP_COMPENSATORY_LEAVEQUOTA_MSG_PROCESS + dto.getProgress(), JsonSerializeHelper.serialize(engineMessage), 5 * 60);
                    }
                }
            });
            return Result.ok(true);
        } catch (Exception ex) {
            log.error("QuotaController.genEmpQuotaForCompensatoryLeave executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Gen emp tx quota exception", Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("初始化固定额度")
    @PostMapping("/genEmpQuotaForFixedQuota")
    @Security(code = "FixedQuotaGen")
    @LogRecordAnnotation(success = "初始化了数据", category = "初始化", menu = "休假管理-假期余额-固定额度明细")
    public Result<Boolean> genEmpQuotaForFixedQuota() {
        UserInfo userInfo = getUserInfo();
        String lockKey = MessageFormat.format("ASYNC_INITIALIZE_FIXED_QUOTA_LOCK_{0}", userInfo.getTenantId());
        if (cacheService.containsKey(lockKey)) {
            return Result.fail("另一个初始化固定额度进程正在运行中，请稍候再尝试!");
        }
        cacheService.cacheValue(lockKey, "1", 30);
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.FIXED_QUOTA_GEN, "ei");
            log.info("QuotaService.genEmpQuotaForFixedQuota dataScope = {}", dataScope);
            quotaService.genEmpQuotaForFixedQuota(userInfo.getTenantId(), userInfo.getUserId(), dataScope);

            return Result.ok(true);
        } catch (Exception ex) {
            cacheService.remove(lockKey);
            log.error("QuotaController.genEmpQuotaForFixedQuota executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Gen emp fixed quota exception", Boolean.FALSE);
        }
    }

    @ApiOperation("获取配额生成进度")
    @RequestMapping(value = "/getProgress", method = RequestMethod.GET)
    public Result getProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(GEN_EMPQUOTA_PROCESS + progress)) {//redisTemplate.hasKey(GEN_EMPQUOTA_PROCESS + progress)
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        String value = cacheService.getValue(GEN_EMPQUOTA_PROCESS + progress);
        value = value == null ? "1" : value;
        Double rate = Double.valueOf(value);
        if (rate >= 1) {
            //执行结果
            String exeResult = cacheService.getValue(GEN_EMPQUOTA_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(exeResult)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(exeResult, AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            //删除缓存
            cacheService.remove(GEN_EMPQUOTA_PROCESS + progress);
            cacheService.remove(GEN_EMPQUOTA_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation("获取配额结转进度")
    @RequestMapping(value = "/getCarryForwardQuotaProgress", method = RequestMethod.GET)
    public Result getCarryForwardQuotaProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(ASYN_CARRY_FORWARD_QUOTA_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        String value = cacheService.getValue(ASYN_CARRY_FORWARD_QUOTA_PROCESS + progress);
        value = value == null ? "1" : value;
        Double rate = Double.valueOf(value);
        if (rate >= 1) {
            //执行结果
            String exeResult = cacheService.getValue(ASYN_CARRY_FORWARD_QUOTA_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(exeResult)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(exeResult, AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            //删除缓存
            cacheService.remove(ASYN_CARRY_FORWARD_QUOTA_PROCESS + progress);
            cacheService.remove(ASYN_CARRY_FORWARD_QUOTA_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation("获取调休配额生成进度")
    @RequestMapping(value = "/getProgressForCompensatoryLeave", method = RequestMethod.GET)
    public Result getProgressForCompensatoryLeave(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS + progress)) {//redisTemplate.hasKey(GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS + progress)
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        String value = cacheService.getValue(GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS + progress);
        value = value == null ? "1" : value;
        Double rate = Double.valueOf(value);
        if (rate >= 1) {
            //执行结果
            String exeResult = cacheService.getValue(GEN_EMP_COMPENSATORY_LEAVEQUOTA_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(exeResult)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(exeResult, AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            //删除缓存
            cacheService.remove(GEN_EMP_COMPENSATORY_LEAVEQUOTA_PROCESS + progress);
            cacheService.remove(GEN_EMP_COMPENSATORY_LEAVEQUOTA_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    /***************************************** 调整当前配额 *****************************************/

    @ApiOperation("批量保存当前配额调整")
    @PostMapping(value = "/saveQuotaAdjust")
    public Result<Boolean> saveQuotaAdjust(@RequestBody QuotaAdjustDto dto) {
        try {
            if (CollectionUtils.isEmpty(dto.getQuotaAdjusts())) {
                return ResponseWrap.wrapResult(AttendanceCodes.ADJUST_QUOTA_EMPTY, Boolean.FALSE);
            }
            String adjustJson = JSON.toJSONString(dto.getQuotaAdjusts());
            UserInfo userInfo = getUserInfo();
            waConfigService.saveEmpQuotaAdjust(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), userInfo.getUserId(), dto.getEmpQuotaId(), adjustJson, dto.getAcctTimeType(), CommonConstant.LANG_CN);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("QuotaController.saveQuotaAdjust executes exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("查询当前配额调整分页列表")
    @PostMapping(value = "/getQuotaAdjustList")
    public Result<AttendancePageResult<QuotaAdjustPageVo>> getQuotaAdjustList(@RequestBody QuotaAdjustPageDto dto) {
        PageBean pageBean = PageUtil.getPageBean(dto);
        UserInfo userInfo = getUserInfo();
        PageList<Map> pageList = (PageList<Map>) waConfigService.getEmpQuotaDetailList(pageBean, userInfo.getTenantId(), dto.getId(), dto.getType());
        List<QuotaAdjustPageVo> items = JSON.parseArray(JSON.toJSONString(pageList), QuotaAdjustPageVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
    }

    @ApiOperation("删除余额调整")
    @DeleteMapping(value = "/deleteQuotaAdjust")
    public Result<Boolean> deQuotaAdjust(@RequestParam("id") Integer id) {
        try {
            UserInfo userInfo = getUserInfo();
            waConfigService.deEmpQuotaAdjust(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), id, CommonConstant.LANG_CN);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("QuotaController.deQuotaAdjust executes exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取年份")
    @GetMapping(value = "/getYearSelectList")
    public Result<ItemsResult<KeyValueIntegerVo>> getYearSelectList() {
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        List<KeyValueIntegerVo> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            int finalI = i;
            list.add(new KeyValueIntegerVo() {{
                setText(year - finalI);
                setValue(year - finalI);
            }});
        }
        return ResponseWrap.wrapResult(new ItemsResult<>(list));
    }

    @ApiOperation("获取余额类型接口")
    @GetMapping(value = "/getBalanceList")
    public Result<ItemsResult<KeyValueVo>> getBalanceList() {
        UserInfo userInfo = getUserInfo();
        List<KeyValueVo> balanceList = selectListMapper.getBalanceList(userInfo.getTenantId());
        return Result.ok(new ItemsResult<>(balanceList));
    }

    @ApiOperation("拖动排序")
    @PostMapping("/dragSort")
    public Result<Boolean> dragSort(@RequestBody DragSort dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "参数为空", Boolean.FALSE);
        }
        List<DragSortItem> items = dto.getItems();
        Optional<Short> optional = items.stream().map(DragSortItem::getSortNum).min(Short::compare);
        if (optional.isPresent()) {
            List<WaLeaveSetting> list = new ArrayList<>();
            Short minSortNum = optional.get();
            int size = 0;
            for (DragSortItem item : items) {
                WaLeaveSetting dict = new WaLeaveSetting();
                dict.setQuotaSettingId(item.getId());
                dict.setQuotaSortNo(minSortNum + size);
                list.add(dict);
                size++;
            }
            selectListMapper.updateQuotaSortNoBatch(list);
        }
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation("调休明细列表")
    @PostMapping("/compensatory/list")
    public Result<AttendancePageResult<EmpCompensatoryQuotaVo>> getEmpCompensatoryQuotaList(@RequestBody CompensatoryQuotaSearchDto dto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "emp.orgid")
                    .replaceAll("empid", "emp.empid");
            dto.setDataScope(orgDataScope);
        }
        return ResponseWrap.wrapResult(quotaService.getEmpCompensatoryQuotaList(dto, getUserInfo()));
    }

    @Deprecated
    @ApiOperation("调休明细列表删除调休明细")
    @DeleteMapping("/compensatory")
    public Result<Boolean> deleteCompensatoryQuota(@RequestParam("quotaId") Long quotaId) {
        quotaService.deleteCompensatoryQuota(quotaId);
        return ResponseWrap.wrapResult(true);
    }

    @ApiOperation("调休明细列表批量删除调休明细")
    @PostMapping("/delCompensatoryQuotas")
    @LogRecordAnnotation(success = "批量删除了{{#num}}数据", category = "批量删除", menu = "休假管理-假期余额-调休明细")
    public Result<String> deleteCompensatoryQuotas(@RequestBody ItemsResult<Long> dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return Result.fail();
        }
        try {
            return quotaService.deleteCompensatoryQuotas(dto.getItems());
        } catch (Exception e) {
            log.error("QuotaController.deleteCompensatoryQuotas has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, "");
        }
    }

    @ApiOperation("假期余额分页列表")
    @PostMapping("/getQuotaList")
    public Result<AttendancePageResult<Map>> queryEmpQuotaList(@RequestBody QuotaEmpDto dto, HttpServletRequest request) {
        log.info("假期余额查询列表 dto={}", JSON.toJSONString(dto));
        UserInfo userInfo = getUserInfo();
        List<String> head = quotaService.getQuotaHeaders(userInfo.getTenantId());
        dto.setMergeWorknoToEmpName(true);
        PageBean pageBean = PageUtil.getPageBean(dto);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        List<Map> list = quotaService.queryEmpQuotaList(dto, pageBean, userInfo.getTenantId());
        return ResponseWrap.wrapResult(this.getPageResult(list, pageBean, dto, head));
    }

    private AttendancePageResult<Map> getPageResult(List list, PageBean pageBean, AttendanceBasePage basePage, List<String> headers) {
        Map map = GridUtil.covertList2GridJson(list, pageBean);
        AttendancePageResult<Map> pageResult = new AttendancePageResult<Map>(list, basePage.getPageNo(), basePage.getPageSize(), (Integer) map.get("total_count"));
        pageResult.setHead(headers);
        return pageResult;
    }

    @ApiOperation("固定额度明细列表")
    @PostMapping("/fix/list")
    public Result<AttendancePageResult<EmpFixQuotaVo>> getEmpFixQuotaList(@RequestBody FixQuotaSearchDto dto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "emp.orgid")
                    .replaceAll("empid", "emp.empid");
            dto.setDataScope(orgDataScope);
        }
        val page = quotaService.getEmpFixQuotaList(dto, getUserInfo());
        return ResponseWrap.wrapResult(page);
    }

    @ApiOperation("固定额度保存")
    @PostMapping("/fix/save")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "休假管理-假期余额-固定额度明细")
    public Result<Boolean> saveOrUpdateFixQuota(@RequestBody FixQuotaDto dto) {
        EmpInfoDTO empInfo = dto.getEmpInfo();
        LogRecordContext.putVariable("name", empInfo.getWorkno() + "(" + empInfo.getName() + ")");
        String msg = quotaService.saveOrUpdateFixQuota(dto);
        if (StringUtil.isNotBlank(msg)) {
            return Result.fail(msg);
        }
        return ResponseWrap.wrapResult(true);
    }

    @ApiOperation("固定额度删除")
    @PostMapping("/fix/delete")
    public Result<Boolean> deleteFixQuota(@RequestParam("empQuotaId") Long empQuotaId) {
        quotaService.deleteFixQuota(empQuotaId);
        return ResponseWrap.wrapResult(true);
    }

    @ApiOperation("固定额度批量删除")
    @PostMapping("/fix/deleteFixQuotas")
    @LogRecordAnnotation(success = "批量删除了{{#num}}数据", category = "批量删除", menu = "休假管理-假期余额-固定额度明细")
    public Result<String> deleteFixQuotas(@RequestBody ItemsResult<Integer> dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return Result.fail();
        }
        try {
            return quotaService.deleteFixQuotas(dto.getItems());
        } catch (Exception e) {
            log.error("QuotaController.deleteFixQuotas has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "delete exception", "");
        }
    }

    @ApiOperation("调休配额重新扣减")
    @PostMapping("/reDecEmpLeaveQuota")
    public Result<Boolean> reDecEmpLeaveQuota(@RequestBody ReDecEmpLeaveQuotaDto dto) {
        quotaService.reDecEmpLeaveQuota(dto);
        return ResponseWrap.wrapResult(true);
    }


    @ApiOperation("保存调休配额")
    @PostMapping("/compensatory/save")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}调休明细", category = "{{#operate}}", menu = "休假管理-假期余额-调休明细")
    public Result<Boolean> saveEmpCompensatoryQuota(@RequestBody EmpCompensatoryQuotaDto dto) {
        try {
            EmpInfoDTO empInfo = dto.getEmpInfo();
            LogRecordContext.putVariable("name", empInfo.getName() + "(" + empInfo.getWorkno() + ")");
            if (null != dto.getQuotaId()) {
                LogRecordContext.putVariable("operate", "编辑");
                return quotaService.update(dto);
            } else {
                LogRecordContext.putVariable("operate", "新增");
                return quotaService.save(dto);
            }
        } catch (Exception e) {
            log.error("QuotaController.saveEmpCompensatoryQuota has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, false);
        }
    }

    @ApiOperation("查询调休配额数据来源")
    @GetMapping("/compensatory/getDataSourceSelections")
    public Result<ItemsResult<KeyValue>> getCompensatoryDataSourceSelections() {
        try {
            List<KeyValue> results = new ArrayList<>();
            for (DataSourceEnum row : DataSourceEnum.values()) {
                results.add(new KeyValue(DataSourceEnum.getDescByName(row.name()), row.name()));
            }
            return ResponseWrap.wrapResult(new ItemsResult<>(results));
        } catch (Exception e) {
            log.error("QuotaController.getCompensatoryDataSourceSelections has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new ItemsResult<>());
        }
    }

    @ApiOperation("调休详情")
    @GetMapping("/compensatory/detail")
    public Result<EmpCompensatoryQuotaDto> getEmpCompensatoryDetail(@RequestParam("quotaId") Long quotaId) {
        try {
            return Result.ok(quotaService.getById(quotaId));
        } catch (Exception e) {
            log.error("QuotaController.getEmpCompensatoryDetail has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, null);
        }
    }

    @ApiOperation("导入员工调休明细")
    @PostMapping(value = "/compensatory/import")
    @LogRecordAnnotation(success = "导入了额度调整", category = "导入", menu = "休假管理-假期余额-调休明细")
    public Result<String> importEmpCompensatory(MultipartFile file) {
        if (null == file) {
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_TEMPLATE_FILE, null);
        }
        try {
            return quotaService.importEmpCompensatory(file);
        } catch (Exception e) {
            log.error("QuotaController.importEmpCompensatory has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null);
        }
    }

    @ApiOperation("导入员工调休明细调休数据")
    @PostMapping(value = "/compensatory/importQuota")
    @LogRecordAnnotation(success = "导入了调休额度", category = "导入", menu = "休假管理-假期余额-调休明细")
    public Result<String> importEmpCompensatoryQuota(MultipartFile file) {
        if (null == file) {
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_TEMPLATE_FILE, null);
        }
        try {
            return quotaService.importEmpCompensatoryQuota(file);
        } catch (Exception e) {
            log.error("QuotaController.importEmpCompensatoryQuota has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null);
        }
    }

    @ApiOperation("获取所选员工当前考勤方案下的调休假单位")
    @GetMapping("/compensatory/getCompensatoryLeaveUnit")
    public Result<KeyValue> getCompensatoryLeaveUnit(@RequestParam("empId") Long empId) {
        try {
            return quotaService.getCompensatoryLeaveUnit(empId);
        } catch (Exception e) {
            log.error("QuotaController.getCompensatoryLeaveUnit has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }
}
