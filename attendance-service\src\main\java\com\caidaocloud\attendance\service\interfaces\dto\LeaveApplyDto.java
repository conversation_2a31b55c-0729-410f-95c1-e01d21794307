package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class LeaveApplyDto extends ExportBasePage implements Serializable {

    @ApiModelProperty("审批状态")
    private Integer status;

    @ApiModelProperty("筛选状态")
    private Integer[] filterStatus;

    @ApiModelProperty("开始日期")
    private Long startDate;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束日期")
    private Long endDate;

    @ApiModelProperty("结束时间")
    private Long endTime;

}
