package com.caidaocloud.attendance.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Calendar;
import java.util.Date;


@RunWith(SpringRunner.class)
@SpringBootTest
public class DateUtilTest {

    @Test
    public void getOvertimeList() throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(1638340110000L));
        calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
        System.out.println("The current date " + calendar.getTime().getTime() / 1000);
    }
}
