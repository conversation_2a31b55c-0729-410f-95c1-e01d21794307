package com.caidaocloud.attendance.service.interfaces.vo.shift;

import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyWorkDateShiftDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/17 13:09
 * @Version 1.0
 */
@Data
public class ShiftInfoDto {

    // 打卡记录
    @ApiModelProperty("打卡记录")
    private List<RegisterRecordDto> records;
    @ApiModelProperty("排班记录")
    private List<MyWorkDateShiftDto> shifts;
}
