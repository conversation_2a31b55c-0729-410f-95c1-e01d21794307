package com.caidaocloud.attendance.service.mongo;

import com.caidaocloud.attendance.service.infrastructure.repository.impl.TaskRepository;
import com.caidaocloud.attendance.service.infrastructure.repository.po.TaskPo;
import com.caidaocloud.attendance.service.interfaces.dto.task.UploadFileResult;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TaskPoTest {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Autowired
    private TaskRepository taskRepository;

    @Test
    public void taskPoTest(){
        TaskPo taskPo = new TaskPo();
        taskPo.setId(snowflakeUtil.createId());
        taskPo.setBelongOrgId("120");
        taskPo.setType("110");
        taskPo.setProgress(BigDecimal.ZERO);
        taskPo.setExt("ext");
        taskPo.setCreateTime(System.currentTimeMillis() / 1000);
        taskPo.setCreator(1L);

        List<UploadFileResult> uploadFiles = new ArrayList<>();
        UploadFileResult ur = new UploadFileResult();
        ur.setFileName("test.txt");
        ur.setSize(100);
        ur.setObjectPath("aaaa/txttest.txt");
        ur.setId(1L);
        uploadFiles.add(ur);
        taskPo.setUploadFiles(uploadFiles);
        //taskRepository.save(taskPo, "120");

        ur.setId(2L);
        ur.setObjectPath("aaaa/hhhhhh.txt");
        uploadFiles.add(ur);
        //taskRepository.update(taskPo, "120");
    }
}
