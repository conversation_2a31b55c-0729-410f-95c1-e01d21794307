<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaCustomLogicConfigMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCustomLogicConfig">
    <id column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="logic_type" jdbcType="VARCHAR" property="logicType" />
    <result column="logic_exp" jdbcType="VARCHAR" property="logicExp" />
    <result column="logic_var" jdbcType="VARCHAR" property="logicVar" />
    <result column="belong_business" jdbcType="VARCHAR" property="belongBusiness" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    config_id, code, name, logic_type, logic_exp, logic_var, belong_business, status, 
    tenant_id, deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_custom_logic_config
    where config_id = #{configId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_custom_logic_config
    where config_id = #{configId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCustomLogicConfig">
    insert into wa_custom_logic_config (config_id, code, name, 
      logic_type, logic_exp, logic_var, 
      belong_business, status, tenant_id, 
      deleted, create_by, create_time, 
      update_by, update_time)
    values (#{configId,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{logicType,jdbcType=VARCHAR}, #{logicExp,jdbcType=VARCHAR}, #{logicVar,jdbcType=VARCHAR}, 
      #{belongBusiness,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{tenantId,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCustomLogicConfig">
    insert into wa_custom_logic_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        config_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="logicType != null">
        logic_type,
      </if>
      <if test="logicExp != null">
        logic_exp,
      </if>
      <if test="logicVar != null">
        logic_var,
      </if>
      <if test="belongBusiness != null">
        belong_business,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="logicType != null">
        #{logicType,jdbcType=VARCHAR},
      </if>
      <if test="logicExp != null">
        #{logicExp,jdbcType=VARCHAR},
      </if>
      <if test="logicVar != null">
        #{logicVar,jdbcType=VARCHAR},
      </if>
      <if test="belongBusiness != null">
        #{belongBusiness,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCustomLogicConfig">
    update wa_custom_logic_config
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="logicType != null">
        logic_type = #{logicType,jdbcType=VARCHAR},
      </if>
      <if test="logicExp != null">
        logic_exp = #{logicExp,jdbcType=VARCHAR},
      </if>
      <if test="logicVar != null">
        logic_var = #{logicVar,jdbcType=VARCHAR},
      </if>
      <if test="belongBusiness != null">
        belong_business = #{belongBusiness,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where config_id = #{configId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCustomLogicConfig">
    update wa_custom_logic_config
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      logic_type = #{logicType,jdbcType=VARCHAR},
      logic_exp = #{logicExp,jdbcType=VARCHAR},
      logic_var = #{logicVar,jdbcType=VARCHAR},
      belong_business = #{belongBusiness,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where config_id = #{configId,jdbcType=BIGINT}
  </update>
</mapper>