package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/2/18
 */
@Data
public class WaWorktimeGridVo implements Serializable {
    private static final long serialVersionUID = 8664748637873231795L;

    @ApiModelProperty("工作日历ID")
    private Integer workCalendarId;

    @ApiModelProperty("工作日历")
    private String workCalendarName;

    @ApiModelProperty("特殊日期分组")
    private String groupName;

    @ApiModelProperty("排班名称")
    private String roundName;

    @ApiModelProperty("适用范围 true 适用全部员工 false 适用部分员工")
    private Boolean isDefault;
}
