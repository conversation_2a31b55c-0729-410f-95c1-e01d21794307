package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.dto.StatisticsReportReqDataDto;
import com.caidaocloud.attendance.service.application.service.impl.StatisticsReportService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/9/16
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/wareport/v1")
@Api(value = "/api/attendance/report/v1", description = "考勤统计报表接口")
public class StatisticsReportController {

    @Autowired
    private StatisticsReportService statisticsReportService;

    @ApiOperation("获取考勤统计报表")
    @PostMapping("/getAnalyzeStatisticsReportPageList")
    public Result getAnalyzeStatisticsReportPageList(@RequestBody StatisticsReportReqDataDto dto) {
        return Result.ok(statisticsReportService.getAnalyzeStatisticsReportPageList(dto));
    }

}
