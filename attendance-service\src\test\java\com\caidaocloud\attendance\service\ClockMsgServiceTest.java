package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.application.service.impl.ClockTaskWorkService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class ClockMsgServiceTest {
    @Resource
    private ClockTaskWorkService clockTaskWorkService;
    @Test
    public void test(){
        clockTaskWorkService.synchronizeEmpClockShiftInfo(null);
    }
}
