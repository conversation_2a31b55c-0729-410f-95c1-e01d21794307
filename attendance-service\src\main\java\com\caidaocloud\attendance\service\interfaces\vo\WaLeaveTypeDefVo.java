package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class WaLeaveTypeDefVo {
    @ApiModelProperty("id")
    private Integer leaveTypeDefId;
    @ApiModelProperty("编码")
    private String leaveTypeDefCode;
    @ApiModelProperty("名称")
    private String leaveTypeDefName;
    @ApiModelProperty("多语言")
    private Object leaveTypeDefLang;
    @ApiModelProperty("是否删除 0:未删除 1:删除")
    private Integer deleted;
    @ApiModelProperty("状态 0:有效 -1:无效")
    private Integer status;
    @ApiModelProperty("所属公司")
    private String belongOrgid;
    @ApiModelProperty("创建用户")
    private Long crtuser;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("更新用户")
    private Long upduser;
    @ApiModelProperty("更新时间")
    private Long updtime;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nLeaveTypeDefName;
}
