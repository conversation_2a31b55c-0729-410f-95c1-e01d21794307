package com.caidaocloud.attendance.service.interfaces.vo.shift;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpShiftVo {
    @ApiModelProperty("员工排班ID")
    private Integer empShiftId;
    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("工作日历ID")
    private Integer workCalendarId;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
}
