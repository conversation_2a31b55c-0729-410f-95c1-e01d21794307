package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 考勤日历
 */
@Data
public class AanlyzeCalendarVo {

    @ApiModelProperty("日期类型")
    private Integer dateType;
    @ApiModelProperty("日期")
    private String workDate;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("员工id")
    private Long empId;
    @ApiModelProperty("考勤结果")
    private List<WaResultVo> waResult;
}
