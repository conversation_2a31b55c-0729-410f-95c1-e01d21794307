package com.caidaocloud.attendance.service.infrastructure.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.dto.FilterBean;
import com.caidaocloud.dto.QueryPageBean;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/14
 **/
public class QueryAdapterUtil {

    /**
     * 创建QueryWrapper
     *
     * @param queryPageBean
     * @param keyWords
     * @return
     */
    public static QueryWrapper createQueryWrapper(QueryPageBean queryPageBean, String... keyWords) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (keyWords.length > 0 && StringUtils.isNotBlank(queryPageBean.getKeywords())) {
            for (String keyWord : keyWords) {
                queryWrapper.like(keyWord, queryPageBean.getKeywords());
            }
        }
        List<FilterBean> filterList = queryPageBean.getFilterList();
        if (!CollectionUtils.isEmpty(filterList)) {
            for (FilterBean filterBean : filterList) {
                filterBean(queryWrapper, filterBean);
            }
        }
        return queryWrapper;
    }

    private static <T> void filterBean(QueryWrapper<T> queryWrapper, FilterBean filterBean) {
        switch (filterBean.getOp()) {
            case lk:
                queryWrapper.like(filterBean.getProp(), filterBean.getValue());
                break;
            case eq:
                queryWrapper.eq(filterBean.getProp(), filterBean.getValue());
                break;
            case gt:
                queryWrapper.gt(filterBean.getProp(), filterBean.getValue());
                break;
            case ge:
                queryWrapper.ge(filterBean.getProp(), filterBean.getValue());
                break;
            case lt:
                queryWrapper.lt(filterBean.getProp(), filterBean.getValue());
                break;
            case le:
                queryWrapper.le(filterBean.getProp(), filterBean.getValue());
                break;
            case bt:
                String[] split = filterBean.getValue().toString().split(",");
                if (!CollectionUtils.isEmpty(Lists.newArrayList(split))) {
                    if (split.length > 1) {
                        queryWrapper.between(filterBean.getProp(), Long.parseLong(split[0]), Long.parseLong(split[1]));
                    } else {
                        queryWrapper.ge(filterBean.getProp(), Long.parseLong(split[0]));
                    }
                }
                break;
            case in:
                queryWrapper.in(filterBean.getProp(), filterBean.getValue().toString().split(","));
                break;
            case ne:
                queryWrapper.ne(filterBean.getProp(), filterBean.getValue());
                break;
            case nm:
                queryWrapper.isNotNull(filterBean.getProp());
                break;
            case em:
                queryWrapper.isNull(filterBean.getProp());
                break;
        }
    }
}
