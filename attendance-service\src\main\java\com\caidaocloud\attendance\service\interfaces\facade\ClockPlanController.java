package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.dto.clock.ClockPlanInfoDto;
import com.caidaocloud.attendance.service.application.dto.clock.VerifyResultDto;
import com.caidaocloud.attendance.service.application.enums.ClockWayEnum;
import com.caidaocloud.attendance.service.application.service.IClockPlanService;
import com.caidaocloud.attendance.service.interfaces.dto.clock.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.VerifyInfoDto;
import com.caidaocloud.attendance.service.interfaces.vo.clock.*;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("/api/attendance/clockPlan/v1")
public class ClockPlanController {

    @Autowired
    private IClockPlanService clockPlanService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private AsyncExecService asyncService;
    private static final String SYNC_EMP_CLOCK_PLAN_PROCESS = "SYNC_EMP_CLOCK_PLAN_PROCESS_";
    private static final String SYNC_EMP_CLOCK_PLAN_MSG_PROCESS = "SYNC_EMP_CLOCK_PLAN_MSG_PROCESS_";

    @ApiOperation("新增或修改打卡方案")
    @PostMapping("/save")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-打卡设置")
    public Result<Boolean> saveClockPlan(@RequestBody ClockPlanDto dto) {
        dto.initPlanName();
        dto.initI18nPlanName();
        if (StringUtils.isBlank(dto.getGroupExp()) || StringUtils.isBlank(dto.getGroupNote())) {
            return ResponseWrap.wrapResult(AttendanceCodes.SCOPE_EMPTY, CommonConstant.FALSE);
        }
        if (StringUtils.isBlank(dto.getPlanName())) {
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NAME_EMPTY, CommonConstant.FALSE);
        }
        if (dto.getPlanName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        if (null == dto.getTimeInterval()) {
            return ResponseWrap.wrapResult(AttendanceCodes.TIME_INTERVAL, CommonConstant.FALSE);
        }
        if (dto.getTimeInterval() > 60 || !Pattern.compile("^(\\d+)(\\.\\d{0,1})?$").matcher(dto.getTimeInterval().toString()).matches()) {
            return ResponseWrap.wrapResult(AttendanceCodes.TIME_INTERVAL_FORMAT_ERROR, CommonConstant.FALSE);
        }
        if (StringUtils.isNotBlank(dto.getDescription()) && dto.getDescription().length() > 1000) {
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_DESCRIPTION_TOO_LONG, CommonConstant.FALSE);
        }
        if (StringUtils.isBlank(dto.getClockWay())) {
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_WAY_EMPTY, CommonConstant.FALSE);
        }
        List<String> clockWays = Stream.of(dto.getClockWay().split(",")).collect(Collectors.toList());
        if (clockWays.contains(ClockWayEnum.GPS.getIndex().toString()) && StringUtils.isBlank(dto.getGps())) {
            return ResponseWrap.wrapResult(AttendanceCodes.SITE_EMPTY, CommonConstant.FALSE);
        }
        if (clockWays.contains(ClockWayEnum.WIFI.getIndex().toString()) && StringUtils.isBlank(dto.getWifi())) {
            return ResponseWrap.wrapResult(AttendanceCodes.WIFI_EMPTY, CommonConstant.FALSE);
        }
        if (clockWays.contains(ClockWayEnum.BLUETOOTH.getIndex().toString()) && StringUtils.isBlank(dto.getBluetooth())) {
            return ResponseWrap.wrapResult(AttendanceCodes.BLUETOOTH_EMPTY, CommonConstant.FALSE);
        }
        try {
            return clockPlanService.saveClockPlan(dto);
        } catch (Exception ex) {
            log.error("ClockPlanController.saveClockPlan executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, CommonConstant.FALSE);
        }
    }

    @ApiOperation("获取打卡方案的分页列表")
    @PostMapping("/list")
    public Result<AttendancePageResult<ClockPlanPageVo>> getClockPlanList(@RequestBody ClockPlanPageDto dto) {
        try {
            AttendancePageResult<ClockPlanPageInfoDto> pageResult = clockPlanService.getClockPlanList(dto);
            List<ClockPlanPageVo> items = ObjectConverter.convertList(pageResult.getItems(), ClockPlanPageVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
        } catch (Exception ex) {
            log.error("ClockPlanController.getClockPlanList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("获取打卡方案的详情")
    @GetMapping("/detail")
    public Result<ClockPlanVo> getClockPlanInfo(@RequestParam("id") Long id) {
        try {
            ClockPlanInfoDto plan = clockPlanService.getClockPlan(id);
            if (null == plan) {
                return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(plan, ClockPlanVo.class));
        } catch (Exception ex) {
            log.error("ClockPlanController.getClockPlanInfo executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, null);
        }
    }

    @ApiOperation("删除打卡方案")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-打卡设置")
    public Result<Boolean> deleteClockPlan(@RequestParam("id") Long id) {
        try {
            clockPlanService.deleteClockPlan(id);
            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception ex) {
            log.error("ClockPlanController.deleteClockPlan executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, CommonConstant.FALSE);
        }
    }

    @Deprecated
    @ApiOperation("校验已选员工是否已适用某方案")
    @PostMapping("/verifySelectedEmployees")
    public Result<ItemsResult<VerifyResultVo>> verifySelectedEmployees(@RequestBody VerifyInfoDto dto) {
        if (CollectionUtils.isEmpty(dto.getEmpIds())) {
            return ResponseWrap.wrapResult(new ItemsResult<>());
        }
        try {
            Long planId = dto.getEntityId() != null ? Long.valueOf(String.valueOf(dto.getEntityId())) : null;
            List<VerifyResultDto> list = clockPlanService.verifySelectedEmployees(planId, dto.getEmpIds());
            return ResponseWrap.wrapResult(ItemsResult.of(ObjectConverter.convertList(list, VerifyResultVo.class)));
        } catch (Exception ex) {
            log.error("ClockPlanController.verifySelectedEmployees executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), new ItemsResult<>());
        }
    }

    @ApiOperation("获取员工的打卡方案")
    @GetMapping("/getEmpPlan")
    public Result<ClockPlanVo> getEmpPlan() {
        try {
            ClockPlanInfoDto plan = clockPlanService.getEmpPlan();
            if (null == plan) {
                return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(plan, ClockPlanVo.class));
        } catch (Exception ex) {
            log.error("ClockPlanController.getEmpPlan executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NOT_EXIST, null);
        }
    }

    /***************************************** 员工打卡方案关联关系 *****************************************/

    @ApiOperation(value = "查询员工打卡方案列表")
    @PostMapping(value = "/getEmpClockPlans")
    public Result<AttendancePageResult<EmpClockPlanDto>> getEmpClockPlans(@RequestBody EmpClockPlanReqDto reqDto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            reqDto.setDataScope(orgDataScope);
        }
        try {
            UserInfo userInfo = getUserInfo();
            return ResponseWrap.wrapResult(clockPlanService.getEmpClockPlans(reqDto, userInfo));
        } catch (Exception e) {
            log.error("ClockPlanController.getEmpClockPlans has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, null);
        }
    }

    @ApiOperation("保存员工打卡方案")
    @PostMapping("/saveEmpClockPlan")
    public Result<Boolean> saveEmpClockPlan(@RequestBody EmpClockPlanInfoDto dto) {
        try {
            if (dto.getEmpInfo()==null || dto.getEmpInfo().getEmpId() == null) {
                log.error("Employee is empty");
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_EMPTY, Boolean.FALSE);
            }
            if (dto.getPlanId() == null) {
                log.error("Punch in scheme is empty");
                return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_EMPTY, Boolean.FALSE);
            }
            if (dto.getStartTime() == null) {
                log.error("Validity period start time is empty");
                return ResponseWrap.wrapResult(AttendanceCodes.START_DATE_EMPTY, Boolean.FALSE);
            }
            if (dto.getEndTime() == null) {
                log.error("Validity end time is empty");
                return ResponseWrap.wrapResult(AttendanceCodes.END_DATE_EMPTY, Boolean.FALSE);
            }
            if (dto.getEndTime() < dto.getStartTime()) {
                log.error("The end time of the validity period is less than the start time");
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_CLOCK_PLAN_PERIOD_ERROR, Boolean.FALSE);
            }
            return clockPlanService.saveEmpClockPlan(dto);
        } catch (Exception e) {
            log.error("ClockPlanController.saveEmpClockPlan has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "删除员工打卡方案")
    @DeleteMapping(value = "/deleteEmpClockPlan")
    public Result<Boolean> deleteEmpClockPlan(@RequestParam("id") Long id) {
        try {
            clockPlanService.deleteEmpClockPlan(id);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("ClockPlanController.deleteEmpClockPlan has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "查询员工打卡方案")
    @GetMapping(value = "/getEmpClockPlan")
    public Result<EmpClockPlanInfoVo> getEmpClockPlan(@RequestParam("id") Long id) {
        try {
            EmpClockPlanDto empClockPlanDto = clockPlanService.getEmpClockPlan(id);
            if (empClockPlanDto == null) {
                return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(empClockPlanDto, EmpClockPlanInfoVo.class));
        } catch (Exception e) {
            log.error("ClockPlanController.getEmpClockPlan has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_NOT_EXIST, null);
        }
    }

    @ApiOperation(value = "打卡方案下拉列表")
    @GetMapping(value = "/getEmpClockPlanOptions")
    public Result<ItemsResult<KeyValue>> getEmpClockPlanOptions() {
        try {
            List<KeyValue> list = clockPlanService.getClockPlanOptions();
            if (CollectionUtils.isNotEmpty(list)) {
                return ResponseWrap.wrapResult(ItemsResult.of(list));
            }
            return ResponseWrap.wrapResult(ItemsResult.of(new ArrayList<>()));
        } catch (Exception e) {
            log.error("ClockPlanController.getEmpClockPlanOptions has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, null);
        }
    }

    @ApiOperation("员工打卡方案同步更新接口")
    @GetMapping("/synchronize")
    public Result<Boolean> synchronizeEmpClockPlan(@RequestParam("progress") String progress) {
        UserInfo userInfo = getUserInfo();
        //重复生成校验
        String lockKey = MessageFormat.format("SYNC_EMP_CLOCK_PLAN_LOCK_{0}", userInfo.getBelongOrgId());
        if (cacheService.containsKey(lockKey)) {
            log.error("The synchronization process of employee check-in plan is running, please try again later!");
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_SYNCHRONIZING, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            cacheService.cacheValue(SYNC_EMP_CLOCK_PLAN_PROCESS + progress, "0.5");
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        log.info("synchronizeEmpClockPlan params ... " + progress + ",user info = " + JSONUtils.ObjectToJson(userInfo));
                        clockPlanService.synchronizeClockPlan(userInfo.getTenantId(), userInfo.getUserId(), ConvertHelper.longConvert(userInfo.getTenantId()));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.CLOCK_PLAN_SYNC_FAILED, Boolean.FALSE).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }
                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(SYNC_EMP_CLOCK_PLAN_PROCESS + progress, "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(SYNC_EMP_CLOCK_PLAN_MSG_PROCESS + progress, JsonSerializeHelper.serialize(engineMessage), 5 * 60);
                    }
                }
            });
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("ClockPlanController.synchronizeEmpClockPlan has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SYNC_FAILED, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("获取同步员工打卡方案同步进度")
    @RequestMapping(value = "/getSyncEmpClockPlanProgress", method = RequestMethod.GET)
    public Result getSyncEmpClockPlanProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(SYNC_EMP_CLOCK_PLAN_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        String rateValue = cacheService.getValue(SYNC_EMP_CLOCK_PLAN_PROCESS + progress);
        Double rate = StringUtil.isBlank(rateValue) ? 0.5d : Double.valueOf(rateValue);
        if (rate >= 1) {
            //执行结果
            String analyzeMsg = cacheService.getValue(SYNC_EMP_CLOCK_PLAN_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(analyzeMsg)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(analyzeMsg, AttendanceEngineMessage.class);
                engineMessage = null != engineMessageForCache ? engineMessageForCache : engineMessage;
            }
            //删除缓存
            cacheService.remove(SYNC_EMP_CLOCK_PLAN_PROCESS + progress);
            cacheService.remove(SYNC_EMP_CLOCK_PLAN_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation(value = "批量删除员工打卡方案")
    @PostMapping(value = "/deleteEmpClockPlans")
    public Result<Boolean> deleteEmpClockPlans(@RequestBody ItemsResult<Long> dto) {
        try {
            clockPlanService.deleteEmpClockPlans(dto.getItems());
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("ClockPlanController.deleteEmpClockPlans has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }
}