package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.application.enums.TimeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.io.Serializable;

/**
 * 考勤汇总穿透查询
 * created by: FoAng
 * create time: 25/3/2024 2:20 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SummaryDetailDto extends AttendanceBasePage implements Serializable {

    @ApiModelProperty("选择周期类型")
    private TimeTypeEnum timeType;

    @ApiModelProperty("开始时间")
    private Long startDate;

    @ApiModelProperty("结束时间")
    private Long endDate;

    @ApiModelProperty("部门查询")
    private Long[] orgIds;

    @ApiModelProperty("关键字")
    private String keywords;

    @ApiModelProperty("应到与打卡类型，默认为0：应到人数, 1: 打卡人数")
    private int workType;

    /**
     * timeType: DAY 默认为0:迟到, 1:早退
     * timeType: WEEK MONTH 默认为0:休假 1:出差 2:加班 3:迟到 4:早退
     */
    @ApiModelProperty("考勤汇总分析列表类型，timeType: DAY 默认为0:迟到, 1:早退\n" +
            "     * timeType: WEEK MONTH 默认为0:休假 1:出差 2:加班 3:迟到 4:早退")
    private int statisticsType;

    @ApiModelProperty("筛选状态")
    private Integer[] filterStatus;

    public SummaryDetailDto configTimePeriod() {
        ImmutablePair<Long, Long> timePeriod = TimeTypeEnum.parsePeriod(timeType, startDate, endDate);
        setStartDate(timePeriod.getLeft());
        setEndDate(timePeriod.getRight());
        return this;
    }
}
