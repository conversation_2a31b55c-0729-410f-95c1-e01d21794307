package com.caidaocloud.attendance.service.interfaces.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外出规则保存DTO
 *
 * <AUTHOR>
 * @Date 2021/9/8
 */
@Data
public class TravelTypeDto {
    @ApiModelProperty("出差规则ID")
    private Long travelTypeId;
    @ApiModelProperty("租户ID")
    private String tenantId;
    @ApiModelProperty("出差类型")
    private String travelTypeName;
    @ApiModelProperty("单位 1 天 2 小时")
    private Integer acctTimeType;
    @ApiModelProperty("最小单位")
    private Float roundTimeUnit;
    @ApiModelProperty("是否包含非工作日：0 否 1 是")
    private Integer ifIncludeNonWorkday;
    @ApiModelProperty("是否必须上传附件：0 不必须 1 必须")
    private Integer ifUploadFile;
    @ApiModelProperty("是否必须填写备注：0 不必须 1 必须")
    private Integer ifWriteRemark;
    @ApiModelProperty("备注说明")
    private String remark;
    @ApiModelProperty("出差期间加班规则：1、不允许提交加班单，2、允许提交加班单，3、自动转调休，默认：2")
    private Integer overtimeRule;
    @ApiModelProperty("{start:出差时长>=）,end:出差时长(<=),unit:单位（1天/2小时），leaveTypeId:假期类型，duration：转换时长，transferUnit:转换单位（1天/2小时）}")
    private List<TravelTransferRulePeriod> autoTransferRules;
    @ApiModelProperty("撤销是否需要走审批流：true是/false否，默认值false")
    private Boolean revokeWorkflow;
    @ApiModelProperty("审批通过单据是否允许撤销：true是/false否，默认值false")
    private Boolean revokePassed;
    @ApiModelProperty("撤销是否需要走审批流,允许撤销的状态多选，逗号分隔：1审批中2已通过")
    private String revokeAllowStatus;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nTravelTypeName;
    @ApiModelProperty("是否国外出差")
    private boolean ifAbroad;

    public void initTravelTypeName() {
        if (StringUtils.isNotBlank(this.travelTypeName)) {
            return;
        }
        if (null == this.i18nTravelTypeName || this.i18nTravelTypeName.isEmpty() || null == this.i18nTravelTypeName.get("default")) {
            return;
        }
        this.setTravelTypeName(this.i18nTravelTypeName.get("default"));
    }

    public void initI18nTravelTypeName() {
        if (null != this.i18nTravelTypeName && !this.i18nTravelTypeName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.travelTypeName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.travelTypeName);
        this.setI18nTravelTypeName(i18nName);
    }
}
