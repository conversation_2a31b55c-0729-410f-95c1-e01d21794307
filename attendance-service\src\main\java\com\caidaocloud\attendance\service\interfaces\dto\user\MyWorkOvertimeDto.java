package com.caidaocloud.attendance.service.interfaces.dto.user;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 我的加班记录
 * <AUTHOR>
 * @date 2021-07-13
 */
@Data
public class MyWorkOvertimeDto extends BasePage {
    @ApiModelProperty("申请开始日期，精确到秒")
    private Long startApplyTime;

    @ApiModelProperty("申请结束日期，精确到秒")
    private Long endApplyTime;

    @ApiModelProperty("审批状态，2：已通过;9：已撤销")
    private Short status;
}
