package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompensatoryDataVo {
    @ApiModelProperty("生效配额")
    private Float validQuota;
    @ApiModelProperty("生效配额单位：1天2小时")
    private Integer validQuotaUnit;
    @ApiModelProperty("失效配额")
    private Float inValidQuota;
    @ApiModelProperty("失效配额单位：1天2小时")
    private Integer inValidQuotaUnit;
    @ApiModelProperty("仅允许申请整数：true/false")
    private Boolean onlyInteger;
}
