<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.SelectListMapper">
    <select id="getWaGroupListToSelect" resultType="com.caidaocloud.attendance.service.application.dto.group.GroupKeyValue">
        SELECT
            wa_group_id as "value",
            wa_group_name as "text",
            i18n_wa_group_name as "i18nText"
        FROM
            wa_group wg
        WHERE
            wg.belong_orgid = #{belongId}
    </select>
    <select id="getBalanceList" resultType="com.caidaocloud.attendance.service.interfaces.vo.KeyValueVo">
        select quota_setting_name as "text",
            quota_setting_id as "value"
        from wa_leave_setting
        where quota_setting_name is not null
        and belong_orgid = #{belongId};
    </select>
    <select id="getLeaveTypeList" resultType="com.caidaocloud.attendance.service.interfaces.vo.KeyValueVo">
    SELECT
      leave_type_def_id     AS "value",
      leave_type_def_name   AS "text"
    FROM wa_leave_type_def
    WHERE status = 0 AND deleted = 0 AND (belong_orgid = '0' OR belong_orgid = #{belongId})
    ORDER BY leave_type_def_code
    </select>

    <update id="updateQuotaSortNoBatch" parameterType="java.util.List">
        <foreach collection="items" item="item" index="index" open="" close="" separator=";">
            update wa_leave_setting
            <set>
                quota_sort_no = #{item.quotaSortNo}
            </set>
            where quota_setting_id = #{item.quotaSettingId}
        </foreach>
    </update>

    <select id="selectMaxQuotaSortNo" resultType="java.lang.Integer">
        select max(quota_sort_no)
        from wa_leave_setting
        where belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
    </select>
</mapper>