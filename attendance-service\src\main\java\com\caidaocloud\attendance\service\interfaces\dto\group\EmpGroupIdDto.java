package com.caidaocloud.attendance.service.interfaces.dto.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class EmpGroupIdDto {
    public EmpGroupIdDto(){
        this.empIds = new ArrayList<>();
    }
    @ApiModelProperty("empGroupId")
    private Integer empGroupId;
    @ApiModelProperty("empId")
    private Long empId;
    @ApiModelProperty("empIds")
    private List<Long> empIds;
    @ApiModelProperty("currentTime")
    private Long currentTime;
}
