package com.caidao1.mobile.service;

import com.caidao1.commons.utils.ReflectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

public class demodemo {

    public static void main(String[] args) {
        gggggg(args);
    }

    public static List<String>  gggggg(String[] args) {

        List<String> sqlList = new ArrayList<>();

        List<String> table = new ArrayList<>();
        table.add("wa_store_time");
        table.add("wa_clock_site");
        table.add("wa_sob");
        table.add("wa_analyze");
        table.add("wa_analyze_statistics_report");
        table.add("wa_approval");
        table.add("wa_approval_chart");
        table.add("wa_approval_flow");
        table.add("wa_batch_leave_file");
        table.add("wa_calendar_group");
        table.add("wa_calendar_group_rel");
        table.add("wa_compensatory_quota_use");
        table.add("wa_day_type");
        table.add("wa_emp_apply_record");
        table.add("wa_emp_batch_leave");
        table.add("wa_emp_compensatory_quota");
        table.add("wa_emp_group");
        table.add("wa_emp_leave");
        table.add("wa_emp_leave_cancel");
        table.add("wa_emp_leave_cancel_daytime");
        table.add("wa_clock_plan");
        table.add("wa_analyze_adjust");
        table.add("wa_emp_group_rule");
        table.add("wa_emp_info_record");
        table.add("wa_emp_leave_cancel_info");
        table.add("wa_emp_leave_time");
        table.add("wa_emp_overtime");
        table.add("wa_emp_overtime_detail");
        table.add("wa_emp_overtime_new");
        table.add("wa_emp_parse_group");
        table.add("wa_emp_quota");
        table.add("wa_emp_quota_use");
        table.add("wa_emp_shift");
        table.add("wa_emp_shift_change");
        table.add("wa_emp_travel");
        table.add("wa_emp_travel_daytime");
        table.add("wa_emp_travel_detail");
        table.add("wa_group");
        table.add("wa_holiday_calendar");
        table.add("wa_leave_calendar_detail");
        table.add("wa_leave_daytime");
        table.add("wa_leave_file");
        table.add("wa_leave_group");
        table.add("wa_leave_quota");
        table.add("wa_leave_quota_config");
        table.add("wa_leave_quota_use");
        table.add("wa_leave_segment");
        table.add("wa_emp_quota_detail");
        table.add("wa_leave_setting");
        table.add("wa_leave_type");
        table.add("wa_leave_type_def");
        table.add("wa_leave_type_group_rel");
        table.add("wa_leave_type_validate");
        table.add("wa_month_confirm_analyze");
        table.add("wa_month_result");
        table.add("wa_notice_config");
        table.add("wa_notify_config");
        table.add("wa_overtime_file");
        table.add("wa_overtime_group");
        table.add("wa_overtime_type");
        table.add("wa_overtime_type_group_rel");
        table.add("wa_parse_group");
        table.add("wa_quota_gen_rule");
        table.add("wa_quota_segment_rel");
        table.add("wa_register_adjust");
        table.add("wa_register_pic");
        table.add("wa_register_record");
        table.add("wa_plan_emp_rel");
        table.add("wa_register_record_bdk");
        table.add("wa_result_confirm");
        table.add("wa_result_confirm_detail");
        table.add("wa_set_object_shift");
        table.add("wa_shift_apply_record");
        table.add("wa_shift_customized");
        table.add("wa_shift_def");
        table.add("wa_shift_rule");
        table.add("wa_store_time_approval");
        table.add("wa_store_time_detail_approval");
        table.add("wa_travel_type");
        table.add("wa_work_round");
        table.add("wa_work_round_shift");
        table.add("wa_workcalendar_detail");
        table.add("wa_workhour_abnormal_appeal");
        table.add("wa_workhour_abnormal_file");
        table.add("wa_workhour_month_analyze");
        table.add("wa_worktime");
        table.add("wa_store_time_detail");
        table.add("wa_worktime_detail");
        table.add("wa_worktime_group");

        String sql = "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   corp_id        TYPE BIGINT USING corp_id::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   corpid         TYPE BIGINT USING corpid::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   crtuser        TYPE BIGINT USING crtuser::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   upduser        TYPE BIGINT USING upduser::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   creator        TYPE BIGINT USING creator::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   updater        TYPE BIGINT USING updater::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   belong_org_id  TYPE VARCHAR(50) USING belong_org_id::VARCHAR(50);\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   orgid          TYPE BIGINT USING orgid::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   empid          TYPE BIGINT USING empid::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   emp_id          TYPE BIGINT USING emp_id::BIGINT;\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   tenant_id          TYPE VARCHAR(50) USING tenant_id::VARCHAR(50);\n" +
                "ALTER TABLE caidaocloud.attendance.%s   ALTER COLUMN   tenant_id          TYPE VARCHAR(50) USING tenant_id::VARCHAR(50);";
        StringBuilder sqlupd = new StringBuilder();
        for (String s : table) {
            sqlupd.append(String.format(sql, s,s,s,s,s,s,s,s,s,s,s,s)).append("\n");
            System.out.println(ReflectionUtils.getPojoNameFromDBName(s));
        }

        StringTokenizer token = new StringTokenizer(sqlupd.toString(), ";");
        while (token.hasMoreElements()){
            sqlList.add(token.nextToken().replace(",", "") + ";");
        }

        return sqlList;
    }
}
