package com.caidaocloud.attendance.service.infrastructure.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/3/8
 **/
public class SqlInjectCheckUtil {
    /**
     * 简单做正则校验，防止sql注入
     *
     * @param sqlExp
     * @return
     */
    public static boolean checkRule(String sqlExp) {
        String reg = "(\\b(select|update|union|delete|insert|trancate|char|into|substr|ascii|declare|exec|count|master|drop|execute)\\b)";
        Pattern sqlPattern = Pattern.compile(reg, Pattern.CASE_INSENSITIVE);
        if (sqlPattern.matcher(sqlExp).find()) {
            return false;
        }
        String singleQuotationReg = "'";
        Pattern singleQuotationPattern = Pattern.compile(singleQuotationReg, Pattern.CASE_INSENSITIVE);
        Matcher m = singleQuotationPattern.matcher(sqlExp);
        int count = 0;
        while (m.find()) {
            count++;
        }
        return !(count % 2 == 1);
    }
}