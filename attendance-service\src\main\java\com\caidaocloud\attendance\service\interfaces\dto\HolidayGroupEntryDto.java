package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HolidayGroupEntryDto implements Serializable {

    @ApiModelProperty("特殊日期分组id")
    private Integer calendarGroupId;

    @ApiModelProperty("特殊日期id")
    private Integer holidayCalendarId;

    @ApiModelProperty("特殊日期分组项id")
    private Integer groupRel;
}
