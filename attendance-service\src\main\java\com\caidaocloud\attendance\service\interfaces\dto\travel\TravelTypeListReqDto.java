package com.caidaocloud.attendance.service.interfaces.dto.travel;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/9/10
 */
@Data
@ApiModel("出差规则搜索")
public class TravelTypeListReqDto extends ExportBasePage {
    @ApiModelProperty("关键字出差规则名称搜索")
    private String keywords;

    @ApiModelProperty("租户ID")
    private String tenantId;
}
