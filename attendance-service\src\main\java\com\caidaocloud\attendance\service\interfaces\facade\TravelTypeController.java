package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.enums.TravelTypeDefEnum;
import com.caidaocloud.attendance.service.application.service.ITravelTypeService;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTransferRulePeriod;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeListReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.TravelTypeSortDto;
import com.caidaocloud.attendance.service.interfaces.vo.TravelTypeVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 出差规则接口
 *
 * <AUTHOR>
 * @Date 2021/9/8
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/traveltype/v1")
@Api(value = "/api/attendance/traveltype/v1", description = "出差规则表接口")
public class TravelTypeController {
    @Resource
    private ITravelTypeService travelTypeService;
    @Autowired
    private EmployeeGroupService employeeGroupService;

    @ApiOperation(value = "新增出差规则", tags = "v1.1")
    @PostMapping(value = "/save")
    @LogRecordAnnotation(success = "新增了{{#name}}", category = "新增", menu = "出差管理-出差规则")
    public Result<Boolean> saveTravelType(@RequestBody TravelTypeDto dto) {
        dto.initTravelTypeName();
        dto.initI18nTravelTypeName();
        if (travelTypeService.checkBeforeSaveOrUpdate(dto)) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_EXIST, Boolean.FALSE);
        }
        travelTypeService.saveOrUpdate(dto);
        LogRecordContext.putVariable("name", dto.getI18nTravelTypeName().get("default"));

        return Result.ok(CommonConstant.TRUE);
    }

    @ApiOperation(value = "修改出差规则", tags = "v1.1")
    @PostMapping(value = "/update")
    @LogRecordAnnotation(success = "编辑了{{#name}}", category = "编辑", menu = "出差管理-出差规则")
    public Result<Boolean> updateTravelType(@RequestBody TravelTypeDto dto) {
        dto.initTravelTypeName();
        dto.initI18nTravelTypeName();
        if (travelTypeService.checkBeforeSaveOrUpdate(dto)) {
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_EXIST, Boolean.FALSE);
        }
        travelTypeService.saveOrUpdate(dto);
        LogRecordContext.putVariable("name", dto.getI18nTravelTypeName().get("default"));
        return Result.ok(CommonConstant.TRUE);
    }

    @ApiOperation(value = "排序", tags = "v1.1")
    @PostMapping(value = "/sort")
    @LogRecordAnnotation(success = "对{{#name}}进行排序", category = "排序", menu = "出差管理-出差规则")
    public Result<Boolean> sort(@RequestBody TravelTypeSortDto dto) {
        WaTravelTypeDo data = travelTypeService.doSort(dto);
        LogRecordContext.putVariable("name", data.getTravelTypeName());
        return Result.ok(CommonConstant.TRUE);
    }

    @ApiOperation(value = "删除出差规则", tags = "v1.1")
    @DeleteMapping(value = "/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "出差管理-出差规则")
    public Result<Boolean> deleteTravelType(@RequestParam("id") Long id) {
        try {
            return travelTypeService.deleteTravelTypeById(id);
        } catch (Exception e) {
            log.error("TravelTypeController.deleteTravelType has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "查询出差规则信息", tags = "v1.1")
    @GetMapping(value = "/detail")
    public Result<TravelTypeVo> getTravelTypeById(@RequestParam("id") Long id) {
        try {
            WaTravelTypeDo typeDo = travelTypeService.selectOneById(id);
            TravelTypeVo travelTypeVo = ObjectConverter.convert(typeDo, TravelTypeVo.class);
            travelTypeVo.setIfAbroad(TravelTypeDefEnum.ABROAD.getIndex().equals(typeDo.getTravelTypeDef()));
            if (StringUtils.isNotBlank(typeDo.getI18nTravelTypeName())) {
                travelTypeVo.setI18nTravelTypeName(FastjsonUtil.toObject(typeDo.getI18nTravelTypeName(), Map.class));
            } else if (StringUtils.isNotBlank(typeDo.getTravelTypeName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", typeDo.getTravelTypeName());
                travelTypeVo.setI18nTravelTypeName(i18nName);
            }
            EmployeeGroupDto employeeGroupDto = employeeGroupService.getEmployeeGroup(String.valueOf(id), "wa_travel_type");
            if (null != employeeGroupDto) {
                travelTypeVo.setGroupExp(employeeGroupDto.getGroupExp());
                travelTypeVo.setGroupNote(employeeGroupDto.getGroupNote());
            }
            if (StringUtil.isNotBlank(typeDo.getAutoTransferRule())) {
                travelTypeVo.setAutoTransferRules(JSON.parseArray(typeDo.getAutoTransferRule(), TravelTransferRulePeriod.class));
            }
            return Result.ok(travelTypeVo);
        } catch (Exception e) {
            log.error("TravelTypeController.getTravelTypeById exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @ApiOperation(value = "获取出差规则分页列表", tags = "v1.1")
    @PostMapping(value = "/list")
    @Security(code = "TravelTypeList")
    public Result<PageResult<?>> getTravelTypePageList(@RequestBody TravelTypeListReqDto dto) throws Exception {
        PageResult<WaTravelTypeDo> pageResult = travelTypeService.getTravelTypePageResult(dto);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return Result.ok(pageResult);
        }
        List<TravelTypeVo> items = Lists.newArrayList();
        for (WaTravelTypeDo item : pageResult.getItems()) {
            TravelTypeVo travelTypeVo = ObjectConverter.convert(item, TravelTypeVo.class);
            travelTypeVo.setTravelTypeName(LangParseUtil.getI18nLanguage(item.getI18nTravelTypeName(), item.getTravelTypeName()));
            items.add(travelTypeVo);
        }
        return Result.ok(new PageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("获取出差类型下拉框")
    @GetMapping(value = "/getTravelTypeList")
    public Result getTravelTypeList() {
        Map map = new HashMap();
        map.put("items", travelTypeService.getTravelTypeList());
        return Result.ok(map);
    }
}
