<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.AnalyzeMapper">
    <select id="getWaAnalyzePageList"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaAnalyzeDo">
        select wa.empid,
               wa.belong_date,
               wa.late_time                          as "lateTime",
               wa.early_time                         as "earlyTime",
               wa.kg_work_time                       as "kgWorkTime",
               wa.work_time                          as "workTime",
               wa.actual_work_time                   as "actualWorkTime",
               wa.register_time                      as "registerTime",
               wa.ot_column_jsob ->> 'time_duration' as "otTime",
            case
                when wa.level_column_jsonb is not null and wa.level_column_jsonb <![CDATA[!=]]> '{}'
                    then (select count(1) total from (select empid,belong_date,(jsonb_each(level_column_jsonb::jsonb)).key as lt,(jsonb_each(level_column_jsonb::jsonb)).value ->> 0 as duration
                            from wa_analyze where analyze_id =wa.analyze_id) tmp
                            where tmp.lt like 'lt_%_key' and tmp.duration::float4 <![CDATA[>]]> 0
                            group by tmp.empid, tmp.belong_date)
                else 0 end                        as "leaveCount",
            case
                when wa.travel_column_jsonb is not null and travel_column_jsonb <![CDATA[!=]]> '{}'
                    then (select count(1) total from (select empid,belong_date,(jsonb_each(travel_column_jsonb::jsonb)).key as lt,(jsonb_each(travel_column_jsonb::jsonb)).value ->> 0 as duration
                            from wa_analyze where analyze_id =wa.analyze_id) tmp
                            where tmp.lt like 'lt_%_key' and tmp.duration::float4 <![CDATA[>]]> 0
                            group by tmp.empid, tmp.belong_date)
                else 0 end                        as "travelCount"
        from wa_analyze wa
        where wa.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and wa.empid in <foreach collection="empIdList" open="(" separator="," item="item" close=")">#{item}</foreach>
          and wa.belong_date between #{startDate} and #{endDate}
        order by wa.empid, wa.belong_date
    </select>
    <select id="getWaAnalyzeByRegisterRecordIds" resultType="com.caidao1.wa.mybatis.model.WaAnalyze">
        select wa.signin_id as "signinId",
               wa.signoff_id as "signoffId"
        from wa_analyze wa
        where wa.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and (wa.signin_id in <foreach collection="registerRecordIds" open="(" separator="," close=")" item="item">#{item}</foreach>
        or wa.signoff_id in <foreach collection="registerRecordIds" open="(" separator="," close=")" item="item">#{item}</foreach>)
    </select>

    <select id="getWaAnalyzeList" resultType="com.caidaocloud.attendance.service.domain.entity.WaAnalyzeDo">
       select
          a.belong_date as "belongDate",
          coalesce (a.late_time,0) as lateTime,
          coalesce (a.early_time,0) as "earlyTime",
          coalesce (a.kg_work_time,0) as "KgWorkTime"
       from wa_analyze a
         join sys_emp_info b on a.empid = b.empid and b.deleted = 0
       where b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and belong_date between #{startDate} and #{endDate}
       and b.empId = #{empId}
    </select>

    <!--获取应到、打卡人数-->
    <select id="getWorkSummaryCount" resultType="java.util.Map">
        WITH emp_shift AS (
            SELECT DISTINCT * FROM (
                SELECT
                    es.empid,
                    sei.orgid,
                    wd.work_date
                FROM wa_emp_shift es
                         JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
                         JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
                         JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id AND sd.date_type = 1
                         JOIN sys_emp_info sei ON sei.empid = es.empid
                WHERE wt.deleted = 0 and wd.work_date BETWEEN #{startDate} AND #{endDate}
                  and wd.work_date between es.start_time and es.end_time
                  AND sei.belong_org_id = #{belongOrgId}
                  AND (sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND #{endDate} <![CDATA[<=]]> sei.termination_date))
            UNION ALL
                SELECT
                    wesc.empid,
                    sei.orgid,
                    wesc.work_date
                FROM wa_emp_shift_change wesc
                    LEFT JOIN sys_emp_info sei ON sei.empid = wesc.empid
                WHERE wesc.belong_org_id = #{belongOrgId} AND wesc.work_date BETWEEN #{startDate} AND #{endDate}
                    AND wesc.status = 2 AND (sei.stats <![CDATA[<>]]> 1 OR (sei.termination_date <![CDATA[<=]]> #{endDate}))
            UNION ALL
                select empid,
                       d.orgid,
                       b.work_date
                from wa_worktime a,
                     wa_worktime_detail b,
                     wa_shift_def c,
                     sys_emp_info d
                where a.belong_orgid = #{belongOrgId}
                  and a.is_default = true
                  and a.deleted = 0
                  and a.work_calendar_id = b.work_calendar_id
                  and b.shift_def_id = c.shift_def_id and b.work_date BETWEEN #{startDate} AND #{endDate}
                  and a.belong_orgid = d.belong_org_id
                  and d.tm_type = 1
                  AND (d.stats <![CDATA[<>]]> 1 OR (d.stats = 1 AND #{endDate} <![CDATA[<=]]> d.termination_date))
                  and d.empid not in (select empid from wa_emp_shift where b.work_date BETWEEN start_time and end_time)
            ) as t
        ),
         emp_register AS (
                 SELECT DISTINCT sei.empid, orgid, wrr.belong_date
                 FROM wa_register_record wrr
                          LEFT JOIN sys_emp_info sei On sei.empid = wrr.empid
                 where if_valid = 1  AND (wrr.approval_status = 2 or is_workflow = false) AND wrr.belong_date BETWEEN #{startDate} AND #{endDate} AND sei.belong_org_id = #{belongOrgId}
        )
        SELECT t1.shiftCount as "workCount", t2.registerCount AS "registerCount"
            FROM (SELECT count(1) as shiftCount FROM emp_shift
                    LEFT JOIN sys_emp_info sei ON sei.empid = emp_shift.empid
                    <where>
                        <if test="orgIds != null and orgIds.length > 0">
                            <foreach collection="orgIds" item="item" open="(" close=")" separator="OR">
                                sei.orgid in (select getsuborgstr('{${item}}'))
                            </foreach>
                        </if>
                        <if test="scope != null and scope != ''">
                            ${scope}
                        </if>
                    </where>
            ) as t1
                LEFT JOIN (SELECT count(1) as registerCount from emp_register
                        LEFT JOIN sys_emp_info sei ON sei.empid = emp_register.empid
                        <where>
                            <if test="orgIds != null and orgIds.length > 0">
                                <foreach collection="orgIds" item="item" open="(" close=")" separator="OR">
                                    sei.orgid in (select getsuborgstr('{${item}}'))
                                </foreach>
                            </if>
                            <if test="scope != null and scope != ''">
                                ${scope}
                            </if>
                        </where>
                    ) t2 ON true
    </select>

    <!--查询考勤汇总-->
    <select id="getStatisticsSummaryCount" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ltTime > 0 THEN 1 ELSE 0 END) AS "ltCount",
            SUM(CASE WHEN otTime > 0 THEN 1 ELSE 0 END) AS "otCount",
            SUM(CASE WHEN trTime > 0 THEN 1 ELSE 0 END) AS "trCount",
            SUM(CASE WHEN lateTime > 0 THEN 1 ELSE 0 END) AS "lateCount",
            SUM(CASE WHEN earlyTime > 0 THEN 1 ELSE 0 END) AS "earlyCount"
        FROM (
            SELECT coalesce(SUM(CASE WHEN level_column_jsonb ->> 'time_duration' > '0' THEN 1 ELSE 0 END), 0)  AS ltTime,
                   coalesce(SUM(CASE WHEN ot_column_jsob ->> 'time_duration' > '0' THEN 1 ELSE 0 END), 0)      AS otTime,
                   coalesce(SUM(CASE WHEN travel_column_jsonb ->> 'time_duration' > '0' THEN 1 ELSE 0 END), 0) AS trTime,
                   coalesce(SUM(CASE WHEN late_time > 0 THEN 1 ELSE 0 END), 0)                                AS lateTime,
                   coalesce(SUM(CASE WHEN early_time > 0 THEN 1 ELSE 0 END), 0)                               AS earlyTime
            FROM wa_analyze wa
                     INNER JOIN sys_emp_info sei ON sei.empid = wa.empid
            <where>
                ((sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND #{endDate} <![CDATA[<=]]> sei.termination_date)))
                    AND wa.belong_date BETWEEN #{startDate} AND #{endDate} AND sei.belong_org_id = #{belongOrgId}
                <if test="orgIds != null and orgIds.length > 0">
                    <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                        sei.orgid in (select getsuborgstr('{${item}}'))
                    </foreach>
                </if>
                <if test="scope != null and scope != ''">
                    ${scope}
                </if>
            </where>
            GROUP BY wa.empid
        ) AS t
    </select>

    <!--查询考勤汇总工作详情-->
    <select id="getSummaryWorkList" resultType="java.util.Map">
        WITH emp_shift AS (
            SELECT DISTINCT * FROM (
                SELECT
                    es.empid,
                    wd.work_date
                FROM wa_emp_shift es
                    JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
                    JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
                    JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id AND sd.date_type = 1
                    JOIN sys_emp_info sei ON sei.empid = es.empid
                WHERE wt.deleted = 0
                    AND wd.work_date BETWEEN #{startDate} AND #{endDate}
                    AND sei.belong_org_id = #{belongOrgId}
                    AND (sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND #{endDate} <![CDATA[<=]]> sei.termination_date))
                    AND wd.work_date BETWEEN es.start_time AND es.end_time
                UNION ALL
                    SELECT
                        wesc.empid,
                        wesc.work_date
                    FROM wa_emp_shift_change wesc
                        LEFT JOIN sys_emp_info sei ON sei.empid = wesc.empid
                    WHERE wesc.belong_org_id = #{belongOrgId} AND wesc.work_date BETWEEN #{startDate} AND #{endDate}
                        AND wesc.status = 2 AND (sei.stats <![CDATA[<>]]> 1 OR (sei.termination_date <![CDATA[<=]]> #{endDate}))
                UNION ALL
                    SELECT
                        empid,
                        b.work_date
                    FROM wa_worktime a,
                        wa_worktime_detail b,
                        wa_shift_def c,
                        sys_emp_info d
                    WHERE a.belong_orgid = #{belongOrgId}
                        AND a.is_default = TRUE
                        AND a.deleted = 0
                        AND a.work_calendar_id = b.work_calendar_id
                        AND b.shift_def_id = c.shift_def_id
                        AND b.work_date BETWEEN #{startDate} AND #{endDate}
                        AND a.belong_orgid = d.belong_org_id
                        AND d.tm_type = 1
                        AND (d.stats <![CDATA[<>]]> 1 OR (d.stats = 1 AND #{endDate} <![CDATA[<=]]> d.termination_date))
                        AND d.empid NOT IN (SELECT empid FROM wa_emp_shift WHERE b.work_date BETWEEN start_time AND end_time)
                ) as t
        )
        SELECT
            <choose>
                <when test="workType != null and workType > 0">
                    wrr.belong_date                                           AS "belongDate",
                </when>
               <otherwise>
                    es.work_date                                              AS "belongDate",
               </otherwise>
            </choose>
            sei.workno                                                        AS "workNo",
            sei.emp_name                                                      AS "empName",
            sco.fullname                                                      AS "orgName",
            CASE
                WHEN wrr.type = 1 THEN 'GPS'
                WHEN wrr.type = 2 THEN '扫码签到'
                WHEN wrr.type = 3 THEN '外勤签到'
                WHEN wrr.type = 4 THEN 'WIFI'
                WHEN wrr.type = 6 THEN '补打卡' END                            AS "signType",
            wrr.reg_date_time                                                 AS "signTime",
            wrr.reg_addr                                                      AS "signAddress",
            wrr.if_valid                                                      AS "signStatus",
            wrr.mob_device_num                                                AS "deviceNo",
            wrr.belong_date                                                   AS "signBelongDate",
            CASE
                WHEN wrr.source_from_type = 0 THEN '考勤'
                WHEN wrr.source_from_type = 1 THEN '企业微信'
                WHEN wrr.source_from_type = 2 THEN '飞书'
                WHEN wrr.source_from_type = 3 THEN '钉钉' END                  AS "source"
         <choose>
             <when test="workType != null and workType > 0">
                 FROM wa_register_record wrr
                     LEFT JOIN emp_shift es ON es.empid = wrr.empid AND es.work_date = wrr.belong_date
                     LEFT JOIN sys_emp_info sei ON sei.empid = es.empid
                     LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid
                <where>
                    wrr.record_id is not null AND wrr.if_valid = 1 AND (wrr.approval_status = 2 OR wrr.is_workflow = FALSE)
                        AND (sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND #{endDate} <![CDATA[<=]]> sei.termination_date))
                    <if test="orgIds != null and orgIds.length > 0">
                        <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                            sei.orgid in (select getsuborgstr('{${item}}'))
                        </foreach>
                    </if>
                    <if test="scope != null and scope != ''">
                        ${scope}
                    </if>
                    <if test="keywords != null and keywords != ''">
                        AND (sei.emp_name like concat('%', #{keywords}, '%') OR sei.workno like concat('%', #{keywords}, '%'))
                    </if>
                </where>
             </when>
             <otherwise>
                 FROM emp_shift es
                     LEFT JOIN wa_register_record wrr ON es.empid = wrr.empid AND es.work_date = wrr.belong_date
                     LEFT JOIN sys_emp_info sei ON sei.empid = es.empid
                     LEFT JOIN sys_corp_org sco ON sco.orgid = sei.orgid
                <where>
                    es.work_date BETWEEN #{startDate} AND #{endDate} AND sei.belong_org_id = #{belongOrgId}
                    <if test="orgIds != null and orgIds.length > 0">
                        <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                            sei.orgid in (select getsuborgstr('{${item}}'))
                        </foreach>
                    </if>
                    <if test="scope != null and scope != ''">
                        ${scope}
                    </if>
                    <if test="keywords != null and keywords != ''">
                        AND (sei.emp_name like concat('%', #{keywords}, '%') OR sei.workno like concat('%', #{keywords}, '%'))
                    </if>
                </where>
             </otherwise>
         </choose>
    </select>
    <!--计算考勤汇总出勤率-->
    <select id="getSummaryWorkRate" resultType="java.util.Map">
        WITH emp_shift AS (
            SELECT DISTINCT * FROM (
                SELECT
                       sei.empid,
                       wd.work_date,
                       sei.orgid
                    FROM wa_emp_shift es
                        JOIN wa_worktime wt ON es.work_calendar_id = wt.work_calendar_id
                        JOIN wa_worktime_detail wd ON wt.work_calendar_id = wd.work_calendar_id
                        JOIN wa_shift_def sd ON wd.shift_def_id = sd.shift_def_id AND sd.date_type = 1
                        JOIN sys_emp_info sei ON sei.empid = es.empid
                    <where>
                        wt.deleted = 0
                        AND wd.work_date BETWEEN #{startDate} AND #{endDate}
                        AND sei.belong_org_id = #{belongOrgId}
                        AND (sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND #{endDate} <![CDATA[<=]]> sei.termination_date))
                        AND wd.work_date BETWEEN es.start_time AND es.end_time
                        <if test="scope != null and scope != ''">
                            ${scope}
                        </if>
                        <if test="orgIds != null and orgIds.length > 0">
                            <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                                sei.orgid in (select getsuborgstr('{${item}}'))
                            </foreach>
                        </if>
                    </where>
                UNION ALL
                    SELECT
                        wesc.empid,
                        wesc.work_date,
                        sei.orgid
                    FROM wa_emp_shift_change wesc
                        LEFT JOIN sys_emp_info sei ON sei.empid = wesc.empid
                    <where>
                        wesc.belong_org_id = #{belongOrgId} AND wesc.work_date BETWEEN #{startDate} AND #{endDate}
                        AND wesc.status = 2 AND (sei.stats <![CDATA[<>]]> 1 OR (sei.termination_date <![CDATA[<=]]> #{endDate}))
                        <if test="scope != null and scope != ''">
                            ${scope}
                        </if>
                        <if test="orgIds != null and orgIds.length > 0">
                            <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                                sei.orgid in (select getsuborgstr('{${item}}'))
                            </foreach>
                        </if>
                    </where>
                UNION ALL
                    SELECT
                           empid,
                           b.work_date,
                           d.orgid
                        FROM wa_worktime a,
                            wa_worktime_detail b,
                            wa_shift_def c,
                            sys_emp_info d
                        WHERE a.belong_orgid = #{belongOrgId}
                            AND a.is_default = TRUE
                            AND a.deleted = 0
                            AND a.work_calendar_id = b.work_calendar_id
                            AND b.shift_def_id = c.shift_def_id
                            AND b.work_date BETWEEN #{startDate} AND #{endDate}
                            AND a.belong_orgid = d.belong_org_id
                            AND d.tm_type = 1
                            AND (d.stats <![CDATA[<>]]> 1 OR (d.stats = 1 AND #{endDate} <![CDATA[<=]]> d.termination_date))
                                AND d.empid NOT IN (SELECT empid
                                    FROM wa_emp_shift
                                WHERE b.work_date BETWEEN start_time AND end_time
                    )) as t),
        emp_register AS (
            SELECT DISTINCT sei.empid, sei.orgid
                FROM wa_register_record wrr
                    LEFT JOIN sys_emp_info sei ON sei.empid = wrr.empid
                <where>
                    if_valid = 1
                    AND wrr.belong_date BETWEEN #{startDate} AND #{endDate}
                    AND wrr.belong_org_id = #{belongOrgId}
                    AND sei.empid is not null
                    <if test="scope != null and scope != ''">
                        ${scope}
                    </if>
                    <if test="orgIds != null and orgIds.length > 0">
                        <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                            sei.orgid in (select getsuborgstr('{${item}}'))
                        </foreach>
                    </if>
                </where>
        ),
        emp_org AS (
            <choose>
                <when test="orgIds != null and orgIds.length > 0">
                    <foreach collection="orgIds" item="item" separator=" UNION ">
                        select #{item} as orgid
                    </foreach>
                </when>
                <otherwise>
                    SELECT DISTINCT orgid as orgid from emp_shift WHERE orgid is not null
                </otherwise>
            </choose>
        )
        SELECT eo.orgid, work.count AS "workCount", register.count AS "registerCount", sco.fullname
                FROM emp_org eo
                    LEFT JOIN LATERAL (SELECT COUNT(1) AS count
                        FROM emp_shift es
                    WHERE es.orgid = ANY (SELECT getsuborgstr(ARRAY [eo.orgid]))) AS work ON TRUE
                    LEFT JOIN LATERAL (SELECT COUNT(1) AS count
                        FROM emp_register er
                    WHERE er.orgid = ANY (SELECT getsuborgstr(ARRAY [eo.orgid]))) AS register ON TRUE
                    LEFT JOIN sys_corp_org sco On sco.orgid = eo.orgid
    </select>
    <!--查询考勤汇总工作、加班时长-->
    <select id="getSummaryTimeRate" resultType="java.util.Map">
        SELECT
               sco.orgid,
               sco.fullname,
               coalesce(SUM(workTime), 0)   AS "workTime",
               coalesce(SUM(t.empcount), 0) AS "empCount",
               coalesce(SUM(otTime), 0)     AS "otTime"
        FROM (
                WITH emp_statis AS (
                    SELECT sei.empid, sei.orgid, SUM(CASE WHEN actual_work_time IS NULL THEN 0 ELSE actual_work_time END) AS work, SUM(coalesce(ot_column_jsob ->> 'time_duration', '0')::float) AS ot
                    FROM wa_analyze wa
                        INNER JOIN sys_emp_info sei ON sei.empid = wa.empid
                    <where>
                        (sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND #{endDate} <![CDATA[<=]]> sei.termination_date))
                        AND wa.belong_date BETWEEN #{startDate} AND #{endDate} AND sei.belong_org_id = #{belongOrgId}
                        <if test="scope != null and scope != ''">
                            ${scope}
                        </if>
                        <if test="orgIds != null and orgIds.length > 0">
                            <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                                sei.orgid in (select getsuborgstr('{${item}}'))
                            </foreach>
                        </if>
                    </where>
                    GROUP BY sei.empid, sei.orgid
        ),
        emp_org AS (
            <choose>
                <when test="orgIds != null and orgIds.length > 0">
                    <foreach collection="orgIds" item="item" separator=" UNION ">
                        select #{item} as orgid
                    </foreach>
                </when>
            <otherwise>
                SELECT DISTINCT orgid FROM emp_statis WHERE orgid IS NOT NULL
            </otherwise>
            </choose>
        )
        SELECT org_count.count AS empcount, eo.orgid, workTime, otTime
                    FROM emp_org eo
                        LEFT JOIN (SELECT COUNT(1) AS count, orgid
                            FROM sys_emp_info sei
                        <where>
                            sei.belong_org_id = #{belongOrgId} AND sei.deleted = 0
                                AND (sei.stats <![CDATA[<>]]> 1 or (sei.stats = 1 AND sei.termination_date <![CDATA[>=]]> #{endDate} ))
                            <if test="scope != null and scope != ''">
                                ${scope}
                            </if>
                        </where>
                        GROUP BY orgid) AS org_count
                    ON org_count.orgid = ANY (SELECT getsuborgstr(ARRAY [eo.orgid]))
                LEFT JOIN (SELECT orgid, SUM(work) AS workTime, SUM(ot) AS otTime
                    FROM emp_statis
                        GROUP BY orgid) AS emp_count
                ON emp_count.orgid = ANY (SELECT getsuborgstr(ARRAY [eo.orgid]))
            ) AS t LEFT JOIN sys_corp_org sco ON sco.orgid = t.orgid GROUP BY sco.orgid, sco.fullname;
    </select>
    <!--考勤汇总休假类型报表-->
    <select id="getSummaryLtRate" resultType="java.util.Map">
        WITH emp_leava AS (
            SELECT  sei.orgid,
                    c.leave_type_id,
                    COUNT(a.empid) AS empCount
            FROM wa_emp_leave a
                    JOIN sys_emp_info sei ON a.empid = sei.empid AND sei.deleted = 0
                    JOIN wa_leave_type c ON a.leave_type_id = c.leave_type_id
                    JOIN wa_leave_type_def d ON d.leave_type_def_id = c.leave_type
                    JOIN wa_emp_leave_time welt ON welt.leave_id = a.leave_id
                    JOIN (select distinct leave_id
                          from wa_leave_daytime
                          where time_duration - cancel_time_duration > 0
                          and leave_date between #{startDate} and #{endDate}) tmp on tmp.leave_id = a.leave_id
                    LEFT JOIN sys_corp_org sco ON sei.orgid = sco.orgid AND sco.deleted = 0
            <where>
                (sei.stats <![CDATA[<>]]> 1 OR (sei.stats = 1 AND sei.termination_date <![CDATA[>=]]> #{endDate}))
                AND sei.belong_org_id = #{belongOrgId} AND a.status IN (2)
                    AND ((#{startDate} BETWEEN welt.start_time AND welt.end_time)
                        OR (#{endDate} BETWEEN welt.start_time AND welt.end_time)
                        OR (welt.start_time <![CDATA[>]]> #{startDate} AND welt.shift_end_time <![CDATA[<]]> #{endDate}))
                    <if test="scope != null and scope != ''">
                        ${scope}
                    </if>
                    <if test="orgIds != null and orgIds.length > 0">
                        <foreach collection="orgIds" item="item" open=" AND (" close=")" separator="OR">
                            sei.orgid in (select getsuborgstr('{${item}}'))
                        </foreach>
                    </if>
            </where>
            GROUP BY sei.orgid, c.leave_type_id
        )
        <if test="orgIds != null and orgIds.length > 0">
            <foreach collection="orgIds" open=", emp_org as (" close=")" item="item" separator=" UNION ">
                select #{item} as orgid
            </foreach>
        </if>
        SELECT wlt.leave_type_id, wlt.leave_name AS "leaveName", SUM(el.empCount) AS "empCount"
            FROM emp_leava el
                <if test="orgIds != null and orgIds.length > 0">
                    INNER JOIN emp_org ON (el.orgid = ANY (SELECT getsuborgstr(ARRAY [emp_org.orgid])))
                </if>
                LEFT JOIN wa_leave_type wlt ON wlt.leave_type_id = el.leave_type_id
        GROUP BY wlt.leave_type_id, wlt.leave_name;
    </select>

</mapper>