package com.caidaocloud.attendance.service.interfaces.vo.register;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("门户补打卡信息vo")
public class WaRegisterRecordOfPortalVo {
    @ApiModelProperty("记录id")
    private Long recordId;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("补卡类型")
    private Integer registerType;
    @ApiModelProperty("补卡时间")
    private Long regDateTime;
    @ApiModelProperty("补卡原因")
    private String reason;
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;
    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;
    @ApiModelProperty("最近审批时间")
    private Long lastApprovalTime;
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("流程key")
    private String businessKey;
    private String bdkRecordId;
}