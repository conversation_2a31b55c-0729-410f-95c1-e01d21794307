package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.report.common.ExportBaseController;
import com.caidao1.report.dto.FilterBean;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.enums.TimePeriodEnum;
import com.caidaocloud.attendance.service.application.service.IExportService;
import com.caidaocloud.attendance.service.application.service.ITaskService;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.export.ExportEmpClockPlanDto;
import com.caidaocloud.attendance.service.interfaces.dto.export.ExportEmpShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.LeaveExtensionReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OtLeftDurationRequestDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensatoryQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaEmpDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 导出接口
 *
 * <AUTHOR>
 * @Date 2021/3/17
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/export/v1")
@Api(value = "/api/attendance/export/v1", description = "导出")
public class ExportController extends ExportBaseController {
    @Autowired
    private IExportService exportService;

    @Autowired
    private ITaskService taskService;

    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @PostMapping(value = "/exportEmpShiftChangeList")
    @ApiOperation("导出员工班次调整记录")
    public Result<TaskDto> exportEmpShiftChangeList(@RequestBody EmpShiftSearchDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("empShiftChange");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_SHIFT_CHANGE_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(orgDataScope);
        }
        log.info("导出员工班次调整记录 DataScope = {}", dataScope);
        exportService.uploadEmpShiftChangeData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportShiftDefList")
    @ApiOperation("导出班次")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "考勤设置-班次设置-班次")
    public Result<TaskDto> exportShiftDefList(@RequestBody ShiftPageDto shiftPageDto) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("shift");
        TaskDto task = taskService.save(taskDto);
        exportService.uploadData(shiftPageDto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportRegisterList")
    @ApiOperation("导出上下班打卡记录")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "打卡记录-日常打卡")
    public Result<TaskDto> exportRegisterList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) {
        requestDto.setTypes(new ArrayList<>(Arrays.asList(1, 2, 4, 5)));
        TaskDto taskDto = new TaskDto();
        taskDto.setType("register");
        TaskDto task = taskService.save(taskDto);
        requestDto.setAnalyze(Boolean.TRUE);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出上下班打卡记录 dataScope = {}", dataScope);
        exportService.uploadData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportAllRegisterList")
    @ApiOperation("导出所有类型打卡记录")
    public Result<TaskDto> exportAllRegisterList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) {
        requestDto.setIfShowAll(Boolean.TRUE);
        requestDto.setAnalyze(Boolean.TRUE);
        TaskDto taskDto = new TaskDto();
        taskDto.setType("register");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.ALL_REGISTER_BDK_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出打卡记录 dataScope = {}", dataScope);
        exportService.uploadData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @GetMapping("/taskProgress")
    @ApiOperation("查询任务进度")
    public Result<TaskDto> taskProgress(@RequestParam("id") Long id) {
        TaskDto task = taskService.getTaskById(id);
        if (null == task) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "任务不存在", null);
        }
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportBdkList")
    @ApiOperation("导出补卡记录")
    public Result<TaskDto> exportBdkList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) {
        requestDto.setTypes(new ArrayList<>(Collections.singletonList(6)));
        TaskDto taskDto = new TaskDto();
        taskDto.setType("bk");
        TaskDto task = taskService.save(taskDto);
        requestDto.setAnalyze(Boolean.TRUE);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_BDK_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出补卡记录 dataScope = {}", dataScope);
        exportService.uploadBdkData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportAnalyzeRegisterList")
    @ApiOperation("导出每日考勤")
    public Result<TaskDto> exportAnalyzeRegisterList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) {
        requestDto.setTypes(new ArrayList<>(Arrays.asList(1, 2, 4, 5, 6)));
        requestDto.setShowType(1);
        TaskDto taskDto = new TaskDto();
        taskDto.setType("day");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.REGISTER_ANALYZE_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        log.info("导出每日考勤 dataScope = {}", dataScope);
        exportService.uploadData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportLeaveList")
    @ApiOperation("导出休假申请列表")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "休假管理-休假记录-休假记录")
    public Result<TaskDto> exportLeaveList(@RequestBody LeaveApplyDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("leave");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_APPLY_RECORD_LIST, "b");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出休假申请列表 DataScope = {}", dataScope);
        exportService.uploadData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportLeaveCancelList")
    @ApiOperation("导出销假申请列表")
    public Result<TaskDto> exportLeaveCancelList(@RequestBody LeaveCancelReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("leaveCancel");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_APPLY_RECORD_LIST, "b");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "c.orgid")
                    .replaceAll("empid", "c.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出销假申请列表 DataScope = {}", dataScope);
        exportService.uploadLeaveCancelData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportOtList")
    @ApiOperation("导出加班记录列表")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "加班管理-加班记录")
    public Result<TaskDto> exportOtList(@RequestBody OvertimeApplyDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("overtime");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OVERTIME_APPLY_RECORD_LIST, "b");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出加班记录列表 dataScope = {}", dataScope);
        exportService.uploadData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportEmpQuotaList")
    @ApiOperation("导出假期配额列表")
    public Result<TaskDto> exportEmpQuotaList(@RequestBody QuotaPageDto requestDto, HttpServletRequest request) throws Exception {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.QUOTA_LIST, "ei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出假期配额列表 dataScope = {}", dataScope);
        //由于年份查询放到了更多筛选里面，因此需要从更多筛选条件中取出来作为单独参数
        List<FilterBean> filterList = requestDto.getFilterList();
        if (CollectionUtils.isNotEmpty(filterList)) {
            Optional<String> yearFilters = filterList.stream().filter(st -> "period_year".equals(st.getField()) && StringUtils.isNotEmpty(st.getMin())).map(FilterBean::getMin).findFirst();
            if (yearFilters.isPresent()) {
                requestDto.setYears(Arrays.stream(yearFilters.get().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                filterList.removeIf(st -> "period_year".equals(st.getField()) && StringUtils.isNotEmpty(st.getMin()));
            }
        }
        requestDto.setFilterList(filterList);
        TaskDto taskDto = new TaskDto();
        taskDto.setType("empquota");
        TaskDto task = taskService.save(taskDto);
        exportService.uploadData(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出考勤统计每日明细列表")
    @PostMapping("/exportDayAnalyseList")
    public Result<TaskDto> getDayAnalyseList(@RequestBody DayAnalysePageDto dto, HttpServletRequest request) throws Exception {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("dayanalyse");
        TaskDto task = taskService.save(taskDto);
        //数据权限
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_DAY_ANALYSE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出考勤统计每日明细列表 dataScope = {}", dataScope);
        exportService.uploadData(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    /**
     * 需求：CAIDAOM-2391
     */
    @ApiOperation("导出考勤统计每日明细列表——动态列")
    @PostMapping("/exportDayAnalyseList/Dynamic")
    public Result<TaskDto> getDayAnalyseListForDynamic(@RequestBody DayAnalysePageDto dto, HttpServletRequest request) throws Exception {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("dayanalyse");
        TaskDto task = taskService.save(taskDto);
        //数据权限
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_DAY_ANALYSE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出考勤统计每日明细列表 dataScope = {}", dataScope);
        exportService.uploadDataByDynamic(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出考勤统计月度汇总分页列表")
    @PostMapping(value = "/exportRegisterStatistics")
    public Result<TaskDto> searchRegisterStatistics(@RequestBody MonthAnalysePageDto dto, HttpServletRequest request) throws Exception {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("registerstatistics");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出考勤统计月度汇总分页列表 dataScope = {}", dataScope);
        exportService.uploadData(dto, task, false, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出考勤统计月度汇总分页列表——动态列")
    @PostMapping(value = "/exportRegisterStatistics/dynamic")
    public Result<TaskDto> searchRegisterStatisticsForDynamic(@RequestBody MonthAnalysePageDto dto, HttpServletRequest request) throws Exception {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("registerstatistics");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出考勤统计月度汇总分页列表 dataScope = {}", dataScope);
        exportService.uploadDataByDynamic(dto, task, false, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }


    @ApiOperation("导出考勤明细汇总分页列表")
    @PostMapping(value = "/exportRegisterAdvanceStatistics")
    public Result<TaskDto> exportRegisterAdvanceStatistics(@RequestBody MonthAnalysePageDto dto, HttpServletRequest request) throws Exception {
        TimePeriodEnum periodEnum = dto.getTimePeriod();
        ImmutablePair<Long, Long> timePeriod = TimePeriodEnum.parsePeriod(periodEnum);
        if (timePeriod == null && (dto.getStartDate() == null || dto.getEndDate() == null)) {
            throw new ServerException("查询时间错误");
        } else if (timePeriod != null) {
            dto.setStartDate(timePeriod.getLeft());
            dto.setEndDate(timePeriod.getRight());
        }
        TaskDto taskDto = new TaskDto();
        taskDto.setType("exportRegisterAdvancestatistics");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_ADVANCE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出考勤统计月度汇总分页列表 dataScope = {}", dataScope);
        exportService.uploadData(dto, task, true, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出考勤明细汇总分页列表-动态列")
    @PostMapping(value = "/exportRegisterAdvanceStatistics/dynamic")
    public Result<TaskDto> exportRegisterAdvanceStatisticsForDynamic(@RequestBody MonthAnalysePageDto dto, HttpServletRequest request) throws Exception {
        TimePeriodEnum periodEnum = dto.getTimePeriod();
        ImmutablePair<Long, Long> timePeriod = TimePeriodEnum.parsePeriod(periodEnum);
        if (timePeriod == null && (dto.getStartDate() == null || dto.getEndDate() == null)) {
            throw new ServerException("查询时间错误");
        } else if (timePeriod != null) {
            dto.setStartDate(timePeriod.getLeft());
            dto.setEndDate(timePeriod.getRight());
        }
        TaskDto taskDto = new TaskDto();
        taskDto.setType("exportRegisterAdvancestatistics");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_ADVANCE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出考勤统计月度汇总分页列表 dataScope = {}", dataScope);
        exportService.uploadDataAdvanceByDynamic(dto, task, true, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出固定配额列表")
    @PostMapping(value = "/exportEmpFixQuotaList")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "休假管理-假期余额-固定额度明细")
    public Result<TaskDto> exportEmpFixQuotaList(@RequestBody FixQuotaSearchDto dto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("fix_quota");
        TaskDto task = taskService.save(taskDto);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "emp.orgid")
                    .replaceAll("empid", "emp.empid");
            dto.setDataScope(orgDataScope);
        }
        exportService.uploadEmpFixQuotaData(dto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出调休明细列表")
    @PostMapping(value = "/exportEmpCompensatoryQuotaList")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "休假管理-假期余额-调休明细")
    public Result<TaskDto> exportEmpCompensatoryQuotaList(@RequestBody CompensatoryQuotaSearchDto dto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("compensatory_quota");
        TaskDto task = taskService.save(taskDto);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "emp.orgid")
                    .replaceAll("empid", "emp.empid");
            dto.setDataScope(orgDataScope);
        }
        exportService.uploadEmpCompensatoryQuotaData(dto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出年假明细列表")
    @PostMapping(value = "/exportAnnualQuotaList")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "休假管理-假期余额-年度假期明细")
    public Result<TaskDto> exportAnnualQuotaList(@RequestBody AnnualLeaveSearchDto dto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("annual_quota");
        TaskDto task = taskService.save(taskDto);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        exportService.uploadAnnualQuotaData(dto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出假期余额")
    @PostMapping(value = "/exportQuotaList")
    public Result<TaskDto> exportQuotaList(@RequestBody QuotaEmpDto dto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("emp_quota");
        TaskDto task = taskService.save(taskDto);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        exportService.uploadEmpQuotaData(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportEmpShiftList")
    @ApiOperation("导出员工日历（班次）")
    public Result<TaskDto> exportEmpShiftList(@RequestBody ExportEmpShiftDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("empShift");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_SHIFT_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出员工日历（班次） DataScope = {}", dataScope);
        exportService.exportEmpShiftList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出员工考勤方案")
    @PostMapping(value = "/exportEmpGroupList")
    public Result<TaskDto> exportEmpGroupList(@RequestBody AttEmpGroupReqDto dto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("empGroup");
        TaskDto task = taskService.save(taskDto);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        exportService.uploadEmpGroupChangeData(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportOutworkList")
    @ApiOperation("导出外勤记录")
    public Result<TaskDto> exportOutworkList(@RequestBody RegisterRecordRequestDto requestDto, HttpServletRequest request) {
        requestDto.setTypes(new ArrayList<>(Arrays.asList(3)));
        TaskDto taskDto = new TaskDto();
        taskDto.setType("outwork");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OUTWORK_REGISTER_RECORD_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        requestDto.setAnalyze(Boolean.TRUE);
        log.info("导出外勤记录 dataScope = {}", dataScope);
        exportService.exportOutworkList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportEmpClockPlanList")
    @ApiOperation("导出员工打卡方案")
    public Result<TaskDto> exportEmpClockPlanList(@RequestBody ExportEmpClockPlanDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("clockPlan");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_CLOCK_PLAN_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("导出员工打卡方案 DataScope = {}", dataScope);
        exportService.exportEmpClockPlanList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出出差记录")
    @PostMapping(value = "/exportTravelList")
    @LogRecordAnnotation(success = "导出了数据", category = "导出", menu = "出差管理-出差规则")
    public Result<TaskDto> exportTravelList(@RequestBody EmpTravelReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("EmpTravel");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_TRAVEL_LIST, "sei");
        requestDto.setDataScope(dataScope);
        log.info("导出员工出差申请 DataScope = {}", dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        exportService.exportEmpTravelList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出异常汇总记录")
    @PostMapping(value = "/exportDayAnalyseAbnormalList")
    @LogRecordAnnotation(success = "导出了异常汇总", category = "异常汇总", menu = "考勤统计-考勤统计-每日统计")
    public Result<TaskDto> exportDayAnalyseAbnormalList(@RequestBody DayAnalyseAbnormalPageDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("DayAnalyseAbnormalList");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_DAY_ANALYSE_ABNORMAL_LIST, "e");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        exportService.exportDayAnalyseAbnormalList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());

        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出加班撤销流程记录")
    @PostMapping(value = "/exportOvertimeRevokeList")
    public Result<TaskDto> exportOvertimeRevokeList(@RequestBody WorkflowRevokeReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("OvertimeRevokeList");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OVERTIME_WORKFLOW_REVOKE_LIST, "b");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("加班流程撤销列表 DataScope = {}", dataScope);
        exportService.exportOvertimeRevokeList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出加班废止流程记录")
    @PostMapping(value = "/exportOvertimeAbolishList")
    public Result<TaskDto> exportOvertimeAbolishList(@RequestBody WorkflowRevokeReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("OvertimeAbolishList");
        TaskDto task = taskService.save(taskDto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.OVERTIME_WORKFLOW_ABOLISH_LIST, "b");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        log.info("加班流程废止列表 DataScope = {}", dataScope);
        exportService.exportOvertimeAbolishList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出出差撤销流程记录")
    @PostMapping(value = "/exportTravelRevokeList")
    public Result<TaskDto> exportTravelRevokeList(@RequestBody WorkflowRevokeReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("TravelRevokeList");
        TaskDto task = taskService.save(taskDto);
        String dataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(dataScope)) {
            dataScope = dataScope.replaceAll("orgid", "ei.orgid");
            requestDto.setDataScope(dataScope);
        }
        log.info("出差流程撤销列表 DataScope = {}", dataScope);
        exportService.exportTravelRevokeList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @ApiOperation("导出出差废止流程记录")
    @PostMapping(value = "/exportTravelAbolishList")
    public Result<TaskDto> exportTravelAbolishList(@RequestBody WorkflowRevokeReqDto requestDto, HttpServletRequest request) {
        TaskDto taskDto = new TaskDto();
        taskDto.setType("TravelAbolishList");
        TaskDto task = taskService.save(taskDto);
        String dataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(dataScope)) {
            dataScope = dataScope.replaceAll("orgid", "ei.orgid");
            requestDto.setDataScope(dataScope);
        }
        log.info("出差流程废止列表 DataScope = {}", dataScope);
        exportService.exportTravelAbolishList(requestDto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportBatchLeaveList")
    @ApiOperation("导出批量休假申请列表")
    public Result<TaskDto> exportBatchLeaveList(@RequestBody BatchLeaveQueryDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.BATCH_LEAVE_APPLY_RECORD_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出批量休假申请列表 DataScope = {}", dataScope);

        TaskDto taskDto = new TaskDto();
        taskDto.setType("batchLeave");
        TaskDto task = taskService.save(taskDto);
        exportService.uploadBatchLeaveData(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportBatchOvertimeList")
    @ApiOperation("导出批量加班申请列表")
    public Result<TaskDto> exportBatchOvertimeList(@RequestBody BatchOvertimeQueryDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.BATCH_OVERTIME_APPLY_RECORD_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出批量加班申请列表 DataScope = {}", dataScope);

        TaskDto taskDto = new TaskDto();
        taskDto.setType("batchOvertime");
        TaskDto task = taskService.save(taskDto);
        exportService.uploadBatchOvertimeData(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportBatchAnalyseAdjustList")
    @ApiOperation("导出批量考勤异常申请列表")
    public Result<TaskDto> exportBatchAnalyseAdjustList(@RequestBody BatchAnalyseResultAdjustQueryDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.BATCH_ANALYSE_RESULT_ADJUST_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出批量考勤异常申请列表 DataScope = {}", dto.getDataScope());

        TaskDto taskDto = new TaskDto();
        taskDto.setType("batchAnalyseAdjust");
        TaskDto task = taskService.save(taskDto);
        exportService.uploadBatchAnalyseAdjustData(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportLeaveExtensionList")
    @ApiOperation("导出假期延期申请列表")
    public Result<TaskDto> exportLeaveExtensionList(@RequestBody LeaveExtensionReqDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_EXTENSION_APPLY_LIST, "b");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出假期延期申请列表 DataScope = {}", dto.getDataScope());
        TaskDto taskDto = new TaskDto();
        taskDto.setType("LeaveExtension");
        TaskDto task = taskService.save(taskDto);
        exportService.exportLeaveExtensionList(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportEmpOtLeftDurationList")
    @ApiOperation("导出加班结余时长列表")
    public Result<TaskDto> exportEmpOtLeftDurationList(@RequestBody OtLeftDurationRequestDto dto, HttpServletRequest request) {
        String dataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(dataScope)) {
            dto.setDataScope(dataScope);
        }
        log.info("加班结余时长列表 dataScope = {}", dataScope);
        TaskDto taskDto = new TaskDto();
        taskDto.setType("EmpOtLeftDurationList");
        TaskDto task = taskService.save(taskDto);
        if (StringUtil.isBlank(dto.getFileName())) {
            dto.setFileName("加班结余");
        }
        if (StringUtil.isBlank(dto.getType())) {
            dto.setType("xlsx");
        }
        exportService.exportEmpOtLeftDurationList(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportLeaveRevokeList")
    @ApiOperation("导出休假撤销申请列表")
    public Result<TaskDto> exportLeaveRevokeList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_REVOKE_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid").replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出休假撤销申请列表 DataScope = {}", dataScope);
        TaskDto taskDto = new TaskDto();
        taskDto.setType("LeaveRevokeList");
        TaskDto task = taskService.save(taskDto);
        exportService.exportLeaveRevokeList(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }

    @PostMapping(value = "/exportLeaveAbolishList")
    @ApiOperation("导出休假废止申请列表")
    public Result<TaskDto> exportLeaveAbolishList(@RequestBody WorkflowRevokeReqDto dto, HttpServletRequest request) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_ABOLISH_LIST, "ei");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid").replaceAll("empid", "ei.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("导出休假废止申请列表 DataScope = {}", dataScope);
        TaskDto taskDto = new TaskDto();
        taskDto.setType("LeaveAbolishList");
        TaskDto task = taskService.save(taskDto);
        exportService.exportLeaveAbolishList(dto, task, getUserInfo(), ResponseWrap.getLocale());
        return ResponseWrap.wrapResult(task);
    }
}
