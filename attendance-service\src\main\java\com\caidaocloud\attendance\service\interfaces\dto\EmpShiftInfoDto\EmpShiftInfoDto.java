package com.caidaocloud.attendance.service.interfaces.dto.EmpShiftInfoDto;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/8/11 17:36
 * @Description:
 **/
@Data
public class EmpShiftInfoDto implements Serializable {
    @ApiModelProperty("员工排班id")
    private Integer empShiftId;
    @ApiModelProperty("员工信息dto")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("员工日历id")
    private Integer workCalendarId;
    @ApiModelProperty("有效期开始时间")
    private Long startTime;
    @ApiModelProperty("有效期结束时间")
    private Long endTime;
}
