package com.caidaocloud.attendance.service.infrastructure.repository.mapper;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2021/3/28
 */
public interface ShiftDefMapper {
    PageList<WaShiftDef> getShiftDefList(@Param("myPageBounds") MyPageBounds myPageBounds,
                                         @Param("belongOrgId") String belongOrgId,
                                         @Param("keywords") String keywords,
                                         @Param("dateTypes") Integer[] dateTypes,
                                         @Param("filter") String filter);
}
