<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.ErrorCollectMapper">
    <insert id="insert">
        insert into error_collect
        (app_id, app_name, app_version, device_id, device_ua, type,
        detail, time, tenant_id, user_id) values
        (#{appId}, #{appName}, #{appVersion}, #{deviceId}, #{deviceUa}, #{type},
        #{detail}, #{time}, #{tenantId,jdbcType=VARCHAR}, #{userId})
    </insert>
</mapper>