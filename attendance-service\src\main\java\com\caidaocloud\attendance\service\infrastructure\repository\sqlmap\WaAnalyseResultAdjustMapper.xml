<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaAnalyseResultAdjustMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyseResultAdjust">
    <id column="adjust_id" jdbcType="BIGINT" property="adjustId" />
    <result column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="empid" jdbcType="BIGINT" property="empid" />
    <result column="belong_date" jdbcType="BIGINT" property="belongDate" />
    <result column="original_signin_id" jdbcType="INTEGER" property="originalSigninId" />
    <result column="original_signoff_id" jdbcType="INTEGER" property="originalSignoffId" />
    <result column="original_signin_time" jdbcType="BIGINT" property="originalSigninTime" />
    <result column="original_signoff_time" jdbcType="BIGINT" property="originalSignoffTime" />
    <result column="original_late_time" jdbcType="REAL" property="originalLateTime" />
    <result column="original_early_time" jdbcType="REAL" property="originalEarlyTime" />
    <result column="original_kg_work_time" jdbcType="INTEGER" property="originalKgWorkTime" />
    <result column="signin_time" jdbcType="BIGINT" property="signinTime" />
    <result column="signoff_time" jdbcType="BIGINT" property="signoffTime" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ext_custom_col" jdbcType="VARCHAR" property="extCustomCol" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="last_empid" jdbcType="BIGINT" property="lastEmpid" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="revoke_status" jdbcType="INTEGER" property="revokeStatus" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    adjust_id, batch_id, empid, belong_date, original_signin_id, original_signoff_id, 
    original_signin_time, original_signoff_time, original_late_time, original_early_time, 
    original_kg_work_time, signin_time, signoff_time, file_path, file_name, business_key, 
    reason, ext_custom_col, status, last_approval_time, last_empid, revoke_reason, revoke_status, 
    tenant_id, deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_analyse_result_adjust
    where adjust_id = #{adjustId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_analyse_result_adjust
    where adjust_id = #{adjustId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyseResultAdjust">
    insert into wa_analyse_result_adjust (adjust_id, batch_id, empid, 
      belong_date, original_signin_id, original_signoff_id, 
      original_signin_time, original_signoff_time, 
      original_late_time, original_early_time, original_kg_work_time, 
      signin_time, signoff_time, file_path, 
      file_name, business_key, reason, 
      ext_custom_col, status, last_approval_time, 
      last_empid, revoke_reason, revoke_status, 
      tenant_id, deleted, create_by, 
      create_time, update_by, update_time
      )
    values (#{adjustId,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, #{empid,jdbcType=BIGINT}, 
      #{belongDate,jdbcType=BIGINT}, #{originalSigninId,jdbcType=INTEGER}, #{originalSignoffId,jdbcType=INTEGER}, 
      #{originalSigninTime,jdbcType=BIGINT}, #{originalSignoffTime,jdbcType=BIGINT}, 
      #{originalLateTime,jdbcType=REAL}, #{originalEarlyTime,jdbcType=REAL}, #{originalKgWorkTime,jdbcType=INTEGER}, 
      #{signinTime,jdbcType=BIGINT}, #{signoffTime,jdbcType=BIGINT}, #{filePath,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{businessKey,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{extCustomCol,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{lastApprovalTime,jdbcType=BIGINT}, 
      #{lastEmpid,jdbcType=BIGINT}, #{revokeReason,jdbcType=VARCHAR}, #{revokeStatus,jdbcType=INTEGER}, 
      #{tenantId,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyseResultAdjust">
    insert into wa_analyse_result_adjust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustId != null">
        adjust_id,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="belongDate != null">
        belong_date,
      </if>
      <if test="originalSigninId != null">
        original_signin_id,
      </if>
      <if test="originalSignoffId != null">
        original_signoff_id,
      </if>
      <if test="originalSigninTime != null">
        original_signin_time,
      </if>
      <if test="originalSignoffTime != null">
        original_signoff_time,
      </if>
      <if test="originalLateTime != null">
        original_late_time,
      </if>
      <if test="originalEarlyTime != null">
        original_early_time,
      </if>
      <if test="originalKgWorkTime != null">
        original_kg_work_time,
      </if>
      <if test="signinTime != null">
        signin_time,
      </if>
      <if test="signoffTime != null">
        signoff_time,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="extCustomCol != null">
        ext_custom_col,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="lastEmpid != null">
        last_empid,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="revokeStatus != null">
        revoke_status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustId != null">
        #{adjustId,jdbcType=BIGINT},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="belongDate != null">
        #{belongDate,jdbcType=BIGINT},
      </if>
      <if test="originalSigninId != null">
        #{originalSigninId,jdbcType=INTEGER},
      </if>
      <if test="originalSignoffId != null">
        #{originalSignoffId,jdbcType=INTEGER},
      </if>
      <if test="originalSigninTime != null">
        #{originalSigninTime,jdbcType=BIGINT},
      </if>
      <if test="originalSignoffTime != null">
        #{originalSignoffTime,jdbcType=BIGINT},
      </if>
      <if test="originalLateTime != null">
        #{originalLateTime,jdbcType=REAL},
      </if>
      <if test="originalEarlyTime != null">
        #{originalEarlyTime,jdbcType=REAL},
      </if>
      <if test="originalKgWorkTime != null">
        #{originalKgWorkTime,jdbcType=INTEGER},
      </if>
      <if test="signinTime != null">
        #{signinTime,jdbcType=BIGINT},
      </if>
      <if test="signoffTime != null">
        #{signoffTime,jdbcType=BIGINT},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyseResultAdjust">
    update wa_analyse_result_adjust
    <set>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=BIGINT},
      </if>
      <if test="empid != null">
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="belongDate != null">
        belong_date = #{belongDate,jdbcType=BIGINT},
      </if>
      <if test="originalSigninId != null">
        original_signin_id = #{originalSigninId,jdbcType=INTEGER},
      </if>
      <if test="originalSignoffId != null">
        original_signoff_id = #{originalSignoffId,jdbcType=INTEGER},
      </if>
      <if test="originalSigninTime != null">
        original_signin_time = #{originalSigninTime,jdbcType=BIGINT},
      </if>
      <if test="originalSignoffTime != null">
        original_signoff_time = #{originalSignoffTime,jdbcType=BIGINT},
      </if>
      <if test="originalLateTime != null">
        original_late_time = #{originalLateTime,jdbcType=REAL},
      </if>
      <if test="originalEarlyTime != null">
        original_early_time = #{originalEarlyTime,jdbcType=REAL},
      </if>
      <if test="originalKgWorkTime != null">
        original_kg_work_time = #{originalKgWorkTime,jdbcType=INTEGER},
      </if>
      <if test="signinTime != null">
        signin_time = #{signinTime,jdbcType=BIGINT},
      </if>
      <if test="signoffTime != null">
        signoff_time = #{signoffTime,jdbcType=BIGINT},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        last_empid = #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        revoke_status = #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where adjust_id = #{adjustId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyseResultAdjust">
    update wa_analyse_result_adjust
    set batch_id = #{batchId,jdbcType=BIGINT},
      empid = #{empid,jdbcType=BIGINT},
      belong_date = #{belongDate,jdbcType=BIGINT},
      original_signin_id = #{originalSigninId,jdbcType=INTEGER},
      original_signoff_id = #{originalSignoffId,jdbcType=INTEGER},
      original_signin_time = #{originalSigninTime,jdbcType=BIGINT},
      original_signoff_time = #{originalSignoffTime,jdbcType=BIGINT},
      original_late_time = #{originalLateTime,jdbcType=REAL},
      original_early_time = #{originalEarlyTime,jdbcType=REAL},
      original_kg_work_time = #{originalKgWorkTime,jdbcType=INTEGER},
      signin_time = #{signinTime,jdbcType=BIGINT},
      signoff_time = #{signoffTime,jdbcType=BIGINT},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      business_key = #{businessKey,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      revoke_status = #{revokeStatus,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where adjust_id = #{adjustId,jdbcType=BIGINT}
  </update>
</mapper>