package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class HolidayDto implements Serializable {

    @ApiModelProperty("日期id")
    private Integer holidayCalendarId;
    @ApiModelProperty("日期名称")
    private String calendarName;
    @ApiModelProperty("日期名称扩展")
    private Object calendarNameLang;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nCalendarName;

    public void initCalendarName() {
        if (StringUtils.isNotBlank(this.calendarName)) {
            return;
        }
        if (null == this.i18nCalendarName || this.i18nCalendarName.isEmpty() || null == this.i18nCalendarName.get("default")) {
            return;
        }
        this.setCalendarName(this.i18nCalendarName.get("default"));
    }

    public void initI18nCalendarName() {
        if (null != this.i18nCalendarName && !this.i18nCalendarName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.calendarName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.calendarName);
        this.setI18nCalendarName(i18nName);
    }
}
