package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 考勤汇总
 * created by: FoAng
 * create time: 22/3/2024 5:13 下午
 */
@Data
public class StatisticsSummaryVo implements Serializable {

    @ApiModelProperty("应到人数")
    private Integer workCount;

    @ApiModelProperty("打卡人数")
    private Integer registerCount;

    @ApiModelProperty("休假人数")
    private Integer ltCount;

    @ApiModelProperty("出差人数")
    private Integer trCount;

    @ApiModelProperty("加班人数")
    private Integer otCount;

    @ApiModelProperty("迟到人数")
    private Integer lateCount;

    @ApiModelProperty("早退人数")
    private Integer earlyCount;
}
