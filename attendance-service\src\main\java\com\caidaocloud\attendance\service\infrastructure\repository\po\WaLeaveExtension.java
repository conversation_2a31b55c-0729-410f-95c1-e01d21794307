package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "wa_leave_extension")
public class WaLeaveExtension {
    private Long id;
    private String tenantId;
    private Long quotaId;
    private Long empId;
    private Integer leaveTypeId;
    private Long configId;
    private Float timeDuration;
    private Integer timeUnit;
    private Long startDate;
    private Long endDate;
    private Long originalEndDate;
    private Integer status;
    private Long lastApprovalTime;
    private String reason;
    private String revokeReason;
    private String fileName;
    private String fileId;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private String processCode;
}