package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;

/**
 * 班次日期类型
 */
public enum DateTypeEnum {
    DATE_TYP_1(1, "工作日", AttendanceCodes.WORK_DAY),
    DATE_TYP_2(2, "休息日", AttendanceCodes.REST_DAY),
    DATE_TYP_3(3, "法定假日", AttendanceCodes.HOLIDAY),
    DATE_TYP_4(4, "特殊休日", AttendanceCodes.SPECIAL_REST_DAY),
    DATE_TYP_5(5, "法定休日", AttendanceCodes.LAW_REST_DAY);

    private Integer index;
    private String name;
    private Integer code;

    DateTypeEnum(Integer index, String name, Integer code) {
        this.name = name;
        this.index = index;
        this.code = code;
    }

    public static String getName(int index) {
        for (DateTypeEnum c : DateTypeEnum.values()) {
            if (c.getIndex() == index) {
                String i18n = "";
                try {
                    i18n = ResponseWrap.wrapResult(c.code, null).getMsg();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtil.isNotBlank(i18n)) {
                    return i18n;
                }
                return c.name;
            }
        }
        return "";
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
