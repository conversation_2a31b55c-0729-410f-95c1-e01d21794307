package com.caidaocloud.attendance.service;

public class DataScopeTest {
    public static void main(String[] args) {
        String abc = "$.orgid in (select * from getsuborgstr(${orgid}))";
        System.out.println(abc);
        abc = abc.replaceAll("\\$\\.", "org.");
        System.out.println(abc);

        abc = "$.orgid in (select * from getsuborgstr(78965))";
        System.out.println(abc);
        abc = abc.replaceAll("\\$\\.", "org.");
        System.out.println(abc);

        try {
            DataScopeTest.class.getClassLoader().loadClass("com.caidaocloud.attendance.service.TokenTest");
        } catch (Exception e){
            e.printStackTrace();
        }
        System.out.println("DataScopeTest ......");
    }
}
