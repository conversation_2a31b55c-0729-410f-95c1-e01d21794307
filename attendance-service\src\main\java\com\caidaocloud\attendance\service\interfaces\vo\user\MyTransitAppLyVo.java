package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyTransitAppLyVo {

    private Long id;
    @ApiModelProperty("业务主键")
    private String businessKey;
    @ApiModelProperty("事件类型 1:加班 2:休假 41:补卡 121:出差 278 调班")
    private Integer eventType;
    @ApiModelProperty("事件名称")
    private String eventName;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    @ApiModelProperty("事件开始时间")
    private String startTime;
    @ApiModelProperty("事件结束时间")
    private String endTime;
    @ApiModelProperty("时长")
    private String duration;
}
