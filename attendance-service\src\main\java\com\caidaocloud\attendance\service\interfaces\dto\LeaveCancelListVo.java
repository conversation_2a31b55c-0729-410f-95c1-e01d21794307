package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("销假列表")
public class LeaveCancelListVo {

    @ApiModelProperty("主键id")
    private Long leaveCancelId;
    @ApiModelProperty("员工工号")
    private String workNo;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("任职组织")
    private String orgName;
    @ApiModelProperty("部门全称")
    private String fullPath;
    @ApiModelProperty("假期类型")
    private String leaveTypeName;
    @ApiModelProperty("销假类型")
    private String leaveCancelTypeName;
    @ApiModelProperty("销假类型Id")
    private Long typeId;
    @ApiModelProperty("销假时长")
    private Float timeDuration;
    @ApiModelProperty("销假时间")
    private List<LeaveCancelPeriod> cancelPeriods;
    @ApiModelProperty("调整事由")
    private String reason;
    @ApiModelProperty("时间单位 1:天,2:小时")
    private Integer timeUnit;
    @ApiModelProperty("申请日期")
    private Long createTime;
    @ApiModelProperty("审批状态文本")
    private String statusName;
    @ApiModelProperty("审批状态 0暂存、1审批中、2已通过、3已拒绝、4已作废、5已退回、8撤销中、9已撤销")
    private Short status;
    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;
    @ApiModelProperty("审批类型 1:请假,2:加班,41:补打卡,45:出差,103:调班,104:销假")
    private Integer funcType;
    @ApiModelProperty("业务主键")
    private String businessKey;

    @ApiModelProperty("原休假时间")
    private String originalLeaveDate;
}
