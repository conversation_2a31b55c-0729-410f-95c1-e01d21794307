package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 年假明细搜索
 */
@Data
@ApiModel("年假明细修改")
public class AnnualLeaveUpdateDto {
    @ApiModelProperty("年假明细ID")
    private Integer empQuotaId;

    @ApiModelProperty("调整额度")
    private Float adjustQuota;

    @ApiModelProperty("调整已使用额度|调整本年已用|固定已使用")
    private Float fixUsedDay;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("年份")
    private Short periodYear;

    @ApiModelProperty("生效日期")
    private Long startDate;

    @ApiModelProperty("失效日期")
    private Long lastDate;

    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;

    @ApiModelProperty("本年额度")
    private Float quotaDay;

    @ApiModelProperty("本年已用")
    private Float usedDay;

    @ApiModelProperty("假期类型")
    private Integer leaveTypeId;

    @ApiModelProperty("额度规则id")
    private Long configId;

    @ApiModelProperty("额度名称")
    private String quotaName;

    @ApiModelProperty("留存配额")
    private Float retainDay;
    @ApiModelProperty("留存配额已使用")
    private Float retainUsedDay;
    @ApiModelProperty("留存有效期")
    private Long retainValidDate;
}
