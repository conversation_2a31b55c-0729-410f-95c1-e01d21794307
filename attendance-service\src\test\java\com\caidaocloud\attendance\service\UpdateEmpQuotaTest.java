package com.caidaocloud.attendance.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidao1.ioc.dto.UpdRowDto;
import com.caidao1.ioc.service.ImportTriggerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class UpdateEmpQuotaTest {
    @Resource
    private ImportTriggerService importTriggerService;

    @Test
    public void testUpdateEmpQuota(){
        String belongId = "56594";
        Map<String, List< UpdRowDto >> addList = new HashMap();

        String json = "{\"wa_emp_quota\":[{\"id\":97,\"idName\":\"emp_quota_id\",\"key\":\"#_产假_ershanli_1262880000\",\"rowCnt\":0,\"row\":[{\"itemCode\":\"empid\",\"itemValue\":5,\"itemType\":\"INTEGER\"},{\"itemCode\":\"leave_type_id\",\"itemValue\":44,\"itemType\":\"INTEGER\"},{\"itemCode\":\"start_date\",\"itemValue\":1262880000,\"itemType\":\"BIGINT\"},{\"itemCode\":\"adjust_quota\",\"itemValue\":-1.0,\"itemType\":\"REAL\"},{\"itemCode\":\"remarks\",\"itemValue\":\"wuhaorenyuan(\",\"itemType\":\"VARCHAR\"},{\"itemCode\":\"upduser\",\"itemValue\":35132,\"itemType\":\"INTEGER\"},{\"itemCode\":\"updtime\",\"itemValue\":1627887085,\"itemType\":\"BIGINT\"}],\"rowData\":[\"ershanli\",\"产假\",\"2010-01-08\",\"10\",\"-1\",\"wuhaorenyuan(\"]}]}";
        Map<String, List<UpdRowDto>> updList = JSON.parseObject(json, new TypeReference<Map<String, List<UpdRowDto>>>(){});;
        importTriggerService.updateEmpQuota(belongId, addList, updList);
    }
}
