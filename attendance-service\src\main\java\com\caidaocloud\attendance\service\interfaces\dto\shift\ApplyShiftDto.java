package com.caidaocloud.attendance.service.interfaces.dto.shift;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/16 20:10
 * @Version 1.0
 */
@Data
@ApiModel("调班申请请求DTO")
public class ApplyShiftDto {
    @ApiModelProperty("调班日期 yyyy-MM-dd")
    private String date;
    @ApiModelProperty("调整班次Id")
    private Integer shiftDefId;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("附件名称，多个使用逗号隔开")
    private String fileName;
    @ApiModelProperty("附件ID，多个使用逗号隔开")
    private String file;
}
