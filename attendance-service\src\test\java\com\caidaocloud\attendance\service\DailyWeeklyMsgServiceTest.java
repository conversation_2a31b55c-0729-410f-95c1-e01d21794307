package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.application.service.msg.DailyWeeklyMsgService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class DailyWeeklyMsgServiceTest {
    @Resource
    private DailyWeeklyMsgService dailyWeeklyMsgService;
    @Test
    public void sendDailyTtlMsg(){
        dailyWeeklyMsgService.sendDailyTtlMsg();
    }

    @Test
    public void sendWeeklyTtlMsg(){
        dailyWeeklyMsgService.sendWeeklyTtlMsg("56566");
    }
}
