package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EmpListDto extends AttendanceBasePage implements Serializable {
    @ApiModelProperty(value = "员工姓名")
    private String empName;

    @ApiModelProperty(value = "工号-员工姓名")
    private String worknoAndempname;

    @ApiModelProperty(value = "1表示员工在职 2表示离职")
    private Integer zzStatus;

    private Integer workType;
    private Integer tmType;

    @ApiModelProperty(value = "部门id")
    private Long orgid;

    private String corpEmail;
    private String belongorgid;
    private String belongid;

    @ApiModelProperty(value = "菜单id")
    private Integer resId;

    private String type;

    @ApiModelProperty("数据权限")
    private String dataScope;
}
