package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2021/2/9
 */
@Data
public class WorkRoundVo implements Serializable{
    private static final long serialVersionUID = 2552006630452059856L;

    @ApiModelProperty("排班计划ID")
    private Integer workRoundId;
    @ApiModelProperty("排班名称")
    private String roundName;
    @ApiModelProperty("班次轮次信息")
    private List<RoundShiftVo> roundShiftList;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nRoundName;
}
