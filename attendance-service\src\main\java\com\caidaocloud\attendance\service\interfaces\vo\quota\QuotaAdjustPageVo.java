package com.caidaocloud.attendance.service.interfaces.vo.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QuotaAdjustPageVo {

    @ApiModelProperty("当年配额调整明细id")
    private Integer empQuotaDetailId;

    @ApiModelProperty("调整余额")
    private String quotaDay;

    @ApiModelProperty("已使用")
    private String usedDay;

    @ApiModelProperty("剩余配额")
    private String surplusQuota;

    @ApiModelProperty("生效日期开始时间，单位秒")
    private Long startDate;

    @ApiModelProperty("生效日期结束时间，单位秒")
    private Long endDate;

    @ApiModelProperty("状态")
    private String statusName;

    @ApiModelProperty("备注")
    private String remarks;
}
