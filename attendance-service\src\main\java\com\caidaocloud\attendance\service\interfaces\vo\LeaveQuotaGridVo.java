package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/3/2
 */
@Data
public class LeaveQuotaGridVo {

    @ApiModelProperty("配额类型ID")
    private Integer quotaSettingId;

    @ApiModelProperty("配额名称")
    private String quotaSettingName;

    @ApiModelProperty("补偿规则")
    private String carryOverType;

//    @ApiModelProperty("试用期不累计")
//    private String isCountInProbation;

    @ApiModelProperty("周期类型")
    private String quotaPeriodTypeName;

    @ApiModelProperty("额度扣减顺序")
    private Integer quotaSortNo;

    @ApiModelProperty("备注")
    private String rmk;


}
