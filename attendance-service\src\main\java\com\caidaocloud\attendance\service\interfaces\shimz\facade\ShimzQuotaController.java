package com.caidaocloud.attendance.service.interfaces.shimz.facade;

import com.caidaocloud.attendance.service.application.shimz.service.ShimzQuotaService;
import com.caidaocloud.attendance.service.interfaces.shimz.dto.GenMigrateLeaveQuotaDto;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 清水假勤定制接口
 *
 * <AUTHOR>
 * @Date 2024/5/27
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/shimz/v1")
@Api(value = "/api/attendance/shimz/v1", description = "清水假勤定制接口")
public class ShimzQuotaController {
    @Autowired
    private ShimzQuotaService shimzQuotaService;

    @ApiOperation(value = "员工部署异动后生成移动休假配额")
    @PostMapping("/genMigrateLeaveQuota")
    public Result<Boolean> genMigrateLeaveQuota(@RequestBody GenMigrateLeaveQuotaDto dto) throws Exception {
        return Result.ok(shimzQuotaService.empDeployChangeAfterTrigger(dto.getBusinessKey(), dto.getCallbackType()));
    }

}
