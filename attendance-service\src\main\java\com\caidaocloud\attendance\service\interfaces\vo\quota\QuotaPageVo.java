package com.caidaocloud.attendance.service.interfaces.vo.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QuotaPageVo {

    @ApiModelProperty("余额id")
    private Integer empQuotaId;

    @ApiModelProperty("1、天，2、小时")
    private Integer acctTimeType;

    @ApiModelProperty("调整额度")
    private Float adjustQuota;

    @ApiModelProperty("调整额度已使用")
    private Float adjustUsedDay;

    @ApiModelProperty("公司id")
    private String belongOrgId;

    @ApiModelProperty("当前剩余")
    private Float curRemain;

    @ApiModelProperty("当年总额度")
    private Float currentYearTotalQuota;

    @ApiModelProperty("本年抵扣")
    private Float deductionDay;

    @ApiModelProperty("员工id")
    private Long empid;

    @ApiModelProperty("员工姓名")
    private String empName;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("调整已使用额度")
    private Float fixUsedDay;

    @ApiModelProperty("冻结额度")
    private Float frozenDay;

    @ApiModelProperty("在途额度")
    private Float inTransitQuota;

    @ApiModelProperty("生效日期：开始时间")
    private Long startDate;

    @ApiModelProperty("生效日期：结束时间")
    private Long lastDate;

    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;

    @ApiModelProperty("余额类型id")
    private String quotaSettingId;

    @ApiModelProperty("余额类型名称")
    private String quotaSettingName;

    @ApiModelProperty("本年额度")
    private Float quotaDay;

    @ApiModelProperty("当前额度")
    private Float nowQuota;

    @ApiModelProperty("当前留存")
    private Float nowRemain;

    @ApiModelProperty("年份")
    private Short periodYear;

    @ApiModelProperty("余额排序")
    private Integer quotaSortNo;

//    @ApiModelProperty("留存总额度")
//    private Float remainTotalQuota;

    @ApiModelProperty("上年结转额度")
    private Float remainDay;

    @ApiModelProperty("上年结转已用")
    private Float remainUsedDay;

    @ApiModelProperty("上年结转有效期至")
    private Long remainValidDate;

//    @ApiModelProperty("余额类型id")
//    private Integer roundTimeUnit;

    @ApiModelProperty("员工状态：0在职1离职2试用期")
    private Integer status;

    @ApiModelProperty("员工状态")
    private String statusTxt;

    @ApiModelProperty("时间单位")
    private String timeUnitName;

//    @ApiModelProperty("余额类型id")
//    private Integer useItType;

    @ApiModelProperty("已使用总额度")
    private Float usedTotalQuota;

    @ApiModelProperty("本年已用")
    private Float usedDay;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("首次参工日期")
    private Long firstWorkDate;

    private String fullPath;
}
