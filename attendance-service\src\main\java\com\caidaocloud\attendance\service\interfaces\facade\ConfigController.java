package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.service.IConfigService;
import com.caidaocloud.attendance.service.domain.entity.SysJobDo;
import com.caidaocloud.attendance.service.interfaces.dto.common.ErrorCollectDto;
import com.caidaocloud.attendance.service.interfaces.dto.config.SysConfigReqDto;
import com.caidaocloud.attendance.service.interfaces.vo.AdjustShiftConfigurationVo;
import com.caidaocloud.attendance.service.interfaces.vo.SysConfigurationVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Aaron.Chen
 * @Date: 2022/3/21 9:25
 * @Description:
 **/
@Slf4j
@RestController
@RequestMapping("/api/attendance/config/v1")
public class ConfigController {

    @Autowired
    private IConfigService configService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation("保存系统参数开关")
    @PostMapping("/saveConfig")
    public Result<Boolean> saveConfig(@RequestBody SysConfigReqDto dto) {
        if (null == dto.getStatus()) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "开关参数为空", Boolean.FALSE);
        }
        try {
            configService.updateConfig(dto);
        } catch (Exception e) {
            log.error("Save or update configuration exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Save or update configuration exception", Boolean.FALSE);
        }
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation("查询调班开关配置详情")
    @GetMapping("/getConfig")
    public Result<AdjustShiftConfigurationVo> getConfig(@RequestParam("configCode") String configCode) {
        try {
            return ResponseWrap.wrapResult(ObjectConverter.convert(configService.getConfiguration(configCode), AdjustShiftConfigurationVo.class));
        } catch (Exception e) {
            log.error("Get configuration exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Get configuration exception", null);
        }
    }

    @ApiOperation("查询系统配置详情")
    @GetMapping("/getSysConfigs")
    public Result<ItemsResult<SysConfigurationVo>> getSysConfigs() {
        try {
            return ResponseWrap.wrapResult(new ItemsResult<>(ObjectConverter.convertList(configService.getSysConfigs(), SysConfigurationVo.class)));
        } catch (Exception e) {
            log.error("Get configuration exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Get configuration exception", null);
        }
    }

    @ApiOperation("系统异常存储")
    @PostMapping("/collect/error")
    public Result<Boolean> collectError(@RequestBody ErrorCollectDto collect) {
        configService.collectError(collect);
        return Result.ok();
    }

    @ApiOperation(value = "查询租户下的职位，职位类，职位族")
    @GetMapping(value = "/selectJobList")
    public Result<ItemsResult<KeyValue>> selectJobList(@Param("jobType") String jobType) {
        UserInfo userInfo = sessionService.getUserInfo();
        String tenantId = userInfo.getTenantId();
        List<KeyValue> selectList = new ArrayList<>();
        if (!SysJobDo.checkTableExist(tenantId)) {
            return ResponseWrap.wrapResult(new ItemsResult<>(selectList));
        }
        List<SysJobDo> jobList = SysJobDo.loadSysJob(tenantId);
        if (CollectionUtils.isNotEmpty(jobList)) {
            Map<String, List<SysJobDo>> map = jobList.stream().sorted(Comparator.comparingInt(SysJobDo::getId)).collect(Collectors.groupingBy(SysJobDo::getLayerType, LinkedHashMap::new, Collectors.toList()));
            Map<String, List<KeyValue>> selectMap = new HashMap<>();
            for (Map.Entry<String, List<SysJobDo>> row : map.entrySet()) {
                List<KeyValue> selectJobList = new ArrayList<>();
                row.getValue().forEach(v -> {
                    selectJobList.add(new KeyValue(v.getName(), v.getId()));
                });
                selectMap.put(row.getKey(), selectJobList);
            }
            switch (jobType) {
                case "job_family":
                    if (selectMap.get("JOB_FAMILY") != null) {
                        selectList.addAll(selectMap.get("JOB_FAMILY"));
                    }
                    break;
                case "job_class":
                    if (selectMap.get("JOB_CATEGORY") != null) {
                        selectList.addAll(selectMap.get("JOB_CATEGORY"));
                    }
                    break;
                case "job_id":
                    if (selectMap.get("JOB_INFO") != null) {
                        selectList.addAll(selectMap.get("JOB_INFO"));
                    }
                    if (selectMap.get("PURE_JOB") != null) {
                        selectList.addAll(selectMap.get("PURE_JOB"));
                    }
                    break;
                default:
                    break;
            }
        }
        return ResponseWrap.wrapResult(new ItemsResult<>(selectList));
    }
}