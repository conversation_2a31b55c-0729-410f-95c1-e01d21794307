package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.ProcessDto;
import com.caidaocloud.attendance.service.application.service.ILeaveApplyService;
import com.caidaocloud.attendance.service.application.service.impl.WfService;
import com.caidaocloud.attendance.service.interfaces.dto.WfApprovalDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WfNoticeParameterItem;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WfTaskUrgeItem;
import com.caidaocloud.attendance.service.interfaces.vo.ProcessRecordVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 业务流程接口
 *
 * <AUTHOR>
 * @Date 2021/3/20
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/wf/v1")
public class WfController {
    @Autowired
    private WfService wfService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("查看业务详情")
    @GetMapping(value = "/getWfFuncDetail")
    public Result getWfFuncDetail(@RequestParam("businessKey") String businessKey,
                                  @RequestParam(value = "nodeId", required = false) String nodeId,
                                  @RequestParam(value = "funcType", required = false) Integer funcType) throws Exception {
        return ResponseWrap.wrapResult(wfService.getWfFuncDetail(businessKey, nodeId, funcType));
    }

    @ApiOperation("查看流程详情")
    @GetMapping(value = "/getWfDetail")
    public Result<WfResponseDto> getWfDetail(@RequestParam("businessKey") String businessKey) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        WfResponseDto wfDetail = wfService.getWfDetail(userInfo.getTenantId(), businessKey, false);
        if (businessKey != null) {
            leaveApplyService.appendHomeLeaveInfo(businessKey, wfDetail);
        }
        LogRecordContext.putVariable("condition", true);
        return ResponseWrap.wrapResult(wfDetail);
    }

    @ApiOperation("工作流审批")
    @PostMapping(value = "/wfApproval")
    public Result workflowApproval(@RequestBody WfApprovalDto dto) {
        try {
            return wfService.workflowApproval(dto);
        } catch (Exception e) {
            log.error("WfController.workflowApproval has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.APPROVED_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("校验工作流是否开启")
    @GetMapping(value = "/checkWorkflowEnabled")
    public Result<Boolean> checkWorkflowEnabled(@RequestParam(value = "funCode") String funCode) {
        try {
            return wfService.checkWorkflowEnabled(funCode);
        } catch (Exception e) {
            log.error("WfController.workflowApproval has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.CHECK_WORKFLOW_ENABLED_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("工作流催办")
    @PostMapping(value = "/wfTaskUrge")
    @LogRecordAnnotation(success = "一键催办了数据", category = "一键催办", menu = "{{#menu}}")
    public Result<Boolean> workflowTaskUrge(@RequestBody List<WfTaskUrgeItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "参数为空", Boolean.FALSE);
        }
        if (CollectionUtils.isNotEmpty(items) && StringUtils.isNotEmpty(items.get(0).getBusinessKey())) {
            String[] split = items.get(0).getBusinessKey().split("_");
            BusinessCodeEnum byCode = BusinessCodeEnum.getByCode(split[1]);
            LogRecordContext.putVariable("menu", byCode == null ? "" : byCode.getMenu());
        }
        try {
            wfService.workflowTaskUrge(items);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("WfController.workflowTaskUrge has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.URGE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("工作流流程变量获取")
    @PostMapping(value = "/notice/detail")
    public Result<Map<String, String>> workflowParameter(@RequestBody WfNoticeParameterItem item) {
        if (StringUtil.isBlank(item.getBusinessKey()) || CollectionUtils.isEmpty(item.getVariables())) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "params is empty", null);
        }
        try {
            Map<String, String> res = wfService.workflowParameter(item);
            if (null == res) {
                return Result.fail("Exception in obtaining workflow process variables");
            }
            return Result.ok(res);
        } catch (Exception e) {
            log.error("WfController.workflowParameter has exception:{}", e.getMessage(), e);
            return Result.fail("Exception in obtaining workflow process variables");
        }
    }

    @ApiOperation(value = "流程记录")
    @PostMapping("/process/record")
    public Result<ProcessRecordVo> recordOfProcess(@RequestBody ProcessDto processDto) {
        try {
            return Result.ok(ObjectConverter.convert(wfService.recordOfProcess(processDto), ProcessRecordVo.class));
        } catch (Exception e) {
            log.error("recordOfProcess exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_PROCESS_RECORD_FAILED, null);
        }
    }
}
