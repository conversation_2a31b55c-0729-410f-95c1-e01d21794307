package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/2/8
 */
@Data
public class WorkRoundGridVo implements Serializable {
    private static final long serialVersionUID = 3940373217581554113L;
    @ApiModelProperty("排班计划ID")
    private Integer workRoundId;

    @ApiModelProperty("排班名称")
    private String roundName;

    @ApiModelProperty("排班周期")
    private String schedulingCycle;
}
