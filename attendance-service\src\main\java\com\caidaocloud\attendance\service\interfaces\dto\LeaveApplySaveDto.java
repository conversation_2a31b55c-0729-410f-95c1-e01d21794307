package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 休假申请DTO
 *
 * <AUTHOR>
 */
@Data
public class LeaveApplySaveDto {
    @ApiModelProperty("假期id")
    private Integer leaveId;
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;
    @ApiModelProperty("开始日期")
    private Long startTime;
    @ApiModelProperty("结束日期")
    private Long endTime;
    @ApiModelProperty("是否选择半天 1 选择 0 不选择")
    private Integer showDay;
    @ApiModelProperty("是否选择小时 1 选择 0 不选择")
    private Integer showMin;
    @ApiModelProperty("半天开始 A 上半天 P 下半天")
    private String shalfDay;
    @ApiModelProperty("半天结束 A 上半天 P 下半天")
    private String ehalfDay;
    @ApiModelProperty("开始时间 类型分钟 ")
    private Integer stime;
    @ApiModelProperty("结束时间 类型分钟 ")
    private Integer etime;
    @ApiModelProperty("原因")
    private String reason;
    @ApiModelProperty("附件")
    private String file;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("持续时间")
    private String duration;
    @ApiModelProperty("每日申请时长")
    private Float dailyDuration;
    @ApiModelProperty("子女个数")
    private Integer childNum;
    @ApiModelProperty("产假类型：1顺产2难产")
    private Integer maternityLeaveType;
    @ApiModelProperty("子女出生日期")
    private Long manufactureDate;
    @ApiModelProperty("探亲事由")
    private String homeLeaveType;
    @ApiModelProperty("婚姻状态")
    private String marriageStatus;
    @ApiModelProperty("幂等校验key")
    private String secretKey;
    @ApiModelProperty("是否为批量申请，可选，批量申请时使用")
    private boolean ifBtch;
    @ApiModelProperty("批量单据ID，可选，批量申请时使用")
    private Long batchId;
    @ApiModelProperty("批量申请时是否更新配额，可选，批量申请时使用")
    private boolean updateQuotaWhenBatch;
    @ApiModelProperty("假期类型单位, 可选，批量申请时使用")
    private Integer acctTimeType;
}
