package com.caidaocloud.attendance.service.interfaces.dto.user;

import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordBdkDto;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordDto;
import lombok.Data;

import java.util.List;

@Data
public class MyWorkEventDto {
    // 打卡记录
    private List<RegisterRecordDto> records;
    // 休假记录
    private List<MyLeaveTimeDto> lts;
    // 加班记录
    private List<MyOverTimeDto> ots;
    // 排班记录
    private List<MyWorkDateShiftDto> shifts;
    // 补打卡记录
    private List<RegisterRecordBdkDto> bdkRecords;

    private List<MyTravelTimeDto> tts;
    //迟到时长
    private Float earlyTime;
    //迟到时长
    private Float lateTime;
    //旷工时长
    private Integer kgWorkTime;
    //员工姓名
    private String empName;
}
