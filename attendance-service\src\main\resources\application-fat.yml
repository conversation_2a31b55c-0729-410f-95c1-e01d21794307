server:
  port: 8080
spring:
  application:
    name: caidaocloud-attendance-service
  cloud:
    nacos:
      discovery:
        server-addr: 172.16.0.141:8848
        namespace: cd2
nacos:
  com:
    alibaba:
      nacos:
        naming:
          cache:
            dir: /tmp
  config:
    type: yaml
    server-addr: 172.16.0.141:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: cd2
    bootstrap:
      enable: true
      log:
        enable: true