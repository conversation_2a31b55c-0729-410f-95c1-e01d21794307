<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.QuotaMapper">

    <select id="getEmpQuotaCountByYearAndType" resultType="java.lang.Integer">
        select count(0)
        from wa_emp_quota q
        join wa_leave_setting wls on q.quota_setting_id = wls.quota_setting_id
        where
        q.empid = #{empid}
        and q.quota_setting_id = #{quotaSettingId}
        and q.period_year = #{periodYear}
        and wls.leave_type <![CDATA[<>]]> 4
        <if test="empQuotaId != null">
            and emp_quota_id <![CDATA[<>]]> #{empQuotaId}
        </if>
    </select>

    <select id="getGroupEmpList" resultType="Long">
        SELECT distinct
        egv.empid
        FROM
        wa_emp_group_view egv
        join sys_emp_info ei on ei.empid = egv.empid
        AND ei.deleted = 0
        and ((ei.stats <![CDATA[<>]]> 1 and hire_date <![CDATA[<=]]> #{curDate}) or (ei.stats = 1 AND ei.termination_date <![CDATA[>=]]> #{curDate}))
        WHERE wa_group_id = #{groupId}
        <!--<if test="year != null and isParenting == null">
            AND extract(year from to_timestamp(ei.hire_date)) <![CDATA[<=]]> #{year}
        </if>-->
        <if test="datafilter != null and datafilter != ''">
            ${datafilter}
        </if>
    </select>



    <select id="getAnnualLeavePageList" resultType="map">
        SELECT * FROM (
        SELECT
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end  as   "fullPath",
        ep.emp_quota_id,
        ep.quota_setting_id,
        ep.start_date,
        ep.last_date,
        ep.empid,
        ep.period_year "period_year",
        ep.adjust_quota,
        ep.remarks,
        ep.in_transit_quota   AS "inTransitQuota",
        ep.dis_cycle_start,
        ep.dis_cycle_end,
        ep.validity_duration,
        ep.validity_unit,
        ep.if_advance,
        ep.used_day,
        ep.quota_day,
        ep.fix_used_day,
        ei.belong_org_id,
        ei.stats "stats",
        ei.workno,
        ei.emp_name,
        ei.hire_date,
        ei.first_work_date,
        remain_day,
        remain_valid_date,
        remain_used_day,
        deduction_day,
        use_it_type,
        use_four2five_rule,
        round_time_unit,
        COALESCE(now_quota,0) AS "now_quota",
        lt.acct_time_type,
        lt.leave_type_id AS "leaveTypeId",
        lt.leave_name AS "leaveName",
        lt.leave_type "leaveType",
        ep.fix_used_day   AS "adjust_used_day",
        ei.termination_date,
        ei.orgid "orgid",
        ei.employ_type "employ_type",
        lqc.rule_name "ruleName",
        lqc.i18n_rule_name "i18nRuleName",
        lqc.config_id "configId",
        temp.retainDay "retainDay",
        temp.retainInTransitQuota "retainInTransitQuota",
        temp.retainUsedDay "retainUsedDay",
        temp.retainValidDate "retainValidDate"
        FROM wa_emp_quota ep
        JOIN sys_emp_info ei ON ep.empid = ei.empid AND ei.deleted = 0
        JOIN sys_corp_org sco ON sco.orgid = ei.orgid AND sco.deleted = 0
        JOIN wa_leave_type lt ON lt.leave_type_id = ep.leave_type_id
        LEFT JOIN wa_leave_quota_config lqc ON lqc.config_id = ep.config_id
        LEFT JOIN (SELECT merge_emp_quota_id,last_date retainValidDate,
        COALESCE(CASE WHEN (last_date IS NULL OR last_date <![CDATA[<]]> #{curDate}) THEN COALESCE(in_transit_quota, 0) + COALESCE(used_day, 0) ELSE quota_day END, 0) retainDay,
        COALESCE(in_transit_quota, 0) retainInTransitQuota,
        COALESCE(used_day, 0) retainUsedDay
        FROM wa_emp_quota WHERE carry_merge AND original_emp_quota_id IS NOT NULL AND merge_emp_quota_id IS NOT NULL AND last_date IS NOT NULL) temp
        ON temp.merge_emp_quota_id=ep.emp_quota_id
        WHERE ei.belong_org_id = #{belongId} AND ei.deleted = 0 AND lt.quota_type in (1,4) AND (ep.carry_merge IS NULL OR ep.carry_merge=false)
        <if test="empQuotaId != null">
            AND ep.emp_quota_id = #{empQuotaId}
        </if>
        <if test="empid != null">
            AND ep.empid = #{empid}
        </if>
        <if test="years != null and years.size() > 0">
            AND ep.period_year IN <foreach collection="years" item="year" open="(" separator="," close=")">#{year}</foreach>
        </if>
        <if test="keywords != null and keywords != ''">
            AND (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
        </if>
        <if test="annualLeaveStatus != null and annualLeaveStatus == true">
            AND ep.last_date IS NOT NULL AND ep.last_date >= #{nowTime}
        </if>
        <if test="annualLeaveStatus != null and !annualLeaveStatus">
            AND (ep.last_date IS NULL OR ep.last_date &lt; #{nowTime})
        </if>
        <if test="startDate != null and endDate != null">
            AND ep.last_date between #{startDate} and #{endDate}
        </if>
        <if test="leaveType != null and leaveType.size() > 0">
            AND lt.leave_type IN <foreach collection="leaveType" item="type" open="(" separator="," close=")">#{type}</foreach>
        </if>
        <if test="datafilter != null and datafilter != ''">
            ${datafilter}
        </if>
        ) AS t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
    </select>

    <select id="getAnnualLeaveGroupPageList" resultType="map">
        select
        (array_agg("configId"))[1] as "configId",
        (array_agg("ruleName"))[1] as "ruleName",
        (array_agg("i18nRuleName"))[1] as "i18nRuleName",
        (array_agg(emp_quota_id))[1] as emp_quota_id,
        empid,
        (array_agg(emp_name))[1] as emp_name,
        (array_agg(workno))[1] as workno,
        (array_agg(hire_date))[1] as hire_date,
        (array_agg(first_work_date))[1] as first_work_date,
        (array_agg("fullPath"))[1] as "fullPath",
        (array_agg(start_date))[1] as start_date,
        (array_agg(last_date))[1] as last_date,
        (array_agg(validity_unit))[1] as validity_unit,
        "leaveTypeId",
        (array_agg(termination_date))[1] as termination_date,
        (array_agg(acct_time_type))[1] as acct_time_type,
        (array_agg("leaveName"))[1] as "leaveName",
        period_year,
        (array_agg(dis_cycle_start))[1] as dis_cycle_start,
        (array_agg(dis_cycle_end))[1] as dis_cycle_end,
        (array_agg(validity_duration))[1] as validity_duration,
        (array_agg(validity_unit))[1] as validity_unit,
        (array_agg(if_advance))[1] as if_advance,
        sum(used_day) as used_day,
        sum(quota_day) as quota_day,
        sum(now_quota) as now_quota,
        sum(deduction_day) as deduction_day,
        sum(adjust_quota) as adjust_quota,
        sum(fix_used_day) as fix_used_day,
        sum(used_day) as used_day,
        sum(remain_day) as remain_day,
        sum("retainDay") as "retainDay",
        sum("retainInTransitQuota") as "retainInTransitQuota",
        sum("retainUsedDay") as "retainUsedDay",
        "retainValidDate",
        sum("inTransitQuota") as "inTransitQuota",
        (array_agg("termination_date"))[1] as "terminationDate",
        (array_agg(remarks))[1] as remarks,
        (array_agg(stats))[1] as stats
        from (SELECT * FROM (
        SELECT
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end  as   "fullPath",
        ep.emp_quota_id,
        ep.quota_setting_id,
        ep.start_date,
        ep.last_date,
        ep.empid,
        ep.period_year "period_year",
        ep.adjust_quota,
        ep.remarks,
        ep.in_transit_quota   AS "inTransitQuota",
        ep.dis_cycle_start,
        ep.dis_cycle_end,
        ep.validity_duration,
        ep.validity_unit,
        ep.if_advance,
        ep.used_day,
        ep.quota_day,
        ep.fix_used_day,
        ei.belong_org_id,
        ei.stats "stats",
        ei.workno,
        ei.emp_name,
        ei.hire_date,
        ei.first_work_date,
        remain_day,
        remain_valid_date,
        remain_used_day,
        deduction_day,
        use_it_type,
        use_four2five_rule,
        round_time_unit,
        COALESCE(now_quota,0) AS "now_quota",
        lt.acct_time_type,
        lt.leave_type_id AS "leaveTypeId",
        lt.leave_name AS "leaveName",
        lt.leave_type "leaveType",
        ep.fix_used_day   AS "adjust_used_day",
        ei.termination_date,
        ei.orgid "orgid",
        ei.employ_type "employ_type",
        lqc.rule_name "ruleName",
        lqc.i18n_rule_name "i18nRuleName",
        lqc.config_id "configId",
        temp.retainDay "retainDay",
        temp.retainInTransitQuota "retainInTransitQuota",
        temp.retainUsedDay "retainUsedDay",
        temp.retainValidDate "retainValidDate"
        FROM wa_emp_quota ep
        JOIN sys_emp_info ei ON ep.empid = ei.empid AND ei.deleted = 0
        JOIN sys_corp_org sco ON sco.orgid = ei.orgid AND sco.deleted = 0
        JOIN wa_leave_type lt ON lt.leave_type_id = ep.leave_type_id
        LEFT JOIN wa_leave_quota_config lqc ON lqc.config_id = ep.config_id
        LEFT JOIN (SELECT merge_emp_quota_id,EXTRACT(epoch from date_trunc('day',to_timestamp(last_date)))::bigint retainValidDate,
        COALESCE(CASE WHEN (last_date IS NULL OR last_date <![CDATA[<]]> #{curDate}) THEN COALESCE(in_transit_quota, 0) + COALESCE(used_day, 0) ELSE quota_day END, 0) retainDay,
        COALESCE(in_transit_quota, 0) retainInTransitQuota,
        COALESCE(used_day, 0) retainUsedDay
        FROM wa_emp_quota WHERE carry_merge AND original_emp_quota_id IS NOT NULL AND merge_emp_quota_id IS NOT NULL AND last_date IS NOT NULL) temp
        ON temp.merge_emp_quota_id=ep.emp_quota_id
        WHERE ei.belong_org_id = #{belongId} AND ei.deleted = 0 AND lt.quota_type in (1,4) AND (ep.carry_merge IS NULL OR ep.carry_merge=false)
        <if test="empQuotaId != null">
            AND ep.emp_quota_id = #{empQuotaId}
        </if>
        <if test="empid != null">
            AND ep.empid = #{empid}
        </if>
        <if test="years != null and years.size() > 0">
            AND ep.period_year IN <foreach collection="years" item="year" open="(" separator="," close=")">#{year}</foreach>
        </if>
        <if test="keywords != null and keywords != ''">
            AND (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
        </if>
        <if test="annualLeaveStatus != null and annualLeaveStatus == true">
            AND ep.last_date IS NOT NULL AND ep.last_date >= #{nowTime}
        </if>
        <if test="annualLeaveStatus != null and !annualLeaveStatus">
            AND (ep.last_date IS NULL OR ep.last_date &lt; #{nowTime})
        </if>
        <if test="startDate != null and endDate != null">
            AND ep.last_date between #{startDate} and #{endDate}
        </if>
        <if test="leaveType != null and leaveType.size() > 0">
            AND lt.leave_type IN <foreach collection="leaveType" item="type" open="(" separator="," close=")">#{type}</foreach>
        </if>
        <if test="datafilter != null and datafilter != ''">
            ${datafilter}
        </if>
        ) AS t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>) to_sum group by empid, "leaveTypeId", period_year, "retainValidDate"
    </select>

    <select id="getEmpFixQuotaList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo">
        SELECT * FROM (
        select quota.emp_quota_id empQuotaId,
        emp.workno,
        quota.empid,
        quota.quota_day quotaDay,
        quota.used_day usedDay,
        quota.adjust_quota adjustQuota,
        quota.remarks,
        quota.in_transit_quota inTransitQuota,
        quota.start_date "startDate",
        quota.last_date,
        emp.emp_name empName,
        quota.leave_type_id leaveTypeId,
        leave_type.acct_time_type as "acctTimeType",
        leave_type.leave_name leaveTypeName,
        leave_type.i18n_leave_name i18nLeaveTypeName,
        emp.hire_date "hireDate",
        privacy.marriage,
        emp.workplace,
        emp.orgid,
        emp.employ_type,
        emp.termination_date "terminationDate",
        emp.stats "empStatus",
        es.social_province socialProvince,
        es.social_city socialCity,
        leave_type.leave_type "leaveTypeDefId"
        from wa_emp_quota quota
        join sys_emp_info emp on quota.empid = emp.empid
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
        left join sys_emp_privacy privacy on quota.empid = privacy.empid
        left join sys_emp_social es ON es.empid = emp.empid AND es.belong_org_id = emp.belong_org_id AND es.valid = 'Y' AND es.deleted = 0
        where leave_type.quota_type = 3 and emp.deleted = 0
        and leave_type.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="marriage != null and marriage.size() >0">
            and privacy.marriage in <foreach collection="marriage" item="marriageItem" open="(" separator="," close=")">#{marriageItem}</foreach>
        </if>
        <if test="workplace != null and workplace.size() >0">
            and emp.workplace in <foreach collection="workplace" item="workplaceItem" open="(" separator="," close=")">#{workplaceItem}</foreach>
        </if>
        <if test="leaveTypeDefId != null and leaveTypeDefId.size() >0">
            and leave_type.leave_type in <foreach collection="leaveTypeDefId" item="leaveTypeDefIdItem" open="(" separator="," close=")">#{leaveTypeDefIdItem}</foreach>
        </if>
        <if test="keywords != null and keywords != ''">
            and (emp.emp_name like concat('%', #{keywords}, '%') or emp.workno like concat('%', #{keywords}, '%'))
        </if>
        <if test="params.datafilter != null and params.datafilter != ''">
            ${params.datafilter}
        </if>
        ) AS t
        <where>
            <if test="params.filter != null and params.filter != ''">
                ${params.filter}
            </if>
        </where>
    </select>
    <insert id="insertFixQuota" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpFixQuotaPo">
        insert into wa_emp_quota (empid, leave_type_id, start_date, last_date,
        quota_day,now_quota, adjust_quota, remarks,
        crttime, crtuser,period_year,belong_org_id)
        values (#{empid,jdbcType=BIGINT}, #{leaveTypeId,jdbcType=BIGINT}, #{startDate,jdbcType=BIGINT},253402271999,
        #{quotaDay,jdbcType=REAL},#{quotaDay,jdbcType=REAL}, #{adjustQuota,jdbcType=REAL}, #{remarks,jdbcType=INTEGER},
        #{crttime,jdbcType=BIGINT}, #{crtuser,jdbcType=BIGINT}, #{periodYear,jdbcType=INTEGER}, #{belongOrgId,jdbcType=INTEGER})
    </insert>
    <update id="updateFixQuota" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpFixQuotaPo">
        update wa_emp_quota set
        empid = #{empid,jdbcType=BIGINT},
        <if test="leaveTypeId != null">
            leave_type_id = #{leaveTypeId,jdbcType=BIGINT},
        </if>
        start_date = #{startDate,jdbcType=BIGINT},
        quota_day = #{quotaDay,jdbcType=REAL},
        now_quota = #{quotaDay,jdbcType=REAL},
        adjust_quota = #{adjustQuota,jdbcType=REAL},
        remarks = #{remarks,jdbcType=INTEGER},
        updtime = #{updtime,jdbcType=BIGINT},
        upduser = #{upduser,jdbcType=BIGINT}
        where emp_quota_id = #{empQuotaId,jdbcType=BIGINT}
    </update>
    <select id="getEmpFixQuota" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo">
        select quota.emp_quota_id empQuotaId,
        quota.empid,
        quota.quota_day quotaDay,
        quota.used_day usedDay,
        quota.adjust_quota adjustQuota,
        quota.remarks,
        quota.in_transit_quota inTransitQuota,
        quota.start_date startDate,
        emp.emp_name empName,
        quota.leave_type_id leaveTypeId,
        leave_type.leave_name leaveTypeName,
        emp.hire_date hireDate,
        privacy.marriage,
        emp.workplace,
        from wa_emp_quota quota join sys_emp_info emp on quota.empid = emp.empid
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
        left join sys_emp_privacy privacy on quota.empid = privacy.empid
        where quota.emp_quota_id = #{empQuotaId} and emp.deleted = 0
        and leave_type.quota_type = 3
    </select>

    <select id="queryEmpFixQuotaByIds" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo">
        select quota.emp_quota_id empQuotaId,
        quota.empid,
        quota.quota_day quotaDay,
        quota.used_day usedDay,
        quota.adjust_quota adjustQuota,
        quota.remarks,
        quota.in_transit_quota inTransitQuota,
        quota.start_date startDate,
        quota.leave_type_id leaveTypeId
        from wa_emp_quota quota
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
        where quota.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} and leave_type.quota_type = 3
        <if test="empQuotaIds != null and empQuotaIds.size() > 0">
            and quota.emp_quota_id in
            <foreach collection="empQuotaIds" item="empQuotaId" open="(" close=")" separator=",">
                #{empQuotaId}
            </foreach>
        </if>
    </select>

    <delete id="deleteFixQuota">
        delete from wa_emp_quota
        where emp_quota_id = #{empQuotaId}
    </delete>

    <delete id="deleteFixQuotas">
        delete from wa_emp_quota
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and emp_quota_id in
        <foreach collection="empQuotaIds" item="empQuotaId" open="(" close=")" separator=",">
            #{empQuotaId}
        </foreach>
    </delete>

    <select id="getCurrentlyEffectEmpQuotaList" resultType="map">
        SELECT
        emp_quota_id,
        ep.empid,
        ei.belong_org_id,
        hire_date,
        ep.start_date,
        ep.last_date,
        COALESCE(quota_day, 0) as quota_day,
        COALESCE(now_quota, 0) as "now_quota",
        lt.acct_time_type,
        lt.leave_type,
        wlqc.now_distribute_rule,
        wlqc.now_rounding_rule,
        ep.dis_cycle_start,
        ep.dis_cycle_end,
        ei.termination_date,
        ei.prodead_line,
        ep.cross_quota_date    as "crossQuotaDate",
        ep.annual_quota        as "annualQuota",
        ep.last_quota          as "lastQuota",
        ep.next_quota          as "nextQuota",
        COALESCE(ep.original_quota_day,0)  as "originalQuotaDay",
        lt.leave_type_id as "leaveTypeId",
        wlqc.if_advance
        FROM wa_emp_quota ep
        JOIN sys_emp_info ei on ep.empid = ei.empid and ei.deleted = 0
        JOIN wa_leave_type lt ON ep.leave_type_id = lt.leave_type_id
        JOIN wa_leave_quota_config wlqc on lt.leave_type_id = wlqc.leave_type_id
        WHERE
        #{curtime} BETWEEN ep.start_date AND ep.last_date
          and lt.quota_restriction_type = 1
          and lt.quota_type = 1
          <if test="belongOrgId != null">
              and lt.belong_orgid=#{belongOrgId,jdbcType=VARCHAR}
          </if>
          <if test="empId != null">
              and ep.empid = #{empId}
          </if>
        and ((ei.stats <![CDATA[<>]]> 1 and hire_date <![CDATA[<=]]> #{curtime}) or (ei.stats = 1 AND ei.termination_date <![CDATA[>=]]> #{curtime}))
    </select>
    <select id="getEmpQuotaDetailPageList"
            resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpQuotaDetail">
        SELECT weq.empid,
               COALESCE(weq.quota_day, 0)        as "quotaDay",
               COALESCE(weq.now_quota, 0)        as "nowQuota",
               COALESCE(weq.used_day, 0)         as "usedDay",
               COALESCE(weq.fix_used_day, 0)     as "fixUsedDay",
               COALESCE(weq.adjust_quota, 0)     as "adjustQuota",
               COALESCE(weq.in_transit_quota, 0) as "inTransitQuota",
               weq.period_year                   as "periodYear",
               weq.dis_cycle_start               as "disCycleStart",
               weq.dis_cycle_end                 as "disCycleEnd",
               weq.start_date                    as "startDate",
               weq.last_date                     as "lastDate",
               weq.emp_quota_id                  as "empQuotaId",
               weq.leave_type_id                 as "leaveTypeId",
               weq.crttime,
               wlqc.carry_over_to                as "carryOverTo",
               wlqc.carry_over_validity_unit     as "carryOverValidityUnit",
               wlqc.carry_over_validity_duration as "carryOverValidityDuration",
               wlqc.carry_over_start_type        as "carryOverStartType",
               weq.config_id                     as "configId",
               wlqc.carry_to_type                as "carryToType",
               wlqc.transfer_type                as "transferType",
               wlqc.max_transfer_quota           as "maxTransferQuota"
        FROM wa_emp_quota weq
                 JOIN wa_leave_type wlt on weq.leave_type_id = wlt.leave_type_id
                 JOIN wa_leave_quota_config wlqc on wlqc.config_id = weq.config_id
                 JOIN sys_emp_info ei on weq.empid = ei.empid
        WHERE weq.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        AND (weq.if_carry_forward is null or weq.if_carry_forward = false)
        AND weq.original_emp_quota_id is null
        AND weq.last_date <![CDATA[<]]> #{curDate}
        AND (COALESCE(weq.quota_day, 0) + COALESCE(weq.adjust_quota, 0) - COALESCE(weq.used_day, 0) -
        COALESCE(weq.fix_used_day, 0) - COALESCE(weq.in_transit_quota, 0)) > 0
        AND wlt.quota_type = 1
        AND wlqc.expiration_rule = 3
        AND wlqc.carry_over_to is not null
        AND wlqc.carry_over_start_type is not null
        AND wlqc.carry_over_validity_unit is not null
        AND wlqc.carry_over_validity_duration is not null
        AND ei.deleted = 0
        AND ((ei.stats <![CDATA[<>]]> 1 AND hire_date <![CDATA[<=]]> #{curDate}) or (ei.stats = 1 AND ei.termination_date <![CDATA[>=]]> #{curDate}))
        AND extract(year from to_timestamp(ei.hire_date)) <![CDATA[<=]]> #{year}
    </select>
    <select id="getWaEmpQuotaCountByLeaveTypeId" resultType="java.lang.Integer">
        select count(0) as num
        from wa_emp_quota
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and leave_type_id = #{leaveTypeId}
    </select>
    <select id="getWaEmpCompensatoryQuotaCountByLeaveTypeId" resultType="java.lang.Integer">
        select count(0)
        from wa_emp_compensatory_quota
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and leave_type_id = #{leaveTypeId}
          and deleted = 0
    </select>

    <update id="updateQuotaDayByAnnualQuota">
        update wa_emp_quota
        set quota_day=annual_quota,
            updtime=#{updtime}
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and cross_quota_date = #{crossQuotaDate}
          and annual_quota is not null
          and annual_quota != quota_day
    </update>

    <select id="getIssuedAnnuallyQuotaListByEffectiveTime" resultType="map">
        SELECT
        emp_quota_id,
        ep.empid,
        ei.belong_org_id,
        hire_date,
        ep.start_date,
        ep.last_date,
        COALESCE(quota_day, 0) as quota_day,
        COALESCE(now_quota, 0) as "now_quota",
        lt.acct_time_type,
        lt.leave_type,
        ep.dis_cycle_start,
        ep.dis_cycle_end,
        ei.termination_date,
        ei.prodead_line,
        ep.cross_quota_date    as "crossQuotaDate",
        ep.annual_quota        as "annualQuota",
        ep.last_quota          as "lastQuota",
        ep.next_quota          as "nextQuota",
        ep.sec_cross_quota_date          as "secCrossQuotaDate",
        ep.third_quota         as "thirdQuota",
        COALESCE(ep.original_quota_day,0)  as "originalQuotaDay",
        lt.leave_type_id as "leaveTypeId",
        ei.stats as "empStatus",
        wlqc.now_distribute_rule,
        wlqc.now_rounding_rule,
        wlqc.if_advance,
        wlqc.config_id as "quotaConfigId",
        wlqc.quota_distribute_rule as "quotaDistributeRule",
        wlqc.day_of_hire_month_dist as "dayOfHireMonthDist"
        FROM wa_emp_quota ep
        JOIN sys_emp_info ei on ep.empid = ei.empid
        JOIN wa_leave_type lt ON ep.leave_type_id = lt.leave_type_id
        LEFT JOIN wa_leave_quota_config wlqc on wlqc.config_id = ep.config_id
        WHERE
        lt.belong_orgid=#{belongOrgId,jdbcType=VARCHAR}
        AND lt.quota_restriction_type = 1
        AND lt.quota_type in (1,4) and ei.deleted = 0
        <if test="empId != null">
            AND ep.empid = #{empId}
        </if>
        AND #{startDate} <![CDATA[<=]]> ep.last_date
        AND #{endDate} <![CDATA[>=]]> ep.start_date
    </select>

    <select id="getEmpLeaveQuotaList" resultType="com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo">
        select distinct
           quota.emp_quota_id     empQuotaId,
           quota.leave_type_id    leaveTypeId,
           leave_type.leave_name  leaveName,
           quota.quota_day        quotaDay,
           quota.used_day         usedDay,
           quota.adjust_quota     adjustQuota,
           quota.fix_used_day     fixUsedDay,
           quota.in_transit_quota inTransitQuota,
           leave_type.quota_type  quotaType,
           leave_type.acct_time_type  unit,
           quota.if_advance          ifAdvance,
           quota.now_quota nowQuotaDay,
           leave_type.i18n_leave_name  i18nLeaveName,
           leave_type.display_quota_detail displayQuotaDetail
        from wa_emp_group_view a, wa_group b,wa_emp_quota quota
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id and leave_type.is_emp_show=true and leave_type.allow_view_quota
        where quota.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and quota.empid = #{empId} and leave_type.quota_type in (1,4) and #{curDate} BETWEEN start_date and last_date
        and a.empid=quota.empid and a.wa_group_id=b.wa_group_id and quota.leave_type_id=any(b.leave_type_ids)
     union all
        select distinct
           quota.emp_quota_id     empQuotaId,
           quota.leave_type_id    leaveTypeId,
           leave_type.leave_name  leaveName,
           quota.quota_day        quotaDay,
           quota.used_day         usedDay,
           quota.adjust_quota     adjustQuota,
           quota.fix_used_day     fixUsedDay,
           quota.in_transit_quota inTransitQuota,
           leave_type.quota_type  quotaType,
           leave_type.acct_time_type  unit,
           quota.if_advance          ifAdvance,
           quota.now_quota nowQuotaDay,
           leave_type.i18n_leave_name  i18nLeaveName,
           leave_type.display_quota_detail displayQuotaDetail
        from wa_emp_group_view a, wa_group b,wa_emp_quota quota
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id and leave_type.is_emp_show=true and leave_type.allow_view_quota
        where quota.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and quota.empid = #{empId} and quota_type=3 and used_day=0 and (quota.in_transit_quota is null or quota.in_transit_quota = 0)
        and a.empid=quota.empid and a.wa_group_id=b.wa_group_id and quota.leave_type_id=any(b.leave_type_ids)
     union all
        select distinct
           quota.quota_id         empQuotaId,
           quota.leave_type_id    leaveTypeId,
           leave_type.leave_name  leaveName,
           quota.quota_day        quotaDay,
           quota.used_day         usedDay,
           quota.adjust_quota_day adjustQuota,
           0                      fixUsedDay,
           quota.in_transit_quota inTransitQuota,
           leave_type.quota_type  quotaType,
           quota.quota_unit       unit,
           0          ifAdvance,
           0          nowQuotaDay,
           leave_type.i18n_leave_name  i18nLeaveName,
           leave_type.display_quota_detail displayQuotaDetail
        from wa_emp_group_view a, wa_group b,wa_emp_compensatory_quota quota
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id and leave_type.is_emp_show=true and leave_type.allow_view_quota
        where tenant_id = #{belongOrgId,jdbcType=VARCHAR}
        and emp_id = #{empId}
        and status = 2
        and #{curDate}  BETWEEN start_date and last_date
        and deleted=0 and a.empid=quota.emp_id and a.wa_group_id=b.wa_group_id and quota.leave_type_id=any(b.leave_type_ids);
    </select>

    <select id="getAnnualLeaveList" resultType="com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo">
      select
        ei.hire_date           hireDate,
        ep.period_year         periodYear,
        ep.start_date          startDate,
        ep.last_date           lastDate,
        lt.acct_time_type      unit,
        ep.quota_day           quotaDay,
        ep.now_quota           nowQuotaDay,
        ep.used_day            usedDay,
        ep.adjust_quota        adjustQuota,
        ep.in_transit_quota    inTransitQuota,
        ep.fix_used_day        fixUsedDay,
        ep.if_advance          ifAdvance,
        lt.quota_type          quotaType,
        ei.workplace,
        lt.rmk description,
        ep.emp_quota_id empQuotaId,
        ep.config_id configId,
        temp.retainDay,
        temp.retainInTransitQuota,
        temp.retainUsedDay,
        temp.retainValidDate
      from wa_emp_quota ep
      JOIN sys_emp_info ei ON ep.empid = ei.empid AND ei.deleted = 0
      JOIN wa_leave_type lt ON lt.leave_type_id = ep.leave_type_id
      LEFT JOIN (SELECT merge_emp_quota_id, last_date retainValidDate,
                COALESCE(CASE WHEN (last_date IS NULL OR last_date <![CDATA[<]]> #{curDate}) THEN COALESCE(in_transit_quota,0)+COALESCE(used_day,0) ELSE quota_day END, 0) retainDay,
                COALESCE(in_transit_quota, 0) retainInTransitQuota,
                COALESCE(used_day, 0) retainUsedDay
                FROM wa_emp_quota WHERE carry_merge AND original_emp_quota_id IS NOT NULL AND merge_emp_quota_id IS NOT NULL AND last_date IS NOT NULL
                <if test="mergeCarry != null and mergeCarry == false">
                    AND FALSE
                </if>
          ) temp
      ON temp.merge_emp_quota_id=ep.emp_quota_id
      WHERE ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND ei.empid = #{empId} AND ep.leave_type_id = #{leaveTypeId}
        <if test="mergeCarry != null and mergeCarry">
            AND (ep.carry_merge IS NULL OR ep.carry_merge=false)
        </if>
      <if test="configId!=null">
         and ep.config_id=#{configId}
      </if>
      <if test="quotaType == 1 or quotaType == 4">
        and #{curDate} BETWEEN start_date and last_date
      </if>
      <if test="quotaType == 3">
        and used_day=0 and ep.in_transit_quota=0
      </if>
    </select>

    <select id="getCompensatoryQuotaList"  resultType="com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo">
        select quota.config_id configId,quota.quota_id empQuotaId,
          quota.overtime_type          overTimeType,
          quota.start_date             startDate,
          quota.last_date              lastDate,
          quota.quota_day              quotaDay,
          quota.in_transit_quota       inTransitQuota,
          quota.adjust_quota_day       adjustQuota,
          quota.used_day               usedDay,
          quota.quota_unit             unit,
          quota.overtime_date          overtimeDate,
          leave_type.rmk description,
          quota.data_source dataSource,
          emp.hire_date hireDate
        from wa_emp_compensatory_quota quota
         join sys_emp_info emp on quota.emp_id = emp.empid and emp.deleted = 0
         join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
        where quota.tenant_id = #{belongOrgId,jdbcType=VARCHAR}
        <if test="leaveTypeId!=null">
            and quota.leave_type_id=#{leaveTypeId}
        </if>
        <if test="configId!=null">
            and quota.config_id=#{configId}
        </if>
        <if test="onlyAutoDataSource!=null and onlyAutoDataSource">
            and quota.data_source='AUTO'
        </if>
        and emp_id = #{empId}
        and quota.status = 2
        and #{curDate} BETWEEN start_date and last_date
        and quota.deleted = 0
        order by case when quota.overtime_date is null then 2 else 1 end,quota.overtime_date desc,quota.last_date,quota.start_date
    </select>

    <select id="getInvalidCompensatory"  resultType="com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo">
        select quota.config_id configId,quota.quota_id empQuotaId,
               quota.overtime_type          overTimeType,
               quota.start_date             startDate,
               quota.last_date              lastDate,
               quota.quota_day              quotaDay,
               quota.in_transit_quota       inTransitQuota,
               quota.adjust_quota_day       adjustQuota,
               quota.used_day               usedDay,
               quota.quota_unit             unit,
               quota.overtime_date          overtimeDate,
               leave_type.rmk description,
               quota.data_source dataSource,
               emp.hire_date hireDate
        from wa_emp_compensatory_quota quota
                 join sys_emp_info emp on quota.emp_id = emp.empid and emp.deleted = 0
                 join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
        where quota.tenant_id = #{belongOrgId,jdbcType=VARCHAR}
          and emp_id = #{empId}
          and quota.status = 2
          and  last_date &lt; #{curDate}
          and quota.deleted = 0
        order by case when quota.overtime_date is null then 2 else 1 end,quota.overtime_date desc,quota.last_date,quota.start_date
    </select>

    <select id="filterByHomeLeaveType" resultType="java.lang.Integer">
        select emp_quota_id
        from wa_emp_quota where home_leave_type
        IN <foreach collection="homeLeaveTypes" item="typeItem" open="(" separator="," close=")">#{typeItem}</foreach>
        and emp_quota_id
        IN <foreach collection="quotaIds" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <select id="filterNotInTransit" resultType="java.lang.Integer">
        select emp_quota_id
        from wa_emp_quota where (in_transit_quota = 0 or in_transit_quota is null)
        and emp_quota_id
        IN <foreach collection="filterQuotaIds" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <select id="getAvailableHomeLeaveQuota" resultType="java.util.Map">
        select quota_day as quotaDay, now_quota as nowQuota, adjust_quota as adjustQuota,
        if_advance as ifAdvance, acct_time_type as accttimetype
        from wa_emp_quota quota inner join wa_leave_type type on quota.leave_type_id = type.leave_type_id where quota.leave_type_id = #{leaveTypeId} and quota.empid = #{empId}
        and quota.start_date &lt;= EXTRACT(epoch FROM now()) and quota.last_date >= EXTRACT(epoch FROM now())
        and (quota.used_day &lt;= 0 or quota.used_day is null) and ( quota.in_transit_quota &lt;= 0 or quota.in_transit_quota is null)
        and home_leave_type
        IN <foreach collection="homeLeaveTypes" item="typeItem" open="(" separator="," close=")">#{typeItem}</foreach>
    </select>

    <select id="queryInvalidCompensatoryQuotaList"  resultType="com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo">
        select quota.config_id configId,quota.quota_id empQuotaId,
               quota.overtime_type          overTimeType,
               quota.start_date             startDate,
               quota.last_date              lastDate,
               quota.quota_day              quotaDay,
               quota.in_transit_quota       inTransitQuota,
               quota.adjust_quota_day       adjustQuota,
               quota.used_day               usedDay,
               quota.quota_unit             unit,
               quota.overtime_date          overtimeDate,
               leave_type.rmk description,
               quota.data_source dataSource,
               emp.hire_date hireDate
        from wa_emp_compensatory_quota quota
        join sys_emp_info emp on quota.emp_id = emp.empid and emp.deleted = 0
        join wa_leave_type leave_type on quota.leave_type_id = leave_type.leave_type_id
        where quota.tenant_id = #{belongOrgId,jdbcType=VARCHAR}
          <if test="leaveTypeId!=null">
              and quota.leave_type_id=#{leaveTypeId}
          </if>
          <if test="onlyAutoDataSource!=null and onlyAutoDataSource">
            and quota.data_source='AUTO'
          </if>
          and emp_id = #{empId}
          and quota.status = 2
          and last_date &lt; #{curDate}
          and quota.deleted = 0;
    </select>

    <select id="queryEmpQuotaList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo">
        select * from wa_emp_quota
        where belong_org_id=#{tenantId}
          <if test="quotaIds != null and quotaIds.size() > 0">
              and emp_quota_id in
              <foreach collection="quotaIds" item="quotaId" open="(" close=")" separator=",">
                  #{quotaId}
              </foreach>
          </if>
    </select>

    <select id="queryNotExpiredEmpQuotaListByYear" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpQuotaDetail">
        SELECT weq.empid,
               COALESCE(weq.quota_day, 0)        as "quotaDay",
               COALESCE(weq.now_quota, 0)        as "nowQuota",
               COALESCE(weq.used_day, 0)         as "usedDay",
               COALESCE(weq.fix_used_day, 0)     as "fixUsedDay",
               COALESCE(weq.adjust_quota, 0)     as "adjustQuota",
               COALESCE(weq.in_transit_quota, 0) as "inTransitQuota",
               weq.period_year                   as "periodYear",
               weq.dis_cycle_start               as "disCycleStart",
               weq.dis_cycle_end                 as "disCycleEnd",
               weq.start_date                    as "startDate",
               weq.last_date                     as "lastDate",
               weq.emp_quota_id                  as "empQuotaId",
               weq.leave_type_id                 as "leaveTypeId",
               weq.crttime,
               wlqc.carry_over_to                as "carryOverTo",
               wlqc.carry_over_validity_unit     as "carryOverValidityUnit",
               wlqc.carry_over_validity_duration as "carryOverValidityDuration",
               wlqc.carry_over_start_type        as "carryOverStartType",
               weq.config_id                     as "configId",
               wlqc.carry_to_type                as "carryToType"
        FROM wa_emp_quota weq
                 JOIN wa_leave_type wlt on weq.leave_type_id = wlt.leave_type_id
                 JOIN wa_leave_quota_config wlqc on wlqc.config_id = weq.config_id
                 JOIN sys_emp_info ei on weq.empid = ei.empid
        WHERE weq.belong_org_id = #{tenantId,jdbcType=VARCHAR}
          AND (weq.if_carry_forward is null or weq.if_carry_forward = false)
          AND weq.original_emp_quota_id is null
          AND weq.last_date <![CDATA[>]]> #{curDate}
          AND wlt.quota_type = 1
          AND wlqc.expiration_rule = 3
          AND wlqc.carry_over_to is not null
          AND wlqc.carry_over_start_type is not null
          AND wlqc.carry_over_validity_unit is not null
          AND wlqc.carry_over_validity_duration is not null
          AND ei.deleted = 0
          AND ((ei.stats <![CDATA[<>]]> 1 AND hire_date <![CDATA[<=]]> #{curDate}) or (ei.stats = 1 AND ei.termination_date <![CDATA[>=]]> #{curDate}))
          AND extract(year from to_timestamp(ei.hire_date)) <![CDATA[<=]]> #{year}
    </select>

    <select id="getNotCarryAnnualLeaveList" resultType="com.caidaocloud.attendance.service.domain.entity.LeaveQuotaDo">
        select weq.*
        from wa_emp_quota weq
        join wa_leave_type wlt on weq.leave_type_id = wlt.leave_type_id
        where weq.belong_org_id=#{tenantId,jdbcType=VARCHAR}
          <if test="empQuotaId != null">
              and weq.emp_quota_id <![CDATA[!=]]> #{empQuotaId}
          </if>
        and wlt.quota_type=#{quotaType}
        and weq.empid=#{empId}
        and weq.config_id=#{configId}
        and period_year=#{periodYear}
        and weq.leave_type_id=#{leaveTypeId}
        and original_emp_quota_id is null
    </select>

    <select id="queryAnnualLeaveRetainQuota" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo">
        select sei.workno,
        sei.emp_name empName,
        weq.emp_quota_id empQuotaId,
        weq.empid,
        weq.quota_day quotaDay,
        weq.used_day usedDay,
        weq.in_transit_quota inTransitQuota,
        weq.start_date startDate,
        weq.last_date lastDate,
        weq.original_emp_quota_id originalEmpQuotaId,
        lt.acct_time_type acctTimeType
        from wa_emp_quota weq
        join sys_emp_info sei on weq.empid=sei.empid
        join wa_leave_type lt on lt.leave_type_id=weq.leave_type_id
        where weq.belong_org_id=#{tenantId,jdbcType=VARCHAR}
        <if test="empQuotaId != null">
            and weq.merge_emp_quota_id=#{empQuotaId}
        </if>
    </select>

    <select id="getExpiredNotCarryAnnualLeaveList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpQuotaDo">
        select weq.*
        from wa_emp_quota weq
        join wa_leave_type wlt on weq.leave_type_id = wlt.leave_type_id
        where weq.belong_org_id=#{tenantId,jdbcType=VARCHAR}
        and wlt.quota_type=1
        and weq.empid=#{empId}
        and weq.config_id=#{configId}
        and period_year=#{periodYear}
        and weq.leave_type_id=#{leaveTypeId}
        and weq.last_date <![CDATA[<]]> #{curDate}
        and (weq.if_carry_forward is null or weq.if_carry_forward = false)
    </select>
</mapper>