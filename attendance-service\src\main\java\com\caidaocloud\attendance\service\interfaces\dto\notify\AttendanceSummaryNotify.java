package com.caidaocloud.attendance.service.interfaces.dto.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/4/24 15:06
 * @Description:
 **/
@Data
public class AttendanceSummaryNotify {

    @ApiModelProperty("考勤分组id")
    private Integer groupId;

    @ApiModelProperty("考勤分组月份")
    private Integer ym;

    @ApiModelProperty("员工编号")
    private List<Long> empIds;

    @ApiModelProperty("开始日期")
    private Long startDate;

    @ApiModelProperty("结束日期")
    private Long endDate;
}
