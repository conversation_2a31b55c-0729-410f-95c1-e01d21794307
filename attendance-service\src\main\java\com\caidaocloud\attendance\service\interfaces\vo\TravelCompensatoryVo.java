package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TravelCompensatoryVo {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("员工工号")
    private String workNo;
    @ApiModelProperty("配额单位")
    private Integer quotaUnit;
    @ApiModelProperty("转换配额")
    private Float quotaDay;
    @ApiModelProperty("配额名称")
    private String quotaName;
    @ApiModelProperty("状态:0失效，1有效")
    private Integer status;
    @ApiModelProperty("所属考勤周期")
    private Integer periodMonth;
}
