<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel">
        <id column="travel_id" jdbcType="BIGINT" property="travelId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="emp_id" jdbcType="BIGINT" property="empId"/>
        <result column="travel_type_id" jdbcType="BIGINT" property="travelTypeId"/>
        <result column="start_time" jdbcType="BIGINT" property="startTime"/>
        <result column="end_time" jdbcType="BIGINT" property="endTime"/>
        <result column="shalf_day" jdbcType="VARCHAR" property="shalfDay"/>
        <result column="ehalf_day" jdbcType="VARCHAR" property="ehalfDay"/>
        <result column="shift_start_time" jdbcType="BIGINT" property="shiftStartTime"/>
        <result column="shift_end_time" jdbcType="BIGINT" property="shiftEndTime"/>
        <result column="time_duration" jdbcType="REAL" property="timeDuration"/>
        <result column="time_unit" jdbcType="SMALLINT" property="timeUnit"/>
        <result column="period_type" jdbcType="SMALLINT" property="periodType"/>
        <result column="province" jdbcType="BIGINT" property="province"/>
        <result column="city" jdbcType="BIGINT" property="city"/>
        <result column="county" jdbcType="INTEGER" property="county"/>
        <result column="travel_mode" jdbcType="VARCHAR" property="travelMode"/>
        <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime"/>
        <result column="last_empid" jdbcType="BIGINT" property="lastEmpid"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason"/>
        <result column="revoke_status" jdbcType="INTEGER" property="revokeStatus"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_id" jdbcType="VARCHAR" property="fileId"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="quota_id" jdbcType="BIGINT" property="quotaId" />
        <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
        <result column="ext_custom_col" jdbcType="VARCHAR" property="extCustomCol" />
        <result column="batch_travel_id" jdbcType="BIGINT" property="batchTravelId" />
        <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    </resultMap>
    <sql id="Base_Column_List">
        travel_id, tenant_id, emp_id, travel_type_id, start_time, end_time, shalf_day, ehalf_day,
        shift_start_time, shift_end_time, time_duration, time_unit, period_type, province,
        city, county, travel_mode, last_approval_time, last_empid, reason, status, revoke_reason,
        revoke_status, file_name, file_id, deleted, create_by, create_time, update_by, update_time,quota_id
        , business_key, ext_custom_col, batch_travel_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wa_emp_travel
        where travel_id = #{travelId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from wa_emp_travel
        where travel_id = #{travelId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel">
        insert into wa_emp_travel (travel_id, tenant_id, emp_id,
        travel_type_id, start_time, end_time,
        shalf_day, ehalf_day, shift_start_time,
        shift_end_time, time_duration, time_unit,
        period_type, province, city,
        county, travel_mode, last_approval_time,
        last_empid, reason, status,
        revoke_reason, revoke_status, file_name,
        file_id, deleted, create_by,
        create_time, update_by, update_time, quota_id
        , business_key, ext_custom_col, batch_travel_id
        )
        values (#{travelId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{empId,jdbcType=BIGINT},
        #{travelTypeId,jdbcType=BIGINT}, #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT},
        #{shalfDay,jdbcType=VARCHAR}, #{ehalfDay,jdbcType=VARCHAR}, #{shiftStartTime,jdbcType=BIGINT},
        #{shiftEndTime,jdbcType=BIGINT}, #{timeDuration,jdbcType=REAL}, #{timeUnit,jdbcType=SMALLINT},
        #{periodType,jdbcType=SMALLINT}, #{province,jdbcType=BIGINT}, #{city,jdbcType=BIGINT},
        #{county,jdbcType=INTEGER}, #{travelMode,jdbcType=VARCHAR}, #{lastApprovalTime,jdbcType=BIGINT},
        #{lastEmpid,jdbcType=BIGINT}, #{reason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
        #{revokeReason,jdbcType=VARCHAR}, #{revokeStatus,jdbcType=INTEGER}, #{fileName,jdbcType=VARCHAR},
        #{fileId,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT},
        #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},#{quotaId,jdbcType=BIGINT}
         , #{businessKey,jdbcType=VARCHAR}, #{extCustomCol,jdbcType=VARCHAR}, #{batchTravelId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel">
        insert into wa_emp_travel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="travelId != null">
                travel_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="travelTypeId != null">
                travel_type_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="shalfDay != null">
                shalf_day,
            </if>
            <if test="ehalfDay != null">
                ehalf_day,
            </if>
            <if test="shiftStartTime != null">
                shift_start_time,
            </if>
            <if test="shiftEndTime != null">
                shift_end_time,
            </if>
            <if test="timeDuration != null">
                time_duration,
            </if>
            <if test="timeUnit != null">
                time_unit,
            </if>
            <if test="periodType != null">
                period_type,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="travelMode != null">
                travel_mode,
            </if>
            <if test="lastApprovalTime != null">
                last_approval_time,
            </if>
            <if test="lastEmpid != null">
                last_empid,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="revokeReason != null">
                revoke_reason,
            </if>
            <if test="revokeStatus != null">
                revoke_status,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="fileId != null">
                file_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="quotaId != null">
                quota_id,
            </if>
            <if test="businessKey != null">
              business_key,
            </if>
            <if test="extCustomCol != null">
              ext_custom_col,
            </if>
            <if test="batchTravelId != null">
              batch_travel_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="travelId != null">
                #{travelId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=BIGINT},
            </if>
            <if test="travelTypeId != null">
                #{travelTypeId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=BIGINT},
            </if>
            <if test="shalfDay != null">
                #{shalfDay,jdbcType=VARCHAR},
            </if>
            <if test="ehalfDay != null">
                #{ehalfDay,jdbcType=VARCHAR},
            </if>
            <if test="shiftStartTime != null">
                #{shiftStartTime,jdbcType=BIGINT},
            </if>
            <if test="shiftEndTime != null">
                #{shiftEndTime,jdbcType=BIGINT},
            </if>
            <if test="timeDuration != null">
                #{timeDuration,jdbcType=REAL},
            </if>
            <if test="timeUnit != null">
                #{timeUnit,jdbcType=SMALLINT},
            </if>
            <if test="periodType != null">
                #{periodType,jdbcType=SMALLINT},
            </if>
            <if test="province != null">
                #{province,jdbcType=BIGINT},
            </if>
            <if test="city != null">
                #{city,jdbcType=BIGINT},
            </if>
            <if test="county != null">
                #{county,jdbcType=INTEGER},
            </if>
            <if test="travelMode != null">
                #{travelMode,jdbcType=VARCHAR},
            </if>
            <if test="lastApprovalTime != null">
                #{lastApprovalTime,jdbcType=BIGINT},
            </if>
            <if test="lastEmpid != null">
                #{lastEmpid,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="revokeReason != null">
                #{revokeReason,jdbcType=VARCHAR},
            </if>
            <if test="revokeStatus != null">
                #{revokeStatus,jdbcType=INTEGER},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileId != null">
                #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="quotaId != null">
                #{quotaId,jdbcType=BIGINT},
            </if>
            <if test="businessKey != null">
              #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="extCustomCol != null">
              #{extCustomCol,jdbcType=VARCHAR},
            </if>
            <if test="batchTravelId != null">
              #{batchTravelId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel">
        update wa_emp_travel
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=BIGINT},
            </if>
            <if test="travelTypeId != null">
                travel_type_id = #{travelTypeId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=BIGINT},
            </if>
            <if test="shalfDay != null">
                shalf_day = #{shalfDay,jdbcType=VARCHAR},
            </if>
            <if test="ehalfDay != null">
                ehalf_day = #{ehalfDay,jdbcType=VARCHAR},
            </if>
            <if test="shiftStartTime != null">
                shift_start_time = #{shiftStartTime,jdbcType=BIGINT},
            </if>
            <if test="shiftEndTime != null">
                shift_end_time = #{shiftEndTime,jdbcType=BIGINT},
            </if>
            <if test="timeDuration != null">
                time_duration = #{timeDuration,jdbcType=REAL},
            </if>
            <if test="timeUnit != null">
                time_unit = #{timeUnit,jdbcType=SMALLINT},
            </if>
            <if test="periodType != null">
                period_type = #{periodType,jdbcType=SMALLINT},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=BIGINT},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=BIGINT},
            </if>
            <if test="county != null">
                county = #{county,jdbcType=INTEGER},
            </if>
            <if test="travelMode != null">
                travel_mode = #{travelMode,jdbcType=VARCHAR},
            </if>
            <if test="lastApprovalTime != null">
                last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
            </if>
            <if test="lastEmpid != null">
                last_empid = #{lastEmpid,jdbcType=BIGINT},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="revokeReason != null">
                revoke_reason = #{revokeReason,jdbcType=VARCHAR},
            </if>
            <if test="revokeStatus != null">
                revoke_status = #{revokeStatus,jdbcType=INTEGER},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileId != null">
                file_id = #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="quotaId != null">
                quota_id = #{quotaId,jdbcType=BIGINT},
            </if>
            <if test="businessKey != null">
              business_key = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="extCustomCol != null">
              ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
            </if>
            <if test="batchTravelId != null">
              batch_travel_id = #{batchTravelId,jdbcType=BIGINT},
            </if>
        </set>
        where travel_id = #{travelId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel">
    update wa_emp_travel
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=BIGINT},
      travel_type_id = #{travelTypeId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      shalf_day = #{shalfDay,jdbcType=VARCHAR},
      ehalf_day = #{ehalfDay,jdbcType=VARCHAR},
      shift_start_time = #{shiftStartTime,jdbcType=BIGINT},
      shift_end_time = #{shiftEndTime,jdbcType=BIGINT},
      time_duration = #{timeDuration,jdbcType=REAL},
      time_unit = #{timeUnit,jdbcType=SMALLINT},
      period_type = #{periodType,jdbcType=SMALLINT},
      province = #{province,jdbcType=BIGINT},
      city = #{city,jdbcType=BIGINT},
      county = #{county,jdbcType=INTEGER},
      travel_mode = #{travelMode,jdbcType=VARCHAR},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      revoke_status = #{revokeStatus,jdbcType=INTEGER},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      quota_id = #{quotaId,jdbcType=BIGINT},
      business_key = #{businessKey,jdbcType=VARCHAR},
      ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      batch_travel_id = #{batchTravelId,jdbcType=BIGINT}
    where travel_id = #{travelId,jdbcType=BIGINT}
  </update>

    <select id="getWaEmpTravelPageList" resultMap="BaseResultMap">
        select * from (
        select
        ei.workno,
        ei.emp_name,
        sco.shortname as "orgName",
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end as "fullPath",
        wtt.travel_type_name as "travelType",
        wtt.i18n_travel_type_name as "i18nTravelTypeName",
        wtt.travel_type_id,
        wet.travel_id,
        wet.quota_id,
        wet.time_duration,
        wet.time_unit,
        wet.period_type,
        wet.start_time,
        wet.end_time,
        wet.shift_start_time,
        wet.shift_end_time,
        wet.shalf_day,
        wet.ehalf_day,
        wet.travel_mode,
        wet.province,
        wet.county,
        wet.city,
        wet.status,
        wet.last_approval_time,
        wet.reason,
        wet.create_time,
        wet.batch_travel_id,
        ei.employ_type,
        ei.orgid,
        wet.process_code as "processCode",
        wet.ext_custom_col
        from wa_emp_travel wet
        join sys_emp_info ei on ei.empid = wet.emp_id and ei.deleted = 0
        left join sys_corp_org sco on sco.orgid = ei.orgid
        left join wa_travel_type wtt on wtt.travel_type_id = wet.travel_type_id
        <where>
            AND wet.deleted=0
            AND ei.belong_org_id = '${belongOrgId}'
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
            <if test="keywords != null and keywords != ''">
                and (ei.emp_name like concat('%', #{keywords}, '%') or ei.workno like concat('%', #{keywords}, '%') or wet.process_code like concat(#{keywords}, '%'))
            </if>
            <if test="entityId!=null">
                and wet.travel_id in (select distinct travel_id from wa_emp_travel_daytime daytime where daytime.entity_id=#{entityId})
            </if>
            <if test="empId != null">
                and wet.emp_id = #{empId}
            </if>
            <if test="travelMode != null">
                and string_to_array(travel_mode,',') <![CDATA[&&]]> string_to_array(#{travelMode},',')
            </if>
            <if test="ifBatch != null and ifBatch">
                AND batch_travel_id is not null
            </if>
        </where>
        order by wet.update_time desc
        ) as t
        <where>
            <if test="filter != null">
                ${filter}
            </if>
            <if test="startDateTime != null">
                AND (
                (t.shift_start_time &gt;= #{startDateTime} AND t.shift_start_time &lt;= #{endDateTime})
                OR (t.shift_end_time &gt;= #{startDateTime} AND t.shift_end_time &lt;= #{endDateTime})
                OR (t.shift_start_time &lt;= #{startDateTime} AND t.shift_end_time &gt;= #{endDateTime})
                )
            </if>
            <if test="status != null and status !=''">
                AND t.status IN (${status})
            </if>
        </where>
    </select>

    <select id="getWaEmpTravelById" resultMap="BaseResultMap">
        select
         ei.workno,
         ei.emp_name,
         co.shortname as "orgName",
         case
             when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
             else co.shortname
             end  as "fullPath",
         ei.hire_date as "hireDate",
         wet.period_type,
         wet.start_time,
         wet.end_time,
         wet.shift_start_time,
         wet.shift_end_time,
         wet.create_time,
         wtt.travel_type_name as "travelType",
         wtt.i18n_travel_type_name as "i18nTravelTypeName",
         wet.province,
         wet.city,
         wet.county,
         wet.travel_mode,
         wet.status,
         case
          when wet.status = 0
           then '暂存'
          when wet.status = 1
           then '审批中'
          when wet.status = 2
           then '已通过'
          when wet.status = 3
           then '已拒绝'
          when wet.status = 4
           then '已作废'
          when wet.status = 5
           then '已退回'
          when wet.status = 9
           then '已撤销' END AS "statusName",
          wet.reason,
          wet.revoke_reason,
          wet.province,
	      wet.county,
	      wet.travel_mode,
	      wet.file_id,
	      wet.file_name,
	      wet.time_duration,
	      wet.time_unit,
          ei.workplace as "workCity",
          ei.employ_type as "employType",
	      wet.shalf_day,
	      wet.ehalf_day,
          wet.ext_custom_col,
          wet.travel_id,wet.create_by,
          wet.process_code
        from
        wa_emp_travel wet
        join wa_travel_type wtt ON wtt.travel_type_id = wet.travel_type_id
        join sys_emp_info ei on ei.empid=wet.emp_id and ei.deleted = 0
        left join sys_corp_org co ON co.orgid = ei.orgid
        where travel_id = #{travelId}
        <if test="corpId != null">
            and ei.corpid = #{corpId}
        </if>
  </select>

    <select id="checkEmpTravelTimeRepeat" resultType="java.lang.Integer">
        SELECT count(0) from (
            SELECT
             shift_start_time,
              CASE
              WHEN(period_type = 1 OR period_type = 4)
              THEN shift_end_time + 86399
              ELSE
                  shift_end_time
              END AS shift_end_time
            FROM wa_emp_travel
            WHERE emp_id = #{empId}
            AND status in (1, 2, 8)
        ) m WHERE
        <![CDATA[
         (
        (#{startTime} >= shift_start_time AND #{startTime} < shift_end_time)
        OR (#{endTime} > shift_start_time AND #{endTime} <= shift_end_time)
        OR (#{startTime} <= shift_start_time AND #{endTime} >= shift_end_time)
        )
        ]]>
    </select>

    <select id="getTravelList" resultMap="BaseResultMap">
        select
        ei.workno,
        ei.emp_name,
        sco.shortname as "orgName",
        case
            when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
            else sco.shortname
            end  as "fullPath",
        wtt.travel_type_name as "travelType",
        wtt.i18n_travel_type_name as "i18nTravelTypeName",
        wtt.travel_type_id,
        wet.travel_id,
        wtd.time_duration,
        wet.time_unit,
        wet.period_type,
        wet.start_time,
        wet.end_time,
        wet.shift_start_time,
        wet.shift_end_time,
        wet.shalf_day,
        wet.ehalf_day,
        wet.travel_mode,
        wet.province,
        wet.county,
        wet.city,
        wet.status,
        wet.last_approval_time,
        wet.reason,
        wet.create_time
        from
        wa_emp_travel wet
        join wa_emp_travel_daytime wtd on wtd.travel_id = wet.travel_id
        join sys_emp_info ei on ei.empid = wet.emp_id and ei.deleted = 0
        left join sys_corp_org sco on sco.orgid = ei.orgid
        left join wa_travel_type wtt on wtt.travel_type_id = wet.travel_type_id
        where wet.deleted=0 and ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and wet.emp_id = #{empId} and wet.status in (1,2) and wtd.travel_date between #{dayTime} and #{endTime}
    </select>

    <select id="queryTravelInfoByTravelTypeId" resultMap="BaseResultMap">
        select * from wa_emp_travel
        where deleted = 0 and tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="travelTypeId != null">
            and travel_type_id = #{travelTypeId}
        </if>
    </select>

    <select id="selectEmpTravelByEmpIds" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpTravelDo">
        select a.time_unit,
        coalesce(b.apply_time_duration, b.time_duration) as time_duration,
        a.emp_id,
        b.real_date,wtt.travel_type_name as travel_type,a.status
        from wa_emp_travel a
        left join wa_emp_travel_daytime b on a.travel_id=b.travel_id
        left join wa_travel_type wtt on a.travel_type_id = wtt.travel_type_id
        where b.time_duration > 0 and b.real_date > 0 and b.real_date=#{travelDate}
        <if test="empIds != null and empIds.size() > 0">
            and a.emp_id in
            <foreach collection="empIds" open="(" close=")" item="empId" separator=",">
                #{empId}
            </foreach>
        </if>
    </select>

    <select id="queryEmpTravelRevokeById" resultMap="BaseResultMap">
        select
        ei.workno,
        ei.emp_name,
        co.shortname as "orgName",
        case
        when co.full_path is not null and co.full_path != ''
        then concat_ws('/', co.full_path, co.shortname)
        else co.shortname
        end  as "fullPath",
        ei.hire_date as "hireDate",
        wet.period_type,
        wet.start_time,
        wet.end_time,
        wet.shift_start_time,
        wet.shift_end_time,
        wwr.create_time,
        wtt.travel_type_name as "travelType",
        wtt.i18n_travel_type_name as "i18nTravelTypeName",
        wet.province,
        wet.city,
        wet.county,
        wet.travel_mode,
        wwr.status,
        case
        when wwr.status = 0
        then '暂存'
        when wwr.status = 1
        then '审批中'
        when wwr.status = 2
        then '已通过'
        when wwr.status = 3
        then '已拒绝'
        when wwr.status = 4
        then '已作废'
        when wwr.status = 5
        then '已退回'
        when wwr.status = 9
        then '已撤销' END AS "statusName",
        wwr.reason,
        wet.revoke_reason,
        wet.province,
        wet.county,
        wet.travel_mode,
        wet.file_id,
        wet.file_name,
        wet.time_duration,
        wet.time_unit,
        ei.workplace as "workCity",
        ei.employ_type as "employType",
        wet.shalf_day,
        wet.ehalf_day,
        wwr.process_code
        from wa_emp_travel wet
        join wa_workflow_revoke wwr on wwr.entity_id=wet.travel_id
        join wa_travel_type wtt ON wtt.travel_type_id = wet.travel_type_id
        join sys_emp_info ei on ei.empid=wet.emp_id and ei.deleted = 0
        left join sys_corp_org co ON co.orgid = ei.orgid
        where wwr.id = #{id}
        <if test="tenantId != null">
            AND ei.belong_org_id = #{tenantId}
        </if>
    </select>
    <delete id="deleteByTravelIds">
     delete from wa_emp_travel
     <where>
        AND travel_id IN
        <foreach collection="travelIds" item="travelId" open="(" close=")" separator=",">
            #{travelId,jdbcType=BIGINT}
        </foreach>
    </where>
    </delete>
</mapper>