package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/2/28
 */
@Data
public class RegisterRecordAnalyzeVo extends RegisterRecordVo {
    @ApiModelProperty("考勤日期")
    private Long belongDate;
    @ApiModelProperty("打卡类型编码")
    private Integer registerType;
    @ApiModelProperty("打卡类型名称")
    private String registerTypeName;
    @ApiModelProperty("打卡结果")
    private Integer resultType;
    @ApiModelProperty("打卡结果名称")
    private String resultTypeName;
}
