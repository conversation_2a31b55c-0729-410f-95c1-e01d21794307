<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaLeaveExtensionMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="quota_id" jdbcType="BIGINT" property="quotaId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="leave_type_id" jdbcType="INTEGER" property="leaveTypeId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="time_duration" jdbcType="REAL" property="timeDuration" />
    <result column="time_unit" jdbcType="INTEGER" property="timeUnit" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="end_date" jdbcType="BIGINT" property="endDate" />
    <result column="original_end_date" jdbcType="BIGINT" property="originalEndDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, quota_id, emp_id, leave_type_id, config_id, time_duration, time_unit, 
    start_date, end_date, original_end_date, status, last_approval_time, reason, revoke_reason, file_name, file_id,
    deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_leave_extension
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_leave_extension
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension">
    insert into wa_leave_extension (id, tenant_id, quota_id, 
      emp_id, leave_type_id, config_id, 
      time_duration, time_unit, start_date, 
      end_date, original_end_date, status, last_approval_time, reason,
      revoke_reason, file_name, file_id, 
      deleted, create_by, create_time, 
      update_by, update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{quotaId,jdbcType=BIGINT}, 
      #{empId,jdbcType=BIGINT}, #{leaveTypeId,jdbcType=INTEGER}, #{configId,jdbcType=BIGINT}, 
      #{timeDuration,jdbcType=REAL}, #{timeUnit,jdbcType=INTEGER}, #{startDate,jdbcType=BIGINT}, 
      #{endDate,jdbcType=BIGINT}, #{originalEndDate,jdbcType=BIGINT}, #{status,jdbcType=INTEGER},
      #{lastApprovalTime,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR},
      #{revokeReason,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{fileId,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension">
    insert into wa_leave_extension
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="leaveTypeId != null">
        leave_type_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="timeDuration != null">
        time_duration,
      </if>
      <if test="timeUnit != null">
        time_unit,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="originalEndDate != null">
        original_end_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=BIGINT},
      </if>
      <if test="leaveTypeId != null">
        #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="timeDuration != null">
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=BIGINT},
      </if>
      <if test="originalEndDate != null">
        #{originalEndDate,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension">
    update wa_leave_extension
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="quotaId != null">
        quota_id = #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=BIGINT},
      </if>
      <if test="leaveTypeId != null">
        leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="timeDuration != null">
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        time_unit = #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=BIGINT},
      </if>
      <if test="originalEndDate != null">
        original_end_date = #{originalEndDate,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaLeaveExtension">
    update wa_leave_extension
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      quota_id = #{quotaId,jdbcType=BIGINT},
      emp_id = #{empId,jdbcType=BIGINT},
      leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      config_id = #{configId,jdbcType=BIGINT},
      time_duration = #{timeDuration,jdbcType=REAL},
      time_unit = #{timeUnit,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=BIGINT},
      end_date = #{endDate,jdbcType=BIGINT},
      original_end_date = #{originalEndDate,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      reason = #{reason,jdbcType=VARCHAR},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryLeaveExtensionList" parameterType="hashmap" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveExtensionDo">
    select * from(
    SELECT a.id,
    b.empid empId,
    b.hire_date as "hireDate",
    b.workno workNo,
    b.emp_name empName,
    b.orgid,
    case
    when sco.full_path is not null and sco.full_path != ''
    then concat_ws('/', sco.full_path, sco.shortname)
    else sco.shortname
    end  as orgName,
    a.time_duration timeDuration,
    a.time_unit timeUnit,
    a.start_date startDate,
    a.end_date endDate,
    a.create_time createTime,
    a.status,
    a.last_approval_time lastApprovalTime,
    a.reason,
    a.revoke_reason revokeReason,
    wlqc.rule_name quotaName,
    wlqc.i18n_rule_name i18nRuleName,
    a.file_id,
    a.file_name,
    b.workplace as workCity,
    b.employ_type as employType,
    a.config_id
    FROM wa_leave_extension a
    JOIN sys_emp_info b ON a.emp_id = b.empid AND b.deleted = 0
    LEFT JOIN wa_leave_quota_config wlqc on wlqc.config_id=a.config_id
    LEFT JOIN sys_corp_org sco on b.orgid = sco.orgid AND sco.deleted = 0
    <where>
      AND a.tenant_id=#{tenantId} AND a.deleted=0
      <if test="dataFilter != null and dataFilter != ''">
        ${dataFilter}
      </if>
      <if test="status != null">
        AND a.status = #{status}
      </if>
      <if test="empId != null">
        AND a.emp_id = #{empId}
      </if>
      <if test="startDate != null and endDate != null">
        AND a.last_approval_time between #{startDate} AND #{endDate}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (b.workno like concat('%', #{keywords}, '%') or b.emp_name like concat('%', #{keywords}, '%'))
      </if>
    </where>) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
    order by createTime desc
  </select>

  <select id="queryLeaveExtensionById" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveExtensionDo">
    SELECT a.id,
    b.empid empId,
    b.hire_date as "hireDate",
    b.workno workNo,
    b.emp_name empName,
    b.orgid,
    case
    when sco.full_path is not null and sco.full_path != ''
    then concat_ws('/', sco.full_path, sco.shortname)
    else sco.shortname
    end  as orgName,
    a.time_duration timeDuration,
    a.time_unit timeUnit,
    a.start_date startDate,
    a.end_date endDate,
    a.create_time createTime,
    a.status,
    a.last_approval_time lastApprovalTime,
    a.reason,
    a.revoke_reason revokeReason,
    wlqc.rule_name quotaName,
    wlqc.i18n_rule_name i18nQuotaName,
    a.file_id,
    a.file_name,
    b.workplace as workCity,
    b.employ_type as employType,a.create_by createBy,a.process_code
    FROM wa_leave_extension a
    JOIN sys_emp_info b ON a.emp_id = b.empid AND b.deleted = 0
    LEFT JOIN wa_leave_quota_config wlqc on wlqc.config_id=a.config_id
    LEFT JOIN sys_corp_org sco on b.orgid = sco.orgid AND sco.deleted = 0
    <where>
      AND a.tenant_id=#{tenantId} AND a.deleted=0  AND id=#{id}
    </where>
  </select>
</mapper>