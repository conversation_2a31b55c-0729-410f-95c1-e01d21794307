package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * created by: FoAng
 * create time: 2021-03-31 11:33
 */
@Data
@Builder
public class MyApplyTypeVo {

    @Builder.Default
    private Integer applyLeave = 1;

    @Builder.Default
    @ApiModelProperty("是否显示补卡 0 : 不显示  1:显示")
    private Integer applySupplement = 0;

    @Builder.Default
    @ApiModelProperty("是否显示出差 0 : 不显示  1:显示")
    private Integer applyTravel = 0;

}
