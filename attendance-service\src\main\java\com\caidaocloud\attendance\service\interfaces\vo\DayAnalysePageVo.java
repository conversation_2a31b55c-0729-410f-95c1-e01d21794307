package com.caidaocloud.attendance.service.interfaces.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class DayAnalysePageVo implements Serializable {

    private Long belongDate;
    private String belongDateWeek;
    private String empName;
    private Long empId;
    private String isKg;
    private String isShift;
    private String orgName;
    private Integer orgId;
    private String other;
    private String reason;
    private Integer shiftEndTime;
    private Integer shiftStartTime;
    private String shiftDefCode;
    private String shiftDefName;
    private Integer workTime;
    private Integer workTimeMinute;
    private String workNo;
    private String timeErrName;
    private Integer isExp;
    private Float lateTime;
    private Integer lateTimeMinute;
    private String normalDate;
    private Integer recordId;
    private Long regDateTime;
    private Integer registerType;
    private String registerTypeName;
    private String resultDesc;
    private String resultDescTxt;
    private Integer resultType;
    private String resultTypeName;
    private Integer signinId;
    private Float actualWorkTime;
    private Float actualWorkTimeMinute;
    private Integer bdkCount;
    private Float earlyTime;
    private Integer earlyTimeMinute;
    private Integer leaveTime;
    private String normalDate2;
    private Integer recordId2;
    private Long regDateTime2;
    private Integer registerTime;
    private Integer registerTimeMinute;
    private Integer registerType2;
    private String registerType2Name;
    private String resultDesc2;
    private Integer resultType2;
    private String resultType2Name;
    private Integer signoffId;
}
