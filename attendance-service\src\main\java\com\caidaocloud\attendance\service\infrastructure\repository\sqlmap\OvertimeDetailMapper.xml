<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.OverTimeDetailMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpOvertimeDetailPo" >
        <id column="detail_id" property="detailId" jdbcType="INTEGER" />
        <result column="overtime_id" property="overtimeId" jdbcType="INTEGER" />
        <result column="start_time" property="startTime" jdbcType="BIGINT" />
        <result column="end_time" property="endTime" jdbcType="BIGINT" />
        <result column="time_duration" property="timeDuration" jdbcType="INTEGER" />
        <result column="date_type" property="dateType" jdbcType="SMALLINT" />
        <result column="real_date" property="realDate" jdbcType="BIGINT" />
        <result column="crttime" property="crttime" jdbcType="BIGINT" />
        <result column="crtuser" property="crtuser" jdbcType="BIGINT" />
        <result column="updtime" property="updtime" jdbcType="BIGINT" />
        <result column="upduser" property="upduser" jdbcType="BIGINT" />
        <result column="rel_time_duration" property="relTimeDuration" jdbcType="INTEGER" />
    </resultMap>
    <select id="queryOvertimeDetails" resultMap="BaseResultMap">
        SELECT * FROM wa_emp_overtime_detail
        WHERE overtime_id = #{overtimeId}
    </select>

    <select id="queryOvertimeDetailList" resultMap="BaseResultMap">
        SELECT * FROM wa_emp_overtime_detail eod
        JOIN wa_emp_overtime eo on eo.ot_id=eod.overtime_id
        WHERE eo.empid = #{empId} and eod.real_date between #{startTime} and #{endTime}
        <if test="status != null">
            and eo.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
    </select>

    <select id="queryEmpLeftDurationOvertimeDetail" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.EmpOvertimeDetailPo">
        SELECT a.*,c.emp_name empName,c.workno,b.empid
        FROM wa_emp_overtime_detail a
        JOIN wa_emp_overtime b ON b.ot_id=a.overtime_id
        JOIN sys_emp_info c ON c.empid=b.empid
        <where>
            <if test="tenantId != null">
                AND b.tenant_id=#{tenantId}
            </if>
            <if test="empIds != null ">
                AND b.empid = ANY(${empIds})
            </if>
            <if test="overtimeTypeIds != null ">
                AND a.overtime_type_id = ANY(${overtimeTypeIds})
            </if>
            AND a.left_duration <![CDATA[>]]> 0 AND (a.carried_forward IS NULL OR a.carried_forward <![CDATA[!=]]> 1)
            AND b.status = 2 AND b.compensate_type = 2
        </where>
    </select>
</mapper>