package com.caidaocloud.attendance.service.interfaces.dto.clock;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/8/11 17:36
 * @Description:
 **/
@Data
public class EmpClockPlanInfoDto implements Serializable {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("员工dto")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("打卡方案id")
    private Long planId;
    @ApiModelProperty("有效期开始时间")
    private Long startTime;
    @ApiModelProperty("有效期结束时间")
    private Long endTime;
}
