<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.OverTimeTypeMapper">
    <resultMap id="BaseResultMap" type="com.caidao1.wa.mybatis.model.WaOvertimeType">
        <id column="overtime_type_id" jdbcType="INTEGER" property="overtimeTypeId" />
        <result column="overtime_type" jdbcType="INTEGER" property="overtimeType" />
        <result column="date_type" jdbcType="INTEGER" property="dateType" />
        <result column="compensate_type" jdbcType="INTEGER" property="compensateType" />
        <result column="off_unit" jdbcType="INTEGER" property="offUnit" />
        <result column="overtime_num" jdbcType="REAL" property="overtimeNum" />
        <result column="off_num" jdbcType="REAL" property="offNum" />
        <result column="is_timeout_check" jdbcType="BIT" property="isTimeoutCheck" />
        <result column="belong_orgid" jdbcType="VARCHAR" property="belongOrgid" />
        <result column="crtuser" jdbcType="BIGINT" property="crtuser" />
        <result column="crttime" jdbcType="BIGINT" property="crttime" />
        <result column="upduser" jdbcType="BIGINT" property="upduser" />
        <result column="updtime" jdbcType="BIGINT" property="updtime" />
        <result column="max_overtime_num" jdbcType="INTEGER" property="maxOvertimeNum" />
        <result column="min_apply_num" jdbcType="REAL" property="minApplyNum" />
        <result column="parse_rule" jdbcType="INTEGER" property="parseRule" />
        <result column="min_unit" jdbcType="REAL" property="minUnit" />
        <result column="is_open_time_control" jdbcType="BIT" property="isOpenTimeControl" />
        <result column="time_control_type" jdbcType="INTEGER" property="timeControlType" />
        <result column="control_time_duration" jdbcType="REAL" property="controlTimeDuration" />
        <result column="is_upload_file" jdbcType="BIT" property="isUploadFile" />
        <result column="min_file_check_time" jdbcType="REAL" property="minFileCheckTime" />
        <result column="useful_time" jdbcType="INTEGER" property="usefulTime" />
        <result column="useful_time_unit" jdbcType="INTEGER" property="usefulTimeUnit" />
        <result column="is_pre_timecontrol" jdbcType="BIT" property="isPreTimecontrol" />
        <result column="pre_control_type" jdbcType="INTEGER" property="preControlType" />
        <result column="pre_control_duration" jdbcType="REAL" property="preControlDuration" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="lv_parse" jdbcType="BIT" property="lvParse" />
        <result column="valid_time_cal_type" jdbcType="INTEGER" property="validTimeCalType" />
        <result column="max_valid_time" jdbcType="REAL" property="maxValidTime" />
        <result column="control_time_unit" jdbcType="REAL" property="controlTimeUnit" />
        <result column="type_name" jdbcType="VARCHAR" property="typeName" />
        <result column="overtime_control" jdbcType="BIT" property="overtimeControl" />
        <result column="is_open_travel" jdbcType="BIT" property="isOpenTravel" />
        <result column="start_date" jdbcType="BIGINT" property="startDate" />
        <result column="end_date" jdbcType="BIGINT" property="endDate" />
        <result column="rounding_rule" jdbcType="INTEGER" property="roundingRule" />
        <result column="overtime_cal_type" jdbcType="INTEGER" property="overtimeCalType" />
        <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
        <result column="revoke_workflow" jdbcType="BIT" property="revokeWorkflow" />
        <result column="min_overtime_unit" jdbcType="REAL" property="minOvertimeUnit" />
        <result column="min_overtime_unit_type" jdbcType="INTEGER" property="minOvertimeUnitType" />
        <result column="i18n_type_name" jdbcType="VARCHAR" property="i18nTypeName" />
    </resultMap>
    <select id="getGroupOtRelCount" resultType="java.lang.Integer">
        select count(0)
        from wa_group
        where belong_orgid = #{belongId}
        and ot_type_ids is not null
        and #{id} = any (ot_type_ids)
    </select>

    <select id="getGroupLeaveRelCount" resultType="java.lang.Integer">
        select count(0)
        from wa_group
        where belong_orgid = #{belongId}
        and leave_type_ids is not null
        and #{id} = any (leave_type_ids)
    </select>

    <select id="queryOtTypes" resultMap="BaseResultMap">
      SELECT overtime_type_id, overtime_type, a.compensate_type, min_apply_num, a.min_overtime_unit,a.min_overtime_unit_type,
      a.date_type,a.overtime_num,a.off_num,a.off_unit,valid_time_cal_type, max_valid_time, a.type_name,valid_punch_type,a.is_open_travel,
      overtime_cal_type,rounding_rule,a.rule_id
      FROM wa_overtime_type a
      <if test="waGroupId != null">
        JOIN wa_group wg on wg.belong_orgid = a.belong_orgid and a.overtime_type_id = any (wg.ot_type_ids)
      </if>
      <if test="summary != null">
        JOIN wa_overtime_transfer_rule otr on otr.rule_id=a.rule_id
      </if>
      WHERE a.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="waGroupId != null">
            and wg.wa_group_id = #{waGroupId}
        </if>
        <if test="date != null">
            and #{date} between ot.start_date and ot.end_date
        </if>
        <if test="summary != null">
            <choose>
                <when test="summary">
                    and otr.transfer_rule=4
                </when>
                <otherwise>
                    and otr.transfer_rule <![CDATA[!=]]> 4
                </otherwise>
            </choose>
        </if>
      ORDER BY a.updtime DESC, overtime_type_id DESC
    </select>
    <select id="getOvertimeTypeList" resultMap="BaseResultMap">
        SELECT ot.overtime_type_id,
               ot.overtime_type,
               ot.date_type,
               ot.compensate_type,
               ot.off_unit,
               ot.overtime_num,
               ot.off_num,
               ot.is_timeout_check,
               ot.belong_orgid,
               ot.crtuser,
               ot.crttime,
               ot.upduser,
               ot.updtime,
               ot.max_overtime_num,
               ot.min_apply_num,
               ot.parse_rule,
               ot.min_unit,
               ot.is_upload_file,
               ot.min_file_check_time,
               ot.useful_time,
               ot.useful_time_unit,
               ot.is_pre_timecontrol,
               ot.pre_control_type,
               ot.pre_control_duration,
               ot.description,
               ot.min_overtime_unit,
               ot.min_overtime_unit_type,
               ot.lv_parse,
               ot.valid_time_cal_type,
               ot.max_valid_time,
               ot.type_name
        FROM wa_emp_group weg
                 JOIN wa_group wg ON weg.wa_group_id = wg.wa_group_id
                 JOIN wa_overtime_type ot ON ot.overtime_type_id = ANY (wg.ot_type_ids)
        WHERE wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
          AND weg.empid = #{empId}
          AND ot.date_type = #{dateType}
          AND #{date} BETWEEN weg.start_time AND weg.end_time
          AND #{date} between ot.start_date and ot.end_date
    </select>

    <select id="queryOvertimeTypeByName" resultMap="BaseResultMap">
        SELECT * FROM wa_emp_group weg
        JOIN wa_group wg ON weg.wa_group_id = wg.wa_group_id
        JOIN wa_overtime_type ot ON ot.overtime_type_id = ANY (wg.ot_type_ids)
        WHERE wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR} AND weg.wa_group_id = #{waGroupId}
        AND ot.type_name = #{typeName}
        <if test="overtimeTypeId != null">
            and ot.overtime_type_id != #{overtimeTypeId}
        </if>
    </select>

    <select id="countRelBetweenTypeAndOt" resultType="int">
        select count(ot_id) from wa_emp_overtime
        where overtime_type_id= #{overtimeTypeId}
    </select>

    <select id="queryOvertimeTypeByIds" resultMap="BaseResultMap">
        select * from wa_overtime_type
        where overtime_type_id in
        <foreach collection="overtimeTypeIds" item="overtimeTypeId" open="(" close=")" separator=",">
          #{overtimeTypeId}
        </foreach>
    </select>


    <select id="queryAllOtTypes" resultMap="BaseResultMap">
      SELECT overtime_type_id, overtime_type, compensate_type, min_apply_num, min_overtime_unit,min_overtime_unit_type,
      date_type,overtime_num,off_num,off_unit,valid_time_cal_type, max_valid_time, type_name,valid_punch_type,is_open_travel,
      start_date,end_date, i18n_type_name
      FROM wa_overtime_type
      WHERE belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
      <if test="status == null">
        and (start_date <![CDATA[>]]> #{curDate} or (#{curDate} <![CDATA[>=]]> start_date and #{curDate} <![CDATA[<=]]> end_date))
      </if>
    </select>

    <select id="getOtTypeByNameAndTime" resultMap="BaseResultMap">
        SELECT * FROM wa_overtime_type WHERE belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        AND (type_name = #{typeName} AND ((#{startDate} BETWEEN start_date AND end_date) OR (#{endDate} BETWEEN start_date AND end_date)
        OR (start_date <![CDATA[>=]]> #{startDate} AND end_date <![CDATA[<=]]> #{endDate})))
        <if test="overtimeTypeId != null">
            and overtime_type_id != #{overtimeTypeId}
        </if>
    </select>

    <select id="getOtTypeAnalyseRule" resultType="com.caidao1.wa.mybatis.model.WaOvertimeType">
        select wot.date_type as "dateType",
        coalesce(wot.lv_parse, false) as "lvParse",
        valid_time_cal_type as "validTimeCalType",
        overtime_cal_type as "overtimeCalType",
        max_valid_time as "maxValidTime",
        valid_punch_type as "validPunchType",
        wot.compensate_type as "compensateType",
        wot.min_overtime_unit as "minOvertimeUnit",
        wot.min_overtime_unit_type as "minOvertimeUnitType",
        wot.is_open_travel,wot.overtime_type_id,
        wot.rule_id,wot.rounding_rule as "roundingRule"
        from wa_overtime_type wot
        <if test="waGroupId != null">
            join wa_group wg on wot.overtime_type_id = any (wg.ot_type_ids)
        </if>
        where wot.belong_orgid = #{tenantId,jdbcType=VARCHAR}
        <if test="waGroupId != null">
            and wg.wa_group_id = #{waGroupId}
        </if>
    </select>

    <select id="queryOvertimeType" resultType="com.caidao1.wa.mybatis.model.WaOvertimeType">
        SELECT ot.compensate_type,ot.overtime_type_id,ot.date_type
        FROM wa_emp_group_view eg
                JOIN wa_group wg ON eg.wa_group_id = wg.wa_group_id
                JOIN wa_overtime_type ot ON ot.overtime_type_id = ANY(wg.ot_type_ids)
        WHERE eg.empid = #{empId} AND ot.date_type = #{dateType} AND #{endDate} BETWEEN ot.start_date and ot.end_date
        ORDER BY CASE WHEN ot.compensate_type=2 THEN 1 WHEN ot.compensate_type=1 THEN 2 ELSE 3 END,ot.compensate_type
    </select>

    <select id="queryOtherTransferRuleOvertimeType" resultType="com.caidao1.wa.mybatis.model.WaOvertimeType">
        SELECT wot.*
        FROM attendance.wa_overtime_type wot
        JOIN attendance.wa_overtime_transfer_rule otr on otr.rule_id=wot.rule_id
        WHERE wot.belong_orgid=#{tenantId} AND otr.transfer_rule=4
    </select>
</mapper>