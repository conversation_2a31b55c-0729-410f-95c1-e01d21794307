package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.utils.ConvertHelper;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.clock.WaClockSiteDto;
import com.caidaocloud.attendance.service.application.service.IClockPlanService;
import com.caidaocloud.attendance.service.application.service.IClockSiteService;
import com.caidaocloud.attendance.service.interfaces.dto.clock.ClockPlanDto;
import com.caidaocloud.attendance.service.interfaces.vo.WaClockSiteVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/26
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/clocksite/v1")
public class ClockSiteController {
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private IClockSiteService clockSiteService;
    @Autowired
    private IClockPlanService clockPlanService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("新增或修改打卡地点")
    @PostMapping("/save")
    public Result<Boolean> saveClockSite(@RequestBody WaClockSiteDto dto) {
        if (StringUtils.isBlank(dto.getSiteName())) {
            return ResponseWrap.wrapResult(AttendanceCodes.LOCATION_ABBREVIATED_EMPTY, CommonConstant.FALSE);
        }
        if (StringUtils.isEmpty(dto.getAddress())) {
            return ResponseWrap.wrapResult(AttendanceCodes.ADDRESS_EMPTY, CommonConstant.FALSE);
        }
        if (dto.getLng() == null || dto.getLat() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LNG_LAT_EMPTY, CommonConstant.FALSE);
        }
        if (dto.getRange() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.GPS_RANGE_EMPTY, CommonConstant.FALSE);
        }
        if (null == dto.getId()) {
            clockSiteService.saveClockSite(dto);
        } else {
            clockSiteService.updateClockSite(dto);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @ApiOperation("获取打卡地点的分页列表")
    @PostMapping("/list")
    public Result<AttendancePageResult<WaClockSiteDto>> getClockSiteList(@RequestBody AttendanceBasePage basePage) {
        UserInfo userInfo = this.getUserInfo();
        if (userInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.USER_INFO_EMPTY, null);
        }
        return ResponseWrap.wrapResult(clockSiteService.getClockSitePageList(basePage, ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId()));
    }

    @ApiOperation("获取打卡地点详情")
    @GetMapping("/detail")
    public Result<WaClockSiteVo> getClockSiteInfo(@RequestParam("id") Long id) {
        WaClockSiteDto siteDto = clockSiteService.getClockSiteById(id);
        if (null == siteDto) {
            return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_LOCATION_NOT_EXIST, null);
        }
        return ResponseWrap.wrapResult(ObjectConverter.convert(siteDto, WaClockSiteVo.class));
    }

    @ApiOperation("删除打卡地点")
    @DeleteMapping("/delete")
    public Result<Boolean> deleteClockSite(@RequestParam("id") Long id) {
        UserInfo userInfo = this.getUserInfo();
        if (userInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.USER_INFO_EMPTY, null);
        }
        List<ClockPlanDto> clockPlanDtos = clockPlanService.getPlanListBySiteId(Long.valueOf(userInfo.getTenantId()), userInfo.getTenantId(), id);
        if (CollectionUtils.isNotEmpty(clockPlanDtos)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_NOT_ALLOW, null);
        }
        clockSiteService.deleteClockSiteById(id);
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @ApiOperation("获取打卡地点列表")
    @GetMapping("/selectListByIds")
    public Result getClockSiteListByIds(@RequestParam("ids") List<Long> ids) {
        return ResponseWrap.wrapResult(ObjectConverter.convertList(clockSiteService.getClockSiteListByIds(ids), WaClockSiteVo.class));
    }
}
