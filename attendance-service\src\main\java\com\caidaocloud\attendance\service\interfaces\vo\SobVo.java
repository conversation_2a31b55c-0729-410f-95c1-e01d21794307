package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SobVo implements Serializable {

    @ApiModelProperty("考核截止日")
    private Long sobEndDate;
    @ApiModelProperty("运算状态值 0未关闭")
    private Integer status;
    private Integer sysPeriodMonth;
    @ApiModelProperty("考勤分组名称")
    private String waGroupName;
    @ApiModelProperty("账套id")
    private Integer waSobId;
    @ApiModelProperty("考勤分组id")
    private Integer waGroupId;
    @ApiModelProperty(value = "考勤帐套名称")
    private String waSobName;
    @ApiModelProperty(value = "考核周期")
    private String waSobRange;
    @ApiModelProperty("考勤周期封存时间")
    private Long sobCloseDate;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nWaSobName;
}
