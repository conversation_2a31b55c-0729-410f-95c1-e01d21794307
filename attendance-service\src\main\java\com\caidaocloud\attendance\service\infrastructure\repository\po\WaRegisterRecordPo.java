package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("wa_register_record")
public class WaRegisterRecordPo {
    @TableId(type = IdType.INPUT)
    private Long recordId;
    private Long empid;
    private Integer registerType;
    private String resultDesc;
    private Integer resultType;
    private String reason;
    private String regAddr;
    private Long regDateTime;
    private BigDecimal lng;
    private BigDecimal lat;
    private Long belongDate;
    private String mobDeviceNum;
    private Integer sourceFromType;
    private Long crtuser;
    private Long crttime;
    private Long upduser;
    private Long updtime;
    private String normalAddr;
    private String normalDate;
    private Integer type;
    private String owRmk;
    private String picList;
    private Long hisRegTime;
    private Integer shiftDefId;
    private Boolean isDeviceError;
    private String oriMobDeviceNum;
    private Boolean isWorkflow;
    private Integer approvalStatus;
    private String approvalReason;
    private String filePath;
    private String revokeReason;
    private Integer siteId;
    private String province;
    private String city;
    private Integer startTime;
    private Integer endTime;
    private Long lastApprovalTime;
    private String belongOrgId;
    private String workno;
    private String empName;
    private Long orgid;
    private Long approvalTime;
    private String orgName;
    private String fullPath;
    private Long empStyle;
    private String empStyleName;
    private Long hireDate;
    private Integer empStatus;
    private String empStatusName;
    private String userName;
    private String registerTypeName;
    private String shortName;
    private String statusName;
    private String files;
    private String fileNames;
    private String workCity;
    //记录是否有效: 0 无效 、1 有效
    private Integer ifValid;
    private Integer clockSiteStatus;
}
