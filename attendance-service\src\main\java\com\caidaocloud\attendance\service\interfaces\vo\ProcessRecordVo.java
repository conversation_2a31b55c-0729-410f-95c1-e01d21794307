package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.application.dto.CancelProcessRecordDto;
import com.caidaocloud.attendance.core.workflow.dto.WfProcessRecordDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "流程记录")
public class ProcessRecordVo {
    @ApiModelProperty("休假/批量休假流程记录")
    private List<WfProcessRecordDto> leaveRecord = new ArrayList<>();
    @ApiModelProperty("销假流程记录")
    private List<CancelProcessRecordDto> leaveCancelRecord = new ArrayList<>();
    @ApiModelProperty("是否自动销假：true/false")
    private boolean autoLeaveCancel;
    @ApiModelProperty("自动销假时间")
    private Long autoLeaveCancelTime;
    @ApiModelProperty("加班流程记录")
    private List<WfProcessRecordDto> otRecord = new ArrayList<>();
    @ApiModelProperty("加班撤销/废止流程记录")
    private List<List<WfProcessRecordDto>> otRevokeRecord = new ArrayList<>();
    @ApiModelProperty("出差流程记录")
    private List<WfProcessRecordDto> travelRecord = new ArrayList<>();
    @ApiModelProperty("出差撤销/废止流程记录")
    private List<List<WfProcessRecordDto>> travelRevokeRecord = new ArrayList<>();
    @ApiModelProperty("休假撤销/废止流程记录")
    private List<List<WfProcessRecordDto>> leaveRevokeRecord = new ArrayList<>();
}
