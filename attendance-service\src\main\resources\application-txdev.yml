server:
  port: 8080
spring:
  application:
    name: caidaocloud-attendance-service
  cloud:
    nacos:
      discovery:
        server-addr: 10.0.0.30:8848
        namespace: cd2
nacos:
  config:
    type: yaml
    server-addr: 10.0.0.30:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: cd2
    bootstrap:
      enable: true
      log:
        enable: true