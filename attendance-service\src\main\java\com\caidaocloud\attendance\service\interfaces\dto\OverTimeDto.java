package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Max;
import java.util.HashMap;
import java.util.Map;

/**
 * 加班类型
 *
 * <AUTHOR>
 */
@Data
public class OverTimeDto {
    @ApiModelProperty("所属id")
    private String belongOrgid;
    @ApiModelProperty("方案id")
    private Integer waGroupId;
    @ApiModelProperty("id")
    private Integer overtimeTypeId;
    @ApiModelProperty("加班类型")
    private Integer overtimeType;
    @ApiModelProperty("日期类型 1:工作日 2:休息日 3:法定假日 4:特殊休日")
    private Integer dateType;
    @ApiModelProperty("补偿类型：0不补偿，1加班费，2调休 3已预付 4其他")
    private Integer compensateType;
    @ApiModelProperty("加班类型名称")
    private String typeName;
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("失效日期")
    private Long endDate;
    @ApiModelProperty("加班说明")
    private String description;
    @ApiModelProperty("最小加班单位:0按分钟加班,1按半小时加班,2按小时加班,3按半天加班,4按天加班")
    private Float minOvertimeUnit;
    @ApiModelProperty("最小申请时长")
    @Max(value = 24, message = "最小起请数不能超过24小时")
    private Float minApplyNum;
    @ApiModelProperty("最大申请时长")
    private Float maxValidTime;
    @ApiModelProperty("有效时长计算： 1 按审批（申请）时长、2 取加班时间段和打卡时间段的交集、3 按打卡时长计算(扣加班休息时长),4 按打卡时长计算(不扣加班休息时长)")
    private Integer validTimeCalType;
    @ApiModelProperty("有效打卡类型 1:同种打卡类型 2:所有打卡类型，3:正常上下班卡 4:外勤卡")
    private Integer validPunchType;
    @ApiModelProperty("是否关联出差申请：true/false,开/关")
    private Boolean isOpenTravel;
    @ApiModelProperty("进位规则: 1 向上取整0.25、2 向上取整0.5、3 向上取整1、4 向下取整0.25、5 向下取整0.5、6 向下取整1、7 四舍五入保留整数、8 四舍五入保留两位小数")
    private Integer roundingRule;
    @ApiModelProperty("加班时长计算 1:申请时长与打卡时长取小值 2:以打卡时长为准")
    private Integer overtimeCalType;
    @ApiModelProperty("加班转调休小时数")
    private Float offNum;
    @ApiModelProperty("加班小时数")
    private Float overtimeNum;

    @ApiModelProperty("转换规则")
    private Long ruleId;
    @ApiModelProperty("撤销是否需要走审批流：true是/false否，默认值false")
    private Boolean revokeWorkflow;
    @ApiModelProperty("审批通过单据是否允许撤销：true是/false否，默认值false")
    private Boolean revokePassed;
    @ApiModelProperty("撤销是否需要走审批流,允许撤销的状态多选，逗号分隔：1审批中2已通过")
    private String revokeAllowStatus;
    @ApiModelProperty("最小加班单位时间单位:1分钟2小时")
    private Integer minOvertimeUnitType;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nTypeName;

    public void initTypeName() {
        if (StringUtils.isNotBlank(this.typeName)) {
            return;
        }
        if (null == this.i18nTypeName || this.i18nTypeName.isEmpty() || null == this.i18nTypeName.get("default")) {
            return;
        }
        this.setTypeName(this.i18nTypeName.get("default"));
    }

    public void initI18nTypeName() {
        if (null != this.i18nTypeName && !this.i18nTypeName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.typeName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.typeName);
        this.setI18nTypeName(i18nName);
    }
}
