package com.caidaocloud.attendance.service.infrastructure.util;

import com.caidaocloud.attendance.service.infrastructure.common.GpsDto;

import java.math.BigDecimal;

public class PositionTransferUtil {

    private final static double pi = 3.1415926535897932384626;

    /**
     * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法
     * <p>
     * 将 GCJ-02 坐标转换成 BD-09 坐标
     *
     * @param gg_lon
     * @param gg_lat
     */
    public static GpsDto gcj02_to_bd09(double gg_lon, double gg_lat) {
        double x = gg_lon, y = gg_lat;
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
        double bd_lon = z * Math.cos(theta) + 0.0065;
        double bd_lat = z * Math.sin(theta) + 0.006;
        return new GpsDto(dataDigit(6, bd_lon), dataDigit(6, bd_lat));
    }

    /**
     * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法
     * <p>
     * 将 BD-09 坐标转换成GCJ-02 坐标
     *
     * @param bd_lat
     * @param bd_lon
     * @return
     */
    public static GpsDto bd09_to_gcj02(double bd_lon, double bd_lat) {
        double x = bd_lon - 0.0065, y = bd_lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
        double gg_lon = z * Math.cos(theta);
        double gg_lat = z * Math.sin(theta);
        return new GpsDto(dataDigit(6, gg_lon), dataDigit(6, gg_lat));
    }

    private static double dataDigit(int digit, double in) {
        return new BigDecimal(String.valueOf(in)).setScale(digit, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static void main(String[] args) {
        System.out.println("gcj02_to_bd09:" + gcj02_to_bd09(121.401661,31.162414));
        System.out.println("bd09_to_gcj02:" + bd09_to_gcj02(121.411878, 31.170737));
    }
}
