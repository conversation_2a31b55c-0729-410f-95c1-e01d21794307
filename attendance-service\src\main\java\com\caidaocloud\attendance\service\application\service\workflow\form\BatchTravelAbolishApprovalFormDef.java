package com.caidaocloud.attendance.service.application.service.workflow.form;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.googlecode.totallylazy.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 批量出差废止
 */
public class BatchTravelAbolishApprovalFormDef extends WfMetaFunFormDef {
    @NotNull
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return Lists.list(
                new WfMetaFunFormFieldDto("workno", "工号", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("name", "姓名", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("org", "部署", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("travelType", "出差类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("visitedPlace", "访问地", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("accessObject", "访问对象", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("purpose", "目的", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("costCenter", "费用承担", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("tel", "联系电话", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("peerPeople", "同行人员", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("businessContact", "不在时业务联络", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("passportNumber", "护照号码", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("remarks", "备注", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("travelTimes", "出差时间", WfFieldDataTypeEnum.Text)
        );
    }
}
