package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.interfaces.vo.AbnormalTimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.AttendanceDetailTimeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class NotifyConfigDto {

    @ApiModelProperty("消息推送配置ID")
    private Long notifyConfigId;
    @ApiModelProperty("打卡提醒推送开关： 0 关闭，1 开启")
    private Integer clockNotifySwitch;
    @ApiModelProperty("上班提醒设置时间")
    private Integer workTime;
    @ApiModelProperty("下班设置提醒设置时间")
    private Integer offWorkTime;
    @ApiModelProperty("日报提醒推送开关： 0 关闭，1 开启")
    private Integer dailyNotifySwitch;
    @ApiModelProperty("推送规则： 0 当日，1 次日")
    private Integer dailyNotifyRule;
    @ApiModelProperty("推送时间 分钟")
    private Integer dailyNotifyTime;
    @ApiModelProperty("是否开启周报通知： 0 关闭，1 开启")
    private Integer weeklyNotifySwitch;
    @ApiModelProperty("上班提醒设置提醒内容")
    private String workNotifyContent;
    @ApiModelProperty("下班提醒设置提醒内容")
    private String offWorkNotifyContent;
    @ApiModelProperty("销假提醒开关 0 关闭，1 开启")
    private Integer leaveCancelSwitch;
    @ApiModelProperty("销假提醒内容")
    private String leaveCancelContent;
    @ApiModelProperty("销假提醒时间 休假结束后xx天提醒")
    private Integer leaveCancelTime;
    @ApiModelProperty("销假提醒频率 每隔xx天提醒一次")
    private Integer leaveCancelFrequency;

    @ApiModelProperty("是否仅通知考勤异常员工：0未开启，1开启")
    private Integer dailyNotifyAbnormal;
    @ApiModelProperty("周报频率：1周日2周一3周二4周三5周四6周五7周六")
    private Integer weeklyNotifyFrequency;
    @ApiModelProperty("周报时间点")
    private Integer weeklyNotifyTime;
    @ApiModelProperty("周报类型：1本周，2上周")
    private Integer weeklyNotifyType;

    @ApiModelProperty("是否开启异常汇总提醒,0关闭,1开启,默认关闭")
    private Integer abnormalSwitch;
    @ApiModelProperty("异常汇总推送范围：1、当前考勤周期内，2、一周内，3、两周内，默认：1")
    private Integer abnormalType;
    @ApiModelProperty("{type:1(每月)/2(每周),date:(type=1代表日，type=2代表1周日2周一3周二4周三5周四6周五7周六),time:分钟}")
    private List<AbnormalTimeDto> abnormalTime;
    @ApiModelProperty("异常汇总提醒自定义内容")
    private String abnormalMsg;

    @ApiModelProperty("是否开启考勤明细推送,0关闭,1开启,默认关闭")
    private Integer attendanceDetailSwitch;
    @ApiModelProperty("考勤明细时间配置")
    private List<AttendanceDetailTimeDto> attendanceDetailTime;
}
