<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.LeaveSetMapper">
    <select id="getLeaveSettingPageList"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveSettingDo">
        SELECT quota_setting_id as "quotaSettingId",
        quota_setting_name as "quotaSettingName",
        quota_sort_no as "quotaSortNo",
        leave_type as "leaveType",
        carry_over_type as "carryOverType",
        rmk,
        quota_period_type as "quotaPeriodType",
        case carry_over_type when 1 then '结转' when 2 then '加班费' when 3 then '作废' else '' end as "carryOverTypeName",
        case when quota_period_type = 1 then ' 自然年' else '合同年' end as "quotaPeriodTypeName"
        FROM wa_leave_setting ls
        WHERE belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="leaveTypeId != null">
            and leave_type_id = #{leaveTypeId}
        </if>
    </select>
    <select id="getLeaveQuotaGroupRuleList" resultType="map">
        SELECT lq.quota_val      as "quotaVal",
               eg.emp_group_name as "empGroupName",
               eg.group_note     as "groupNote",
               eg.group_exp      as "groupExp"
        FROM wa_leave_quota lq
                 JOIN pay_emp_group eg ON lq.emp_group_id = eg.emp_group_id
        WHERE lq.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
          AND lq.quota_setting_id = #{quotaSettingId}
        order by eg.updtime desc
    </select>

    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo" >
        <id column="leave_type_id" property="leaveTypeId" jdbcType="INTEGER" />
        <result column="leave_name" property="leaveName" jdbcType="VARCHAR" />
        <result column="leave_code" property="leaveCode" jdbcType="VARCHAR" />
        <result column="leave_type" property="leaveType" jdbcType="INTEGER" />
        <result column="acct_time_type" property="acctTimeType" jdbcType="INTEGER" />
        <result column="round_time_unit" property="roundTimeUnit" jdbcType="REAL" />
        <result column="is_check_min_time" property="isCheckMinTime" jdbcType="BIT" />
        <result column="min_leave_time" property="minLeaveTime" jdbcType="REAL" />
        <result column="is_check_max_time" property="isCheckMaxTime" jdbcType="BIT" />
        <result column="max_leave_time" property="maxLeaveTime" jdbcType="REAL" />
        <result column="is_rest_day" property="isRestDay" jdbcType="BIT" />
        <result column="is_legal_holiday" property="isLegalHoliday" jdbcType="BIT" />
        <result column="is_upload_file" property="isUploadFile" jdbcType="BIT" />
        <result column="is_emp_show" property="isEmpShow" jdbcType="BIT" />
        <result column="is_used_in_advance" property="isUsedInAdvance" jdbcType="BIT" />
        <result column="is_used_in_probation" property="isUsedInProbation" jdbcType="BIT" />
        <result column="belong_orgid" property="belongOrgid" jdbcType="VARCHAR" />
        <result column="rmk" property="rmk" jdbcType="VARCHAR" />
        <result column="crtuser" property="crtuser" jdbcType="BIGINT" />
        <result column="crttime" property="crttime" jdbcType="BIGINT" />
        <result column="upduser" property="upduser" jdbcType="BIGINT" />
        <result column="updtime" property="updtime" jdbcType="BIGINT" />
        <result column="quota_it_type" property="quotaItType" jdbcType="INTEGER" />
        <result column="quota_four2five_rule" property="quotaFour2fiveRule" jdbcType="INTEGER" />
        <result column="use_it_type" property="useItType" jdbcType="INTEGER" />
        <result column="use_four2five_rule" property="useFour2fiveRule" jdbcType="INTEGER" />
        <result column="max_month_time" property="maxMonthTime" jdbcType="REAL" />
        <result column="is_check_month_time" property="isCheckMonthTime" jdbcType="BIT" />
        <result column="gender_type" property="genderType" jdbcType="BIGINT" />
        <result column="orders" property="orders" jdbcType="INTEGER" />
        <result column="is_sum_show" property="isSumShow" jdbcType="BIT" />
        <result column="min_file_check_time" property="minFileCheckTime" jdbcType="REAL" />
        <result column="pro_setting_id" property="proSettingId" jdbcType="INTEGER" />
        <result column="quota_max" property="quotaMax" jdbcType="REAL" />
        <result column="is_check_quarter_time" property="isCheckQuarterTime" jdbcType="BIT" />
        <result column="max_quarter_time" property="maxQuarterTime" jdbcType="REAL" />
        <result column="is_check_year_time" property="isCheckYearTime" jdbcType="BIT" />
        <result column="max_year_time" property="maxYearTime" jdbcType="REAL" />
        <result column="leave_name_lang" property="leaveNameLang" jdbcType="OTHER" />
        <result column="is_open_time_control" property="isOpenTimeControl" jdbcType="BIT" />
        <result column="time_control_type" property="timeControlType" jdbcType="INTEGER" />
        <result column="control_time_duration" property="controlTimeDuration" jdbcType="REAL" />
        <result column="superior_quota_setting_ids" property="superiorQuotaSettingIds" jdbcType="ARRAY" />
        <result column="is_check_month_num" property="isCheckMonthNum" jdbcType="BIT" />
        <result column="is_check_quarter_num" property="isCheckQuarterNum" jdbcType="BIT" />
        <result column="is_check_year_num" property="isCheckYearNum" jdbcType="BIT" />
        <result column="max_month_num" property="maxMonthNum" jdbcType="INTEGER" />
        <result column="max_quarter_num" property="maxQuarterNum" jdbcType="INTEGER" />
        <result column="max_year_num" property="maxYearNum" jdbcType="INTEGER" />
        <result column="timeliness_control_jsonb" property="timelinessControlJsonb" jdbcType="OTHER" />
        <result column="is_retention_priority" property="isRetentionPriority" jdbcType="BIT" />
        <result column="timescale" property="timescale" jdbcType="INTEGER" />
        <result column="is_adjust_work_hour" property="isAdjustWorkHour" jdbcType="BIT" />
        <result column="is_fileupd_notice" property="isFileupdNotice" jdbcType="BIT" />
        <result column="fileupd_notice_content" property="fileupdNoticeContent" jdbcType="VARCHAR" />
        <result column="link_outside_sign" property="linkOutsideSign" jdbcType="BIT" />
        <result column="quota_check" property="quotaCheck" jdbcType="BIT" />
        <result column="probation_min_time" property="probationMinTime" jdbcType="INTEGER" />
        <result column="is_cal_worktime" property="isCalWorktime" jdbcType="BIT" />
        <result column="is_not_analyze" property="isNotAnalyze" jdbcType="BIT" />
        <result column="is_warn_msg" property="isWarnMsg" jdbcType="BIT" />
        <result column="warn_msg" property="warnMsg" jdbcType="VARCHAR" />
        <result column="warn_msg_lang" property="warnMsgLang" jdbcType="OTHER" />
        <result column="is_workhour_convert" property="isWorkhourConvert" jdbcType="BIT" />
        <result column="convert_duration" property="convertDuration" jdbcType="INTEGER" />
        <result column="quota_sorts" property="quotaSorts" jdbcType="VARCHAR" />
        <result column="icon" property="icon" jdbcType="VARCHAR" />
        <result column="continuous_application" property="continuousApplication" jdbcType="BIT" />
        <result column="i18n_leave_name" property="i18nLeaveName" jdbcType="VARCHAR" />

        <result column="acct_time_type_txt" property="acctTimeTypeTxt" jdbcType="VARCHAR" />
        <result column="is_rest_day_txt" property="isRestDaytxt" jdbcType="VARCHAR" />
        <result column="is_legal_holiday_txt" property="isLegalHolidayTxt" jdbcType="VARCHAR" />
        <result column="gender_type_txt" property="genderTypeTxt" jdbcType="VARCHAR" />
        <result column="leave_type_name" property="leaveTypeName" jdbcType="VARCHAR" />
        <result column="i18n_leave_type_name" property="i18nLeaveTypeName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="getLeaveTypePageList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT leave_type_id,
        leave_name,
        leave_name_lang,
        leave_code,
        leave_type,
        round_time_unit,
        is_check_min_time,
        min_leave_time,
        is_check_max_time,
        max_leave_time,
        acct_time_type,
        is_rest_day,
        is_legal_holiday,
        gender_type,
        case acct_time_type when 1 then '天' when 2 then '小时' end     as "acct_time_type_txt",
        case when is_rest_day then '是' else '否' end                  as "is_rest_day_txt",
        case when is_legal_holiday then '是' else '否' end             as "is_legal_holiday_txt",
        a.gender_type  as "gender_type_txt",
        is_used_in_advance,
        is_used_in_probation,
        a.orders,
        a.superior_quota_setting_ids,
        a.crttime,
        a.updtime,
        a.is_warn_msg,
        a.warn_msg,
        wltd.leave_type_def_name                                     as "leave_type_name",
        a.quota_restriction_type as "quotaRestrictionType",
        a.quota_type as "quotaType",
        wltd.leave_type_def_code as "leaveTypeDefCode"
        FROM wa_leave_type a
        JOIN wa_leave_type_def wltd ON a.leave_type = wltd.leave_type_def_id AND wltd.status = 0
        WHERE a.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="keywords != null and keywords != ''">
            AND (a.leave_name like concat('%', #{keywords}, '%')
            OR a.leave_code like CONCAT('%', #{keywords}, '%'))
        </if>
        ) as t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
    </select>
    <select id="getLeaveTypeByGroupId"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo">
        select
            distinct
                 wlt.acct_time_type as "acctTimeType",
                 wlt.leave_type_id as "leaveTypeId",
                 wlt.leave_type as "leaveType",
                 wlt.leave_name as "leaveName",
                 wlt.orders,wlt.quota_restriction_type as "quotaRestrictionType",
                 quota_type as "quotaType",
                 wlt.gender_type as "genderType",
                 ltd.leave_type_def_code as "leaveTypeDefCode"
        <choose>
            <when test="waGroupId != null">
                from wa_group wg
                    join wa_leave_type wlt on wg.belong_orgid = wlt.belong_orgid and wlt.leave_type_id = any (wg.leave_type_ids)
            </when>
            <otherwise>
                from wa_leave_type wlt
            </otherwise>
        </choose>
        left join wa_leave_type_def ltd on wlt.leave_type = ltd.leave_type_def_id
        <where>
             ltd.status = 0 and ltd.deleted = 0
            <if test="waGroupId != null">
               AND wg.wa_group_id = #{waGroupId}  AND wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
            </if>
        </where>
        order by wlt.orders
    </select>
    <select id="getLeaveTypeBySobId"
            resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo">
        select distinct wlt.leave_type_id as "leaveTypeId", wlt.leave_name as "leaveName", wlt.leave_type as "leaveType",wlt.orders
        from wa_sob ws
        join wa_group wg on ws.wa_group_id = wg.wa_group_id
        join wa_leave_type wlt on wlt.leave_type_id = any (wg.leave_type_ids)
        where ws.wa_sob_id = #{sobId}
        and ws.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        order by orders
    </select>

    <select id="queryLeaveTypeByGroupId" resultMap="BaseResultMap">
        SELECT distinct leave_type_id,
        leave_name,
        i18n_leave_name,
        leave_name_lang,
        leave_code,
        leave_type,
        round_time_unit,
        is_check_min_time,
        min_leave_time,
        is_check_max_time,
        max_leave_time,
        acct_time_type,
        is_rest_day,
        is_legal_holiday,
        gender_type,
        case acct_time_type when 1 then '天' when 2 then '小时' end     as "acct_time_type_txt",
        case when is_rest_day then '是' else '否' end                  as "is_rest_day_txt",
        case when is_legal_holiday then '是' else '否' end             as "is_legal_holiday_txt",
        a.gender_type  as "gender_type_txt",
        is_used_in_advance,
        is_used_in_probation,
        a.orders,
        a.superior_quota_setting_ids,
        a.crttime,
        a.updtime,
        a.is_warn_msg,
        a.warn_msg,
        wltd.leave_type_def_name as "leave_type_name",
        wltd.i18n_leave_type_def_name as "i18n_leave_type_name",
        a.quota_restriction_type as "quotaRestrictionType",
        a.quota_type as "quotaType",
        a.orders,a.apply_number_type "applyNumberType"
        FROM wa_leave_type a
        JOIN wa_leave_type_def wltd ON a.leave_type = wltd.leave_type_def_id AND wltd.status = 0
        WHERE a.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="quotaRestrictionType != null">
            AND a.quota_restriction_type = #{quotaRestrictionType}
        </if>
        <if test="quotaType != null">
            AND a.quota_type = #{quotaType}
        </if>
        ORDER BY orders asc;
    </select>
    <select id="getLeaveTypeList" resultType="hashmap">
        SELECT DISTINCT c.leave_type_id "leaveType",leave_name "leaveName",i18n_leave_name "i18nLeaveName",leave_name_lang,c.acct_time_type "acctTimeType",false
        "isContinulyCount",c.round_time_unit "roundTimeUnit",c.orders,case when c.timescale isnull then 1 else c.timescale end as "timescale",
        c.is_adjust_work_hour AS "isAdjustWorkHour",c.is_fileupd_notice AS "isFileupdNotice",c.fileupd_notice_content AS "fileupdNoticeContent",
        c.leave_type AS "leaveTypeDef",  c.is_warn_msg as "isWarnMsg",c.warn_msg  as "warnMsg",
        c.quota_type,
        c.warn_msg_lang as "warnMsgLang", false as "workhourConvert",c.reason_must  as "reasonMust",d.leave_type_def_code as "leaveTypeCode",c.leave_cancel_type as "leaveCancelType",
        c.enclosure_later as "enclosureLater",coalesce(c.min_file_check_time,-1) as "minFileCheckTime",coalesce(c.is_emp_show, false) as "isEmpShow"
        FROM wa_emp_group_view a,wa_group b,wa_leave_type c
        JOIN wa_leave_type_def d ON d.leave_type_def_id = c.leave_type AND d.status = 0
        WHERE
        empid = #{empId} and a.wa_group_id=b.wa_group_id and c.leave_type_id=any(b.leave_type_ids) and c.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <choose>
            <when test="gender != null">
                AND (c.gender_type=#{gender} or c.gender_type=0)
            </when>
            <otherwise>
                AND c.gender_type=0
            </otherwise>
        </choose>
        <choose>
            <when test="quotaType == 1">
                AND c.quota_type in (1,4)
            </when>
            <otherwise>
                <if test="quotaType != null">
                    AND c.quota_type = #{quotaType}
                </if>
            </otherwise>
        </choose>
        <if test="cityId!= null">
            AND ((leave_area is null or leave_area = '')
            OR (string_to_array(leave_area,',')::int[] @> array[#{cityId}]))
        </if>
        order by orders asc
    </select>

    <update id="updateLeaveType">
        update wa_leave_type
        set leave_name = #{leaveName}
        where leave_type = #{leaveType}
        and belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
    </update>
</mapper>