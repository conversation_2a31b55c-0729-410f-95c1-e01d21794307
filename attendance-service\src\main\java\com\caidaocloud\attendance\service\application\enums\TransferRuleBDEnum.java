package com.caidaocloud.attendance.service.application.enums;

import com.caidaocloud.attendance.service.application.dto.TransferRulePeriod;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.postgresql.util.PGobject;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public enum TransferRuleBDEnum {
    TO_SCALE(1, "按比例转换") {
        @Override
        public Map<String, Object> calTimeDuration(BigDecimal duration, Object periods, BigDecimal transferTime) {
            Map<String, Object> map = new HashMap<>();
            map.put("duration", BigDecimal.ZERO);
            map.put("unit", 2);
            if (transferTime != null && transferTime.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal result = duration.multiply(transferTime).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                map.put("duration", result);
                return map;
            }
            return map;
        }
    },
    STEP_BY_DAY(2, "阶梯规则（天）") {
        @Override
        public Map<String, Object> calTimeDuration(BigDecimal duration, Object periods, BigDecimal transferTime) {
            return calculateStepDuration(duration, periods, TimeUnit.DAY);
        }
    },
    STEP_BY_HOUR(3, "阶梯规则（小时）") {
        @Override
        public Map<String, Object> calTimeDuration(BigDecimal duration, Object periods, BigDecimal transferTime) {
            return calculateStepDuration(duration, periods, TimeUnit.HOUR);
        }
    },
    OTHER(4, "其他规则") {
        @Override
        public Map<String, Object> calTimeDuration(BigDecimal duration, Object periods, BigDecimal transferTime) {
            return new HashMap<>();
        }
    };

    private final Integer index;
    private final String desc;
    private static final BigDecimal MINUTES_PER_HOUR = BigDecimal.valueOf(60);

    TransferRuleBDEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    // 公共方法用于处理阶梯规则
    private static Map<String, Object> calculateStepDuration(BigDecimal duration, Object periods, TimeUnit defaultUnit) {
        Map<String, Object> map = new HashMap<>();
        map.put("duration", BigDecimal.ZERO);
        map.put("unit", defaultUnit);
        if (duration.compareTo(BigDecimal.ZERO) <= 0 || periods == null) {
            return map;
        }
        List<TransferRulePeriod> rules = getPeriods(periods);
        for (TransferRulePeriod rule : rules) {
            BigDecimal start = BigDecimal.valueOf(rule.getStart()).multiply(MINUTES_PER_HOUR);
            BigDecimal end = BigDecimal.valueOf(rule.getEnd()).multiply(MINUTES_PER_HOUR);
            if (duration.compareTo(start) >= 0 && duration.compareTo(end) < 0) {
                map.put("duration", new BigDecimal(String.valueOf(rule.getDuration())));
                map.put("unit", rule.getUnit());
                return map;
            }
        }
        return map;
    }

    public static List<TransferRulePeriod> getPeriods(Object transferPeriods) {
        if (transferPeriods == null) {
            return null;
        }
        try {
            if (transferPeriods instanceof PGobject) {
                PGobject pGobject = (PGobject) transferPeriods;
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(pGobject.getValue(),
                        new TypeReference<List<TransferRulePeriod>>() {
                        });
            }
        } catch (Exception e) {
            log.error("解析TransferRulePeriods失败: {}", e.getMessage(), e);
        }
        return null;
    }

    public abstract Map<String, Object> calTimeDuration(BigDecimal duration, Object periods, BigDecimal transferTime);

    public static String getDescByIndex(int index) {
        for (TransferRuleBDEnum t : values()) {
            if (t.getIndex() == index) {
                return t.getDesc();
            }
        }
        return null;
    }

    public static TransferRuleBDEnum getTransferRuleEnum(Integer index) {
        if (index == null) {
            return null;
        }
        for (TransferRuleBDEnum one : values()) {
            if (one.getIndex().equals(index)) {
                return one;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public String getDesc() {
        return desc;
    }

    // 时间单位枚举
    public enum TimeUnit {
        DAY(1), HOUR(2);

        private final int value;

        TimeUnit(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static TimeUnit fromInt(int value) {
            for (TimeUnit unit : values()) {
                if (unit.getValue() == value) {
                    return unit;
                }
            }
            return HOUR;
        }
    }
}

