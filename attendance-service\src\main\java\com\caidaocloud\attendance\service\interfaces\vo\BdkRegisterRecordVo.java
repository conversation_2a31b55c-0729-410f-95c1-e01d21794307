package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/2/28
 */
@Data
public class BdkRegisterRecordVo extends RegisterRecordBdkVo {
    @ApiModelProperty("补打卡原因")
    private String bdkReason;
    @ApiModelProperty("审批状态编码")
    private Integer approvalStatus;
    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;
    @ApiModelProperty("审批时间")
    private Long approvalTime;

    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;

    @ApiModelProperty("查看的工作流id")
    private String businessKey;

    private String fullPath;
    @ApiModelProperty("打卡时间,多个以逗号隔开")
    private String regDateTimes;
}
