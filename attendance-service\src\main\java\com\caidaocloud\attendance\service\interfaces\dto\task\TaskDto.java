package com.caidaocloud.attendance.service.interfaces.dto.task;

import com.caidaocloud.attendance.service.application.enums.TaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/4/16 11:42
 * @Description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskDto {
    private Long id;
    private String tenantId;
    private String type;
    private Integer progress = 1;
    private List<UploadFileResult> uploadFiles = new ArrayList<>();
    private String status = TaskStatusEnum.IN_PROCESS.getName();
    private String reason;
    private String ext;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
}
