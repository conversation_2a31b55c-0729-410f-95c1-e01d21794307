package com.caidaocloud.attendance.service.infrastructure.util;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/10/20
 */
public class NumberCheckUtil {
    /**
     * 位数和最大值校验
     *
     * @param object          数值
     * @param maxDecimalDigit 最大小数位
     * @param maxValue        最大值
     * @return true 数据不合法 false 数据合法
     */
    public static boolean checkDigitAndValueIsError(Object object, Integer maxDecimalDigit, Double maxValue) {
        if (object != null) {
            if (object instanceof BigDecimal) {
                BigDecimal number = (BigDecimal) object;
                // 最大值校验
                if (number.doubleValue() > 0 && number.doubleValue() > maxValue) {
                    return true;
                }
                // 小位数校验
                if (maxDecimalDigit != null) {
                    String numberString = number.toPlainString();
                    if (numberString.contains(".")) {
                        String[] numberStringArray = numberString.split("\\.");
                        return numberStringArray.length > 1 && numberStringArray[1].length() > maxDecimalDigit;
                    }
                }
            } else if (object instanceof Integer) {
                Integer number = (Integer) object;
                // 最大值校验
                return number > 0 && number > maxValue.intValue();
            }
        }
        return false;
    }

    public static boolean defaultCheckDigitAndValueIsError(Object object) {
        if (object != null) {
            if (object instanceof BigDecimal) {
                return checkDigitAndValueIsError(object, 2, 9999.99d);
            } else if (object instanceof Integer) {
                return checkDigitAndValueIsError(object, null, 9999d);
            }
        }
        return false;
    }

    public static boolean checkDate(String startDate) {
        String regular = "^((\\d{2}(([02468][048])|([13579][26]))[-|/]?((((0?[13578])|(1[02]))[-|/]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[-|/]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[-|/]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[-|/]?((((0?[13578])|(1[02]))[-|/]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[-|/]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[-|/]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1][0-9])|([2][0-4]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";
        Pattern r = Pattern.compile(regular);
        Matcher a = r.matcher(startDate);
        return a.matches();
    }
}
