package com.caidaocloud.attendance.service.fileimport;

import com.alibaba.fastjson.TypeReference;
import com.caidao1.commons.service.StartupServiceImpl;
import com.caidao1.ioc.dto.ImportResult;
import com.caidao1.ioc.dto.ImportResultMessage;
import com.caidao1.ioc.util.SessionHolder;
import com.caidaocloud.attendance.service.AttendanceApplication;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.imports.service.application.service.ImportsService;
import com.caidaocloud.imports.service.interfaces.dto.ImportFileDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class ImportFileWaRegisterRecordTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Autowired
    private ImportsService importsService;
    @Autowired
    private StartupServiceImpl startupService;
    @Resource
    private CacheService cacheService;

    @Before
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(wac).build();
    }

    @Test
    public void noMockImportFileTest(){
        String strUrl = "/Users/<USER>/Downloads/打卡记录模板(1).xlsx";
        File file = new File(strUrl);
        try {
            InputStream inputStream = new FileInputStream(file);
            //mock---file转multipartfile(测试使用)
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "multipart/form-data", IOUtils.toByteArray(inputStream));

            ImportFileDto query = new ImportFileDto();
            query.setFile(multipartFile);
            // 89 打卡记录导入
            query.setResId(89);
            ImportResult result = importsService.importFile(query);
            try {
                startupService.initEmp(SessionHolder.getBelongOrgId());
            }catch (Exception e){
                log.error("initEmp err,{}", e.getMessage(), e);
            }

            log.info("importFile ImportResult={}", FastjsonUtil.toJson(result));
        } catch (Exception e){
            log.error("importsService importFile err,{}", e.getMessage(), e);
        }
    }

    @Test
    public void importFile(){
        String result = "";
        String path = "/Users/<USER>/Downloads/打卡记录模板(1).xlsx";
        // dev token
        String accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6ImNhaWRhb3Rlc3QiLCJ0aW1lIjoxNjM0MDI2MDcxODk4LCJ0eXBlIjoyLCJ1c2VySWQiOiIzNTE0MiJ9.sqOueWSpSFWiCoHtUg66U5JhOro-jaceMiyMy-Q0Yl_DVRXc1J8tpqlmJyUjHK8bU5FkQYtdXeYRnbuUyowkLQ";
        // fat token
        // accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6InRlbmNlbnQtdGVzdDMiLCJ0aW1lIjoxNjI4MjIwNjAzMDM2LCJ0eXBlIjoyLCJ1c2VySWQiOiIzNTExMyJ9.PPbGY-0WdiCLsMphxBwN_fXObrYQyKzhz32aormiOG2331_coHeuQfKHDaRbRZQ7-yfyFrBMzskhAM8qsdns4A";
        try {
            result =  mockMvc.perform(
                MockMvcRequestBuilders
                    .fileUpload("/api/attendance/imports/v1/importFile")
                    .file(
                            new MockMultipartFile("file", "打卡记录模板(1).xlsx", ", multipart/form-data", new FileInputStream(new File(path)))
                    ).header("Access-Token", accessToken)
                    // 89 打卡记录导入
                    .param("resId", "89")
            ).andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            log.error("mockMvc importFile err,{}", e.getMessage(), e);
        }

        log.info("importFile result={}", result);
        Result<ImportResult> importResult = FastjsonUtil.toObject(result, new TypeReference<Result<ImportResult>>(){});

        ImportResult ir = importResult.getData();

        long progress = System.currentTimeMillis();
        String content = "{\"templateId\":" + ir.getTemplateId() + ",\"rtnFileName\":\""
            + ir.getFileName() + "\",\"funcId\":" + ir.getFuncId() + ",\"progress\":\"" + progress + "\"}";
        try {
            result = mockMvc.perform(
                    MockMvcRequestBuilders.post("/api/attendance/imports/v1/checkImports")
                            .header("Access-Token", accessToken)
                            .header("Accept", "application/json, text/plain, */*")
                            .content(content).contentType(MediaType.APPLICATION_JSON)
            ).andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e){
            log.error("mockMvc checkImports err,{}", e.getMessage(), e);
        }

        log.info("checkImports result={}", result);

        ///api/attendance/imports/v1/saveImportData

        try {
            result = mockMvc.perform(
                    MockMvcRequestBuilders.post("/api/attendance/imports/v1/saveImportData")
                            .header("Access-Token", accessToken)
                            .header("Accept", "application/json, text/plain, */*")
                            .content(content).contentType(MediaType.APPLICATION_JSON)
            ).andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e){
            log.error("mockMvc saveImportData err,{}", e.getMessage(), e);
        }

        log.info("saveImportData result={}", result);
        Result<ImportResultMessage> resultMsg = FastjsonUtil.toObject(result, new TypeReference<Result<ImportResultMessage>>(){});
        ImportResultMessage vo = resultMsg.getData();
        log.info("message vo message={}", vo.getMessage());

        try {
            result = mockMvc.perform(
                    MockMvcRequestBuilders.post("/api/attendance/imports/v1/getProgress")
                            .header("Access-Token", accessToken)
                            .header("Accept", "application/json, text/plain, */*")
                            .content(content).contentType(MediaType.APPLICATION_JSON)
            ).andExpect(MockMvcResultMatchers.status().isOk())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e){
            log.error("mockMvc getProgress err,{}", e.getMessage(), e);
        }
        log.info("getProgress result={}", result);

        cacheService.remove("importDataList_" + progress);
    }
}
