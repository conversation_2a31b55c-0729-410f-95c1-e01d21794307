package com.caidaocloud.attendance.service.interfaces.shimz.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EmpOvertimeDetailDto {
    @ApiModelProperty("员工")
    private List<Long> empIds;
    @ApiModelProperty("加班开始日期")
    private Long startDate;
    @ApiModelProperty("加班结束日期")
    private Long endDate;
    @ApiModelProperty("关键字")
    private String keywords;
    @ApiModelProperty("审批状态：1、审批中，2、已通过")
    private List<Short> status;
}
