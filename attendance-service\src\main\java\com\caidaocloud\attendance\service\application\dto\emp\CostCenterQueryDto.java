package com.caidaocloud.attendance.service.application.dto.emp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("成本中心查询参数")
public class CostCenterQueryDto {
    @ApiModelProperty("成本中心名称/编码，可选")
    private String costCenterNameOrCode;
    @ApiModelProperty("日期时间（单位毫秒），可选")
    private Long dateTime;
    @ApiModelProperty("状态（0 已启用 1 已停用），可选")
    private Integer status;
}
