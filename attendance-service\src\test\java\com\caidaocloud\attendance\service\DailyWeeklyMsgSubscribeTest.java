package com.caidaocloud.attendance.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class DailyWeeklyMsgSubscribeTest {
    @Bean("ttlDelayQueue")
    public Queue ttlQueue(){
        //Map<String, Object> map = new HashMap();
        //过期后进入死信队列
        //map.put("x-dead-letter-exchange", "TTL_DELAY_DEAD_LETTER_FANOUT_EXCHANGE");
        return QueueBuilder.durable("taoge.ttl.queue.hello").exclusive().autoDelete().build();
    }

    @Bean("ttlDelayExchange")
    public CustomExchange delayExchange() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange("taoge.ttl.exchange.hello", "x-delayed-message",
                true, false, args);
    }

    @Bean
    public Binding delayBinding(@Qualifier("ttlDelayQueue") Queue ttlDelayQueue,
            @Qualifier("ttlDelayExchange") CustomExchange ttlDelayExchange) {
        return BindingBuilder.bind(ttlDelayQueue).to(ttlDelayExchange).
                with("taoge.ttl.routingKey.hello").noargs();
    }

    //监听延时队列
    @RabbitHandler
    @RabbitListener(queues = "taoge.ttl.queue.hello")
    public void process(String message) {
        message += "-------" + System.currentTimeMillis();
        log.info("start DailyWeeklyMsgSubscribe={}", message);
        log.info("end DailyWeeklyMsgSubscribe={}", message);
    }

    @Test
    public void test(){
        process("涛哥");
    }
}
