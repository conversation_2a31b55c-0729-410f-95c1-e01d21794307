package com.caidaocloud.attendance.service.infrastructure.repository.po;

import java.math.BigDecimal;

public class WaRegisterRecordBdkPo {
    private Long recordId;

    private Long empid;

    private Integer registerType;

    private String resultDesc;

    private Integer resultType;

    private String reason;

    private String regAddr;

    private Long regDateTime;

    private BigDecimal lng;

    private BigDecimal lat;

    private Long belongDate;

    private String mobDeviceNum;

    private Long crtuser;

    private Long crttime;

    private Long upduser;

    private Long updtime;

    private String normalAddr;

    private String normalDate;

    private Integer type;

    private String owRmk;

    private String picList;

    private Long hisRegTime;

    private Integer shiftDefId;

    private Boolean isDeviceError;

    private String oriMobDeviceNum;

    private Boolean isWorkflow;

    private Integer approvalStatus;

    private String approvalReason;

    private String filePath;

    private String revokeReason;

    private Long siteId;

    private String province;

    private String city;

    private Integer startTime;

    private Integer endTime;

    private Long corpid;

    private String belongOrgId;

    private Long lastApprovalTime;

    private Integer ifValid;

    private String regDateTimes;

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Long getEmpid() {
        return empid;
    }

    public void setEmpid(Long empid) {
        this.empid = empid;
    }

    public Integer getRegisterType() {
        return registerType;
    }

    public void setRegisterType(Integer registerType) {
        this.registerType = registerType;
    }

    public String getResultDesc() {
        return resultDesc;
    }

    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc == null ? null : resultDesc.trim();
    }

    public Integer getResultType() {
        return resultType;
    }

    public void setResultType(Integer resultType) {
        this.resultType = resultType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getRegAddr() {
        return regAddr;
    }

    public void setRegAddr(String regAddr) {
        this.regAddr = regAddr == null ? null : regAddr.trim();
    }

    public Long getRegDateTime() {
        return regDateTime;
    }

    public void setRegDateTime(Long regDateTime) {
        this.regDateTime = regDateTime;
    }

    public BigDecimal getLng() {
        return lng;
    }

    public void setLng(BigDecimal lng) {
        this.lng = lng;
    }

    public BigDecimal getLat() {
        return lat;
    }

    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

    public Long getBelongDate() {
        return belongDate;
    }

    public void setBelongDate(Long belongDate) {
        this.belongDate = belongDate;
    }

    public String getMobDeviceNum() {
        return mobDeviceNum;
    }

    public void setMobDeviceNum(String mobDeviceNum) {
        this.mobDeviceNum = mobDeviceNum == null ? null : mobDeviceNum.trim();
    }

    public Long getCrtuser() {
        return crtuser;
    }

    public void setCrtuser(Long crtuser) {
        this.crtuser = crtuser;
    }

    public Long getCrttime() {
        return crttime;
    }

    public void setCrttime(Long crttime) {
        this.crttime = crttime;
    }

    public Long getUpduser() {
        return upduser;
    }

    public void setUpduser(Long upduser) {
        this.upduser = upduser;
    }

    public Long getUpdtime() {
        return updtime;
    }

    public void setUpdtime(Long updtime) {
        this.updtime = updtime;
    }

    public String getNormalAddr() {
        return normalAddr;
    }

    public void setNormalAddr(String normalAddr) {
        this.normalAddr = normalAddr == null ? null : normalAddr.trim();
    }

    public String getNormalDate() {
        return normalDate;
    }

    public void setNormalDate(String normalDate) {
        this.normalDate = normalDate == null ? null : normalDate.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOwRmk() {
        return owRmk;
    }

    public void setOwRmk(String owRmk) {
        this.owRmk = owRmk == null ? null : owRmk.trim();
    }

    public String getPicList() {
        return picList;
    }

    public void setPicList(String picList) {
        this.picList = picList == null ? null : picList.trim();
    }

    public Long getHisRegTime() {
        return hisRegTime;
    }

    public void setHisRegTime(Long hisRegTime) {
        this.hisRegTime = hisRegTime;
    }

    public Integer getShiftDefId() {
        return shiftDefId;
    }

    public void setShiftDefId(Integer shiftDefId) {
        this.shiftDefId = shiftDefId;
    }

    public Boolean getIsDeviceError() {
        return isDeviceError;
    }

    public void setIsDeviceError(Boolean isDeviceError) {
        this.isDeviceError = isDeviceError;
    }

    public String getOriMobDeviceNum() {
        return oriMobDeviceNum;
    }

    public void setOriMobDeviceNum(String oriMobDeviceNum) {
        this.oriMobDeviceNum = oriMobDeviceNum == null ? null : oriMobDeviceNum.trim();
    }

    public Boolean getIsWorkflow() {
        return isWorkflow;
    }

    public void setIsWorkflow(Boolean isWorkflow) {
        this.isWorkflow = isWorkflow;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalReason() {
        return approvalReason;
    }

    public void setApprovalReason(String approvalReason) {
        this.approvalReason = approvalReason == null ? null : approvalReason.trim();
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath == null ? null : filePath.trim();
    }

    public String getRevokeReason() {
        return revokeReason;
    }

    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason == null ? null : revokeReason.trim();
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Long getCorpid() {
        return corpid;
    }

    public void setCorpid(Long corpid) {
        this.corpid = corpid;
    }

    public String getBelongOrgId() {
        return belongOrgId;
    }

    public void setBelongOrgId(String belongOrgId) {
        this.belongOrgId = belongOrgId;
    }

    public Long getLastApprovalTime() {
        return lastApprovalTime;
    }

    public void setLastApprovalTime(Long lastApprovalTime) {
        this.lastApprovalTime = lastApprovalTime;
    }

    public Integer getIfValid() {
        return ifValid;
    }

    public void setIfValid(Integer ifValid) {
        this.ifValid = ifValid;
    }

    public String getRegDateTimes() {
        return regDateTimes;
    }

    public void setRegDateTimes(String regDateTimes) {
        this.regDateTimes = regDateTimes;
    }
}