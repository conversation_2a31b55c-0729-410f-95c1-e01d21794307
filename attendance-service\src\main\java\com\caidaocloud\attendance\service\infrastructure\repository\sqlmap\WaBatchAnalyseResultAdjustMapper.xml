<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaBatchAnalyseResultAdjustMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchAnalyseResultAdjust">
    <id column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="empid" jdbcType="BIGINT" property="empid" />
    <result column="wa_sob_id" jdbcType="INTEGER" property="waSobId" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="end_date" jdbcType="BIGINT" property="endDate" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ext_custom_col" jdbcType="VARCHAR" property="extCustomCol" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="last_empid" jdbcType="BIGINT" property="lastEmpid" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="revoke_status" jdbcType="INTEGER" property="revokeStatus" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    batch_id, empid, wa_sob_id, start_date, end_date, file_path, file_name, business_key, 
    reason, ext_custom_col, status, last_approval_time, last_empid, revoke_reason, revoke_status, 
    tenant_id, deleted, create_by, create_time, update_by, update_time,process_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_batch_analyse_result_adjust
    where batch_id = #{batchId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_batch_analyse_result_adjust
    where batch_id = #{batchId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchAnalyseResultAdjust">
    insert into wa_batch_analyse_result_adjust (batch_id, empid, wa_sob_id, 
      start_date, end_date, file_path, 
      file_name, business_key, reason, 
      ext_custom_col, status, last_approval_time, 
      last_empid, revoke_reason, revoke_status, 
      tenant_id, deleted, create_by, 
      create_time, update_by, update_time
      )
    values (#{batchId,jdbcType=BIGINT}, #{empid,jdbcType=BIGINT}, #{waSobId,jdbcType=INTEGER}, 
      #{startDate,jdbcType=BIGINT}, #{endDate,jdbcType=BIGINT}, #{filePath,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{businessKey,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{extCustomCol,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{lastApprovalTime,jdbcType=BIGINT}, 
      #{lastEmpid,jdbcType=BIGINT}, #{revokeReason,jdbcType=VARCHAR}, #{revokeStatus,jdbcType=INTEGER}, 
      #{tenantId,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchAnalyseResultAdjust">
    insert into wa_batch_analyse_result_adjust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="waSobId != null">
        wa_sob_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="extCustomCol != null">
        ext_custom_col,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="lastEmpid != null">
        last_empid,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="revokeStatus != null">
        revoke_status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="waSobId != null">
        #{waSobId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=BIGINT},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchAnalyseResultAdjust">
    update wa_batch_analyse_result_adjust
    <set>
      <if test="empid != null">
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="waSobId != null">
        wa_sob_id = #{waSobId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=BIGINT},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        last_empid = #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        revoke_status = #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where batch_id = #{batchId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchAnalyseResultAdjust">
    update wa_batch_analyse_result_adjust
    set empid = #{empid,jdbcType=BIGINT},
      wa_sob_id = #{waSobId,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=BIGINT},
      end_date = #{endDate,jdbcType=BIGINT},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      business_key = #{businessKey,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      revoke_status = #{revokeStatus,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where batch_id = #{batchId,jdbcType=BIGINT}
  </update>

  <select id="selectEmpWaAnalyzeList" resultType="com.caidao1.wa.mybatis.model.WaAnalyze">
    SELECT wa.empid,
    wa.belong_date      AS "belongDate",
    wa.late_time        as "lateTime",
    wa.early_time       as "earlyTime",
    wa.kg_work_time     as "kgWorkTime",
    wa.signin_id        AS "signinId",
    wa.signoff_id       AS "signoffId",
    wa.reg_signin_time  AS "regSigninTime",
    wa.reg_signoff_time AS "regSignoffTime"
    FROM wa_analyze wa
    WHERE
    wa.belong_org_id = #{tenantId} AND wa.empid = #{empid}
    AND wa.belong_date BETWEEN #{startDate} AND #{endDate}
    <if test="abnormal != null and abnormal == true">
      AND (wa.is_kg &gt; 0 OR wa.late_time &gt; 0 OR wa.early_time &gt; 0)
    </if>
    ORDER BY wa.belong_date
  </select>

  <select id="selectPageList" parameterType="hashmap" resultType="hashmap">
    SELECT * from(
    SELECT waadjust.batch_id,
    waadjust.create_time as crttime,
    waadjust.last_approval_time,
    waadjust.status,
    waadjust.business_key,
    waadjust.empid,
    ei.workno,
    ei.emp_name,
    org.orgid,
    org.shortname        as org_name,
    case
    when org.full_path is not null and org.full_path != ''
    then concat_ws('/', org.full_path, org.shortname)
    else org.shortname
    end              as org_full_path,
    post.post_id,
    post.chn_name        as post_name,
    sob.wa_sob_name as "waSobName"
    FROM wa_batch_analyse_result_adjust waadjust
    JOIN sys_emp_info ei ON waadjust.empid = ei.empid AND ei.deleted = 0
    JOIN wa_sob sob ON sob.wa_sob_id = waadjust.wa_sob_id
    LEFT JOIN sys_corp_org org on ei.orgid = org.orgid AND org.deleted = 0
    LEFT JOIN sys_org_position post on ei.post_id = post.post_id
    <where>
      waadjust.tenant_id = #{tenantId}
      <if test="empid != null">
        AND waadjust.empid = #{empid}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
      </if>
      <if test="startDate != null and endDate != null">
        AND waadjust.start_date <![CDATA[<=]]> #{endDate} AND waadjust.end_date <![CDATA[>=]]> #{startDate}
      </if>
      <if test="datafilter != null and datafilter != ''">
        ${datafilter}
      </if>
    </where>
    ) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
  </select>
</mapper>