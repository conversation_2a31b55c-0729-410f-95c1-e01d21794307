package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.dto.EmpShiftRecordDto;
import com.caidaocloud.attendance.service.application.service.IEmpInfoService;
import com.caidaocloud.attendance.service.interfaces.vo.shift.EmpShiftRecordVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/4/1 10:26
 * @Description:
 **/
@Slf4j
@RestController
@RequestMapping("/api/attendance/emp/v1")
@Api(value = "/api/attendance/emp/v1", description = "员工相关接口")
public class EmpInfoController {

    @Autowired
    private IEmpInfoService empInfoService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation("班次以及打卡记录查询")
    @GetMapping("/getShiftAndRecords")
    public Result<EmpShiftRecordVo> getShiftAndRecords(@RequestParam("regDate") Long regDate) {
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            EmpShiftRecordDto empShiftDto = empInfoService.getShiftAndRecords(regDate, userInfo.getStaffId());
            return ResponseWrap.wrapResult(ObjectConverter.convert(empShiftDto, EmpShiftRecordVo.class));
        } catch (Exception e) {
            log.error("EmpInfoController.getShiftAndRecords has exception: {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "未知错误", null);
        }
    }
}
