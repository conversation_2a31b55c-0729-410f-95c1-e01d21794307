package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class HolidayEntryVo implements Serializable {

    @ApiModelProperty("特殊日期日期项id")
    private Integer calendarDetailId;

    @ApiModelProperty("日期类型:1、工作日，2休息日，3法定假日")
    private Integer dateType;

    @ApiModelProperty("特殊日期id")
    private Integer holidayCalendar;

    @ApiModelProperty("是否适用于门店考勤：0否1是")
    private Boolean isApplyStore;

    @ApiModelProperty("特殊日期项日期")
    private Long calendarDate;

    @ApiModelProperty("如类型是工作日，则该工作日替换哪天？")
    private Long specDate;

    @ApiModelProperty("替换班次")
    private Integer replaceShift;
}
