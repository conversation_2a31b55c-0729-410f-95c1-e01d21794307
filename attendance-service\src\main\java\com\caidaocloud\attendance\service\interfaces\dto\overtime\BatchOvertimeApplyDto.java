package com.caidaocloud.attendance.service.interfaces.dto.overtime;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.interfaces.dto.OverApplySaveDto;
import com.caidaocloud.dto.UserInfo;
import com.qcloud.cos.utils.Md5Utils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量加班申请DTO
 *
 * <AUTHOR>
 * @Date 2024/6/24
 */
@Data
public class BatchOvertimeApplyDto {
    @ApiModelProperty("考勤周期ID")
    private Integer waSobId;
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("附件地址")
    private String filePath;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("加班明细")
    private List<OverApplySaveDto> overtimeList;

    public static String getIdempotentString(BatchOvertimeApplyDto model) {
        UserInfo userInfo = UserContext.preCheckUser();
        return Md5Utils.md5Hex(String.format("%s_%s_%s_%s", "BATCH_OT", userInfo.getTenantId(), userInfo.getUserId(),
                model.getEmpid()));
    }

    public List<OverApplySaveDto> getOvertimeList() {
        this.overtimeList.forEach(saveDto -> saveDto.setEmpId(this.empid));
        return this.overtimeList;
    }

}
