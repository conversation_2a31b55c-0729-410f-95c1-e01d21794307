package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName(value = "wa_overtime_transfer_rule")
@Data
public class WaOvertimeTransferRulePo {
    private Long ruleId;
    private String tenantId;
    private String ruleName;
    private Integer compensateType;
    private Integer leaveTypeId;
    private Integer transferRule;
    private Object transferPeriods;
    private Float transferTime;
    private String note;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private Float timeDuration;
}
