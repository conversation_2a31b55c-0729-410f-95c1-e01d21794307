<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.ShiftDefMapper">
    <resultMap id="BaseResultMap" type="com.caidao1.wa.mybatis.model.WaShiftDef" >
        <id column="shift_def_id" property="shiftDefId" jdbcType="INTEGER" />
        <result column="date_type" property="dateType" jdbcType="INTEGER" />
        <result column="is_night" property="isNight" jdbcType="BIT" />
        <result column="shift_def_name" property="shiftDefName" jdbcType="VARCHAR" />
        <result column="shift_def_code" property="shiftDefCode" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="INTEGER" />
        <result column="end_time" property="endTime" jdbcType="INTEGER" />
        <result column="is_noon_rest" property="isNoonRest" jdbcType="BIT" />
        <result column="noon_rest_start" property="noonRestStart" jdbcType="INTEGER" />
        <result column="noon_rest_end" property="noonRestEnd" jdbcType="INTEGER" />
        <result column="rest_total_time" property="restTotalTime" jdbcType="INTEGER" />
        <result column="work_total_time" property="workTotalTime" jdbcType="INTEGER" />
        <result column="on_duty_start_time" property="onDutyStartTime" jdbcType="INTEGER" />
        <result column="on_duty_end_time" property="onDutyEndTime" jdbcType="INTEGER" />
        <result column="off_duty_start_time" property="offDutyStartTime" jdbcType="INTEGER" />
        <result column="off_duty_end_time" property="offDutyEndTime" jdbcType="INTEGER" />
        <result column="overtime_start_time" property="overtimeStartTime" jdbcType="INTEGER" />
        <result column="overtime_end_time" property="overtimeEndTime" jdbcType="INTEGER" />
        <result column="belong_orgid" property="belongOrgid" jdbcType="VARCHAR" />
        <result column="crtuser" property="crtuser" jdbcType="BIGINT" />
        <result column="crttime" property="crttime" jdbcType="BIGINT" />
        <result column="is_default" property="isDefault" jdbcType="BIT" />
        <result column="is_halfday_time" property="isHalfdayTime" jdbcType="BIT" />
        <result column="halfday_time" property="halfdayTime" jdbcType="INTEGER" />
        <result column="is_flexible_work" property="isFlexibleWork" jdbcType="BIT" />
        <result column="flexible_on_duty_start_time" property="flexibleOnDutyStartTime" jdbcType="INTEGER" />
        <result column="flexible_on_duty_end_time" property="flexibleOnDutyEndTime" jdbcType="INTEGER" />
        <result column="flexible_off_duty_start_time" property="flexibleOffDutyStartTime" jdbcType="INTEGER" />
        <result column="flexible_off_duty_end_time" property="flexibleOffDutyEndTime" jdbcType="INTEGER" />
        <result column="flexible_work_type" property="flexibleWorkType" jdbcType="INTEGER" />
        <result column="rest_periods" property="restPeriods" jdbcType="OTHER" />
        <result column="overtime_rest_periods" property="overtimeRestPeriods" jdbcType="OTHER" />
        <result column="is_adjust_work_hour" property="isAdjustWorkHour" jdbcType="BIT" />
        <result column="adjust_work_hour_json" property="adjustWorkHourJson" jdbcType="OTHER" />
        <result column="is_special" property="isSpecial" jdbcType="BIT" />
        <result column="special_work_time" property="specialWorkTime" jdbcType="INTEGER" />
        <result column="is_apply_overtime" property="isApplyOvertime" jdbcType="BIT" />
        <result column="multi_checkin_times" property="multiCheckinTimes" jdbcType="OTHER" />
        <result column="multi_work_times" property="multiWorkTimes" jdbcType="OTHER" />
        <result column="rest_time_desc" property="restTimeDesc" jdbcType="VARCHAR" />
        <result column="orgid" property="orgid" jdbcType="BIGINT" />
        <result column="effect_start_time" property="effectStartTime" jdbcType="BIGINT" />
        <result column="effect_end_time" property="effectEndTime" jdbcType="BIGINT" />
        <result column="i18n_shift_def_name" property="i18nShiftDefName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="getShiftDefList" resultMap="BaseResultMap">
        SELECT shift_def_id,
            date_type,
            shift_def_name,
            shift_def_code,
            start_time,
            end_time,
            is_noon_rest,
            noon_rest_start,
            noon_rest_end,
            rest_total_time,
            work_total_time,
            on_duty_start_time,
            on_duty_end_time,
            off_duty_start_time,
            off_duty_end_time,
            overtime_start_time,
            overtime_end_time,
            rest_periods,
            rest_time_desc,
            ei.effect_start_time,
            ei.effect_end_time,
            ei.crttime,i18n_shift_def_name
        FROM wa_shift_def ei
        WHERE ei.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="dateTypes != null and dateTypes.length > 0">
            and ei.date_type in <foreach collection="dateTypes" open="(" separator="," close=")" item="item">#{item}</foreach>
        </if>
        <if test="keywords != null and keywords!= ''">
            and shift_def_name like concat('%',#{keywords},'%')
        </if>
        <if test="filter != null and filter !=''">
            ${filter}
        </if>
    </select>
</mapper>