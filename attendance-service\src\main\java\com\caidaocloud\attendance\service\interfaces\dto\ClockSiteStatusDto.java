package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ClockSiteStatusDto {
    @ApiModelProperty(value = "主键Id", required = true)
    private List<Integer> recordIds;
    @ApiModelProperty(value = "打卡地点状态：0无效1有效", required = true, allowableValues = "0,1")
    private Integer clockSiteStatus;
}
