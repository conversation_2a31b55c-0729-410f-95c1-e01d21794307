package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.BatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchAnalyseResultAdjustService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.BatchAnalyseResultAdjustQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.RevokeBatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.interfaces.vo.BatchAnalyseResultAdjustPageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.WaAbnormalResultVo;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 批量考勤异常申请-门户
 *
 * <AUTHOR>
 * @Date 2024/6/24
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/analyseResultAdjust/portal/v1")
@Api(value = "/api/attendance/batch/analyseResultAdjust/portal/v1", description = "批量考勤异常申请-门户")
public class WaBatchAnalyseResultAdjustPortalController {
    @Autowired
    private WaBatchAnalyseResultAdjustService waBatchAnalyseResultAdjustService;

    @ApiOperation(value = "指定日期范围查询员工考勤数据")
    @GetMapping(value = "/getWaResultList")
    public Result<List<WaAbnormalResultVo>> getWaResultList(@RequestParam("startDate") Long startDate,
                                                            @RequestParam("endDate") Long endDate) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        return Result.ok(waBatchAnalyseResultAdjustService.getWaResultList(userInfo.getStaffId(), startDate, endDate));
    }

    @ApiOperation(value = "指定日期查询员工考勤数据")
    @GetMapping(value = "/getWaResult")
    public Result<WaAbnormalResultVo> getWaResult(@RequestParam("date") Long date) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        try {
            return Result.ok(waBatchAnalyseResultAdjustService.getWaResult(userInfo.getStaffId(), date));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 获取考勤数据失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201910", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "检查考勤异常申请数据")
    @PostMapping(value = "/check")
    public Result<Boolean> check(@RequestBody BatchAnalyseResultAdjustDto adjustDto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        adjustDto.setEmpid(userInfo.getStaffId());
        try {
            waBatchAnalyseResultAdjustService.check(adjustDto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 申请失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201911", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "保存考勤异常申请数据")
    @PostMapping(value = "/save")
    public Result<Boolean> save(@RequestBody BatchAnalyseResultAdjustDto adjustDto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        adjustDto.setEmpid(userInfo.getStaffId());
        try {
            return Result.ok(waBatchAnalyseResultAdjustService.save(adjustDto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 申请失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201911", WebUtil.getRequest()));
        }
    }

    @ApiOperation(value = "分页列表")
    @PostMapping(value = "/page")
    public Result<AttendancePageResult<BatchAnalyseResultAdjustPageListVo>> getPageList(@RequestBody QueryPageBean queryPageBean) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        BatchAnalyseResultAdjustQueryDto dto = new BatchAnalyseResultAdjustQueryDto();
        dto.setEmpid(userInfo.getStaffId());
        PageUtil.doCopyFieldProperty(queryPageBean, dto);
        PageBean pageBean = PageUtil.getPageBean(dto);
        waBatchAnalyseResultAdjustService.preHandleFilterField(pageBean);
        PageList<BatchAnalyseResultAdjustPageListVo> pageList = waBatchAnalyseResultAdjustService.getPageList(pageBean, dto, UserContext.getAndCheckUser());
        AttendancePageResult<BatchAnalyseResultAdjustPageListVo> pageResult = new AttendancePageResult<>(pageList, pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revoke(@RequestBody RevokeBatchAnalyseResultAdjustDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            waBatchAnalyseResultAdjustService.revoke(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            // 撤销失败
            return Result.fail(MessageHandler.getMessage("caidao.exception.error_201102", WebUtil.getRequest()));
        }
    }
}
