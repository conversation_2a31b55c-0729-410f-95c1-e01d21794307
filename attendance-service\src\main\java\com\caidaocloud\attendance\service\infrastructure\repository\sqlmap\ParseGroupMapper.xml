<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.ParseGroupMapper">
    <select id="getWaParseGroupList" resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaParseGroupPo">
        SELECT eg.empid,
        eg.start_time,
        eg.end_time,
        wg.wa_group_id,
        wg.cyle_startdate,
        wg.cyle_month,
        wpg.late_cycle,
        wpg.late_unit,
        wpg.late_count,
        wpg.late_allow_unit,
        wpg.late_allow_number,
        wpg.early_cycle,
        wpg.early_unit,
        wpg.early_count,
        wpg.early_allow_unit,
        wpg.early_allow_number,
        wpg.absent_limit,
        wpg.ot_parse,
        wpg.lv_parse,
        wpg.register_miss,
        wpg.is_analyze_late_early,
        wpg.absent_condition_jsonb,
        wpg.is_outer_sign,
        wpg.ot_pase_jsonb,
        wpg.is_flexible_working,
        wpg.ot_sum_parse,
        wpg.clock_type,
        wpg.clock_rule,
        wpg.out_parse_rule,
        wpg.allowed_date_type
        FROM wa_emp_group eg
        JOIN wa_group wg on eg.wa_group_id = wg.wa_group_id
        JOIN wa_parse_group wpg on wpg.parse_group_id = wg.parse_group_id
        WHERE wg.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        AND eg.empid in <foreach collection="empIds" open="(" separator="," close=")" item="item">#{item}</foreach>
        <![CDATA[
        AND eg.start_time <= #{endDate}
        AND eg.end_time >= #{startDate}
         ]]>
    </select>
</mapper>