package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RegisterRecordBdkDto {
    @ApiModelProperty("打卡记录ID")
    private Long recordId;
    @ApiModelProperty("打卡方式： 1 GPS 2 扫码 3 外勤 4 蓝牙 5 WIFI 6 补卡")
    private Integer type;
    @ApiModelProperty("打卡方式")
    private String typeName;
    @ApiModelProperty("打卡时间")
    private Long regDateTime;
    @ApiModelProperty("打卡地点")
    private String regAddr;
    @ApiModelProperty("设备号")
    private String mobDeviceNum;
    @ApiModelProperty("创建用户")
    private String userName;
    private String reason;
    private String normalDate;

    /**
     * 补卡信息
     */
    @ApiModelProperty("补打卡原因")
    private String bdkReason;
    @ApiModelProperty("审批状态编码")
    private Integer approvalStatus;
    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;
    @ApiModelProperty("审批时间")
    private Long approvalTime;
    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;
    @ApiModelProperty("查看的工作流id")
    private String businessKey;

    /**
     * 每日考勤信息
     */
    @ApiModelProperty("考勤日期")
    private Long belongDate;
    @ApiModelProperty("打卡类型编码：1 签到 2 签退")
    private Integer registerType;
    @ApiModelProperty("打卡类型名称")
    private String registerTypeName;
    @ApiModelProperty("考勤状态编码；打卡结果：1 正常 2 异常")
    private Integer resultType;
    @ApiModelProperty("考勤状态名称")
    private String resultTypeName;

    /**
     * 员工信息
     */
    @ApiModelProperty("帐号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("组织ID")
    private Long orgid;
    @ApiModelProperty("组织")
    private String orgName;
    @ApiModelProperty("组织全路径")
    private String fullPath;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;

    @ApiModelProperty("打卡异常结果描述")
    private String resultDesc;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    private Long crttime;

    @ApiModelProperty("备注")
    private String owRmk;
    @ApiModelProperty("附件id")
    private String files;
    @ApiModelProperty("附件名包括扩展名")
    private String fileNames;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态文本")
    private String statusName;
    @ApiModelProperty("申请类型")
    private String applyName;

    @ApiModelProperty("打卡地点状态:0无效1有效")
    private Integer clockSiteStatus;

    @ApiModelProperty("打卡时间,多个以逗号隔开")
    private String regDateTimes;
}
