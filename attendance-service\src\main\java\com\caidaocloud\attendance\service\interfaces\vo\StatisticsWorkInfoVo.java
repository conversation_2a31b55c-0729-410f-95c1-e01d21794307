package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 25/3/2024 2:01 下午
 */
@Data
public class StatisticsWorkInfoVo implements Serializable {

    @ApiModelProperty("考勤日期")
    private Long belongDate;

    @ApiModelProperty("工号")
    private String workNo;

    @ApiModelProperty("姓名")
    private String empName;

    @ApiModelProperty("任职组织")
    private String orgName;

    @ApiModelProperty("打卡方式")
    private String signType;

    @ApiModelProperty("归属日期")
    private Long signBelongDate;

    @ApiModelProperty("打卡时间")
    private Long signTime;

    @ApiModelProperty("打卡地点")
    private String signAddress;

    @ApiModelProperty("打卡状态")
    private Integer signStatus;

    @ApiModelProperty("设备号")
    private String deviceNo;

    @ApiModelProperty("打卡数据来源")
    private String source;
}
