package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.application.service.IEmpTravelService;
import com.caidaocloud.attendance.service.application.service.ILeaveApplyService;
import com.caidaocloud.attendance.service.application.service.IOvertimeApplyService;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyseResultAdjustDo;
import com.caidaocloud.attendance.service.domain.entity.WaBatchAnalyseResultAdjustDo;
import com.caidaocloud.attendance.service.domain.entity.WaBatchOvertimeDo;
import com.caidaocloud.attendance.service.domain.repository.IAttendanceEmpGroupRepository;
import com.caidaocloud.attendance.service.domain.repository.IWaBatchAnalyseResultAdjustRepository;
import com.caidaocloud.attendance.service.domain.repository.IWaCustomLogicConfigRepository;
import com.caidaocloud.attendance.service.domain.service.WaBatchLeaveDomainService;
import com.caidaocloud.attendance.service.domain.service.WaBatchTravelDomainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/11/7
 */
@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class WaBatchTest {
    @Autowired
    private WaBatchLeaveDomainService waBatchLeaveDomainService;
    @Autowired
    private WaBatchOvertimeDo waBatchOvertimeDo;
    @Autowired
    private WaBatchTravelDomainService waBatchTravelDomainService;
    @Autowired
    private IEmpTravelService empTravelService;
    @Autowired
    private WaBatchAnalyseResultAdjustDo waBatchAnalyseResultAdjustDo;
    @Autowired
    private WaAnalyseResultAdjustDo waAnalyseResultAdjustDo;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private ILeaveApplyService leaveApplyService;

    @Resource
    private IAttendanceEmpGroupRepository attendanceEmpGroupRepository;
    @Autowired
    private IWaBatchAnalyseResultAdjustRepository waBatchAnalyseResultAdjustRepository;
    @Autowired
    private IWaCustomLogicConfigRepository waCustomLogicConfigRepository;

    @Test
    public void testDel() {
        waCustomLogicConfigRepository.deleteById(11L);
        waBatchAnalyseResultAdjustRepository.deleteById(11L);
        attendanceEmpGroupRepository.deleteById(11L);

        waBatchLeaveDomainService.deleteById(1965331528464385L);
        leaveApplyService.deleteByBatchId("11", 1965331528464385L);

        waBatchOvertimeDo.deleteById(1968252564019201L);
        overtimeApplyService.deleteByBatchId("11", 1968252564019201L);

        waBatchTravelDomainService.deleteById(1913691739502593L);
        empTravelService.deleteByBatchId("11", 1913691739502593L);

        waAnalyseResultAdjustDo.deleteByBatchId(1970847181182977L);
        waBatchAnalyseResultAdjustDo.deleteById(1970847181182977L);
    }
}
