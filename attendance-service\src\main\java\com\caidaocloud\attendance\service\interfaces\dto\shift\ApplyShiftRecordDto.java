package com.caidaocloud.attendance.service.interfaces.dto.shift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/15 22:19
 * @Version 1.0
 */
@Data
public class ApplyShiftRecordDto {

    @ApiModelProperty("主键id")
    private Long recId;
    @ApiModelProperty("租户id")
    private String tenantId;
    @ApiModelProperty("员工id")
    private Long empId;
    @ApiModelProperty("调班日期")
    private Long workDate;
    @ApiModelProperty("原班次id")
    private Integer oldShiftDefId;
    @ApiModelProperty("新班次id")
    private Integer newShiftDefId;
    @ApiModelProperty("原班次")
    private String oldShift;
    @ApiModelProperty("调整后班次")
    private String newShift;
    @ApiModelProperty("调整事由")
    private String reason;
    @ApiModelProperty("审核状态 0暂存、1审批中、2已通过、3已拒绝、4已作废、5已退回、8撤销中、9已撤销")
    private Integer status;
    @ApiModelProperty("文件id,多个用逗号隔开")
    private String fileId;
    @ApiModelProperty("撤销原因")
    private String revokeReason;
    @ApiModelProperty("文件名称,多个用逗号隔开")
    private String fileName;
    @ApiModelProperty("删除标识 0:未删除 1:已删除")
    private Integer deleted;
    @ApiModelProperty("创建人")
    private Long createBy;
    @ApiModelProperty("创建时间")
    private Long createTime;
    @ApiModelProperty("最后修改人")
    private Long updateBy;
    @ApiModelProperty("最后更新时间")

    //其他字段
    private Long updateTime;
    @ApiModelProperty("工号")
    private String workNo;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("组织名称")
    private String orgName;
    @ApiModelProperty("部门全称")
    private String fullPath;
    @ApiModelProperty("审批状态文本")
    private String statusName;
    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;
    @ApiModelProperty("审批类型 1:请假,2:加班,41:补打卡,45:出差,103:调班")
    private Integer funcType;
    @ApiModelProperty("业务主键")
    private String businessKey;

    private String oldI18nShiftDefName;
    private String newI18nShiftDefName;
}
