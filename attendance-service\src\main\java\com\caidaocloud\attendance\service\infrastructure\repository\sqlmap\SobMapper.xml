<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.SobMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.SobPo">
        <id column="wa_sob_id" jdbcType="INTEGER" property="waSobId"/>
        <result column="corpid" jdbcType="BIGINT" property="corpid"/>
        <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId"/>
        <result column="wa_sob_name" jdbcType="VARCHAR" property="waSobName"/>
        <result column="sys_period_month" jdbcType="INTEGER" property="sysPeriodMonth"/>
        <result column="wa_group_id" jdbcType="INTEGER" property="waGroupId"/>
        <result column="start_date" jdbcType="BIGINT" property="startDate"/>
        <result column="end_date" jdbcType="BIGINT" property="endDate"/>
        <result column="sob_end_date" jdbcType="BIGINT" property="sobEndDate"/>
        <result column="sob_close_date" jdbcType="BIGINT" property="sobCloseDate"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="crtuser" jdbcType="BIGINT" property="crtuser"/>
        <result column="crttime" jdbcType="BIGINT" property="crttime"/>
        <result column="upduser" jdbcType="BIGINT" property="upduser"/>
        <result column="updtime" jdbcType="BIGINT" property="updtime"/>
        <result column="is_lock" jdbcType="BIT" property="isLock"/>
    </resultMap>
    <select id="queryList" resultMap="BaseResultMap">
        select *
        from wa_sob
        where belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and wa_sob_name = #{sobName}
        <if test="sobId != null">
            and wa_sob_id <![CDATA[<>]]> #{sobId}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="getEmpIdListByGroup" resultType="java.lang.Long">
        <choose>
            <when test="isDefault">
                <![CDATA[
                    select empid
                    from sys_emp_info emp
                    where not exists(
                            select g.empid
                            from wa_emp_group g
                                     INNER JOIN sys_emp_info e on g.empid = e.empid
                            where e.belong_org_id=#{belongid}
                                    and g.start_time <=  #{endDate} and g.end_time >= #{startDate}
                                    and g.empid = emp.empid and g.wa_group_id <> #{waGroupId}
                                    )
                    and emp.belong_org_id=#{belongid} and emp.deleted = 0
                    and ((emp.stats <> 1 and #{currentDate} >= coalesce(emp.internship_date, emp.hire_date)) or (emp.stats = 1 and #{startDate} <= emp.termination_date))
                   ${datafilter}
                ]]>
            </when>
            <otherwise>
                <![CDATA[
                    select g.empid
                    from wa_emp_group g
                             INNER JOIN sys_emp_info emp on g.empid = emp.empid
                    where wa_group_id = #{waGroupId}
                      and emp.belong_org_id=#{belongid}
                      and g.start_time <= #{endDate}
                      and g.end_time >= #{startDate}
                      and ((emp.stats <> 1 and #{currentDate} >= coalesce(emp.internship_date, emp.hire_date)) or (emp.stats = 1 and #{startDate} <= emp.termination_date))
                      ${datafilter}
                ]]>
            </otherwise>
        </choose>
    </select>

    <select id="querySobByGroupIdAndPeriod" resultMap="BaseResultMap">
        SELECT * FROM wa_sob
        WHERE belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        AND wa_group_id = #{groupId}
        AND sys_period_month = #{period}
    </select>

    <select id="queryWaSobIdByDateRangeAndPeriodMonth" resultMap="BaseResultMap" parameterType="map">
      select *
      from wa_sob 
      where status=0
      <if test="belongOrgId != null">
          and belong_org_id=#{belongOrgId,jdbcType=VARCHAR}
      </if>
      <if test="dateTime != null">
          and #{dateTime} between start_date and end_date
      </if>
      <if test="groupId!=null">
        and wa_group_id = #{groupId}
      </if>
      <if test="periodMonths != null and periodMonths.size() > 0">
          and sys_period_month in 
          <foreach collection="periodMonths" item="periodMonth" open="(" close=")" separator=",">
            #{periodMonth}
          </foreach>
      </if>
    </select>

    <select id="queryWaSobByEndDateAndTenantId" resultMap="BaseResultMap">
        select *
        from wa_sob
        where belong_org_id = #{tenantId,jdbcType=VARCHAR}
        <if test="startDate!=null and endDate!=null">
            and end_date between #{startDate} and #{endDate}
        </if>
    </select>
</mapper>