package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.ioc.util.ListHelper;
import com.caidaocloud.attendance.service.application.enums.MaternityLeaveTypeEnum;
import com.caidaocloud.attendance.service.application.service.IGroupConditionService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/attendance/empGroup/v1")
public class EmpGroupController {

    @Autowired
    private IGroupConditionService groupConditionService;

    @ApiOperation(value = "分组条件 下拉框数据")
    @GetMapping("/selectCondList")
    public Result<Map> selectCondList(@RequestParam(value = "type", required = false) Integer type) {
        List<Map> list = new ArrayList<Map>() {{
            /*add(new HashMap() {{
                put("text", "职位族");
                put("value", "job_family");
            }});
            add(new HashMap() {{
                put("text", "职位类");
                put("value", "job_class");
            }});
            add(new HashMap() {{
                put("text", "职位");
                put("value", "job_id");
            }});*/
            add(new HashMap() {{
                put("text", "专业职级");
                put("value", "job_grade");
            }});
            add(new HashMap() {{
                put("text", "账号");
                put("value", "workno");
            }});
            add(new HashMap() {{
                put("text", "合同公司");
                put("value", "company_contract_id");
            }});
            add(new HashMap() {{
                put("text", "任职组织");
                put("value", "orgid");
            }});
            add(new HashMap() {{
                put("text", "任职组织（包含下级）");
                put("value", "subOrg");
            }});
            add(new HashMap() {{
                put("text", "司龄");
                put("value", "corp_age");
            }});
            add(new HashMap() {{
                put("text", "工龄");
                put("value", "job_age");
            }});
            add(new HashMap() {{
                put("text", "司龄（至年底）");
                put("value", "corp_year_end_age");
            }});
            add(new HashMap() {{
                put("text", "工作地");
                put("value", "workplace");
            }});
            add(new HashMap() {{
                put("text", "员工类型");
                put("value", "employ_type");
            }});
            add(new HashMap() {{
                put("text", "员工状态");
                put("value", "stats");
            }});
            add(new HashMap() {{
                put("text", "社保缴纳地");
                put("value", "social_city");
            }});
            add(new HashMap() {{
                put("text", "婚姻状态");
                put("value", "marriage");
            }});
            add(new HashMap() {{
                put("text", "子女年龄");
                put("value", "parental_leave");
            }});
            add(new HashMap() {{
                put("text", "探亲事由");
                put("value", "visiting_reason");
            }});
            add(new HashMap() {{
                put("text", "入职日期");
                put("value", "hire_date");
            }});
        }};
        list.addAll(groupConditionService.getQuotaRuleCondition());
        Map<String, List<Map<String, Object>>> map = ListHelper.convertMapList(list, new ListHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("value");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("text");
            }
        }, false);
        Map map1 = new HashMap();
        map1.put("items", map.get("options"));
        return ResponseWrap.wrapResult(map1);
    }

    @ApiImplicitParam(name = "type", value = "下拉类型,type=1自动销假，type=2产假", required = false)
    @ApiOperation(value = "分组条件 下拉框数据")
    @GetMapping("/selectRuleCondList")
    public Result<Map> selectRuleCondList(@RequestParam(value = "type", required = false) Integer type) {
        List<Map> list = new ArrayList<Map>() {{
            add(new HashMap() {{
                put("text", "休假时长");
                put("value", "total_time_duration");
            }});
            add(new HashMap() {{
                put("text", "任职组织");
                put("value", "orgid");
            }});
            add(new HashMap() {{
                put("text", "任职组织（包含下级）");
                put("value", "subOrg");
            }});
            add(new HashMap() {{
                put("text", "员工类型");
                put("value", "employ_type");
            }});
            add(new HashMap() {{
                put("text", "员工状态");
                put("value", "stats");
            }});
            add(new HashMap() {{
                put("text", "入职日期");
                put("value", "hire_date");
            }});
        }};
        Map<String, List<Map<String, Object>>> map = ListHelper.convertMapList(list, new ListHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("value");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("text");
            }
        }, false);
        Map map1 = new HashMap();
        if (type == 2) {
            Result<Map> result = selectCondList(type);
            List<Map<String, Object>> items = (List<Map<String, Object>>) result.getData().get("items");
            Map<String, Object> ml1 = new HashMap<>();
            ml1.put("text", "产假类型");
            ml1.put("value", "maternity_type");
            items.add(ml1);
            Map<String, Object> ml2 = new HashMap<>();
            ml2.put("text", "子女个数");
            ml2.put("value", "child_num");
            items.add(ml2);
        } else {
            map1.put("items", map.get("options"));
        }
        return ResponseWrap.wrapResult(map1);
    }

    /**
     * 下拉产假类型：1、顺产，2、难产
     */
    @ApiOperation(value = "产假类型")
    @GetMapping("/getMaternityLeaveType")
    public Result<List<KeyValue>> getMaternityLeaveType() {
        List<KeyValue> list = new ArrayList<>();
        for (MaternityLeaveTypeEnum value : MaternityLeaveTypeEnum.values()) {
            list.add(new KeyValue(MaternityLeaveTypeEnum.getNameByIndex(value.getIndex()), value.getIndex()));
        }
        return Result.ok(list);
    }

    @ApiOperation("获取假期额度生成匹配条件")
    @GetMapping("/condition/quota")
    public Result<List<ConditionDataVo>> quotaCondition() {
        return Result.ok(groupConditionService.quotaCondition());
    }

    @ApiOperation("获取考勤方案匹配条件")
    @GetMapping("/condition/group")
    public Result<List<ConditionDataVo>> empGroupCondition() {
        return Result.ok(groupConditionService.empGroupCondition());
    }
}

