package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class HolidayGroupVo implements Serializable {

    @ApiModelProperty("分组id")
    private Integer calendarGroupId;
    @ApiModelProperty("分组名")
    private String groupName;
    @ApiModelProperty("特殊日期")
    private List<HolidayGroupEntryPageVo> holidayCalendars;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nGroupName;
}
