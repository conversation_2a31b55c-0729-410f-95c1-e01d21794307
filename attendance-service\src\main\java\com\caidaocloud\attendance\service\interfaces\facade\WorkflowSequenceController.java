package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowSequenceService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/attendance/workflowSequence/v1")
@Api(value = "/api/attendance/workflowSequence/v1", description = "流程条件序列流注册数据约束", tags = "v1.0")
public class WorkflowSequenceController {

    @Autowired
    private WorkflowSequenceService workflowSequenceService;

    /**
     * 请假回调
     */
    @GetMapping("/leave")
    @ApiOperation(value = "休假回调接口")
    public Result<String> leaveCallback(@RequestParam(value = "businessId") String businessId, @RequestParam(value = "code") String code) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code)) {
            log.error("businessId:{}, code:{}", businessId, code);
            return Result.fail("参数为空");
        }
        try {
            String result = workflowSequenceService.getLeaveWorkflowSequenceDateConstraint(businessId, code);
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call back leave error: {}", e.getMessage(), e);
            return Result.fail("请假回调失败！");
        }
    }

    /**
     * 批量休假回调
     */
    @GetMapping("/batchLeave")
    @ApiOperation(value = "批量休假回调")
    public Result<String> batchLeaveCallback(@RequestParam(value = "businessId") String businessId,
                                             @RequestParam(value = "code") String code,
                                             @RequestParam(value = "funcCode") String funcCode) {
        log.info("batchLeaveCallback businessId={}, code={}", businessId, code);
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code)) {
            log.error("businessId:{}, code:{}", businessId, code);
            return Result.fail("参数为空");
        }
        try {
            String result;
            if (BusinessCodeEnum.BATCH_LEAVE.getCode().equals(funcCode)) {
                result = workflowSequenceService.getBatchLeaveWorkflowSequenceDateConstraint(businessId, code);
            } else {
                result = workflowSequenceService.getBatchLeaveRevokeWorkflowSequenceDateConstraint(businessId, code);
            }
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call batch leave error:{}", e.getMessage(), e);
            return Result.fail("批量休假流程条件回调失败！");
        }
    }

    /**
     * 加班回调
     */
    @GetMapping("/overtime")
    @ApiOperation(value = "加班回调接口")
    public Result<String> overTimeCallback(@RequestParam(value = "businessId") String businessId,
                                           @RequestParam(value = "code") String code,
                                           @RequestParam(value = "funcCode") String funcCode) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code) || StringUtil.isBlank(funcCode)) {
            log.error("businessId:{}, code:{}, funcCode:{}", businessId, code, funcCode);
            return Result.fail("参数为空");
        }
        try {
            String result = null;
            if (BusinessCodeEnum.OVERTIME.getCode().equals(funcCode)) {
                result = workflowSequenceService.getOvertimeWorkflowSequenceDateConstraint(businessId, code);
            } else {
                result = workflowSequenceService.getOvertimeRevokeWorkflowSequenceDateConstraint(businessId, code);
            }
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call back overtime error:{}", e.getMessage(), e);
            return Result.fail("加班回调失败！");
        }
    }

    /**
     * 批量加班回调
     */
    @GetMapping("/batchOvertime")
    @ApiOperation(value = "批量加班回调接口")
    public Result<String> batchOvertimeCallback(@RequestParam(value = "businessId") String businessId,
                                                @RequestParam(value = "code") String code,
                                                @RequestParam(value = "funcCode") String funcCode) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code) || StringUtil.isBlank(funcCode)) {
            log.error("businessId:{}, code:{}, funcCode:{}", businessId, code, funcCode);
            return Result.fail("参数为空");
        }
        try {
            String result = workflowSequenceService.getBatchOvertimeWorkflowSequenceDateConstraint(businessId, code);
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call back batch overtime error:{}", e.getMessage(), e);
            return Result.fail("批量加班回调失败！");
        }
    }

    /**
     * 出差回调
     */
    @GetMapping("/travel")
    @ApiOperation(value = "出差回调接口")
    public Result<String> travelCallback(@RequestParam(value = "businessId") String businessId,
                                         @RequestParam(value = "code") String code,
                                         @RequestParam(value = "funcCode") String funcCode) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code) || StringUtil.isBlank(funcCode)) {
            log.error("businessId:{}, code:{}, funcCode:{}", businessId, code, funcCode);
            return Result.fail("参数为空");
        }
        try {
            String result = null;
            if (BusinessCodeEnum.TRAVEL.getCode().equals(funcCode)) {
                result = workflowSequenceService.getTravelWorkflowSequenceDateConstraint(businessId, code);
            } else {
                result = workflowSequenceService.getTravelRevokeWorkflowSequenceDateConstraint(businessId, code);
            }
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call travel travel error:{}", e.getMessage(), e);
            return Result.fail("出差序列流回调失败！");
        }
    }

    /**
     * 批量出差回调
     */
    @GetMapping("/batchTravel")
    @ApiOperation(value = "批量出差回调")
    public Result<String> batchTravelCallback(@RequestParam(value = "businessId") String businessId,
                                              @RequestParam(value = "code") String code,
                                              @RequestParam(value = "funcCode") String funcCode) {
        log.info("batchTravelCallback businessId={}, code={}", businessId, code);
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code)) {
            log.error("businessId:{}, code:{}", businessId, code);
            return Result.fail("参数为空");
        }
        try {
            String result;
            if (BusinessCodeEnum.BATCH_TRAVEL.getCode().equals(funcCode)) {
                result = workflowSequenceService.getBatchTravelWorkflowSequenceDateConstraint(businessId, code);
            } else {
                result = workflowSequenceService.getBatchTravelRevokeWorkflowSequenceDateConstraint(businessId, code);
            }
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call batch travel error:{}", e.getMessage(), e);
            return Result.fail("批量出差回调失败！");
        }
    }

    /**
     * 销假回调
     */
    @GetMapping("/leaveCancel")
    @ApiOperation(value = "销假回调接口")
    public Result<String> leaveCancel(@RequestParam(value = "businessId") String businessId, @RequestParam(value = "code") String code) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code)) {
            log.error("businessId:{}, code:{}", businessId, code);
            return Result.fail("参数为空");
        }
        try {
            String result = workflowSequenceService.getLeaveCancelWorkflowSequenceDateConstraint(businessId, code);
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call leave cancel error:{}", e.getMessage(), e);
            return Result.fail("销假回调失败！");
        }
    }

    /**
     * 调休付现
     */
    @GetMapping("/compensatory")
    @ApiOperation(value = "调休付现回调接口")
    public Result<String> compensatoryCallback(@RequestParam(value = "businessId") String businessId, @RequestParam(value = "code") String code) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code)) {
            log.error("businessId:{}, code:{}", businessId, code);
            return Result.fail("参数为空");
        }
        try {
            String result = workflowSequenceService.getCompensatoryWorkflowSequenceDateConstraint(businessId, code);
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call back compensatory error: {}", e.getMessage());
            return Result.fail("调休付现回调失败！");
        }
    }

    @GetMapping("/leaveExtension")
    @ApiOperation(value = "假期延期回调接口")
    public Result<String> leaveExtensionCallback(@RequestParam(value = "businessId") String businessId, @RequestParam(value = "code") String code) {
        if (StringUtil.isBlank(businessId) || StringUtil.isBlank(code)) {
            log.error("businessId:{}, code:{}", businessId, code);
            return Result.fail("参数为空");
        }
        try {
            String result = workflowSequenceService.getLeaveExtensionWorkflowSequenceDateConstraint(businessId, code);
            if (null == result) {
                return Result.fail("数据不存在");
            }
            return Result.ok(result);
        } catch (Exception e) {
            log.error("workflow sequence call back leaveExtensionCallback error: {}", e.getMessage(), e);
            return Result.fail("假期延期回调失败！");
        }
    }
}
