package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value="wa_quota_gen_rule")
public class WaQuotaGenRulePo {
    private Long quotaRuleId;

    private String tenantId;

    private Integer leaveTypeId;

    private Long configId;

    private String conditionExp;

    private String conditionNote;

    private Integer quotaVal;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;
}