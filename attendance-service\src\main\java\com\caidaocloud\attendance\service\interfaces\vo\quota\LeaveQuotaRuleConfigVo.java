package com.caidaocloud.attendance.service.interfaces.vo.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeaveQuotaRuleConfigVo {
    @ApiModelProperty("余额规则配置主键")
    private Long configId;
    @ApiModelProperty("额度规则名称")
    private String ruleName;
    @ApiModelProperty("额度规则有效期生效日期")
    private Long ruleStartDate;
    @ApiModelProperty("额度规则有效期失效日期")
    private Long ruleEndDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    @ApiModelProperty("有效期类型：1 限制有效期 、2 不限制有效期")
    private Integer validityPeriodType;

    private Integer sort;

    private String status;

}