package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "caidao", name = "priorCal", havingValue = "priorServiceLen")
public class HrPriorCalSubscribe {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = "caidaocloud.attendance.priorCal.queue", durable = "true"), exchange = @Exchange(value = "caidaocloud.hr.priorCal.exchange", type = ExchangeTypes.FANOUT)))
    public void handle(String msg) {
        if (StringUtils.isBlank(msg)) {
            return;
        }
        PriorCalEventDto event = FastjsonUtil.toObject(msg, new TypeReference<PriorCalEventDto>() {
        });
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(event.getTenantId());
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            String sql = buildSql(event);
            if (StringUtils.isBlank(sql)) {
                return;
            }
            transactionTemplate.execute(transactionStatus -> {
                try {
                    jdbcTemplate.execute(sql);
                    return true;
                } catch (Exception e) {
                    log.error("Transaction commit failed", e);
                    transactionStatus.setRollbackOnly();
                    throw new ServerException();
                }
            });
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    private String buildSql(PriorCalEventDto event) {
        if (CollectionUtils.isEmpty(event.getResultMap()) || StringUtils.isBlank(event.getTenantId())) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        event.getResultMap().forEach((k, v) -> {
            stringBuilder.append(String.format("update sys_emp_info set work_age=%s where belong_org_id='%s' and empid=%s;\n",
                    Float.valueOf(StringUtils.defaultString(v, "0")), event.getTenantId(), k)
            );
        });
        return stringBuilder.toString();
    }
}