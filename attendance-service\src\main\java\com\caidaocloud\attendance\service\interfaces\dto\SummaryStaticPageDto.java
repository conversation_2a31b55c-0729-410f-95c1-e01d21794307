package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.application.enums.TimeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 查询考勤汇总
 * created by: FoAng
 * create time: 22/3/2024 4:53 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SummaryStaticPageDto extends AttendanceBasePage implements Serializable {

    @ApiModelProperty("选择周期类型")
    private TimeTypeEnum timeType;

    @ApiModelProperty("筛选部门")
    private String[] orgIds;

    @ApiModelProperty("选择开始日期")
    private Long startDate;

    @ApiModelProperty("选择结束日期")
    private Long endDate;
}
