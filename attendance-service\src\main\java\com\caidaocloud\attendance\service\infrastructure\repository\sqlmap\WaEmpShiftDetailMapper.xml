<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpShiftDetailMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftDetailPo">
    <id column="detail_id" jdbcType="BIGINT" property="detailId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="work_date" jdbcType="BIGINT" property="workDate" />
    <result column="shift_id" jdbcType="INTEGER" property="shiftId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    detail_id, tenant_id, emp_id, work_date, shift_id, deleted, create_by, create_time, 
    update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_emp_shift_detail
    where detail_id = #{detailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_emp_shift_detail
    where detail_id = #{detailId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftDetailPo">
    insert into wa_emp_shift_detail (detail_id, tenant_id, emp_id, 
      work_date, shift_id, deleted, 
      create_by, create_time, update_by, 
      update_time)
    values (#{detailId,jdbcType=BIGINT}, #{tenantId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, 
      #{workDate,jdbcType=BIGINT}, #{shiftId,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftDetailPo">
    insert into wa_emp_shift_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="workDate != null">
        work_date,
      </if>
      <if test="shiftId != null">
        shift_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="detailId != null">
        #{detailId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="workDate != null">
        #{workDate,jdbcType=BIGINT},
      </if>
      <if test="shiftId != null">
        #{shiftId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftDetailPo">
    update wa_emp_shift_detail
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="workDate != null">
        work_date = #{workDate,jdbcType=BIGINT},
      </if>
      <if test="shiftId != null">
        shift_id = #{shiftId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpShiftDetailPo">
    update wa_emp_shift_detail
    set tenant_id = #{tenantId,jdbcType=INTEGER},
      emp_id = #{empId,jdbcType=INTEGER},
      work_date = #{workDate,jdbcType=BIGINT},
      shift_id = #{shiftId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>

  <select id="queryByParams" resultMap="BaseResultMap">
    select * from wa_emp_shift_detail
    where tenant_id = #{tenantId,jdbcType=VARCHAR} and deleted = 0
    and work_date between #{startDate} and #{endDate}
    <if test="empIds != null and empIds.size() > 0">
      and emp_id in
      <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
        #{empId}
      </foreach>
    </if>
  </select>

  <insert id="saveEmpShiftDetails">
    <if test="details != null and details.size() > 0">
      <foreach collection="details" item="detail" separator=";">
        insert into wa_emp_shift_detail (detail_id, tenant_id, emp_id, work_date, shift_id, create_by, create_time, update_by, update_time)
        values (#{detail.detailId}, #{detail.tenantId}, #{detail.empId}, #{detail.workDate}, #{detail.shiftId},
        #{detail.createBy}, #{detail.createTime}, #{detail.updateBy}, #{detail.updateTime})
      </foreach>
    </if>
  </insert>

  <update id="updateEmpShiftDetails">
    <if test="details != null and details.size() > 0">
      <foreach collection="details" item="detail" separator=";">
        update wa_emp_shift_detail
        set tenant_id = #{detail.tenantId}, emp_id = #{detail.empId}, work_date = #{detail.workDate}, shift_id = #{detail.shiftId},
        update_by = #{detail.updateBy}, update_time = #{detail.updateTime}
        where detail_id = #{detail.detailId}
      </foreach>
    </if>
  </update>
</mapper>