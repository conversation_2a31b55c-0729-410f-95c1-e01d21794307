package com.caidaocloud.attendance.service.interfaces.vo.leave;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请假日期信息VO
 *
 * <AUTHOR>
 * @Date 2024/6/17
 */
@Data
@ApiModel("请假日期信息VO")
public class LeaveDateInfoVo {
    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
    @ApiModelProperty("假期类型名称")
    private String leaveTypeName;
    @ApiModelProperty("开始日期")
    private Long startDate;
    @ApiModelProperty("结束日期")
    private Long endDate;
    @ApiModelProperty("请假时长")
    private Double duration;
    @ApiModelProperty("时长单位 1 天 2 小时")
    private Integer unit;
    @ApiModelProperty("时长单位名称")
    private String unitTxt;
}
