package com.caidaocloud.attendance.service.interfaces.dto.integrate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


@Data
public class SysDataInputDto implements Serializable {
    private static final long serialVersionUID = -3838501168645270157L;

    @ApiModelProperty("数据流入ID")
    private Integer sysDataInputId;
    @ApiModelProperty("数据源类型")
    private String sourceType;
    @ApiModelProperty("数据源配置")
    private Object sourceConfig;
    @ApiModelProperty("数据源表达式")
    private String sourceExp;
    @ApiModelProperty("输出类型")
    private String targetType;
    @ApiModelProperty("输出列")
    private String targetCol;
    @ApiModelProperty("输出表达式")
    private String targetExp;
    @ApiModelProperty("定时器")
    private String trigger;
    @ApiModelProperty("后置触发")
    private String afterTrigger;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("最后执行时间")
    private Long workTime;
    @ApiModelProperty("用工单位")
    private Integer belongOrgId;
    @ApiModelProperty("创建人")
    private Integer crtuser;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("修改人")
    private Integer upduser;
    @ApiModelProperty("修改时间")
    private Long updtime;
    @ApiModelProperty("说明")
    private String note;
    @ApiModelProperty("前置触发")
    private String beforeTrigger;
    @ApiModelProperty("成功判断")
    private String sourceGreen;
    @ApiModelProperty("回调类型")
    private String callbackType;
    @ApiModelProperty("回调表达式")
    private String callbackExp;
    @ApiModelProperty("是否记录源数据")
    private Boolean sourceSave;
    @ApiModelProperty("异常后置回调")
    private String errorTrigger;
    @ApiModelProperty("最终校验表达式")
    private String finalCheckExp;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nNote;

    public void initNote() {
        if (StringUtils.isNotBlank(this.note)) {
            return;
        }
        if (null == this.i18nNote || this.i18nNote.isEmpty() || null == this.i18nNote.get("default")) {
            return;
        }
        this.setNote(this.i18nNote.get("default"));
    }

    public void initI18nNote() {
        if (null != this.i18nNote && !this.i18nNote.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.note)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.note);
        this.setI18nNote(i18nName);
    }
}
