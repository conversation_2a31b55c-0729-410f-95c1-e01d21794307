package com.caidaocloud.attendance.service.application.enums;

/**
 * 外出规则：类型定义
 */
public enum TravelTypeDefEnum {
    HOME(1, "国内出差"),
    ABROAD(2, "国外出差");

    private Integer index;

    private String name;

    TravelTypeDefEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (TravelTypeDefEnum c : TravelTypeDefEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
