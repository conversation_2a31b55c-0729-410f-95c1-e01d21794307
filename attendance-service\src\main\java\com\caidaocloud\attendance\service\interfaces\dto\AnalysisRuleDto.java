package com.caidaocloud.attendance.service.interfaces.dto;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.service.application.dto.WaCustomParseRuleDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("考勤方案设置-保存出勤规则参数DTO")
public class AnalysisRuleDto implements Serializable {
    private Integer parseGroupId;

    private String parseGroupName;

    @ApiModelProperty("迟到豁免时长")
    private BigDecimal lateCount;

    @ApiModelProperty("迟到豁免时长单位：1 小时，2 分钟")
    private Integer lateUnit;

    @ApiModelProperty("早退豁免时长")
    private BigDecimal earlyCount;

    @ApiModelProperty("早退豁免时长单位：1 小时，2 分钟")
    private Integer earlyUnit;

    @ApiModelProperty("允许迟到次数")
    private Integer lateAllowNumber;

    @ApiModelProperty("迟到豁免类型：1 按照次数，2 按照分钟")
    private Integer lateAllowUnit;

    @ApiModelProperty("允许早退次数")
    private Integer earlyAllowNumber;

    @ApiModelProperty("早退豁免类型：1 按照次数，2 按照分钟")
    private Integer earlyAllowUnit;

    private Integer parseType;

    @ApiModelProperty("旷工判断")
    private BigDecimal absentLimit;

    private Boolean otParse;
    private Boolean lvParse;
    private Integer registerMiss;
    private Boolean ignoreLocationExp;
    private Boolean isAnalyzeLateEarly;

    @ApiModelProperty("异常类型：1早退加迟到，2迟到或早退")
    private Integer abnormalType;
    // 数据格式： [{"end": "6", "start": "5", "duration": "7", "durationUnit": "1"}, {"end": 2, "start": 1, "duration": 0, "durationUnit": "1"}] start/end/duration 最大值设置为9999
    @ApiModelProperty("旷工逻辑规则")
    private Object absentConditionJsonb;

    @ApiModelProperty("开启自定义分析规则开关")
    private Boolean openCustomPaseRule;

    @ApiModelProperty("自定义迟到分析规则")
    private WaCustomParseRuleDto customLatePaseRule;

    @ApiModelProperty("自定义早退分析规则")
    private WaCustomParseRuleDto customEarlyPaseRule;

    @ApiModelProperty("自定义旷工分析规则")
    private WaCustomParseRuleDto customAbsentPaseRule;

    private Boolean isOuterSign;
    private Object otPaseJsonb;
    private Boolean isFlexibleWorking;
    private Boolean otSumParse;
    private Integer clockType;
    private Object clockRuleDto;
    private Integer waGroupId;
    @ApiModelProperty("弹性分析开关 1:关闭 2:开启")
    private Integer flexibleWorkSwitch;
    @ApiModelProperty("弹性分析开关 1:按弹性区间分析 2:按班次分析")
    private Integer flexibleWorkType;

    @ApiModelProperty("外勤分析规则：1 出差单 2 外勤打卡 3 出差单联动外勤打卡")
    private Integer outParseRule;
    @ApiModelProperty("允许打卡日期类型：1(工作日),2(休息日),3(法定假日),4(特殊休日),多个类型逗号分隔")
    private String allowedDateType;
    @ApiModelProperty("外勤联动班次打卡")
    private Boolean fieldClockLinkShift;

    private Integer minLateTime;
    private Short minLateTimeUnit;
    private Integer minEarlyTime;
    private Short minEarlyTimeUnit;
    private Boolean leaveExemptionSwitch;

    @ApiModelProperty("统计转换类型：1、按阶梯统计，2、按比例统计，默认值1")
    private Integer statisticType;
    @ApiModelProperty("迟到早退转旷工按比例转换时长，默认1:1转换")
    private Integer convertTime;
    @ApiModelProperty("迟到早退转旷工按比例转换比例，默认1:1转换")
    private Integer convertScale;
    @ApiModelProperty("迟到早退转旷工上限，超过上限转旷工，默认1:1转换")
    private Integer convertTimeLimit;
    @ApiModelProperty("迟到早退按比例转换迟到早退，默认1:1转换")
    private Integer expConvertScale;
    @ApiModelProperty("迟到早退按比例转换旷工比例(迟到早退值)，默认1:1转换")
    private Integer convertKgScale;
    @ApiModelProperty("是否豁免迟到早退休假类型，1、正常上/下班享有，2、休半天假是享有，3、休小时假时享有")
    private String leaveExemptionType;

    public Integer getWaGroupId() {
        return waGroupId;
    }

    public void setWaGroupId(Integer waGroupId) {
        this.waGroupId = waGroupId;
    }

    public Integer getClockType() {
        return clockType;
    }

    public void setClockType(Integer clockType) {
        this.clockType = clockType;
    }

    public Object getClockRuleDto() {
        return clockRuleDto;
    }

    public void setClockRuleDto(Object clockRuleDto) {
        this.clockRuleDto = clockRuleDto;
    }

    public Integer getParseGroupId() {
        return parseGroupId;
    }

    public void setParseGroupId(Integer parseGroupId) {
        this.parseGroupId = parseGroupId;
    }

    public String getParseGroupName() {
        return parseGroupName;
    }

    public void setParseGroupName(String parseGroupName) {
        this.parseGroupName = parseGroupName;
    }

    public BigDecimal getLateCount() {
        return lateCount;
    }

    public void setLateCount(BigDecimal lateCount) {
        this.lateCount = lateCount;
    }

    public Integer getLateUnit() {
        return lateUnit;
    }

    public void setLateUnit(Integer lateUnit) {
        this.lateUnit = lateUnit;
    }

    public BigDecimal getEarlyCount() {
        return earlyCount;
    }

    public void setEarlyCount(BigDecimal earlyCount) {
        this.earlyCount = earlyCount;
    }

    public Integer getEarlyUnit() {
        return earlyUnit;
    }

    public void setEarlyUnit(Integer earlyUnit) {
        this.earlyUnit = earlyUnit;
    }

    public Integer getParseType() {
        return parseType;
    }

    public void setParseType(Integer parseType) {
        this.parseType = parseType;
    }

    public BigDecimal getAbsentLimit() {
        return absentLimit;
    }

    public void setAbsentLimit(BigDecimal absentLimit) {
        this.absentLimit = absentLimit;
    }

    public Boolean getOtParse() {
        return otParse;
    }

    public void setOtParse(Boolean otParse) {
        this.otParse = otParse;
    }

    public Boolean getLvParse() {
        return lvParse;
    }

    public void setLvParse(Boolean lvParse) {
        this.lvParse = lvParse;
    }

    public Integer getRegisterMiss() {
        return registerMiss;
    }

    public void setRegisterMiss(Integer registerMiss) {
        this.registerMiss = registerMiss;
    }

    public Integer getLateAllowNumber() {
        return lateAllowNumber;
    }

    public void setLateAllowNumber(Integer lateAllowNumber) {
        this.lateAllowNumber = lateAllowNumber;
    }

    public Integer getLateAllowUnit() {
        return lateAllowUnit;
    }

    public void setLateAllowUnit(Integer lateAllowUnit) {
        this.lateAllowUnit = lateAllowUnit;
    }

    public Integer getEarlyAllowNumber() {
        return earlyAllowNumber;
    }

    public void setEarlyAllowNumber(Integer earlyAllowNumber) {
        this.earlyAllowNumber = earlyAllowNumber;
    }

    public Integer getEarlyAllowUnit() {
        return earlyAllowUnit;
    }

    public void setEarlyAllowUnit(Integer earlyAllowUnit) {
        this.earlyAllowUnit = earlyAllowUnit;
    }

    public Boolean getIgnoreLocationExp() {
        return ignoreLocationExp;
    }

    public void setIgnoreLocationExp(Boolean ignoreLocationExp) {
        this.ignoreLocationExp = ignoreLocationExp;
    }

    public Boolean getIsAnalyzeLateEarly() {
        return isAnalyzeLateEarly;
    }

    public void setIsAnalyzeLateEarly(Boolean analyzeLateEarly) {
        isAnalyzeLateEarly = analyzeLateEarly;
    }

    public Boolean getIsOuterSign() {
        return isOuterSign;
    }

    public void setIsOuterSign(Boolean outerSign) {
        isOuterSign = outerSign;
    }

    public Boolean getOuterSign() {
        return isOuterSign;
    }

    public void setOuterSign(Boolean outerSign) {
        isOuterSign = outerSign;
    }

    public Boolean getFlexibleWorking() {
        return isFlexibleWorking;
    }

    public void setFlexibleWorking(Boolean flexibleWorking) {
        isFlexibleWorking = flexibleWorking;
    }

    public Boolean getIsFlexibleWorking() {
        return isFlexibleWorking;
    }

    public void setIsFlexibleWorking(Boolean flexibleWorking) {
        isFlexibleWorking = flexibleWorking;
    }

    public Boolean getOtSumParse() {
        return otSumParse;
    }

    public void setOtSumParse(Boolean otSumParse) {
        this.otSumParse = otSumParse;
    }

    public Object getAbsentConditionJsonb() {
        return absentConditionJsonb != null ? JSON.toJSONString(absentConditionJsonb) : null;
    }

    public void setAbsentConditionJsonb(Object absentConditionJsonb) {
        this.absentConditionJsonb = absentConditionJsonb;
    }

    public Object getOtPaseJsonb() {
        return otPaseJsonb != null ? JSON.toJSONString(otPaseJsonb) : null;
    }

    public void setOtPaseJsonb(Object otPaseJsonb) {
        this.otPaseJsonb = otPaseJsonb;
    }

    public Integer getOutParseRule() {
        return outParseRule;
    }

    public void setOutParseRule(Integer outParseRule) {
        this.outParseRule = outParseRule;
    }
}
