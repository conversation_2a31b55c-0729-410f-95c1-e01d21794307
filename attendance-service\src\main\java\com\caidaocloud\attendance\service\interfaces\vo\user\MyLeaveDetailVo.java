package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MyLeaveDetailVo {
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("司龄")
    private Float corpAge;
    @ApiModelProperty("假期类型 1:年假 2:调休 3:固定配额 ")
    private Integer quotaType;
    @ApiModelProperty("总配额")
    private Float totalDay;
    @ApiModelProperty("已使用")
    private Float usedDay;
    @ApiModelProperty("剩余")
    private Float leftDay;
    @ApiModelProperty("工作地")
    private String workplace;
    @ApiModelProperty("假期单位")
    private Integer unit;
    @ApiModelProperty("假期单位文本")
    private String unitTxt;
    @ApiModelProperty("年假明细")
    private List<MyAnnualLeaveVo> annualList;
    @ApiModelProperty("调休明细")
    private List<MyCompensotaryVo> compenList;
    @ApiModelProperty("额度说明")
    private String description;

    @ApiModelProperty("总当前额度")
    private Float totalCurrentDay;
    @ApiModelProperty("总已使用当前额度")
    private Float totalCurrentUsedDay;
    @ApiModelProperty("总剩余当前额度")
    private Float totalCurrentLeftDay;
    @ApiModelProperty("总的可预支额度")
    private Float prepayAbleDay;
    @ApiModelProperty("总的剩余可预支")
    private Float leftPrepayAbleDay;
    @ApiModelProperty("是否可预支：true/false")
    private Boolean ifAdvance;

    @ApiModelProperty("留存配额")
    private Float retainDay;
    @ApiModelProperty("留存配额已使用")
    private Float retainUsedDay;
    @ApiModelProperty("留存有效期")
    private Long retainValidDate;
}
