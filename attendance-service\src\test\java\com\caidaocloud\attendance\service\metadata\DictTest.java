package com.caidaocloud.attendance.service.metadata;


import com.caidaocloud.attendance.core.annoation.dto.WorkplaceDto;
import com.caidaocloud.attendance.core.annoation.feign.BccServiceFeignClient;
import com.caidaocloud.attendance.core.annoation.feign.HrServiceFeignClient;
import com.caidaocloud.attendance.core.annoation.feign.PassMetadataFeign;
import com.caidaocloud.attendance.service.AttendanceApplication;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class DictTest {
    @Resource
    private BccServiceFeignClient bccServiceFeignClient;
    @Resource
    private PassMetadataFeign passMetadataFeign;

    @Test
    public void getEnum() {
        System.out.println(bccServiceFeignClient.getEnableDictList("EmployType", "Employee"));
    }

    @Before
    public void before() {
        SecurityUserInfo securityUser = new SecurityUserInfo();
        securityUser.setEmpId(0L);
        securityUser.setTenantId("11");
        securityUser.setIsAdmin(true);
        securityUser.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(securityUser);
    }

    @Test
    public void metaDataTest() {
        Result one = passMetadataFeign.one("entity.hr.EmpWorkInfo");
        System.out.println(one);
        Result one1 = passMetadataFeign.one("entity.hr.EmpPrivateInfo");
        System.out.println(one1);
    }

    @Resource
    private HrServiceFeignClient hrServiceFeignClient;

    @Test
    public void workplaceTest() {
        Result<List<WorkplaceDto>> workPlace = hrServiceFeignClient.getWorkPlace();
        System.out.println(workPlace);
    }
}
