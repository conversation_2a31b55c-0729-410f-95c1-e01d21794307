package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("批量考勤异常申请列表查询参数")
public class BatchAnalyseResultAdjustQueryDto extends ExportBasePage {
    @ApiModelProperty("员工id，可选，员工端查询使用")
    private Long empid;
}
