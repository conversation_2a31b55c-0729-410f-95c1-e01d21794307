package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelDto;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchTravelService;
import com.caidaocloud.attendance.service.interfaces.dto.travel.RevokeEmpTraveDto;
import com.caidaocloud.attendance.service.interfaces.vo.EmpTravelVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.MessageFormat;

@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/travel/portal/v1")
@Api(value = "/api/attendance/batch/travel/portal/v1", description = "门户-批量出差")
public class WaBatchTravelPortalController {
    private static final String LOCK_KEY_OF_CHECK = "PORTAL_BATCH_TRAVEL_CHECK_LOCK_{0}_{1}";
    private static final String LONK_KEY_OF_SAVE = "PORTAL_BATCH_TRAVEL_SAVE_LOCK_{0}_{1}";
    @Resource
    private CacheService cacheService;
    @Autowired
    private WaBatchTravelService waBatchTravelService;

    @ApiOperation(value = "获取出差时长")
    @PostMapping(value = "/getTimeDuration")
    public Result getTimeDuration(@RequestBody BatchTravelDto dto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        dto.setEmpId(userInfo.getStaffId());
        String lockKey = MessageFormat.format(LOCK_KEY_OF_CHECK, userInfo.getUserId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return waBatchTravelService.getTimeDuration(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "保存出差申请单")
    @PostMapping(value = "/save")
    public Result save(@RequestBody BatchTravelDto dto) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        dto.setEmpId(userInfo.getStaffId());
        String lockKey = MessageFormat.format(LONK_KEY_OF_SAVE, userInfo.getUserId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return waBatchTravelService.save(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }
    
    @ApiOperation(value = "获取出差记录分页列表")
    @PostMapping("/page")
    public Result<PageResult<EmpTravelVo>> getPageList(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(waBatchTravelService.getPageListOfPortal(queryPageBean));
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revokeEmpTravel(@RequestBody RevokeEmpTraveDto dto) {
        if (StringUtils.isEmpty(dto.getRecokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRecokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            return waBatchTravelService.revokeEmpTravel(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_FAILED, Boolean.FALSE);
        }
    }
}
