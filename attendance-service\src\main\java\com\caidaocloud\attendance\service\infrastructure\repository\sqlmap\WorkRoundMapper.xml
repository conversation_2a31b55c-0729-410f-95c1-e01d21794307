<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkRoundMapper">

    <select id="getWorkRoundList" resultType="com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo">
        SELECT * FROM (
        SELECT
        work_round_id,work_round_id AS "workRoundId", round_name AS "roundName",crttime, i18n_round_name AS "i18nRoundName"
        FROM
        wa_work_round
        WHERE belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        <if test="keywords !=null and keywords != ''">
            and round_name like concat('%', #{keywords}, '%')
        </if>
        )as t
        <where>
            <if test="filter != null and filter != ''">
                ${filter}
            </if>
        </where>
    </select>
</mapper>