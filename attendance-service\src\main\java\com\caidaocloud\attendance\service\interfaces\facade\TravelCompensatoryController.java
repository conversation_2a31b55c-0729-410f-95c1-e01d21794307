package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.service.ITravelCompensatoryService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.TravelCompensatoryItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.TravelCompensatoryReqDto;
import com.caidaocloud.attendance.service.interfaces.vo.TravelCompensatoryVo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/attendance/travelCompensatory/v1")
@Api(value = "/api/attendance/travelCompensatory/v1", description = "出差转调休")
public class TravelCompensatoryController {

    @Autowired
    private ITravelCompensatoryService travelCompensatoryService;
    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("出差转调休列表")
    @PostMapping(value = "/list")
    @Security(code = "TravelCompensatoryRecordList")
    public Result<AttendancePageResult<TravelCompensatoryVo>> getEmpCompensatoryCaseList(@RequestBody TravelCompensatoryReqDto dto, HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.TRAVEL_COMPENSATORY_RECORD_LIST, "b");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("出差转调休列表 DataScope = {}", dataScope);
            PageList<TravelCompensatoryItemDto> pageList = travelCompensatoryService.getTravelCompensatoryList(dto, pageBean, getUserInfo());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<TravelCompensatoryVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(TravelCompensatoryVo.class);
            AttendancePageResult<TravelCompensatoryVo> pageResult = new AttendancePageResult<TravelCompensatoryVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("TravelCompensatoryController.getTravelCompensatoryList has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }
}
