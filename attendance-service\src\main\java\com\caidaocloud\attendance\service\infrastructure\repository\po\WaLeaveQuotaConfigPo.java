package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.attendance.service.application.enums.ApplyQuotaEnum;
import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaConfigDo;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Data
@TableName(value = "wa_leave_quota_config")
public class WaLeaveQuotaConfigPo {
    private Long configId;
    private String tenantId;
    private Integer leaveTypeId;
    private Integer distributionCycle;
    private Long disCycleStart;
    private Long disCycleEnd;
    private Integer validityPeriodType;
    private Float validityDuration;
    private Integer validityUnit;
    private Integer validityStartType;
    private Integer quotaDistributeRule;
    private Integer quotaRoundingRule;
    private Integer nowDistributeRule;
    private Integer nowRoundingRule;
    private Integer ifAdvance;
    private Integer expirationRule;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
    private Long carryOverTo;
    private Integer carryOverStartType;
    private Float carryOverValidityDuration;
    private Integer carryOverValidityUnit;
    private Integer convertRule;
    private Integer childRule;
    private Boolean validityExtension;
    private String invalidDate;
    private Integer invalidType;
    private String description;
    private String ruleName;
    private Long ruleStartDate;
    private Long ruleEndDate;
    private String remark;
    private Integer sort;
    private Integer containPrenatalLeave;
    private Integer applyCompensatoryCash;
    private Integer expireHandle;
    private String applyTypes;
    private Float validQuotaLimit;
    private Boolean leaveExtension;
    private Integer extensionUnit;
    private Integer maxExtension;
    private Integer extensionTime;
    private String i18nRuleName;
    /**
     * 本年额度发放规则按照入职月比例发放时的临界日期
     */
    private Integer dayOfHireMonthDist;
    private Integer carryToType;
    private String groupExpCondition;
    private Integer transferType;
    private Float maxTransferQuota;

    public LeaveQuotaConfigDo convertToDo() {
        Set<ApplyQuotaEnum> applyTypeSet = null;
        if (StringUtils.isNotBlank(this.applyTypes)) {
            applyTypeSet = FastjsonUtil.toObject(this.applyTypes, Set.class);
            this.applyTypes = null;
        }
        LeaveQuotaConfigDo leaveQuotaConfigDo = FastjsonUtil.toObject(FastjsonUtil.toJsonStr(this), LeaveQuotaConfigDo.class);
        if (applyTypeSet != null) {
            leaveQuotaConfigDo.setApplyTypes(applyTypeSet);
        }
        return leaveQuotaConfigDo;
    }
}