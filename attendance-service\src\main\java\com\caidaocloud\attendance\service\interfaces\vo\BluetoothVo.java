package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BluetoothVo {
    @ApiModelProperty("id")
    private Integer ibeaconId;

    @ApiModelProperty("蓝牙名字")
    private String bluetoothName;

    /**
     * UUID :  是“Universally Unique Identifier”的简称，通用唯一识别码的意思。
     * 对于蓝牙设备，每个服务都有通用、独立、唯一的UUID与之对应。也就是说，在同一时间、同一地点，不可能有两个相同的UUID标识的不同服务。
     */
    @ApiModelProperty("uuid")
    private String uuid;

    /**
     * iBeacon设备的Major，又称为主要值。
     * 相当于群组号，同一个组里Beacon有相同的Major。
     * 取值范围：[0，65535]
     */
    @ApiModelProperty("major")
    private String major;

    /**
     * 主要值名称
     */
    @ApiModelProperty("majorName")
    private String majorName;

    /**
     * iBeacon设备的Minor,又称为次要值。
     * 相当于识别群组里单个的Beacon。
     * 取值范围：[0，65535]
     */
    @ApiModelProperty("minor")
    private String minor;

    /**
     * 主要值名称
     */
    @ApiModelProperty("minorName")
    private String minorName;
}
