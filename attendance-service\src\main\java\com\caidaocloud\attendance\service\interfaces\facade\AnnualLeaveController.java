package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.AnnualLeaveDto;
import com.caidaocloud.attendance.service.application.dto.quota.AnnualLeaveRetainDto;
import com.caidaocloud.attendance.service.application.service.impl.AnnualLeaveService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveRetainRequest;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveUpdateDto;
import com.caidaocloud.attendance.service.interfaces.vo.quota.AnnualLeaveDetailVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.AnnualLeavePageVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.AnnualLeaveRetainVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 年假明细接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/annualLeave/v1")
@Api(value = "/api/attendance/annualLeave/v1", description = "年假明细接口")
public class AnnualLeaveController {
    @Resource
    private AnnualLeaveService annualLeaveService;
    @Resource
    private ISessionService sessionService;

    @ApiOperation(value = "年假明细分页列表", tags = "v1.1")
    @PostMapping("/list")
    public Result<AttendancePageResult<AnnualLeavePageVo>> pageList(@RequestBody AnnualLeaveSearchDto dto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        List<AnnualLeaveDto> list = annualLeaveService.pageList(dto, null, sessionService.getUserInfo());
        if (CollectionUtils.isEmpty(list)) {
            return ResponseWrap.wrapResult(new AttendancePageResult<>());
        }
        PageList<AnnualLeaveDto> pageList = (PageList<AnnualLeaveDto>) list;
        List<AnnualLeavePageVo> items = ObjectConverter.convertList(pageList, AnnualLeavePageVo.class);
        items.forEach(it->{
            it.changeHomeLeaveStatus();
        });
        PageBean pageBean = PageUtil.bulidPageBean(dto.getPageNo(), dto.getPageSize());
        return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
    }

    @ApiOperation(value = "新增年假明细", tags = "v1.1")
    @PostMapping("/save")
    @LogRecordAnnotation(success = "新增了{{#name}}的{{#content}}", category = "新增", menu = "休假管理-假期余额-年度假期明细")
    public Result save(@RequestBody AnnualLeaveUpdateDto dto) {
        EmpInfoDTO empInfo = dto.getEmpInfo();
        LogRecordContext.putVariable("name", empInfo.getWorkno() + "(" + empInfo.getName() + ")");
        String errorMsg = annualLeaveService.saveOrUpdateAnnualLeave(dto);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.fail(errorMsg);
        }
        return Result.ok(true);
    }

    @ApiOperation(value = "修改年假明细", tags = "v1.2")
    @PostMapping("/update")
    @LogRecordAnnotation(success = "编辑了{{#name}}的{{#content}}", category = "编辑", menu = "休假管理-假期余额-年度假期明细")
    public Result update(@RequestBody AnnualLeaveUpdateDto dto) {
        if (StringUtil.isEmpty(dto.getEmpQuotaId())) {
            return Result.ok(dto);
        }
        EmpInfoDTO empInfo = dto.getEmpInfo();
        LogRecordContext.putVariable("name", empInfo.getWorkno() + "(" + empInfo.getName() + ")");
        LogRecordContext.putVariable("content", dto.getQuotaName());
        String errorMsg = annualLeaveService.saveOrUpdateAnnualLeave(dto);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.fail(errorMsg);
        }

        return Result.ok(true);
    }

    @ApiOperation(value = "年假明细详情", tags = "v1.1")
    @GetMapping("/details")
    public Result<AnnualLeaveDetailVo> details(Integer empQuotaId) {
        if (null == empQuotaId) {
            return Result.fail();
        }
        AnnualLeaveDto dto = annualLeaveService.getAnnualLeaveDetails(empQuotaId);
        AnnualLeaveDetailVo vo = null == dto ? null : ObjectConverter.convert(dto, AnnualLeaveDetailVo.class);
        return Result.ok(vo);
    }

    @ApiOperation(value = "删除年假明细", tags = "v1.1")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了{empName{#empId}}的{{#content}}", category = "删除", menu = "休假管理-假期余额-年度假期明细")
    public Result delete(Integer empQuotaId) {
        if (null == empQuotaId) {
            return Result.fail();
        }
        annualLeaveService.deleteEmpQuota(empQuotaId);
        return Result.ok(empQuotaId);
    }

    @ApiOperation(value = "批量删除年假明细", tags = "v1.1")
    @PostMapping("/deleteAnnualLeaves")
    @LogRecordAnnotation(success = "批量删除了{{#num}}条数据", category = "批量删除", menu = "休假管理-假期余额-年度假期明细")
    public Result<String> deleteAnnualLeaves(@RequestBody ItemsResult<Integer> dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return Result.fail();
        }
        try {
            return annualLeaveService.deleteEmpQuotas(dto.getItems());
        } catch (Exception e) {
            log.error("AnnualLeaveController.deleteAnnualLeaves has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, "");
        }
    }

    @ApiOperation(value = "年假留存列表")
    @GetMapping("/retain/list")
    public Result<ItemsResult<AnnualLeaveRetainVo>> getAnnualLeaveRetainList(@RequestParam("empQuotaId") Integer empQuotaId) {
        try {
            List<AnnualLeaveRetainDto> list = annualLeaveService.getAnnualLeaveRetainList(empQuotaId);
            return Result.ok(new ItemsResult<>(ObjectConverter.convertList(list, AnnualLeaveRetainVo.class)));
        } catch (Exception e) {
            log.error("getAnnualLeaveRetainList exception {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, null);
        }
    }

    @ApiOperation(value = "年假留存详情")
    @GetMapping("/retain/detail")
    public Result<AnnualLeaveRetainVo> getAnnualLeaveRetainDetail(@RequestParam("empQuotaId") Integer empQuotaId) {
        try {
            AnnualLeaveRetainDto dto = annualLeaveService.getAnnualLeaveRetainDetail(empQuotaId);
            return Result.ok(ObjectConverter.convert(dto, AnnualLeaveRetainVo.class));
        } catch (Exception e) {
            log.error("getAnnualLeaveRetainDetail exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @ApiOperation(value = "年假留存保存")
    @PostMapping("/retain/save")
    public Result<Boolean> saveAnnualLeaveRetain(@RequestBody AnnualLeaveRetainRequest dto) {
        if (dto.getQuotaDay() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.RETAIN_QUOTA_EMPTY, Boolean.FALSE);
        }
        if (dto.getQuotaDay() <= 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.RETAIN_QUOTA_LESS_ZERO, Boolean.FALSE);
        }
        if (dto.getLastDate() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.RETAIN_VALIDATE_EMPTY, Boolean.FALSE);
        }
        try {
            return annualLeaveService.saveOrUpdateAnnualLeaveRetain(dto);
        } catch (Exception e) {
            log.error("saveAnnualLeaveRetain exception {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, null);
        }
    }

    @ApiOperation(value = "年假留存编辑")
    @PostMapping("/retain/update")
    public Result<Boolean> updateAnnualLeaveRetain(@RequestBody AnnualLeaveRetainRequest dto) {
        if (dto.getQuotaDay() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.RETAIN_QUOTA_EMPTY, Boolean.FALSE);
        }
        if (dto.getQuotaDay() <= 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.RETAIN_QUOTA_LESS_ZERO, Boolean.FALSE);
        }
        if (dto.getLastDate() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.RETAIN_VALIDATE_EMPTY, Boolean.FALSE);
        }
        try {
            return annualLeaveService.saveOrUpdateAnnualLeaveRetain(dto);
        } catch (Exception e) {
            log.error("updateAnnualLeaveRetain exception {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, null);
        }
    }

    @ApiOperation("导入员工年假结转数据")
    @PostMapping(value = "/retain/import")
    @LogRecordAnnotation(success = "导入了年假结转", category = "导入", menu = "休假管理-假期余额-年度假期明细")
    public Result<String> importEmpRetainQuota(MultipartFile file) {
        if (null == file) {
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_TEMPLATE_FILE, null);
        }
        try {
            return annualLeaveService.importEmpAnnualLeaveQuotaRetain(file);
        } catch (Exception e) {
            log.error("importEmpRetainQuota has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null);
        }
    }
}