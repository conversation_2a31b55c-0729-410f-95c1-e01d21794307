package com.caidaocloud.attendance.service.infrastructure.util;

import com.caidao1.commons.session.AuthPageBean;
import com.caidao1.report.common.OpEnum;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.util.FastjsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class PageUtil {

    public static PageBean bulidPageBean(Integer pageNo, Integer pageSize) {
        PageBean pageBean = new PageBean();
        if (pageNo == 0) {
            pageBean.setPosStart(pageNo);
            pageBean.setCount(pageSize);
        } else {
            pageBean.setPosStart((pageNo - 1) * pageSize);
            pageBean.setCount(pageSize);
        }
        pageBean.setPage(pageNo);
        return pageBean;
    }

    public static PageBean getPageBean(Integer pageNo, Integer pageSize) {
        if (pageNo < 0) {
            pageNo = 1;
        }
        if (pageSize <= 0) {
            pageSize = 10;
        }
        PageBean pageBean = new PageBean();
        if (pageNo == 0) {
            pageBean.setCount(RowBounds.NO_ROW_LIMIT - 1);
            pageBean.setPosStart(pageNo);
        } else {
            pageBean.setCount(pageSize);
            pageBean.setPosStart((pageNo - 1) * pageSize);
        }
        pageBean.setPage(pageNo);
        return pageBean;
    }

    @Deprecated
    public static PageBean getPageBean(AttendanceBasePage page) {
        if (page.getPageNo() < 0) {
            page.setPageNo(1);
        }
        if (page.getPageSize() <= 0) {
            page.setPageSize(10);
        }
        PageBean pageBean = (new ObjectConverter<AttendanceBasePage, PageBean>()).objectConvert(page, PageBean.class);
        if (page.getPageNo() == 0) {
            pageBean.setCount(RowBounds.NO_ROW_LIMIT - 1);
            pageBean.setPosStart(page.getPageNo());
            pageBean.setFilterList(page.getFilterList());
        } else {
            pageBean.setCount(page.getPageSize());
            pageBean.setPosStart((page.getPageNo() - 1) * page.getPageSize());
            pageBean.setFilterList(page.getFilterList());
        }
        pageBean.setPage(page.getPageNo());
        return pageBean;
    }

    public static PageBean getNewPageBean(AttendanceBasePage page) {
        if (page.getPageNo() < 0) {
            page.setPageNo(1);
        }
        if (page.getPageSize() <= 0) {
            page.setPageSize(10);
        }
        PageBean pageBean = FastjsonUtil.convertObject(page, PageBean.class);
        if (page.getPageNo() == 0) {
            pageBean.setCount(RowBounds.NO_ROW_LIMIT - 1);
            pageBean.setPosStart(page.getPageNo());
        } else {
            pageBean.setCount(page.getPageSize());
            pageBean.setPosStart((page.getPageNo() - 1) * page.getPageSize());
        }
        pageBean.setPage(page.getPageNo());
        return pageBean;
    }

    public static AuthPageBean getAuthPageBean(AttendanceBasePage page, String dataScope) {
        if (page.getPageNo() < 0) {
            page.setPageNo(1);
        }
        if (page.getPageSize() <= 0) {
            page.setPageSize(10);
        }
        AuthPageBean pageBean = new AuthPageBean();
        pageBean.setPage(page.getPageNo());
        pageBean.setCount(page.getPageSize());
        pageBean.setDataScope(dataScope);
        pageBean.setPosStart((page.getPageNo() - 1) * page.getPageSize());
        pageBean.setFilterList(page.getFilterList());
        return pageBean;
    }

    public static List<FilterBean> getFilterList(OpEnum op, String min, String... fields) {
        List<FilterBean> filterList = new ArrayList<>();
        if (fields == null) {
            return filterList;
        }
        for (int i = 0; i < fields.length; i++) {
            FilterBean filterBean = new FilterBean();
            filterBean.setField(fields[i]);
            filterBean.setOp(op);
            filterBean.setMin(min);
            filterList.add(filterBean);
        }
        return filterList;
    }

    public static void doCopyFieldProperty(QueryPageBean source, AttendanceBasePage target) {
        target.setPageNo(source.getPageNo());
        target.setPageSize(source.getPageSize());
        target.setTotal(source.getTotal());
        target.setKeywords(source.getKeywords());
        if (CollectionUtils.isNotEmpty(source.getFilterList())) {
            List<FilterBean> filterList = source.getFilterList().stream().map(item -> {
                FilterBean filterBean = new FilterBean();
                filterBean.setField(item.getProp());
                String value = null != item.getValue() ? String.valueOf(item.getValue()) : null;
                switch (item.getOp()) {
                    case lk:
                        filterBean.setOp(OpEnum.lk);
                        filterBean.setMin(value);
                        break;
                    case eq:
                        filterBean.setOp(OpEnum.eq);
                        filterBean.setMin(value);
                        break;
                    case gt:
                        filterBean.setOp(OpEnum.gt);
                        filterBean.setMin(value);
                        break;
                    case ge:
                        filterBean.setOp(OpEnum.ge);
                        filterBean.setMin(value);
                        break;
                    case lt:
                        filterBean.setOp(OpEnum.lt);
                        filterBean.setMin(value);
                        break;
                    case le:
                        filterBean.setOp(OpEnum.le);
                        filterBean.setMin(value);
                        break;
                    case bt:
                        if (StringUtils.isNotBlank(value)) {
                            String[] valeArray = value.split(",");
                            List<Long> valueList = Arrays.stream(valeArray).map(Long::valueOf)
                                    .sorted(Long::compareTo).collect(Collectors.toList());
                            if (valueList.size() > 1) {
                                filterBean.setOp(OpEnum.bt);
                                filterBean.setMin(valueList.get(0).toString());
                                filterBean.setMax(valueList.get(valueList.size() - 1).toString());
                            } else {
                                filterBean.setOp(OpEnum.ge);
                                filterBean.setMin(valueList.get(0).toString());
                            }
                        }
                        break;
                    case in:
                        filterBean.setOp(OpEnum.in);
                        filterBean.setMin(value);
                        break;
                    case ne:
                        filterBean.setOp(OpEnum.ne);
                        filterBean.setMin(value);
                        break;
                    case nm:
                        filterBean.setOp(OpEnum.nm);
                        break;
                    case em:
                        filterBean.setOp(OpEnum.em);
                        break;
                }
                return filterBean;
            }).collect(Collectors.toList());
            target.setFilterList(filterList);
        }
    }
}
