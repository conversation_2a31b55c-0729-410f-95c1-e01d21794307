package com.caidaocloud.attendance.core.wa.enums;

/**
 * 考勤方案规则：休假外出申请校验规则
 */
public enum LeaveOutCheckRuleEnum {
    LEAVE_OUT_NOTCOEXIST(1, "休假外出时间不允许重叠"),
    LEAVE_OUT_COEXIST(2, "休假外出允许时间重叠");

    private Integer index;

    private String name;

    LeaveOutCheckRuleEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (LeaveOutCheckRuleEnum c : LeaveOutCheckRuleEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setValue(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
