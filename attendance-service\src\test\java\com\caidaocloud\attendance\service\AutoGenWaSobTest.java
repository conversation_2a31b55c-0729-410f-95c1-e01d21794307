package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.application.service.ISobService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class AutoGenWaSobTest {

    @Autowired
    private ISobService sobService;
    @Test
    public void getOvertimeList() {
        sobService.autoGenerateAttendancePeriod();
    }
}
