package com.caidaocloud.attendance.service.interfaces.vo.shift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/16 15:07
 * @Version 1.0
 */
@Data
public class ChangeShiftVo {

    @ApiModelProperty("主键id")
    private Long recId;
    @ApiModelProperty("工号")
    private String workNo;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("组织名称")
    private String orgName;
    @ApiModelProperty("部门全称")
    private String fullPath;
    @ApiModelProperty("调班日期")
    private Long workDate;
    @ApiModelProperty("原班次")
    private String oldShift;
    @ApiModelProperty("调整后班次")
    private String newShift;
    @ApiModelProperty("申请日期")
    private Long createTime;
    @ApiModelProperty("调整事由")
    private String reason;
    @ApiModelProperty("审批状态文本")
    private String statusName;
    @ApiModelProperty("审批状态 0暂存、1审批中、2已通过、3已拒绝、4已作废、5已退回、8撤销中、9已撤销")
    private Integer status;
    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;
    @ApiModelProperty("审批类型 1:请假,2:加班,41:补打卡,45:出差,103:调班")
    private Integer funType;
    @ApiModelProperty("业务主键")
    private String businessKey;

}
