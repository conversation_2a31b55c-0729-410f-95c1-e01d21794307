package com.caidaocloud.attendance.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidao1.ioc.dto.ImportResult;
import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.token.TokenVerify;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;

import java.util.Date;

public class TokenTest {
    private static final TokenTest tt = new TokenTest();

    private TokenTest(){
        System.out.println("..........TokenTest.....");
    }

    public static void main(String[] args) {
        testToObject();

        long nowTime = DateUtil.getOnlyDate(new Date());
        System.out.println(nowTime);
        System.out.println(nowTime - 7 * 24 * 60 * 60);

        System.out.println("-------");

        String token = TokenGenerator.getToken("35128", "TencentHR", 2);
        System.out.println(token);

        String access_token = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6InRlbmNlbnQtdGVzdDMiLCJ0aW1lIjoxNjI3MjcwMzE0NTAyLCJ0eXBlIjoyLCJ1c2VySWQiOiIzNTEzMiJ9.e6g3h0ILqP3jdxYUGwjCoamf80In2YHUKjEIn-sdIvKe9P_mgS-nx8FrVOpPnr6kGbR5H6s4ZKMQBL9wUP6Y5Q";

        System.out.println(access_token);
        access_token = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6InRlbmNlbnQtdGVzdDMiLCJ0aW1lIjoxNjI3MjcwMzE0NTAyLCJ0eXBlIjoyLCJ1c2VySWQiOiIzNTEzMiJ9.e6g3h0ILqP3jdxYUGwjCoamf80In2YHUKjEIn-sdIvKe9P_mgS-nx8FrVOpPnr6kGbR5H6s4ZKMQBL9wUP6Y5Q";
        TokenDto tokenDataModel = TokenVerify.getTokenDataModel(access_token);
        System.out.println(JSON.toJSONString(tokenDataModel));

    }

    public static void testToObject(){
        String result = "{\"data\":{\"result\":true,\"templateId\":2994,\"funcId\":16,\"templateName\":\"5bf812925df7ec3fa7a7ff47463cc14f\",\"fileName\":\"1260429307033606\",\"message\":null},\"code\":0,\"msg\":\"success\",\"serverTime\":1634027466729,\"success\":true}";
        Result<ImportResult> importResult = FastjsonUtil.toObject(result, new TypeReference<Result<ImportResult>>(){});

        System.out.println(FastjsonUtil.toJson(importResult));
    }
}
