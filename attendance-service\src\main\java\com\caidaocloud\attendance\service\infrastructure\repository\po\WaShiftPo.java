package com.caidaocloud.attendance.service.infrastructure.repository.po;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

/**
 * 班次
 */
//@Entity(value = "wa_shift", noClassnameStored = true)
//@Indexes(@Index(name = "b_e_w", value = "-b,-e,w"))
@Component
@Data
@Accessors(chain = true)
public class WaShiftPo {
    //@Id
    private String id;
    private Long corpid;
    //@Property("b")
    private String belongOrgid;
    //@Property("e")
    private Long empid;
    //@Property("w")
    private Long workDate;
    private Integer calendarDateType;
    private Integer shiftDefId;
    private Integer dateType;
    private Boolean isNight;
    private String shiftDefName;
    private String shiftDefCode;
    private Integer startTime;
    private Integer endTime;
    private Boolean isNoonRest;
    private Integer noonRestStart;
    private Integer noonRestEnd;
    private Integer restTotalTime;
    private Integer workTotalTime;
    private Integer onDutyStartTime;
    private Integer onDutyEndTime;
    private Integer offDutyStartTime;
    private Integer offDutyEndTime;
    private Integer overtimeStartTime;
    private Integer overtimeEndTime;
    private Boolean isDefault;
    private Boolean isHalfdayTime;
    private Integer halfdayTime;
    private Boolean isFlexibleWork;
    private Integer flexibleOnDutyStartTime;
    private Integer flexibleOnDutyEndTime;
    private Integer flexibleOffDutyStartTime;
    private Integer flexibleOffDutyEndTime;
    private Integer flexibleWorkType;
    private Object restPeriods;
    private Object overtimeRestPeriods;
    private Boolean isAdjustWorkHour;
    private Object adjustWorkHourJson;
    private Boolean isSpecial;
    private Integer specialWorkTime;
    private Boolean isApplyOvertime;
    private Object multiCheckinTimes;
    private Object multiWorkTimes;
    private String restTimeDesc;
    private Long orgid;
    private Long effectStartTime;
    private Long effectEndTime;
    private Long crtuser;
    private Long crttime;
}
