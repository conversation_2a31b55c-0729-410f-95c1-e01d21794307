package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AnalyzeDetailVo {

    @ApiModelProperty("应打卡时间")
    private Integer registerTime;
    @ApiModelProperty("打卡时间段")
    private String registerPerIodTime;
    @ApiModelProperty("实打卡时间")
    private Integer actRegisterTime;
    @ApiModelProperty("补打卡时间")
    private Integer makeUpTime;
    @ApiModelProperty("打卡结果")
    private Integer registerResult;

}
