package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OverTimeVo {
    @ApiModelProperty(value = "补偿类型")
    private String compensateType;

    @ApiModelProperty(value = "日期类型")
    private String dateType;

    @ApiModelProperty(value = "最小单位")
    private Float minUnit;

    private Float offNum;

    private Float overtimeNum;

    @ApiModelProperty(value = "加班类型")
    private String overtimeType;

    @ApiModelProperty("主键id")
    private Integer overtimeTypeId;

    @ApiModelProperty("加班类型+补偿类型 拼接")
    private String otTypeName;
}
