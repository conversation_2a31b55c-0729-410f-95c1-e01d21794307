package com.caidaocloud.attendance.service.application.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/7/15 17:36
 * @Description:
 **/
public enum BigDecimalOvertimeUnitEnum {
    MINUTE("按分钟加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal applyTime, BigDecimal minOvertimeUnit, int type) {
            return applyTime;
        }
    },
    HALF_HOUR("按半小时加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal applyTime, BigDecimal minOvertimeUnit, int type) {
            return applyTime.divide(SECONDS_IN_HALF_HOUR, 0, RoundingMode.DOWN)
                    .multiply(SECONDS_IN_HALF_HOUR);
        }
    },
    HOUR("按小时加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal applyTime, BigDecimal minOvertimeUnit, int type) {
            if (type == 2) {
                return applyTime.divide(SECONDS_IN_HOUR.multiply(minOvertimeUnit), 0, RoundingMode.DOWN)
                        .multiply(minOvertimeUnit)
                        .multiply(SECONDS_IN_HOUR);
            } else {
                return applyTime.divide(SECONDS_IN_MINUTE.multiply(minOvertimeUnit), 0, RoundingMode.DOWN)
                        .multiply(minOvertimeUnit)
                        .multiply(SECONDS_IN_MINUTE);
            }
        }
    },
    HALF_DAY("按半天加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal applyTime, BigDecimal minOvertimeUnit, int type) {
            // workingTime一天的标准时长，1800是3600/2的结果
            BigDecimal halfDay = workingTime.multiply(SECONDS_IN_HALF_DAY).setScale(0, RoundingMode.DOWN);
            return applyTime.divide(halfDay, 0, RoundingMode.DOWN).multiply(halfDay);
        }
    },
    DAY("按天加班") {
        @Override
        public BigDecimal getTime(BigDecimal workingTime, BigDecimal applyTime, BigDecimal minOvertimeUnit, int type) {
            // workingTime一天的标准时长
            BigDecimal day = workingTime.multiply(SECONDS_IN_DAY).setScale(0, RoundingMode.DOWN);
            return applyTime.divide(day, 0, RoundingMode.DOWN).multiply(day);
        }
    };

    private final String desc;

    // 时间常量定义
    private static final BigDecimal SECONDS_IN_MINUTE = new BigDecimal("60");
    private static final BigDecimal SECONDS_IN_HALF_HOUR = new BigDecimal("1800");
    private static final BigDecimal SECONDS_IN_HOUR = new BigDecimal("3600");
    private static final BigDecimal SECONDS_IN_HALF_DAY = new BigDecimal("0.5");
    private static final BigDecimal SECONDS_IN_DAY = new BigDecimal("1");

    BigDecimalOvertimeUnitEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByOrdinal(int ordinal) {
        for (BigDecimalOvertimeUnitEnum item : values()) {
            if (item.ordinal() == ordinal) {
                return item.getDesc();
            }
        }
        return "";
    }

    public static BigDecimalOvertimeUnitEnum getByOrdinal(int ordinal) {
        for (BigDecimalOvertimeUnitEnum item : values()) {
            if (item.ordinal() == ordinal) {
                return item;
            }
        }
        return MINUTE;
    }

    public abstract BigDecimal getTime(BigDecimal workingTime, BigDecimal applyTime, BigDecimal minOvertimeUnit, int type);
}
