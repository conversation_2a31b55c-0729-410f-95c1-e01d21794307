package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RegisterRecordBdkVo {
    @ApiModelProperty("打卡记录ID")
    private Long recordId;
    @ApiModelProperty("帐号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("组织")
    private String orgName;
    @ApiModelProperty("组织全路径")
    private String fullPath;
    @ApiModelProperty("打卡方式")
    private String typeName;
    @ApiModelProperty("打卡时间")
    private Long regDateTime;
    @ApiModelProperty("打卡地点")
    private String regAddr;
    @ApiModelProperty("设备号")
    private String mobDeviceNum;
    @ApiModelProperty("打卡时间范围")
    private String normalDate;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("部门主键id")
    private Integer orgid;
    @ApiModelProperty("考勤日期(归属日期)")
    private Long belongDate;
    @ApiModelProperty("打卡地点状态:0无效1有效")
    private Integer clockSiteStatus;
}
