package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("批量打卡分析计算结果DTO")
public class BatchClockAnalyseResultDto {
    @ApiModelProperty("需要分析的员工ID列表")
    private List<Long> empIdList;

    public static BatchClockAnalyseResultDto doBuild() {
        return new BatchClockAnalyseResultDto();
    }

    public static BatchClockAnalyseResultDto doBuild(List<Long> empIdList) {
        return new BatchClockAnalyseResultDto().setEmpIdList(empIdList);
    }
}
