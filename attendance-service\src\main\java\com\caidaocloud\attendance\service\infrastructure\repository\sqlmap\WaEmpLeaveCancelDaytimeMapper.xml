<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpLeaveCancelDaytimeMapper" >
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelDaytime" >
    <id column="leave_cancel_daytime_id" property="leaveCancelDaytimeId" jdbcType="BIGINT" />
    <result column="leave_cancel_id" property="leaveCancelId" jdbcType="BIGINT" />
    <result column="leave_cancel_info_id" property="leaveCancelInfoId" jdbcType="BIGINT" />
    <result column="leave_cancel_date" property="leaveCancelDate" jdbcType="BIGINT" />
    <result column="shalf_day" property="shalfDay" jdbcType="CHAR" />
    <result column="ehalf_day" property="ehalfDay" jdbcType="CHAR" />
    <result column="start_time" property="startTime" jdbcType="INTEGER" />
    <result column="end_time" property="endTime" jdbcType="INTEGER" />
    <result column="period_type" property="periodType" jdbcType="SMALLINT" />
    <result column="time_unit" property="timeUnit" jdbcType="SMALLINT" />
    <result column="time_duration" property="timeDuration" jdbcType="REAL" />
    <result column="date_type" property="dateType" jdbcType="INTEGER" />
    <result column="real_date" property="realDate" jdbcType="BIGINT" />
    <result column="shift_def_id" property="shiftDefId" jdbcType="INTEGER" />
    <result column="apply_time_duration" property="applyTimeDuration" jdbcType="REAL" />
    <result column="before_adjust_time_duration" property="beforeAdjustTimeDuration" jdbcType="REAL" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="BIGINT" />
    <result column="update_by" property="updateBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    leave_cancel_daytime_id, leave_cancel_id, leave_cancel_info_id, leave_cancel_date, 
    shalf_day, ehalf_day, start_time, end_time, period_type, time_unit, time_duration, 
    date_type, real_date, shift_def_id, apply_time_duration, before_adjust_time_duration, 
    deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wa_emp_leave_cancel_daytime
    where leave_cancel_daytime_id = #{leaveCancelDaytimeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wa_emp_leave_cancel_daytime
    where leave_cancel_daytime_id = #{leaveCancelDaytimeId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelDaytime" >
    insert into wa_emp_leave_cancel_daytime (leave_cancel_daytime_id, leave_cancel_id, 
      leave_cancel_info_id, leave_cancel_date, shalf_day, 
      ehalf_day, start_time, end_time, 
      period_type, time_unit, time_duration, 
      date_type, real_date, shift_def_id, 
      apply_time_duration, before_adjust_time_duration, 
      deleted, create_by, create_time, 
      update_by, update_time)
    values (#{leaveCancelDaytimeId,jdbcType=BIGINT}, #{leaveCancelId,jdbcType=BIGINT}, 
      #{leaveCancelInfoId,jdbcType=BIGINT}, #{leaveCancelDate,jdbcType=BIGINT}, #{shalfDay,jdbcType=CHAR}, 
      #{ehalfDay,jdbcType=CHAR}, #{startTime,jdbcType=INTEGER}, #{endTime,jdbcType=INTEGER}, 
      #{periodType,jdbcType=SMALLINT}, #{timeUnit,jdbcType=SMALLINT}, #{timeDuration,jdbcType=REAL}, 
      #{dateType,jdbcType=INTEGER}, #{realDate,jdbcType=BIGINT}, #{shiftDefId,jdbcType=INTEGER}, 
      #{applyTimeDuration,jdbcType=REAL}, #{beforeAdjustTimeDuration,jdbcType=REAL}, 
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelDaytime" >
    insert into wa_emp_leave_cancel_daytime
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="leaveCancelDaytimeId != null" >
        leave_cancel_daytime_id,
      </if>
      <if test="leaveCancelId != null" >
        leave_cancel_id,
      </if>
      <if test="leaveCancelInfoId != null" >
        leave_cancel_info_id,
      </if>
      <if test="leaveCancelDate != null" >
        leave_cancel_date,
      </if>
      <if test="shalfDay != null" >
        shalf_day,
      </if>
      <if test="ehalfDay != null" >
        ehalf_day,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="periodType != null" >
        period_type,
      </if>
      <if test="timeUnit != null" >
        time_unit,
      </if>
      <if test="timeDuration != null" >
        time_duration,
      </if>
      <if test="dateType != null" >
        date_type,
      </if>
      <if test="realDate != null" >
        real_date,
      </if>
      <if test="shiftDefId != null" >
        shift_def_id,
      </if>
      <if test="applyTimeDuration != null" >
        apply_time_duration,
      </if>
      <if test="beforeAdjustTimeDuration != null" >
        before_adjust_time_duration,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="createBy != null" >
        create_by,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateBy != null" >
        update_by,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="leaveCancelDaytimeId != null" >
        #{leaveCancelDaytimeId,jdbcType=BIGINT},
      </if>
      <if test="leaveCancelId != null" >
        #{leaveCancelId,jdbcType=BIGINT},
      </if>
      <if test="leaveCancelInfoId != null" >
        #{leaveCancelInfoId,jdbcType=BIGINT},
      </if>
      <if test="leaveCancelDate != null" >
        #{leaveCancelDate,jdbcType=BIGINT},
      </if>
      <if test="shalfDay != null" >
        #{shalfDay,jdbcType=CHAR},
      </if>
      <if test="ehalfDay != null" >
        #{ehalfDay,jdbcType=CHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="periodType != null" >
        #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="timeUnit != null" >
        #{timeUnit,jdbcType=SMALLINT},
      </if>
      <if test="timeDuration != null" >
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="dateType != null" >
        #{dateType,jdbcType=INTEGER},
      </if>
      <if test="realDate != null" >
        #{realDate,jdbcType=BIGINT},
      </if>
      <if test="shiftDefId != null" >
        #{shiftDefId,jdbcType=INTEGER},
      </if>
      <if test="applyTimeDuration != null" >
        #{applyTimeDuration,jdbcType=REAL},
      </if>
      <if test="beforeAdjustTimeDuration != null" >
        #{beforeAdjustTimeDuration,jdbcType=REAL},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null" >
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelDaytime" >
    update wa_emp_leave_cancel_daytime
    <set >
      <if test="leaveCancelId != null" >
        leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT},
      </if>
      <if test="leaveCancelInfoId != null" >
        leave_cancel_info_id = #{leaveCancelInfoId,jdbcType=BIGINT},
      </if>
      <if test="leaveCancelDate != null" >
        leave_cancel_date = #{leaveCancelDate,jdbcType=BIGINT},
      </if>
      <if test="shalfDay != null" >
        shalf_day = #{shalfDay,jdbcType=CHAR},
      </if>
      <if test="ehalfDay != null" >
        ehalf_day = #{ehalfDay,jdbcType=CHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="periodType != null" >
        period_type = #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="timeUnit != null" >
        time_unit = #{timeUnit,jdbcType=SMALLINT},
      </if>
      <if test="timeDuration != null" >
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="dateType != null" >
        date_type = #{dateType,jdbcType=INTEGER},
      </if>
      <if test="realDate != null" >
        real_date = #{realDate,jdbcType=BIGINT},
      </if>
      <if test="shiftDefId != null" >
        shift_def_id = #{shiftDefId,jdbcType=INTEGER},
      </if>
      <if test="applyTimeDuration != null" >
        apply_time_duration = #{applyTimeDuration,jdbcType=REAL},
      </if>
      <if test="beforeAdjustTimeDuration != null" >
        before_adjust_time_duration = #{beforeAdjustTimeDuration,jdbcType=REAL},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where leave_cancel_daytime_id = #{leaveCancelDaytimeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelDaytime" >
    update wa_emp_leave_cancel_daytime
    set leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT},
      leave_cancel_info_id = #{leaveCancelInfoId,jdbcType=BIGINT},
      leave_cancel_date = #{leaveCancelDate,jdbcType=BIGINT},
      shalf_day = #{shalfDay,jdbcType=CHAR},
      ehalf_day = #{ehalfDay,jdbcType=CHAR},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      period_type = #{periodType,jdbcType=SMALLINT},
      time_unit = #{timeUnit,jdbcType=SMALLINT},
      time_duration = #{timeDuration,jdbcType=REAL},
      date_type = #{dateType,jdbcType=INTEGER},
      real_date = #{realDate,jdbcType=BIGINT},
      shift_def_id = #{shiftDefId,jdbcType=INTEGER},
      apply_time_duration = #{applyTimeDuration,jdbcType=REAL},
      before_adjust_time_duration = #{beforeAdjustTimeDuration,jdbcType=REAL},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where leave_cancel_daytime_id = #{leaveCancelDaytimeId,jdbcType=BIGINT}
  </update>

  <select id="selectLeaveCancelDaytimeList" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveCancelDaytimeDo">
    select wld.leave_cancel_date,
    wld.shalf_day,
    wld.ehalf_day,
    wld.start_time,
    wld.end_time,
    wld.period_type,
    welc.leave_id,
    wld.create_time,
    welc.empid
    from wa_emp_leave_cancel_daytime wld
    join wa_emp_leave_cancel welc on wld.leave_cancel_id = welc.leave_cancel_id
    where welc.tenant_id = #{tenantId,jdbcType=VARCHAR}
    and welc.status = 2
    and wld.leave_cancel_date between #{startTime} and #{endTime}
  </select>
</mapper>