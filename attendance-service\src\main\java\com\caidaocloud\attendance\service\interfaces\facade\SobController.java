package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.ioc.util.ListHelper;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.group.GroupKeyValue;
import com.caidaocloud.attendance.service.application.service.ISobService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.SelectListMapper;
import com.caidaocloud.attendance.service.infrastructure.util.KeyToFilterUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.SobDto;
import com.caidaocloud.attendance.service.interfaces.dto.sob.SobItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.sob.WaSobDto;
import com.caidaocloud.attendance.service.interfaces.vo.KeyValueVo;
import com.caidaocloud.attendance.service.interfaces.vo.SobOptionVo;
import com.caidaocloud.attendance.service.interfaces.vo.SobVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/attendance/sob/v1")
@Api(value = "/api/attendance/sob/v1", description = "考勤周期接口")
public class SobController {

    @Autowired
    private WaSobService waSobService;

    @Autowired
    private WaAttendanceConfigService waConfigService;

    @Autowired
    private SelectListMapper selectListMapper;

    @Autowired
    private ISessionService sessionService;

    @Autowired
    private ISobService sobService;

    @ApiOperation("考勤周期列表")
    @PostMapping("/list")
    public Result<AttendancePageResult<SobVo>> searchSobList(@RequestBody AttendanceBasePage basePage) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<FilterBean> filterBeans = KeyToFilterUtil.KeyToFilterList(basePage.getKeywords(), "wa_sob_name", basePage.getFilterList());
        pageBean.setFilterList(filterBeans);
        Map<String, Object> paramsMap = new HashMap<>();
        UserInfo userInfo = sessionService.getUserInfo();
        paramsMap.put("belongid", userInfo.getTenantId());
        try {
            List<Map> list = waSobService.searchWaSobList(pageBean, paramsMap);
            if (CollectionUtils.isEmpty(list)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageList pageList = (PageList) list;
            List<SobItemDto> sobList = JSON.parseArray(JSON.toJSONString(list)).toJavaList(SobItemDto.class);
            List<SobVo> sobVos = Lists.newArrayList();
            for (SobItemDto sobItemDto : sobList) {
                SobVo sobVo = ObjectConverter.convert(sobItemDto, SobVo.class);
                if (StringUtils.isNotBlank(sobItemDto.getI18nWaSobName())) {
                    sobVo.setI18nWaSobName(FastjsonUtil.toObject(sobItemDto.getI18nWaSobName(), Map.class));
                } else if (StringUtils.isNotBlank(sobItemDto.getWaSobName())) {
                    Map<String, String> i18nName = new HashMap<>();
                    i18nName.put("default", sobItemDto.getWaSobName());
                    sobVo.setI18nWaSobName(i18nName);
                }
                sobVo.setWaSobName(LangParseUtil.getI18nLanguage(sobItemDto.getI18nWaSobName(), sobVo.getWaSobName()));
                sobVo.setWaGroupName(LangParseUtil.getI18nLanguage(sobItemDto.getI18nWaGroupName(), sobVo.getWaGroupName()));
                sobVos.add(sobVo);
            }
            AttendancePageResult<SobVo> pageResult = new AttendancePageResult<>(sobVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception ex) {
            log.error("SobController.searchSobList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("新增或修改考勤周期")
    @PostMapping("/save")
    public Result<Boolean> saveSob(@RequestBody WaSobDto dto) {
        dto.initWaSobName();
        dto.initI18nWaSobName();
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            WaSob sob = ObjectConverter.convert(dto, WaSob.class);
            if (null != dto.getI18nWaSobName()) {
                sob.setI18nWaSobName(FastjsonUtil.toJson(dto.getI18nWaSobName()));
            }
            if (sobService.checkSobName(userInfo.getTenantId(), sob.getWaSobId(), sob.getWaSobName(), null)) {
                return ResponseWrap.wrapResult(AttendanceCodes.SOB_NAME_DUPLICATED, Boolean.FALSE);
            }
            waSobService.saveOrUpWaSob(sob, ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getUserId(), userInfo.getTenantId());
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("SobController.saveSob executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("删除考勤周期")
    @DeleteMapping("/delete")
    public Result<Boolean> delSobById(@RequestParam("id") Integer id) {
        try {
            waSobService.delWaSobById(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("SobController.delSobById executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("封存（关闭）考勤周期")
    @GetMapping("/close")
    public Result<Boolean> closeSob(@RequestParam("id") Integer id) {
        try {
            waSobService.closeWaSob(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("SobController.closeSob executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SOB_CLOSE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("锁定考勤周期")
    @GetMapping("/lock")
    public Result<Boolean> lockSob(@RequestParam("id") Integer id) {
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            waSobService.lockSob(ConvertHelper.longConvert(userInfo.getTenantId()), id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("SobController.lockSob executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SOB_LOCKED_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("解除锁定考勤周期")
    @GetMapping("/unLock")
    public Result<Boolean> unLockSob(@RequestParam("id") Integer id) {
        try {
            waSobService.unLockSob(UserContext.getCorpId(), id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("SobController.unLockSob executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SOB_UNLOCKED_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取考勤周期下拉列表")
    @PostMapping("/selectSobOptions")
    public Result<AttendancePageResult<SobOptionVo>> showWaSobCombo(@RequestBody SobDto dto) {

        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            Map<String, Object> paramsMap = new HashMap<>();
            UserInfo userInfo = sessionService.getUserInfo();
            paramsMap.put("belongid", userInfo.getTenantId());
            if (StringUtils.isNotBlank(dto.getSobName())) {
                paramsMap.put("waSobName", dto.getSobName());
            }
            if (dto.getStatus() != null) {
                paramsMap.put("status", dto.getStatus());
            }
            if(null != dto.getEmpId()){
                List<Integer> waGroupIds = waSobService.listWaGroupIdByEmpId(dto.getEmpId());
                paramsMap.put("waGroupIds", waGroupIds);
            }
            pageBean.setOrder("sys_period_month.desc");
            List<Map> list = waSobService.searchWaSobList(pageBean, paramsMap);
            if (CollectionUtils.isNotEmpty(list)) {
                PageList<Map> pageList = (PageList<Map>) list;
                list.forEach(l -> {
                    if (null != l.get("i18n_wa_sob_name")) {
                        String i18nName = (String) l.get("i18n_wa_sob_name");
                        String i18n = LangParseUtil.getI18nLanguage(i18nName, null);
                        if (StringUtil.isNotBlank(i18n)) {
                            l.put("wa_sob_name", i18n);
                        }
                    }
                });
                List<SobOptionVo> sobVos = JSON.parseArray(JSON.toJSONString(list)).toJavaList(SobOptionVo.class);
                return ResponseWrap.wrapResult(new AttendancePageResult<>(sobVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
            }
            return ResponseWrap.wrapResult(new AttendancePageResult<>());
        } catch (Exception ex) {
            log.error("SobController.unLockSob executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SELECT_OPTION_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("分组条件下拉框")
    @GetMapping(value = "/selectWaGroupList")
    public Result<Map<String, List<Map<String, Object>>>> selectWaGroupList() {
        List<Map> list = waConfigService.getWaGroupList(new PageBean(true));
        Map<String, List<Map<String, Object>>> listMap = ListHelper.convertMapList(list, new ListHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("wa_group_id");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("wa_group_name");
            }
        }, false);
        listMap.put("items", listMap.remove("options"));
        return ResponseWrap.wrapResult(listMap);
    }

    @ApiOperation("计算周期开始时间、结束时间")
    @GetMapping(value = "/getWaGroupCycle")
    public Result<Map> getWaGroupCycle(@RequestParam("sysPeriodMonth") Integer sysPeriodMonth, @RequestParam("waGroupId") Integer waGroupId) {
        Map waGroupCycle = waConfigService.getWaGroupCycle(sysPeriodMonth, waGroupId);
        return ResponseWrap.wrapResult(waGroupCycle);
    }

    @ApiOperation("获取考勤方案下拉框")
    @GetMapping("/getWaGroupSelList")
    public Result<ItemsResult<KeyValueVo>> getWaGroupSelList() {
        UserInfo userInfo = sessionService.getUserInfo();
        List<GroupKeyValue> list = selectListMapper.getWaGroupListToSelect(userInfo.getTenantId());
        list.forEach(l -> {
            l.setText(LangParseUtil.getI18nLanguage(l.getI18nText(), l.getText()));
        });
        return Result.ok(new ItemsResult<>(ObjectConverter.convertList(list, KeyValueVo.class)));
    }

    @ApiOperation("测试自动生成考勤周期")
    @GetMapping("/autoGenSob")
    public Result autoGenSob() {
        sobService.autoGenerateAttendancePeriod();
        return Result.ok();
    }
}