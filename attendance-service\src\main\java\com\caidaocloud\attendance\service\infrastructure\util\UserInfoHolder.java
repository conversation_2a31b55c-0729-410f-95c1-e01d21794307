package com.caidaocloud.attendance.service.infrastructure.util;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.WebUtil;

import javax.servlet.http.HttpServletRequest;

public abstract class UserInfoHolder {
    private static final String CAIDAOCLOUD_USER_REQUEST_ATTR = "_CAIDAOCLOUD_USER_REQUEST_ATTR_";

    public static void setUserInfo(UserInfo userInfo) {
        if (null == userInfo) {
            return;
        }

        HttpServletRequest request = WebUtil.getRequest();
        PreCheck.preCheckArgument(null == request, "The request is empty. The UserInfoHolder does not support asynchronous calls");
        request.setAttribute(CAIDAOCLOUD_USER_REQUEST_ATTR, userInfo);
    }

    public static UserInfo getUserInfo() {
        HttpServletRequest request = WebUtil.getRequest();
        PreCheck.preCheckArgument(null == request, "The request is empty. The UserInfoHolder does not support asynchronous calls");

        // 优先从 request 中获取
        Object userInfo = request.getAttribute(CAIDAOCLOUD_USER_REQUEST_ATTR);
        if (userInfo == null) {
            throw new ServerException("User certificate verification failed, please login and try again");
        }
        return (UserInfo) userInfo;
    }
}