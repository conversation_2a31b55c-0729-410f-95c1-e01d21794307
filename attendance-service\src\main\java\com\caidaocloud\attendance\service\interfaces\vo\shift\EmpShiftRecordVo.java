package com.caidaocloud.attendance.service.interfaces.vo.shift;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/3/31 21:03
 * @Description:
 **/
@Data
@NoArgsConstructor
public class EmpShiftRecordVo {
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("上班时间")
    private Integer startTime;
    @ApiModelProperty("下班时间")
    private Integer endTime;
    @ApiModelProperty("打卡记录时间")
    private List<Long> records = new ArrayList<>();
    @ApiModelProperty("补卡条数每次")
    private Integer supplementNumber;
}
