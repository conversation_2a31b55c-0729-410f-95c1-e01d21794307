package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 假期类型设置VO
 * <AUTHOR>
 * @date 2021/1/31
 */
@Data
public class LeaveTypeVo {
    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
    @ApiModelProperty("假期名称")
    private String leaveName;
    @ApiModelProperty("假期编码")
    private String leaveCode;
    @ApiModelProperty("假期类型")
    private Integer leaveType;
    @ApiModelProperty("假期类型名称")
    private String leaveTypeName;
    @ApiModelProperty("休假单位")
    private Integer acctTimeType;
    @ApiModelProperty("休假最小单位")
    private Float roundTimeUnit;
    @ApiModelProperty("休息日顺延")
    private Boolean isRestDay;
    @ApiModelProperty("法定假日顺延")
    private Boolean isLegalHoliday;
    @ApiModelProperty("试用期是否可以请假")
    private Boolean isUsedInProbation;
    @ApiModelProperty("全年额度折算类型")
    private Integer quotaItType;
    @ApiModelProperty("全年额度舍位规则")
    private Integer quotaFour2fiveRule;
    @ApiModelProperty("当前额度折算规则")
    private Integer useItType;
    @ApiModelProperty("当前额度舍位规则")
    private Integer useFour2fiveRule;
    @ApiModelProperty("适用性别")
    private Long genderType;
    @ApiModelProperty("附件校验")
    private Boolean isUploadFile;
    @ApiModelProperty("附件校验时长")
    private Float minFileCheckTime;
    @ApiModelProperty("是否显示假期说明")
    private Boolean isWarnMsg;
    @ApiModelProperty("假期说明")
    private String warnMsg;
    @ApiModelProperty("额度限制类型： 1 限额、2 不限额")
    private Integer quotaRestrictionType;
    @ApiModelProperty("额度类型： 1 按年发放、2 调休、3 固定额度")
    private Integer quotaType;
    @ApiModelProperty("单次休假限制")
    private Boolean ifCheckTime;
    @ApiModelProperty("单次休假限制 下限（包含")
    private Float minLeaveTime;
    @ApiModelProperty("单次休假限制 上限（包含）")
    private Float maxLeaveTime;
    @ApiModelProperty("每月休假限制")
    private Boolean isCheckMonthTime;
    @ApiModelProperty("每月休假限制 下限（包含")
    private Float minMonthTime;
    @ApiModelProperty("每月休假限制 上限（包含）")
    private Float maxMonthTime;
    @ApiModelProperty("每年休假限制")
    private Boolean isCheckYearTime;
    @ApiModelProperty("每年休假限制 下限（包含）")
    private Float minYearTime;
    @ApiModelProperty("每年休假限制 上限（包含）")
    private Float maxYearTime;
    @ApiModelProperty("上级休假类型")
    private Integer upperLevel;
    @ApiModelProperty("休假原因必填开关：true/false")
    private Boolean reasonMust;
    @ApiModelProperty("员工是否可申请：true/false")
    private Boolean isEmpShow;

    @ApiModelProperty("休假限制开关：1试用期，2入职日期")
    private Integer leaveLimitSwitch;
    @ApiModelProperty("休假限制开关参数设置，单位天")
    private Integer leaveLimitDays;
    @ApiModelProperty("销假类型： 1 自动销假、2 手动销假")
    private Integer leaveCancelType;
    @ApiModelProperty("销假说明")
    private String leaveCancelRemark;
    @ApiModelProperty("假期时效控制：true/false")
    private Boolean isOpenTimeControl;
    @ApiModelProperty("假期时效时长")
    private Float controlTimeDuration;
    @ApiModelProperty("假期时效类型：-1 提前,1 延后")
    private Integer timeControlType;
    @ApiModelProperty("单位：1 天 2 小时")
    private Integer controlTimeUnit;
    @ApiModelProperty("适用地区")
    private String leaveArea;

    @ApiModelProperty("销假类型，多个逗号隔开")
    private String allowedCancelType;
    @ApiModelProperty("允许多次销假开关字段，默认true(开启)，false(关闭)")
    private Boolean allowedMultipleCancel;
    @ApiModelProperty("销假后补附件开关字段，默认false(关闭)，true(开启)")
    private Boolean enclosureLater;

    @ApiModelProperty("是否允许查看余额，默认true(允许)，false(不允许)")
    private Boolean allowViewQuota;

    @ApiModelProperty("销假类型附件是否必填,包含销假类型序号则必填，逗号分隔")
    private String cancelAttachmentRequired;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;

    @ApiModelProperty("销假时间：1审批结束后，2休假结束后")
    private Integer cancelTime;
    @ApiModelProperty("销假时间类型：1自动销假，2指定天数后自动销假")
    private Integer cancelTimeType;
    @ApiModelProperty("联动cancel_time_type字段为2时，指定自动销假的天数，3考核截止日当天自动销假")
    private Integer cancelTimeDay;

    @ApiModelProperty("查看规则：1、按生效日期查看，2、按失效日期合并查看额度，3、按当前有效额度合并查看")
    private Integer viewType;

    @ApiModelProperty("适用连续日期休假：false关闭，true开启")
    private Boolean continuousApplication;
    @ApiModelProperty("申请时效时间段：假期多段时效性控制 数据结构：{min:1,//请假最小天数 max:2,//请假最大天数 type:1,//类型 -1 提前 1 延后 unit:1,//单位 1 天  2 小时  默认传1（天） duration:1,//时效控管时长}")
    private Object timelinessControlJsonb;
    @ApiModelProperty("调休复现申请数字输入整数限制：0开启，1关闭")
    private Integer applyNumberType;

    @ApiModelProperty("上级假期类型新字段，支持假期类型和额度类型设置，字段值格式为：[上级类型,ID]， 上级类型枚举值有：假期类型 leaveType、额度类型 quotaType")
    private String[] upperType;

    @ApiModelProperty("上级假期余额校验符号：eq、gt、ge、lt、le")
    private String upperLevelQuotaCheckSymbol;

    @ApiModelProperty("上级假期余额校验")
    private Float upperLevelQuotaCheck;

    @ApiModelProperty("上级假期额度类型ID")
    private Long upperLevelQuotaTypeId;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nLeaveName;
    @ApiModelProperty("休假申请单是否展示余额，默认false不显示")
    private Boolean displayQuotaDetail;
    @ApiModelProperty("是否是带薪假,FALSE否,TRUE是,默认否")
    private Boolean paidLeave;

    @ApiModelProperty("撤销是否需要走审批流：true是/false否，默认值false")
    private Boolean revokeWorkflow;
    @ApiModelProperty("审批通过单据是否允许撤销：true是/false否，默认值false")
    private Boolean revokePassed;
    @ApiModelProperty("撤销是否需要走审批流,允许撤销的状态多选，逗号分隔：1审批中2已通过")
    private String revokeAllowStatus;
}
