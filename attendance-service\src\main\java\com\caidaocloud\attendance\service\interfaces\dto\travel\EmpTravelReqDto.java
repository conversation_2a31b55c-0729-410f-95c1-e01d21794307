package com.caidaocloud.attendance.service.interfaces.dto.travel;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("出差记录搜索")
public class EmpTravelReqDto extends ExportBasePage {

    @ApiModelProperty("关键字出差记录名称搜索")
    private String keywords;

    private String belongOrgId;

    private Long empId;

    private Long entityId;

    private Long startDateTime;

    private Long endDateTime;

    private Integer[] status;

    private boolean ifBatch;
}
