<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaTravelTypeMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType">
    <id column="travel_type_id" jdbcType="BIGINT" property="travelTypeId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="travel_type_name" jdbcType="VARCHAR" property="travelTypeName" />
    <result column="acct_time_type" jdbcType="INTEGER" property="acctTimeType" />
    <result column="round_time_unit" jdbcType="REAL" property="roundTimeUnit" />
    <result column="if_include_non_workday" jdbcType="INTEGER" property="ifIncludeNonWorkday" />
    <result column="if_upload_file" jdbcType="INTEGER" property="ifUploadFile" />
    <result column="if_write_remark" jdbcType="INTEGER" property="ifWriteRemark" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="overtime_rule" jdbcType="BIGINT" property="overtimeRule" />
    <result column="auto_transfer_rule" jdbcType="VARCHAR" property="autoTransferRule" />
    <result column="revoke_workflow" jdbcType="BIT" property="revokeWorkflow" />
    <result column="revoke_passed" jdbcType="BIT" property="revokePassed" />
    <result column="revoke_allow_status" jdbcType="VARCHAR" property="revokeAllowStatus" />
    <result column="i18n_travel_type_name" jdbcType="VARCHAR" property="i18nTravelTypeName" />
    <result column="travel_type_def" jdbcType="INTEGER" property="travelTypeDef" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
  </resultMap>
  <sql id="Base_Column_List">
    travel_type_id, tenant_id, travel_type_name, acct_time_type, round_time_unit, if_include_non_workday,
    if_upload_file, if_write_remark, remark, deleted, create_by, create_time, update_by,
    update_time,overtime_rule,auto_transfer_rule,revoke_workflow,revoke_passed,revoke_allow_status,i18n_travel_type_name, travel_type_def, sort_num
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wa_travel_type
    where travel_type_id = #{travelTypeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_travel_type
    where travel_type_id = #{travelTypeId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType">
    insert into wa_travel_type (travel_type_id, tenant_id, travel_type_name,
    acct_time_type, round_time_unit, if_include_non_workday,
    if_upload_file, if_write_remark, remark,
    deleted, create_by, create_time,
    update_by, update_time,overtime_rule,auto_transfer_rule,revoke_workflow,revoke_passed,revoke_allow_status,
    i18n_travel_type_name, travel_type_def, sort_num)
    values (#{travelTypeId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{travelTypeName,jdbcType=VARCHAR},
    #{acctTimeType,jdbcType=INTEGER}, #{roundTimeUnit,jdbcType=REAL}, #{ifIncludeNonWorkday,jdbcType=INTEGER},
    #{ifUploadFile,jdbcType=INTEGER}, #{ifWriteRemark,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
    #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
    #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{overtimeRule,jdbcType=INTEGER},
    #{autoTransferRule,jdbcType=VARCHAR},#{revokeWorkflow,jdbcType=BIT},#{revokePassed,jdbcType=BIT},
    #{revokeAllowStatus,jdbcType=VARCHAR},#{i18nTravelTypeName,jdbcType=VARCHAR}, #{travelTypeDef,jdbcType=INTEGER},
    #{sortNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType">
    insert into wa_travel_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="travelTypeId != null">
        travel_type_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="travelTypeName != null">
        travel_type_name,
      </if>
      <if test="acctTimeType != null">
        acct_time_type,
      </if>
      <if test="roundTimeUnit != null">
        round_time_unit,
      </if>
      <if test="ifIncludeNonWorkday != null">
        if_include_non_workday,
      </if>
      <if test="ifUploadFile != null">
        if_upload_file,
      </if>
      <if test="ifWriteRemark != null">
        if_write_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="overtimeRule != null">
        overtime_rule,
      </if>
      <if test="autoTransferRule != null">
        auto_transfer_rule,
      </if>
      <if test="revokeWorkflow != null">
        revoke_workflow,
      </if>
      <if test="revokePassed != null">
        revoke_passed,
      </if>
      <if test="revokeAllowStatus != null">
        revoke_allow_status,
      </if>
      <if test="i18nTravelTypeName != null">
        i18n_travel_type_name,
      </if>
      <if test="travelTypeDef != null">
        travel_type_def,
      </if>
      <if test="sortNum != null">
        sort_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="travelTypeId != null">
        #{travelTypeId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="travelTypeName != null">
        #{travelTypeName,jdbcType=VARCHAR},
      </if>
      <if test="acctTimeType != null">
        #{acctTimeType,jdbcType=INTEGER},
      </if>
      <if test="roundTimeUnit != null">
        #{roundTimeUnit,jdbcType=REAL},
      </if>
      <if test="ifIncludeNonWorkday != null">
        #{ifIncludeNonWorkday,jdbcType=INTEGER},
      </if>
      <if test="ifUploadFile != null">
        #{ifUploadFile,jdbcType=INTEGER},
      </if>
      <if test="ifWriteRemark != null">
        #{ifWriteRemark,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="overtimeRule != null">
        #{overtimeRule,jdbcType=INTEGER},
      </if>
      <if test="autoTransferRule != null">
        #{autoTransferRule,jdbcType=VARCHAR},
      </if>
      <if test="revokeWorkflow != null">
        #{revokeWorkflow,jdbcType=BIT},
      </if>
      <if test="revokePassed != null">
        #{revokePassed,jdbcType=BIT},
      </if>
      <if test="revokeAllowStatus != null">
        #{revokeAllowStatus,jdbcType=VARCHAR},
      </if>
      <if test="i18nTravelTypeName != null">
        #{i18nTravelTypeName,jdbcType=VARCHAR},
      </if>
      <if test="travelTypeDef != null">
        #{travelTypeDef,jdbcType=INTEGER},
      </if>
      <if test="sortNum != null">
        #{sortNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType">
    update wa_travel_type
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="travelTypeName != null">
        travel_type_name = #{travelTypeName,jdbcType=VARCHAR},
      </if>
      <if test="acctTimeType != null">
        acct_time_type = #{acctTimeType,jdbcType=INTEGER},
      </if>
      <if test="roundTimeUnit != null">
        round_time_unit = #{roundTimeUnit,jdbcType=REAL},
      </if>
      <if test="ifIncludeNonWorkday != null">
        if_include_non_workday = #{ifIncludeNonWorkday,jdbcType=INTEGER},
      </if>
      <if test="ifUploadFile != null">
        if_upload_file = #{ifUploadFile,jdbcType=INTEGER},
      </if>
      <if test="ifWriteRemark != null">
        if_write_remark = #{ifWriteRemark,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="overtimeRule != null">
        overtime_rule = #{overtimeRule,jdbcType=INTEGER},
      </if>
      <if test="autoTransferRule != null">
        auto_transfer_rule = #{autoTransferRule,jdbcType=VARCHAR},
      </if>
      <if test="revokeWorkflow != null">
        revoke_workflow = #{revokeWorkflow,jdbcType=BIT},
      </if>
      <if test="revokePassed != null">
        revoke_passed = #{revokePassed,jdbcType=BIT},
      </if>
      <if test="revokeAllowStatus != null">
        revoke_allow_status = #{revokeAllowStatus,jdbcType=VARCHAR},
      </if>
      <if test="i18nTravelTypeName != null">
        i18n_travel_type_name = #{i18nTravelTypeName,jdbcType=VARCHAR},
      </if>
      <if test="travelTypeDef != null">
        travel_type_def = #{travelTypeDef,jdbcType=INTEGER},
      </if>
      <if test="sortNum != null">
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
    </set>
    where travel_type_id = #{travelTypeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType">
    update wa_travel_type
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
    travel_type_name = #{travelTypeName,jdbcType=VARCHAR},
    acct_time_type = #{acctTimeType,jdbcType=INTEGER},
    round_time_unit = #{roundTimeUnit,jdbcType=REAL},
    if_include_non_workday = #{ifIncludeNonWorkday,jdbcType=INTEGER},
    if_upload_file = #{ifUploadFile,jdbcType=INTEGER},
    if_write_remark = #{ifWriteRemark,jdbcType=INTEGER},
    remark = #{remark,jdbcType=VARCHAR},
    deleted = #{deleted,jdbcType=INTEGER},
    create_by = #{createBy,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=BIGINT},
    update_by = #{updateBy,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=BIGINT},
    overtime_rule = #{overtimeRule,jdbcType=INTEGER},
    auto_transfer_rule = #{autoTransferRule,jdbcType=VARCHAR},
    revoke_workflow = #{revokeWorkflow,jdbcType=BIT},
    revoke_passed = #{revokePassed,jdbcType=BIT},
    revoke_allow_status = #{revokeAllowStatus,jdbcType=VARCHAR},
    i18n_travel_type_name = #{i18nTravelTypeName,jdbcType=VARCHAR},
    travel_type_def = #{travelTypeDef,jdbcType=INTEGER},
    sort_num = #{sortNum,jdbcType=INTEGER}
    where travel_type_id = #{travelTypeId,jdbcType=BIGINT}
  </update>

  <select id="selectTravelTypePageList"
          resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType">
      SELECT <include refid="Base_Column_List" />
      FROM wa_travel_type
      WHERE tenant_id = #{tenantId,jdbcType=VARCHAR} AND deleted=0
      <if test="keywords != null and keywords != ''">
        AND emp.travel_type_name like concat('%', #{keywords}, '%')
      </if>
  </select>
</mapper>