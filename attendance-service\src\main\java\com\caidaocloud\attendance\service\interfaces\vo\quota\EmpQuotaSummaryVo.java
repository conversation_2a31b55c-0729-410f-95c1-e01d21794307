package com.caidaocloud.attendance.service.interfaces.vo.quota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 员工假期配额汇总VO
 *
 * <AUTHOR>
 * @Date 2024/6/20
 */
@Data
@ApiModel("员工假期配额汇总VO")
public class EmpQuotaSummaryVo {
    @ApiModelProperty("本年配额")
    private Float totalQuota;
    @ApiModelProperty("本年已用")
    private Float totalUsed;
    @ApiModelProperty("本年剩余")
    private Float totalSurplus;
    @ApiModelProperty("申请中")
    private Float totalInTransit;
    @ApiModelProperty("单位")
    private Integer unit;
    @ApiModelProperty("单位文本")
    private String unitTxt;
    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;
    @ApiModelProperty("假期类型名称")
    private String leaveTypeName;
}
