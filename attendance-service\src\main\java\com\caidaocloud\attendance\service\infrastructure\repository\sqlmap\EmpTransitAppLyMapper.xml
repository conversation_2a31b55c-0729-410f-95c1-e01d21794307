<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpTransitAppLyMapper">
    <select id="getEmpTransitAppLyList" resultType="com.caidaocloud.attendance.service.domain.entity.EmpTransitAppLyDo">
      select * from (
       select
           a.leave_id            id,
           2                     eventType,
           c.leave_name          eventName,
           a.crttime             applyTime,
           (CASE WHEN (welt.period_type = 1 or welt.period_type = 4) THEN to_char(to_timestamp(coalesce(welt.shift_start_time, welt.start_time)), 'yyyy-MM-dd')
                 WHEN welt.period_type = 9 THEN to_char(to_timestamp(coalesce(welt.start_time,welt.shift_start_time)), 'yyyy-MM-dd') || CASE WHEN welt.shalf_day = 'A' THEN '上半天' ELSE '下半天' END
                 ELSE to_char(to_timestamp(coalesce(welt.shift_start_time, welt.start_time)),'yyyy-MM-dd hh24:mi') END) AS startTime,
           (CASE WHEN (welt.period_type = 1 or welt.period_type = 4) THEN to_char(to_timestamp(coalesce(welt.shift_end_time, welt.end_time)),'yyyy-MM-dd')
                 WHEN welt.period_type = 9 THEN to_char(to_timestamp(coalesce(welt.end_time,welt.shift_end_time)), 'yyyy-MM-dd') || CASE WHEN welt.ehalf_day = 'A' THEN '上半天' ELSE '下半天' END
                 ELSE to_char(to_timestamp(coalesce(welt.shift_end_time, welt.end_time)),'yyyy-MM-dd hh24:mi') END) AS endTime,
           (CASE a.time_unit WHEN 1 THEN a.total_time_duration || '天' ELSE a.total_time_duration / 60 || '小时' END) duration
       from wa_emp_leave a
         JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted = 0
         JOIN wa_leave_type c ON a.leave_type_id = c.leave_type_id and c.is_emp_show=true
         JOIN wa_emp_leave_time welt on welt.leave_id = A.leave_id
         where b.empid = #{empId}
         and b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
         and a.status = 1
       union all
      select
            a.ot_id                      id,
            1                            eventType,
            case when a.date_type = 1 then '工作日加班'
            when a.date_type = 2 then '休息日加班'
            when a.date_type = 3 then '法定节假日加班'
            when a.date_type = 4 then '特殊休日加班'
            end         eventName,
            a.crttime     applyTime,
            to_char(to_timestamp(a.start_time), 'yyyy-MM-dd hh24:mi') AS startTime,
            to_char(to_timestamp(a.end_time), 'yyyy-MM-dd hh24:mi')   AS endTime,
            a.ot_duration / 60 || '小时' || a.ot_duration % 60 || '分钟' AS duration
            from wa_emp_overtime a
            JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted = 0
            where a.empid = #{empId}
            and b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            and a.status = 1
        union all
          select
            a.travel_id              id,
            121                      eventType,
            b.travel_type_name       eventName,
            a.create_time            applyTime,
            (CASE WHEN (a.period_type = 1 or a.period_type = 4) THEN to_char(to_timestamp(coalesce(a.shift_start_time, a.start_time)), 'yyyy-MM-dd')
                  WHEN a.period_type = 9 THEN to_char(to_timestamp(coalesce(a.shift_start_time,a.start_time)), 'yyyy-MM-dd') || CASE WHEN a.shalf_day = 'A' THEN '上半天' ELSE '下半天' END
                  ELSE to_char(to_timestamp(coalesce(a.shift_start_time, a.start_time)),'yyyy-MM-dd hh24:mi') END) AS startTime,
	        (CASE WHEN (a.period_type = 1 or a.period_type = 4) THEN to_char(to_timestamp(coalesce(a.shift_end_time, a.end_time)),'yyyy-MM-dd')
                  WHEN a.period_type = 9 THEN to_char(to_timestamp(coalesce(a.shift_end_time,a.end_time)), 'yyyy-MM-dd') || CASE WHEN a.ehalf_day = 'A' THEN '上半天' ELSE '下半天' END
	              ELSE to_char(to_timestamp(coalesce(a.shift_end_time, a.end_time)),'yyyy-MM-dd hh24:mi') END) AS endTime,
            (CASE a.time_unit WHEN 1 THEN a.time_duration || '天' ELSE a.time_duration / 60 || '小时' END)          duration
            from wa_emp_travel a
            join wa_travel_type b
          on a.travel_type_id = b.travel_type_id
            where a.emp_id = #{empId}
            and a.tenant_id = #{belongOrgId,jdbcType=VARCHAR}
            and a.status = 1
            and a.deleted = 0
        union all
          select
            record_id                   id,
            41                          eventType,
            '补卡'                       enventName,
            crttime                     applyTime,
            to_char(to_timestamp(reg_date_time), 'yyyy-MM-dd hh24:mi')    startTime,
            null                        endTime,
            null                        duration
          from wa_register_record_bdk
          where empid = #{empId}
          and belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
          and type = 6
          and approval_status = 1
        union all
         select
            rec_id                    id,
            278                       eventType,
            '调班'                     enventName,
            create_time               applyTime,
            to_char(to_timestamp(work_date), 'yyyy-MM-dd')    startTime,
            null                        endTime,
            null                        duration
          from wa_shift_apply_record
          where emp_id = #{empId}
          and tenant_id = #{belongOrgId,jdbcType=VARCHAR}
          and status = 1
        union all
         select
           a.leave_cancel_id            id,
           279                   eventType,
           '销假'                 eventName,
           a.create_time         applyTime,
           (select
           (CASE WHEN (period_type = 1 or period_type = 4) THEN to_char(to_timestamp(coalesce(start_time, shift_start_time)), 'yyyy-MM-dd')
                 WHEN period_type = 9 THEN to_char(to_timestamp(coalesce(start_time, shift_start_time)), 'yyyy-MM-dd') || CASE WHEN shalf_day = 'A' THEN '上半天' ELSE '下半天' END
                 ELSE to_char(to_timestamp(coalesce(shift_start_time, start_time)),'yyyy-MM-dd hh24:mi') END) AS startTime
           from wa_emp_leave_cancel_info where leave_cancel_id= a.leave_cancel_id order by shift_end_time asc limit 1),
           (select
           (CASE WHEN (period_type = 1 or period_type = 4) THEN to_char(to_timestamp(coalesce(end_time,shift_end_time)), 'yyyy-MM-dd')
                 WHEN period_type = 9 THEN to_char(to_timestamp(coalesce(end_time, shift_end_time)), 'yyyy-MM-dd') || CASE WHEN ehalf_day = 'A' THEN '上半天' ELSE '下半天' END
                 ELSE to_char(to_timestamp(coalesce(shift_end_time, end_time)),'yyyy-MM-dd hh24:mi') END) AS endTime
           from wa_emp_leave_cancel_info where leave_cancel_id= a.leave_cancel_id order by shift_end_time asc limit 1),
           CASE WHEN a.type_id=2 THEN
            (CASE a.time_unit WHEN 1 THEN a.time_duration || '天' ELSE a.time_duration / 60 || '小时' END) ELSE '-' END as duration
       from wa_emp_leave_cancel a
         JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted = 0
         where b.empid = #{empId}
         and b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
         and a.status = 1
      ) as t
    </select>
</mapper>