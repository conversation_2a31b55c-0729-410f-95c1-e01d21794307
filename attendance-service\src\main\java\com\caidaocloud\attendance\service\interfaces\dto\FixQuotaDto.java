package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FixQuotaDto {
    @ApiModelProperty("主键")
    private Long empQuotaId;
    // @ApiModelProperty("员工ID")
    // private Long empid;
    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("假期类型ID")
    private Long leaveTypeId;//假期类型
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("可用")
    private Float quotaDay;
    @ApiModelProperty("调整")
    private Float adjustQuota;
    @ApiModelProperty("备注")
    private String remarks;
}
