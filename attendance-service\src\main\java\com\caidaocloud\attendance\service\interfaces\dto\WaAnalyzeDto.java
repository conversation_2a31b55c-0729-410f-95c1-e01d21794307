package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WaAnalyzeDto {

    @ApiModelProperty("员工号")
    private String workNo;
    @ApiModelProperty("所属日期")
    private Long belongDate;
    @ApiModelProperty("所属班次")
    private String shiftDefName;
    @ApiModelProperty("打卡类型")
    private Integer clockType;
    @ApiModelProperty("打卡规则")
    private String clockRule;
    @ApiModelProperty("应出勤时长")
    private Integer workTime;
    @ApiModelProperty("实出勤时长")
    private Float actualWorkTime;
    @ApiModelProperty("上班打卡时间")
    private Integer startTime;
    @ApiModelProperty("下班打卡时间")
    private Integer endTime;
    @ApiModelProperty("最早上班打卡时间")
    private Integer onDutyStartTime;
    @ApiModelProperty("最晚上班打卡时间")
    private Integer onDutyEndTime;
    @ApiModelProperty("最早下班打卡时间")
    private Integer offDutyStartTime;
    @ApiModelProperty("最晚下班打卡时间")
    private Integer offDutyEndTime;
    @ApiModelProperty("签到时间")
    private Long regSignInTime;
    @ApiModelProperty("签退时间")
    private Long regSignOffTime;
    @ApiModelProperty("上班打卡结果")
    private Integer regSignInResult;
    @ApiModelProperty("下班打卡结果")
    private Integer regSignOffResult;
    @ApiModelProperty("上班补卡结果")
    private Long makeUpSignTime;
    @ApiModelProperty("下班补卡结果")
    private Long makeOffSignTime;
    private Integer signinId;
    private Integer signoffId;
    private String belongOrgId;
    private Long empid;
    private Float lateTime;
    private Float earlyTime;
    private Integer kgWorkTime;
}
