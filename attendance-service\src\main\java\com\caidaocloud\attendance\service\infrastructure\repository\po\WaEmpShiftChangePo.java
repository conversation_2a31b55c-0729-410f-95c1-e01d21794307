package com.caidaocloud.attendance.service.infrastructure.repository.po;

public class WaEmpShiftChangePo {
    private Integer recId;

    private String belongOrgId;

    private Long empid;

    private Long workDate;

    private Integer oldShiftDefId;

    private Integer newShiftDefId;

    private String remark;

    private Integer status;

    private Long crtuser;

    private Long crttime;

    private Long upduser;

    private Long updtime;

    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }

    public String getBelongOrgId() {
        return belongOrgId;
    }

    public void setBelongOrgId(String belongOrgId) {
        this.belongOrgId = belongOrgId;
    }

    public Long getEmpid() {
        return empid;
    }

    public void setEmpid(Long empid) {
        this.empid = empid;
    }

    public Long getWorkDate() {
        return workDate;
    }

    public void setWorkDate(Long workDate) {
        this.workDate = workDate;
    }

    public Integer getOldShiftDefId() {
        return oldShiftDefId;
    }

    public void setOldShiftDefId(Integer oldShiftDefId) {
        this.oldShiftDefId = oldShiftDefId;
    }

    public Integer getNewShiftDefId() {
        return newShiftDefId;
    }

    public void setNewShiftDefId(Integer newShiftDefId) {
        this.newShiftDefId = newShiftDefId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCrtuser() {
        return crtuser;
    }

    public void setCrtuser(Long crtuser) {
        this.crtuser = crtuser;
    }

    public Long getCrttime() {
        return crttime;
    }

    public void setCrttime(Long crttime) {
        this.crttime = crttime;
    }

    public Long getUpduser() {
        return upduser;
    }

    public void setUpduser(Long upduser) {
        this.upduser = upduser;
    }

    public Long getUpdtime() {
        return updtime;
    }

    public void setUpdtime(Long updtime) {
        this.updtime = updtime;
    }
}