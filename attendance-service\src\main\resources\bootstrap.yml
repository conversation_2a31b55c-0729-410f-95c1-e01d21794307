spring:
  profiles:
    active: dev

---
spring:
  profiles: test
nacos:
  config:
    type: yaml
    server-addr: 106.52.150.12:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: herman
    bootstrap:
      enable: true
      log:
        enable: true

---
spring:
  profiles: local
nacos:
  config:
    type: yaml
    server-addr: 106.52.150.12:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: herman
    bootstrap:
      enable: true
      log:
        enable: true

---
spring:
  profiles: dev
nacos:
  config:
    type: yaml
#    server-addr: 192.168.120.202:8848
    server-addr: 124.71.192.140:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: cd2
    bootstrap:
      enable: true
      log:
        enable: true

---
spring:
  profiles: fat
nacos:
  config:
    type: yaml
    server-addr: 172.16.0.141:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: cd2
    bootstrap:
      enable: true
      log:
        enable: true

---
spring:
  profiles: txdev
nacos:
  config:
    type: yaml
    server-addr: 10.0.0.30:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: cd2
    bootstrap:
      enable: true
      log:
        enable: true