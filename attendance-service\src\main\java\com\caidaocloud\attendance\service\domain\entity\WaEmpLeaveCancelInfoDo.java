package com.caidaocloud.attendance.service.domain.entity;

import com.caidaocloud.attendance.service.domain.repository.IWaEmpLeaveCancelInfoRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Data
@Service
public class WaEmpLeaveCancelInfoDo {
    private Long leaveCancelInfoId;

    private Long leaveCancelId;

    private Long startTime;

    private Long endTime;

    private String shalfDay;

    private String ehalfDay;

    private Short periodType;

    private Integer timeUnit;

    private Float timeDuration;

    private Long shiftStartTime;

    private Long shiftEndTime;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    @Autowired
    private IWaEmpLeaveCancelInfoRepository waEmpLeaveCancelInfoRepository;

    public Integer checkTimeRepeat(Long empId, Integer leaveId, Long startTime, Long endTime) {
        return waEmpLeaveCancelInfoRepository.checkTimeRepeat(empId, leaveId, startTime, endTime);
    }

    public void save(WaEmpLeaveCancelInfoDo cancelInfoDo) {
        waEmpLeaveCancelInfoRepository.save(cancelInfoDo);
    }

    public List<WaEmpLeaveCancelInfoDo> getLeaveCancelInfoList(Long leaveCancelId) {
        return waEmpLeaveCancelInfoRepository.getLeaveCancelInfoList(leaveCancelId);
    }

    public List<WaEmpLeaveCancelInfoDo> getListByLeaveCancelId(List<Long> leaveCancelIds) {
        return waEmpLeaveCancelInfoRepository.getListByLeaveCancelId(leaveCancelIds);
    }
}