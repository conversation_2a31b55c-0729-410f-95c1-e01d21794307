package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.*;
import com.caidao1.ext.entity.SysDynamicColumns;
import com.caidao1.ext.service.ISysDynamicColumnsService;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.commons.utils.TimeRangeCheckUtil;
import com.caidaocloud.attendance.core.mobile.utils.JsonTool;
import com.caidaocloud.attendance.core.wa.dto.*;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.RemoteSmartWorkTimeService;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.*;
import com.caidaocloud.attendance.service.application.dto.clock.ClockAnalyseDataCacheDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.event.publish.PaySyncWaPublish;
import com.caidaocloud.attendance.service.application.service.IClockSignService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpLeaveMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.OtRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaOvertimeTransferRuleMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaOvertimeTransferRulePo;
import com.caidaocloud.attendance.service.infrastructure.util.HandleMantissaUtil;
import com.caidaocloud.attendance.service.interfaces.dto.FlexibleDto;
import com.caidaocloud.dto.TimeSlot;
import com.caidaocloud.em.HalfDayTypeEnum;
import com.caidaocloud.em.OtValidTimeCalTypeEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考勤分析结果计算服务类
 *
 * <AUTHOR>
 * @Date 2021/9/6
 */
@Slf4j
@Service
public class AnalyzeResultCalculateService {
    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    private static final int PAGE_SIZE = 1000;

    /**
     * 考勤分析时是否启用打卡分析： true 启用、false 不启用
     */
    @Value("${caidaocloud.data.waanalyze.enableclockparse:false}")
    private boolean enableclockparse;
    /**
     * 考勤分析时，请假、出差、加班、补卡 数据是否包含审批中的数据，默认不包含
     */
    @Value("${caidaocloud.data.waanalyze.all:false}")
    private boolean waanalyzeAll;
    /**
     * 考勤分析-休假分析新逻辑开启配置：true 开启 false 不开启
     */
    @Value("${caidaocloud.data.ltanalyze:false}")
    private boolean ltanalyze;
    @Value("${caidaocloud.data.newLeaveDataAnalyzeLogic:true}")
    private boolean newLeaveDataAnalyzeLogic;
    @Value("${caidaocloud.data.newTravelDataAnalyzeLogic:true}")
    private boolean newTravelDataAnalyzeLogic;
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private WaAnalyzeMapper waAnalyzeMapper;
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private EmpLeaveMapper empLeaveMapper;
    @Autowired
    private OtRecordMapper otRecordMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaShiftCustomizedMapper waShiftCustomizedMapper;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private ISysDynamicColumnsService iSysDynamicColumnsService;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private WaRegisterRecordDo waRegisterRecordDo;
    @Autowired
    private WaTravelTypeDo waTravelTypeDo;
    @Autowired
    private WaEmpTravelDaytimeDo waEmpTravelDaytimeDo;
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Resource
    private PaySyncWaPublish paySyncWaPublish;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private WaEmpLeaveCancelService empLeaveCancelService;
    @Resource
    private WaOvertimeTransferRuleMapper overtimeTransferRuleMapper;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Autowired
    private IClockSignService clockSignService;

    /**
     * 考勤分析
     *
     * @param belongId
     * @param startDate
     * @param endDate
     * @param tmType
     * @param empIdList
     * @param parseGroup
     * @param waSob
     * @param userId
     * @return
     * @throws Exception
     */
    public int analyzeStep1(String belongId, Long startDate, Long endDate, Integer tmType,
                            List<Long> empIdList, WaParseGroup parseGroup, WaSob waSob, Long userId, boolean isJob, boolean includeInProgress) throws Exception {
        Integer row = 0;
        if (CollectionUtils.isNotEmpty(empIdList)) {
            int len = 300;
            int size = empIdList.size();
            int num = size / len;
            if (size % len != 0) {
                num++;
            }
            log.info("===tmType=" + tmType + " 分" + num + "次执行");
            int fromIndex = 0;
            for (int i = 1; i <= num; i++) {
                int toIndex = i * len;
                if (toIndex > size) {
                    toIndex = size;
                }
                Long[] empIdArrs = empIdList.subList(fromIndex, toIndex).toArray(new Long[]{});
                fromIndex = toIndex;
                AnalyzeResultCalculateDto calculateDto = new AnalyzeResultCalculateDto();
                calculateDto.setBelongid(belongId);
                calculateDto.setStartDate(startDate);
                calculateDto.setEndDate(endDate);
                calculateDto.setEmpids(empIdArrs);
                calculateDto.setTmType(tmType);
                calculateDto.setParseGroup(parseGroup);
                calculateDto.setWaSob(waSob);
                calculateDto.setUserId(userId);
                calculateDto.setJob(isJob);
                calculateDto.setIncludeInProgress(includeInProgress);
                row += SpringUtil.getBean(AnalyzeResultCalculateService.class).analysisWaRegisterStep(calculateDto);
                log.info("===执行到第" + i + "批完成");
            }
        }
        return row;
    }

    /**
     * 考勤分析
     *
     * @param calculateDto
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer analysisWaRegisterStep(AnalyzeResultCalculateDto calculateDto) throws Exception {
        log.info("analysisWaRegisterStep start time={}", System.currentTimeMillis());
        Long[] empIds = calculateDto.getEmpids();
        Long startDate = calculateDto.getStartDate();
        Long endDate = calculateDto.getEndDate();
        String belongid = calculateDto.getBelongid();
        WaParseGroup parseGroup = calculateDto.getParseGroup();
        WaSob waSob = calculateDto.getWaSob();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("belongid", belongid);
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        if (parseGroup != null) {
            Boolean ignoreLocationExp = parseGroup.getIgnoreLocationExp();
            paramsMap.put("ignoreLocationExp", ignoreLocationExp);// 是否忽略地点异常的签到签退数据
            paramsMap.put("registerMiss", parseGroup.getRegisterMiss());
            paramsMap.put("clockType", parseGroup.getClockType());
            paramsMap.put("allowedDateType", parseGroup.getAllowedDateType());
        } else {
            paramsMap.put("registerMiss", 2);// 默认当旷工
            paramsMap.put("clockType", 2);//默认二次卡
        }
        String anyEmpIds = null;
        if (empIds != null && empIds.length > 0) {
            anyEmpIds = "'{" + StringUtils.join(empIds, ",") + "}'";
            paramsMap.put("anyEmpids2", anyEmpIds);
        }
        Integer ymdstart = Integer.valueOf(DateUtil.parseDateToPattern(new Date(startDate * 1000), "yyyyMMdd"));
        Integer ymstart = Integer.valueOf(ymdstart.toString().substring(0, 6));
        paramsMap.put("ymstart", ymstart);
        paramsMap.put("ymdstart", ymdstart);
        Integer ymdend = Integer.valueOf(DateUtil.parseDateToPattern(new Date(endDate * 1000), "yyyyMMdd"));
        Integer ymend = Integer.valueOf(ymdend.toString().substring(0, 6));
        paramsMap.put("ymend", ymend);
        paramsMap.put("ymdend", ymdend);
        paramsMap.put("crtuser", 0L);
        paramsMap.put("crttime", DateUtil.getCurrentTime(true));

        Map<Long, Object> empIdMaps = new HashMap<>();
        if (empIds != null && empIds.length > 0) {
            for (Long empId : empIds) {
                empIdMaps.put(empId, null);
            }
        }
        StopWatch st = new StopWatch();
        st.start("Query clock-in data");
        log.info("Query clock-in data start time={}", System.currentTimeMillis());
        // 查询员工打卡记录
        List<WaRegisterRecordDo> recordDoList = this.getAllRegisterRecordList(calculateDto);
        if (CollectionUtils.isNotEmpty(recordDoList)) {
            recordDoList.forEach(o -> empIdMaps.put(o.getEmpid(), o.getShiftDefId()));
        }
        st.stop();
        log.info("Query clock-in data end time={}", System.currentTimeMillis());
        log.info("Query clock-in data: {}", recordDoList.size());
        if (empIdMaps.size() > 0) {
            String anyEmpid2 = "'{" + StringUtils.join(empIdMaps.keySet(), ",").concat("}'");
            paramsMap.put("anyEmpids", anyEmpid2);
        } else {
            paramsMap.remove("anyEmpids");
        }
        st.start("Query basic data");
        log.info("Query basic data start time={}", System.currentTimeMillis());
        // 获取考勤信息和考勤分析分组信息
        WaAnalyzCalDTO dto = this.getWaAnalyzeInfo(paramsMap);
        st.stop();
        log.info("Query basic data end time={}", System.currentTimeMillis());
        dto.setJob(calculateDto.isJob());

        // 打卡类型区分
        Map<String, WaRegisterRecord> singIns = new HashMap<>();// 二次卡签到
        Map<String, WaRegisterRecord> singOffs = new HashMap<>();// 二次卡签退
        Map<String, List<WaRegisterRecord>> signOnceRecs = new HashMap<>();// 一次卡
        Map<String, List<WaRegisterRecord>> signZeroRecs = new HashMap<>();// 不打卡
        Map<String, List<WaRegisterRecord>> outRecs = new HashMap<>();// 外勤打卡
        if (CollectionUtils.isNotEmpty(recordDoList)) {
            st.start("Analyze clock-in data");
            log.info("Analyze clock-in data start time={}", System.currentTimeMillis());

            List<WaRegisterRecord> registerRecordList = ObjectConverter.convertList(recordDoList, WaRegisterRecord.class);
            this.checkAndAnalyzeRegisterRecord(belongid, registerRecordList, dto);

            Map<String, List<WaRegisterRecord>> empBelongDateRegListMap = registerRecordList.stream().collect(Collectors.groupingBy(o -> String.format("%s_%s", o.getEmpid(), o.getBelongDate())));
            dto.setEmpBelongDateRegListMap(empBelongDateRegListMap);

            st.stop();
            log.info("Analyze clock-in data end time={}", System.currentTimeMillis());

            st.start("Combine employee clock-in data based on attendance rules");
            log.info("Combine employee clock-in data based on attendance rules start time={}", System.currentTimeMillis());

            this.getRegisterRecordByClockType(empBelongDateRegListMap, dto, singIns, singOffs, signOnceRecs, signZeroRecs, outRecs);

            st.stop();
            log.info("Combine employee clock-in data based on attendance rules end time={}", System.currentTimeMillis());
        }
        dto.setSignOnceRegisterRecordMap(signOnceRecs);
        dto.setSignZeroRegisterRecordMap(signZeroRecs);
        dto.setWaSob(waSob);

        //休假单是否不参加考勤分析
        paramsMap.put("leaveTypeIsNotAnalyze", false);
        // 休假单是否包含审批中单据
        paramsMap.put("includeInProgress", calculateDto.isIncludeInProgress());
        // 如果按弹性工作时间进行分析，先取出请假数据进行分析
        if (parseGroup != null && BooleanUtils.isTrue(parseGroup.getIsFlexibleWorking())) {
            dto.setEmpLeaveInfo(this.getEmpLeaveList(paramsMap));
        }
        // 新字段，弹性分析开关是否开启
        if (parseGroup != null && FlexibleEnum.OPEN.getIndex().equals(parseGroup.getFlexibleWorkSwitch())) {
            if (FlexibleRuleEnum.SHIFT_ANALYZE.getIndex().equals(parseGroup.getFlexibleWorkType())) {
                dto.setEmpLeaveInfo(this.getEmpLeaveList(paramsMap));
                //查询出差单据
                if (dto.getEmpTravelInfoList() == null && !StringUtil.isEmptyOrNull(anyEmpIds)) {
                    List<WaEmpTravelDaytimeDo> empTravelList = getEmpTravelList(belongid, anyEmpIds, startDate, endDate);
                    dto.setEmpTravelInfo(empTravelList);
                }
            }
        }
        dto.setBelongid(belongid);
        dto.setUserId(calculateDto.getUserId() == null ? 0 : calculateDto.getUserId());
        log.info("Analyze external clock-in");
        // 考勤分析结果
        List<WaAnalyze> resultWa = new ArrayList<>();
        st.start("Analyze external clock-in");
        log.info("External clock-in records count: {}", outRecs.size());
        // 外勤打卡分析
        List<WaAnalyze> outSignResult = this.analyzeOutSignResult(outRecs, dto);
        List<String> outSignKey = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(outSignResult)) {
            resultWa.addAll(outSignResult);
            outSignKey = outSignResult.stream().map(o -> String.join("_", String.valueOf(o.getEmpid()), String.valueOf(o.getBelongDate()))).collect(Collectors.toList());
        }
        st.stop();
        log.info("Analyze secondary clock-in data");
        st.start("Analyze secondary clock-in data");
        Map<String, Map<String, Object>> excludeFieldRecordMap = new HashMap<>();
        // 二次卡数据分析
        if (MapUtils.isNotEmpty(singIns) || MapUtils.isNotEmpty(singOffs)) {
            List<WaAnalyze> analyzeList = this.analyzeResult(singIns, singOffs, dto, paramsMap, excludeFieldRecordMap);
            if (CollectionUtils.isNotEmpty(analyzeList)) {
                for (WaAnalyze analyze : analyzeList) {
                    String key = String.format("%s_%s", analyze.getEmpid(), analyze.getBelongDate());
                    if (outSignKey.contains(key)) {
                        continue;
                    }
                    // 中途卡分析
                    analyzeMidwayClock(analyze, dto);
                    resultWa.add(analyze);
                }
            }
        }
        st.stop();
        log.info("Analyze single clock-in data");
        st.start("Analyze single clock-in data");
        // 一次卡数据分析
        if (MapUtils.isNotEmpty(signOnceRecs)) {
            List<WaAnalyze> analyzeList = this.analyzeOneCardResult(signOnceRecs, dto, excludeFieldRecordMap);
            if (CollectionUtils.isNotEmpty(analyzeList)) {
                for (WaAnalyze analyze : analyzeList) {
                    String key = String.format("%s_%s", analyze.getEmpid(), analyze.getBelongDate());
                    if (!outSignKey.contains(key)) {
                        resultWa.add(analyze);
                    }
                }
            }
        }
        st.stop();
        dto.setWaAnalyzeList(resultWa);
        dto.setEmpOutRegMap(outRecs);
        log.info("Analyze business trip");
        st.start("Analyze business trip");
        // 出差分析
        if (newTravelDataAnalyzeLogic) {
            this.analyzeTravelDataNew(resultWa, paramsMap, dto);
        } else {
            this.analyzeTravelData(resultWa, paramsMap, dto);
        }
        st.stop();
        log.info("Analyze leave and overtime");
        st.start("Analyze leave and overtime");
        // 请假，加班分析
        this.analyzeLeaveOtData(paramsMap, dto, resultWa);
        st.stop();
        log.info("Set check-in and check-out");
        st.start("Set check-in and check-out");
        Set<String> keySet = excludeFieldRecordMap.keySet();
        for (WaAnalyze analyze : resultWa) {
            String key = String.format("%s_%s", analyze.getEmpid(), analyze.getBelongDate());
            if (keySet.contains(key)) {
                Map<String, Object> regMap = excludeFieldRecordMap.get(key);
                Integer signInId = (Integer) regMap.get("signInId");
                if (null != signInId) {
                    Long signInTime = (Long) regMap.get("signInTime");
                    analyze.setSigninId(signInId);
                    analyze.setRegSigninTime(signInTime);
                }
                Integer signOffId = (Integer) regMap.get("signOffId");
                if (null != signOffId) {
                    Long signOffTime = (Long) regMap.get("signOffTime");
                    analyze.setSignoffId(signOffId);
                    analyze.setRegSignoffTime(signOffTime);
                }
            }
        }
        st.stop();
        log.info("Exclude analysis results outside time range");
        st.start("Exclude analysis results outside time range");
        for (int i = resultWa.size() - 1; i >= 0; i--) {
            WaAnalyze d = resultWa.get(i);
            if (!(d.getBelongDate() >= startDate && d.getBelongDate() <= endDate)) {
                resultWa.remove(i);
            }
            empIdMaps.put(d.getEmpid(), d.getShiftDefId());
        }
        st.stop();
        if (!empIdMaps.isEmpty()) {
            String anyEmpid2 = empIdMaps.keySet().toString().replace("[", "'{").replace("]", "}'");
            paramsMap.put("anyEmpids", anyEmpid2);
        } else {
            paramsMap.remove("anyEmpids");
        }
        log.info("Record daily attendance rules for employees");
        st.start("Record daily attendance rules for employees");
        // 记录员工每天的出勤规则
        resultWa.forEach(analyze -> {
            if (analyze.getClockType() == null) {
                //查询员工考勤分析分组配置的出勤规则（考勤方案-分析规则-出勤规则），如未查到则默认二次卡出勤规则
                WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                if (analyzeInfo != null && analyzeInfo.getClock_type() != null) {
                    analyze.setClockType(analyzeInfo.getClock_type());
                } else {
                    analyze.setClockType(ParseGroupClockTypeEnum.SIGN_TWICE.getIndex());
                }
            }
        });
        st.stop();
        log.info("Time conversion");
        st.start("Time conversion");
        this.convertTimeToInteger(resultWa);
        st.stop();
        log.info("Save and update analysis results");
        st.start("Save and update analysis results");
        this.saveOrUpdateAnalyze(resultWa, belongid, 0L, paramsMap, dto);
        st.stop();
        Object clockType = paramsMap.get("clockType");
        dto.setClockType((Integer) clockType);
        if (empIds != null) {
            paramsMap.put("empIdList", new ArrayList<>(Arrays.asList(empIds)));
        }
        log.info("Generate absenteeism records");
        st.start("Generate absenteeism records");
        // 生成旷工记录
        this.absentEmpRecord(paramsMap, startDate, endDate, dto);
        st.stop();
        log.info("Process unscheduled employees with clock-in records");
        st.start("Process unscheduled employees with clock-in records");
        // 加入未排班但有签到记录的处理；
        this.saveNoShiftRecord(paramsMap);
        st.stop();
        log.info("Insert empty records for periods without data within calculation range");
        st.start("Insert empty records for periods without data within calculation range");
        //插入核算区间内 没有记录的数据，以空记录显示出来
        long currentime = DateUtil.getCurrentTime(true);
        if (calculateDto.isJob()) {
            currentime = DateUtil.addDate(currentime * 1000, -1);
        }
        if (startDate <= currentime) {
            StringBuffer dateRangeStr = new StringBuffer(startDate + "");
            for (long d = startDate; d <= endDate; ) {
                d = DateUtil.addDate(d * 1000, 1);
                if (d > currentime) {
                    break;
                }
                if (dateRangeStr.toString().length() > 0) {
                    dateRangeStr.append(",");
                }
                dateRangeStr.append(d);
            }
            paramsMap.put("dateRange", dateRangeStr);
            this.insertEmptyAnalyzeRecord(paramsMap);
        }
        st.stop();
        log.info("Sync actual overtime duration");
        st.start("Sync actual overtime duration");
        //同步实际加班时长
        Map<String, Float> relOtTimeDurationMap = dto.getRelOtTimeDurationMap();
        if (MapUtils.isNotEmpty(relOtTimeDurationMap)) {
            List<WaEmpOvertimeDetail> overtimeDetails = new ArrayList<>();
            relOtTimeDurationMap.forEach((k, v) -> {
                String ks[] = k.split("_");
                if (ks.length > 1) {
                    WaEmpOvertimeDetail detail = new WaEmpOvertimeDetail();
                    detail.setDetailId(Integer.valueOf(ks[1]));
                    detail.setRelTimeDuration(v);
                    overtimeDetails.add(detail);
                }
            });
            importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", overtimeDetails);
        }
        st.stop();
        log.info("Sync overtime conversion duration");
        st.start("Sync overtime conversion duration");
        //同步加班转换时长
        Map<String, String> transferMap = dto.getTransferMap();
        if (MapUtils.isNotEmpty(transferMap)) {
            List<WaEmpOvertimeDetail> overtimeDetails = new ArrayList<>();
            transferMap.forEach((k, v) -> {
                String ks[] = k.split("_");
                if (ks.length > 2) {
                    WaEmpOvertimeDetail otDetail = new WaEmpOvertimeDetail();
                    otDetail.setDetailId(Integer.valueOf(ks[1]));
                    String dur[] = v.split("_");
                    otDetail.setTransferDuration(Float.valueOf(dur[0]));
                    otDetail.setTransferUnit(Integer.valueOf(dur[1]));
                    overtimeDetails.add(otDetail);
                }
            });
            importService.fastUpdList(WaEmpOvertimeDetail.class, "detailId", overtimeDetails);
        }
        st.stop();
        log.info("Analysis completed");
        log.info("Attendance analyze time :{}", st.prettyPrint());
        return resultWa.size();
    }

    /**
     * 中途三次卡逻辑分析
     *
     * @param analyze
     * @param dto
     */
    private void analyzeMidwayClock(WaAnalyze analyze, WaAnalyzCalDTO dto) {
        // 检查员工班次是否开启中途三次卡逻辑，如果开启则检查中途是否打卡，如中途未打卡，则当天缺卡，走缺卡逻辑分析
        EmpShiftInfo shiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
        if (null == shiftDef || CollectionUtils.isEmpty(shiftDef.getMidwayClockTimeList())) {
            return;
        }
        TimeSlot midwayTimeSlot = shiftDef.getMidwayClockTimeList().get(0);
        Integer midwayStart = Optional.ofNullable(midwayTimeSlot.getStartTime()).orElse(0);
        Integer midwayEnd = Optional.ofNullable(midwayTimeSlot.getEndTime()).orElse(0);
        if (midwayStart > midwayEnd) {
            midwayEnd = midwayEnd + 1440;
        }

        long midwayStartTime = analyze.getBelongDate() + (midwayStart * 60);
        long midwayEndTime = analyze.getBelongDate() + (midwayEnd * 60);

        //查询员工当天所有的打卡记录（含外勤）
        List<WaRegisterRecord> empBelongDateRegList = dto.getEmpBelongDateRegListByDateEmpId(analyze.getEmpid(), analyze.getBelongDate());
        long midwayCount = empBelongDateRegList.stream().filter(o -> o.getRegDateTime() >= midwayStartTime
                && o.getRegDateTime() <= midwayEndTime).count();
        if (midwayCount <= 0) {
            // 员工当天对应的考勤分组及分析分组
            WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
            if (shiftDef.getDateType() == 1 && empAnalyzeInfo != null) {
                //打卡记录缺失的情况处理逻辑 0 不处理 2 当旷工
                if (empAnalyzeInfo.getRegister_miss() != null && empAnalyzeInfo.getRegister_miss() == 2) {
                    String allowedDateType = empAnalyzeInfo.getAllowedDateType();
                    List<Integer> allowedDateTypes = new ArrayList<>();
                    if (StringUtils.isNotBlank(allowedDateType)) {
                        allowedDateTypes = Arrays.stream(allowedDateType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    }
                    if (!allowedDateTypes.contains(shiftDef.getDateType())) {
                        analyze.setIsKg(0);
                    } else {
                        analyze.setIsKg(1);
                        analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                        analyze.setErrMsg(StringUtil.appendStr(analyze.getErrMsg(), "缺失中途卡记录当旷工处理", "   "));
                        analyze.setLateTime(0f);
                        analyze.setEarlyTime(0f);
                    }
                }
            }
        }
    }

    /**
     * 查询参加本次计算的员工的打卡记录（所有出勤规则的打卡记录）
     *
     * @param calculateDto
     * @return
     */
    private List<WaRegisterRecordDo> getAllRegisterRecordList(AnalyzeResultCalculateDto calculateDto) {
        Long[] empids = calculateDto.getEmpids();
        if (empids == null || empids.length == 0) {
            return new ArrayList<>();
        }
        List<Integer> approvalStatusList = Lists.newArrayList(2);
        if (waanalyzeAll) {
            approvalStatusList.add(1);
        }
        List<Long> empIdList = new ArrayList<>(Arrays.asList(empids));
        Long startDate = calculateDto.getStartDate();
        Long endDate = calculateDto.getEndDate();
        AttendanceBasePage basePage = new AttendanceBasePage();
        basePage.setPageSize(PAGE_SIZE);
        basePage.setPageNo(1);
        AttendancePageResult<WaRegisterRecordDo> pageResult = waRegisterRecordDo.getAllRegisterRecordPageList(basePage, calculateDto.getBelongid(),
                empIdList, startDate, endDate, null, null, ValidStatusEnum.VALID.getIndex(), approvalStatusList);
        //总条数
        int totalCount = pageResult.getTotal();
        if (totalCount <= PAGE_SIZE) {
            return pageResult.getItems().stream().filter(record -> record.getBelongDate() != null).collect(Collectors.toList());
        } else {
            List<WaRegisterRecordDo> allRecordList = new ArrayList<>();
            allRecordList.addAll(pageResult.getItems());

            //总页数
            int totalPage = (int) Math.ceil((double) totalCount / PAGE_SIZE);

            for (int pageNo = 2; pageNo <= totalPage; pageNo++) {
                basePage.setPageNo(pageNo);
                pageResult = waRegisterRecordDo.getAllRegisterRecordPageList(basePage, calculateDto.getBelongid(), empIdList, startDate,
                        endDate, null, null, ValidStatusEnum.VALID.getIndex(), approvalStatusList);
                allRecordList.addAll(pageResult.getItems().stream().filter(record -> record.getBelongDate() != null).collect(Collectors.toList()));
            }
            return allRecordList;
        }
    }

    private Map<String, WaRegisterRecord> sortMapByKey(Map<String, WaRegisterRecord> map) {
        if (map == null || map.isEmpty()) {
            return map;
        }
        Map<String, WaRegisterRecord> sortMap = new TreeMap<String, WaRegisterRecord>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                String key1 = o1.split("_")[1];
                String key2 = o2.split("_")[1];
                if (key1.equals(key2)) {
                    return o2.compareTo(o1);
                }
                return key2.compareTo(key1);
            }
        });
        sortMap.putAll(map);
        return sortMap;
    }

    /**
     * 根据员工出勤规则组合员工打卡数据
     *
     * @param empBelongDateRegListMap
     * @param dto
     * @param singIns
     * @param singOffs
     * @param signOnceRecs
     * @param signZeroRecs
     * @param outRecs
     */
    private void getRegisterRecordByClockType(Map<String, List<WaRegisterRecord>> empBelongDateRegListMap,
                                              WaAnalyzCalDTO dto,
                                              Map<String, WaRegisterRecord> singIns,
                                              Map<String, WaRegisterRecord> singOffs,
                                              Map<String, List<WaRegisterRecord>> signOnceRecs,
                                              Map<String, List<WaRegisterRecord>> signZeroRecs,
                                              Map<String, List<WaRegisterRecord>> outRecs) {
        empBelongDateRegListMap.forEach((empIdAndBelongDateKey, regList) -> {
            String[] keyArray = empIdAndBelongDateKey.split("_");
            Long empId = Long.valueOf(keyArray[0]);
            Long belongDate = Long.valueOf(keyArray[1]);
            // 查询考勤分析规则
            WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(empId, belongDate);
            //外勤打卡
            List<Integer> nonOutRegIdList = null;
            List<WaRegisterRecord> outRegList = regList.stream().filter(o -> ClockWayEnum.FIELD.getIndex().equals(o.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(outRegList)) {
                //外勤分析规则，如未设置默认外勤打卡
                Integer outParseRule = analyzeInfo == null || analyzeInfo.getOutParseRule() == null
                        ? TravelTypeParseRuleEnum.TRAVEL_REG.getIndex() : analyzeInfo.getOutParseRule();
                if (TravelTypeParseRuleEnum.TRAVEL_REG.getIndex().equals(outParseRule) &&
                        null != analyzeInfo.getFieldClockLinkShift() && analyzeInfo.getFieldClockLinkShift()) {
                    // 外勤打卡 且 联动班次打卡
                    nonOutRegIdList = outRegList.stream().map(WaRegisterRecord::getRecordId).collect(Collectors.toList());
                } else {
                    outRecs.put(empIdAndBelongDateKey, outRegList);
                }
            }
            //非外勤签到
            List<Integer> finalNonOutRegIdList = nonOutRegIdList;
            regList = regList.stream().filter(o ->
                            !ClockWayEnum.FIELD.getIndex().equals(o.getType())
                                    || (null != finalNonOutRegIdList && finalNonOutRegIdList.contains(o.getRecordId())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(regList)) {
                if (analyzeInfo != null && analyzeInfo.getClock_type() != null
                        && !ParseGroupClockTypeEnum.SIGN_TWICE.getIndex().equals(analyzeInfo.getClock_type())) {
                    //一次卡 || 不打卡
                    if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(analyzeInfo.getClock_type())) {
                        signOnceRecs.put(empIdAndBelongDateKey, regList);
                    } else if (ParseGroupClockTypeEnum.SIGN_ZERO.getIndex().equals(analyzeInfo.getClock_type())) {
                        signZeroRecs.put(empIdAndBelongDateKey, regList);
                    }
                } else {
                    //二次卡
                    Optional<WaRegisterRecord> signInOptional = regList.stream().min(Comparator.comparing(WaRegisterRecord::getRegDateTime));
                    signInOptional.ifPresent(waRegisterRecord -> singIns.put(empIdAndBelongDateKey, waRegisterRecord));

                    if (regList.size() > 1) {
                        Optional<WaRegisterRecord> signOffOptional = regList.stream().max(Comparator.comparing(WaRegisterRecord::getRegDateTime));
                        signOffOptional.ifPresent(waRegisterRecord -> singOffs.put(empIdAndBelongDateKey, waRegisterRecord));
                    }
                }
            }
        });
    }

    @Transactional
    public void insertEmptyAnalyzeRecord(Map<String, Object> paramsMap) {
        Integer row = waAnalyzeMapper.insertEmptyAnalyzeRecord(paramsMap);
        log.info("插入了 {} 条空记录", row);
    }

    /**
     * 自动插入没有对应班次的记录
     *
     * @param paramsMap
     */
    @Transactional
    public void saveNoShiftRecord(Map<String, Object> paramsMap) {
        Integer row = waAnalyzeMapper.saveNoShiftRecord(paramsMap);
        log.info("插入了" + row + "条未设置班次的记录");
    }

    /**
     * 考勤分析数据存储
     *
     * @param resultWa
     * @param tenantId
     * @param userid
     * @param paramsMap
     * @param dto
     */
    @Transactional
    public void saveOrUpdateAnalyze(List<WaAnalyze> resultWa,
                                    String tenantId, Long userid, Map<String, Object> paramsMap, WaAnalyzCalDTO dto) {
        //现在获取所有，在比较，不存在的则新增，存在的则修改
        List<WaAnalyze> walist = waAnalyzeMapper.findWaanalyzeList(paramsMap);
        Map<String, WaAnalyze> waMaps = new HashMap<>();
        List<Integer> listDelAnalyzeId = new ArrayList<>();
        if (walist != null && walist.size() > 0) {
            for (WaAnalyze waAnalyze : walist) {
                String key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
                if (waMaps.containsKey(key)) {
                    listDelAnalyzeId.add(waAnalyze.getAnalyzeId());
                    continue;
                }
                waMaps.put(key, waAnalyze);
            }
        }
        if (CollectionUtils.isNotEmpty(listDelAnalyzeId)) {
            WaAnalyzeExample delExample = new WaAnalyzeExample();
            delExample.createCriteria().andAnalyzeIdIn(listDelAnalyzeId);
            int delRow = waAnalyzeMapper.deleteByExample(delExample);
            log.info("===========================del:" + delRow + "条==========================");
        }
        //动态列
        List<SysDynamicColumns> dynamicColumns = iSysDynamicColumnsService.findDynamicColumns(tenantId, "179");
        List<WaAnalyze> analyzeInsertList = new ArrayList<>();
        List<WaAnalyze> analyzeUpdList = new ArrayList<>();

        for (WaAnalyze waAnalyze : resultWa) {
            waAnalyze.setBelongOrgId(tenantId);
            String key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
            if (waMaps.containsKey(key)) {
                WaAnalyze old = waMaps.get(key);
                BeanUtils.copyProperties(waAnalyze, old, new String[]{"analyzeId", "crtuser", "crttime"});
                old.setUpduser(userid);
                old.setUpdtime(DateUtil.getCurrentTime(true));
                calculateDynamicRule(old, dynamicColumns, dto);
                waAnalyzeMapper.updateByPrimaryKey(old);
                analyzeUpdList.add(old);
                waMaps.remove(key);
            } else {
                waAnalyze.setCrtuser(userid);
                waAnalyze.setCrttime(DateUtil.getCurrentTime(true));
                calculateDynamicRule(waAnalyze, dynamicColumns, dto);
                waAnalyzeMapper.insertSelective(waAnalyze);
                analyzeInsertList.add(waAnalyze);
            }
        }
        if (MapUtils.isNotEmpty(waMaps)) {
            List<Integer> listId = new ArrayList<>();
            for (Map.Entry<String, WaAnalyze> entry : waMaps.entrySet()) {
                WaAnalyze analyze = entry.getValue();
                listId.add(analyze.getAnalyzeId());
            }
            //Tried to send an out-of-range integer as a 2-byte   最大32367个参数
            int len = 10000;
            if (listId.size() > len) {
                int size = listId.size() / len;
                for (int i = 0; i <= size; i++) {
                    int from = i * len;
                    int to = (i + 1) * len;
                    if (to > listId.size()) {
                        to = listId.size();
                    }
                    List<Integer> sublist = listId.subList(from, to);
                    WaAnalyzeExample exp = new WaAnalyzeExample();
                    exp.createCriteria().andAnalyzeIdIn(sublist);
                    int delRow = waAnalyzeMapper.deleteByExample(exp);
                    log.info("===========================删除2：" + delRow + "条==========================");
                }
            } else {
                WaAnalyzeExample exp = new WaAnalyzeExample();
                exp.createCriteria().andAnalyzeIdIn(listId);
                int delRow = waAnalyzeMapper.deleteByExample(exp);
                log.info("===========================删除1：" + delRow + "条==========================");
            }
        }
    }

    /**
     * 计算动态列的值
     *
     * @param waAnalyze
     * @param dynamicColumns
     */
    private void calculateDynamicRule(WaAnalyze waAnalyze, List<SysDynamicColumns> dynamicColumns, WaAnalyzCalDTO dto) {
        if (CollectionUtils.isNotEmpty(dynamicColumns)) {
            Map extcusttomecoljson = new HashMap();
//			AtomicReference<Double> total_duration = new AtomicReference<>(0.0);

            dynamicColumns.forEach(columns -> {

                String EXT_ID = "EXT_COL_?_ID".replace("?", columns.getDynamicColumnsId().toString());
                String EXT_TXT = "EXT_COL_?_TXT".replace("?", columns.getDynamicColumnsId().toString());

                if (columns.getIsCalculateItem()) {
                    BigDecimal decimal = groovyScriptCalculateValue(waAnalyze, columns.getLogicExp(), dto);
                    if (decimal != null) {
                        decimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);//四舍五入保留两位小数
                    }
                    extcusttomecoljson.put(EXT_ID, decimal);
                    extcusttomecoljson.put(EXT_TXT, columns.getColumnZhName());
                }
//				Integer dou = calculateValue(waAnalyze,columns.getLogicExp());

            });
//			extcusttomecoljson.put("duration",total_duration.get());

            waAnalyze.setExtCustomColJson(extcusttomecoljson);
        }
    }

    private BigDecimal groovyScriptCalculateValue(WaAnalyze analyze, String logicExp, WaAnalyzCalDTO dto) {
        if (StringUtils.isBlank(logicExp)) {
            return null;
        }
        Map binding = new HashMap();

        WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
        leaveTypeExample.createCriteria().andBelongOrgidEqualTo(analyze.getBelongOrgId());
        leaveTypeExample.setOrderByClause("leave_type_id");
        List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
        if (CollectionUtils.isNotEmpty(leaveTypeList)) {
            for (WaLeaveType leaveType : leaveTypeList) {
                binding.put("lt_" + leaveType.getLeaveTypeId() + "_key", 0);
            }
        }
        BaseConst.WA_OT_COMPENSATE.forEach((key, value) -> {
            binding.put("ot_" + key + "_key", 0);
        });

        Map<String, Object> otJsonMap = (Map<String, Object>) analyze.getOtColumnJsob();

        if (otJsonMap != null) {
            binding.putAll(otJsonMap);
        }
        Map<String, Object> ltJsonMap = (Map<String, Object>) analyze.getLevelColumnJsonb();
        if (ltJsonMap != null) {
            binding.putAll(ltJsonMap);
        }

        binding.put("actualWorkTime", analyze.getActualWorkTime() == null ? 0f : analyze.getActualWorkTime());
        binding.put("workTime", analyze.getWorkTime() == null ? 0 : analyze.getWorkTime());
        binding.put("registerTime", analyze.getRegisterTime() == null ? 0 : analyze.getRegisterTime());
        binding.put("shiftDefId", analyze.getShiftDefId() == null ? 0 : analyze.getShiftDefId());
        EmpShiftInfo shiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
        binding.put("shiftDef", shiftDef);
        //查询前一天班次
        Long preBelongDate = DateUtil.addDate(analyze.getBelongDate() * 1000, -1);
        EmpShiftInfo preShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), null, preBelongDate, dto);
        binding.put("preShiftDef", preShiftDef);
        binding.put("analyze", analyze);
        return groovyScriptEngine.executeBigDecimal(logicExp, binding);
    }

    /**
     * 请假和出差时长数据类型转换
     *
     * @param resultWa
     */
    public void convertTimeToInteger(List<WaAnalyze> resultWa) {
        if (CollectionUtils.isNotEmpty(resultWa)) {
            for (WaAnalyze analyze : resultWa) {
                //休假
                Map<String, Object> ltJsonMap = (Map<String, Object>) analyze.getLevelColumnJsonb();
                if (MapUtils.isNotEmpty(ltJsonMap)) {
                    Map<String, Object> map = new HashMap<>();
                    ltJsonMap.forEach((k, v) -> {
                        if (v != null && ("time_duration".equals(k) || k.endsWith("_key_minute") || k.endsWith("_key_unit"))) {
                            map.put(k, Float.valueOf(v.toString()).intValue());
                        } else {
                            map.put(k, v);
                        }
                    });
                    analyze.setLevelColumnJsonb(map);
                }

                Map<String, Object> originLtJsonMap = (Map<String, Object>) analyze.getOriginLevelColumnJsonb();
                if (MapUtils.isNotEmpty(originLtJsonMap)) {
                    Map<String, Object> map = new HashMap<>();
                    originLtJsonMap.forEach((k, v) -> {
                        if (v != null && ("time_duration".equals(k) || k.endsWith("_key_minute") || k.endsWith("_key_unit"))) {
                            map.put(k, Float.valueOf(v.toString()).intValue());
                        } else {
                            map.put(k, v);
                        }
                    });
                    analyze.setOriginLevelColumnJsonb(map);
                }
                //出差
                Map<String, Object> travelJsonMap = (Map<String, Object>) analyze.getTravelColumnJsonb();
                if (MapUtils.isNotEmpty(travelJsonMap)) {
                    Map<String, Object> map = new HashMap<>();
                    travelJsonMap.forEach((k, v) -> {
                        if (v != null && ("time_duration".equals(k) || k.endsWith("_key_minute") || k.endsWith("_key_unit"))) {
                            map.put(k, Float.valueOf(v.toString()).intValue());
                        } else {
                            map.put(k, v);
                        }
                    });
                    if (!map.containsKey("valid_status")) {
                        //设置出差单有效状态
                        map.put("valid_status", ValidStatusEnum.VALID.getIndex());
                    }
                    analyze.setTravelColumnJsonb(map);
                }

                Map<String, Object> originTravelJsonMap = (Map<String, Object>) analyze.getOriginTravelColumnJsonb();
                if (MapUtils.isNotEmpty(originTravelJsonMap)) {
                    Map<String, Object> map = new HashMap<>();
                    originTravelJsonMap.forEach((k, v) -> {
                        if (v != null && ("time_duration".equals(k) || k.endsWith("_key_minute") || k.endsWith("_key_unit"))) {
                            map.put(k, Float.valueOf(v.toString()).intValue());
                        } else {
                            map.put(k, v);
                        }
                    });
                    if (!map.containsKey("valid_status")) {
                        //设置出差单有效状态
                        map.put("valid_status", ValidStatusEnum.VALID.getIndex());
                    }
                    analyze.setOriginTravelColumnJsonb(map);
                }
            }
        }
    }

    private boolean checkMinLateTime(float lateTime, WaAnalyzInfo analyzeInfo) {
        // 迟到最小分析时长校验
        if (lateTime <= 0) {
            return false;
        }
        if (null == analyzeInfo || null == analyzeInfo.getMinLateTime() || analyzeInfo.getMinLateTime() <= 0) {
            return true;
        }
        Short minLateTimeUnit = analyzeInfo.getMinLateTimeUnit();
        Integer minLateTime = analyzeInfo.getMinLateTime();
        if (MinLateEarlyTimeUnitEnum.SECOND.getIndex().equals(minLateTimeUnit)) {
            return lateTime * 60 > minLateTime;
        } else if (MinLateEarlyTimeUnitEnum.MINUTE.getIndex().equals(minLateTimeUnit)) {
            return lateTime > minLateTime;
        } else if (MinLateEarlyTimeUnitEnum.HOUR.getIndex().equals(minLateTimeUnit)) {
            return lateTime > minLateTime * 60;
        }
        return true;
    }

    private boolean checkMinEarlyTime(float earlyTime, WaAnalyzInfo analyzeInfo) {
        // 早退最小分析时长校验
        if (earlyTime <= 0) {
            return false;
        }
        if (null == analyzeInfo || null == analyzeInfo.getMinEarlyTime() || analyzeInfo.getMinEarlyTime() <= 0) {
            return true;
        }
        Short minEarlyTimeUnit = analyzeInfo.getMinEarlyTimeUnit();
        Integer minEarlyTime = analyzeInfo.getMinEarlyTime();
        if (MinLateEarlyTimeUnitEnum.SECOND.getIndex().equals(minEarlyTimeUnit)) {
            return earlyTime * 60 > minEarlyTime;
        } else if (MinLateEarlyTimeUnitEnum.MINUTE.getIndex().equals(minEarlyTimeUnit)) {
            return earlyTime > minEarlyTime;
        } else if (MinLateEarlyTimeUnitEnum.HOUR.getIndex().equals(minEarlyTimeUnit)) {
            return earlyTime > minEarlyTime * 60;
        }
        return true;
    }

    /**
     * 根据签到签退数据组合分析结果（二次卡分析逻辑）
     *
     * @param singIns
     * @param singOffs
     * @param dto
     * @param paramsMap
     * @return
     */
    public List<WaAnalyze> analyzeResult(Map<String, WaRegisterRecord> singIns, Map<String, WaRegisterRecord> singOffs, WaAnalyzCalDTO dto, Map<String, Object> paramsMap, Map<String, Map<String, Object>> excludeFieldRecordMap) {
        log.info("analyzeResultForMultiClock start time={}", System.currentTimeMillis());
        //按归属日期排序
        singIns = sortMapByKey(singIns);
        singOffs = sortMapByKey(singOffs);
        String noShiftMsg = "缺少对应班次";
        List<WaAnalyze> resultWa = new ArrayList<>();

        //签到记录分析
        log.info("analyzeResultForMultiClock For SignIn start time={}", System.currentTimeMillis());
        if (MapUtils.isNotEmpty(singIns)) {
            for (Map.Entry<String, WaRegisterRecord> map : singIns.entrySet()) {
                String key = map.getKey();
                WaAnalyze analyze = new WaAnalyze();
                WaRegisterRecord reg = map.getValue();
                Integer regType = reg.getType(); // 用于统计补打卡次数 regType = 6 代表补打卡类型
                Long belongDate = reg.getBelongDate();
                if (belongDate == null) {
                    belongDate = DateUtil.getDateLong(reg.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                }
                analyze.setClockType(ParseGroupClockTypeEnum.SIGN_TWICE.getIndex());
                analyze.setBelongOrgId(dto.getBelongid());
                analyze.setBelongDate(belongDate);
                Map<String, Object> regMap = new HashMap<>();
                regMap.put("signInId", reg.getRecordId());
                regMap.put("signInTime", reg.getRegDateTime());
                analyze.setSigninId(reg.getRecordId());
                analyze.setEmpid(reg.getEmpid());
                analyze.setRegSigninTime(reg.getRegDateTime());
                if (regType != null && regType == 6) {
                    analyze.setBdkCount(1); // 如果是补打卡类型则记录一次
                }
                float lateTime = 0;
                //查询班次
                EmpShiftInfo shiftDef = getEmpShiftDefByInfo(reg.getEmpid(), null, reg.getBelongDate(), dto);
                //查询考勤分析分组
                WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                //在考勤分析中，按天分析内进行如果员工缺少班次信息，则提示员工缺少对应班次，在按月分析内，扩展一列”缺少班次天数”。进行缺少班次数据统计。（KEN）
                if (shiftDef == null || shiftDef.getShiftDefId() == null) {
                    analyze.setIsShift(1);// 是否缺少班次
                    analyze.setErrMsg(noShiftMsg);
                } else {
                    analyze.setIsShift(0);
                }
                if (shiftDef != null
                        && shiftDef.getShiftDefId() != null
                        && (reg.getShiftDefId() == null || !shiftDef.getShiftDefId().equals(reg.getShiftDefId()))) {
                    reg.setShiftDefId(shiftDef.getShiftDefId());
                    waRegisterRecordMapper.updateByPrimaryKeySelective(reg);
                }
                Long regtime = reg.getRegDateTime();
                if (shiftDef != null) {
                    analyze.setShiftDefId(shiftDef.getShiftDefId());
                }
                // 当是工作日是才计算实际工作小时数，应工作小时数，迟到小时数
                if (shiftDef != null && shiftDef.getDateType() == 1) {
                    // AD-7 20170828 只有当异常是时间异常时才计算迟到小时数
                    //CLOUD-8623
                    Boolean isFlexibleWorking = false; // 是否按弹性时间分析迟到分钟数
                    if (analyzeInfo != null) {
                        isFlexibleWorking = BooleanUtils.toBoolean(analyzeInfo.getIs_flexible_working());
                    } else {
                        SysEmpInfo empInfo = dto.getEmpInfo(analyze.getEmpid());
                        if (dto.getJob() || null == empInfo) {
                            log.error("analyzeResult.analyzeInfo为null，empInfo:{}", empInfo == null ? "empInfo is null" : JSONUtils.ObjectToJson(empInfo));
                        } else {
                            throw new CDException(String.format("%s(%s)未查到匹配的%s日的考勤分析规则", empInfo.getEmpName(), empInfo.getWorkno(), DateUtil.getDateStrByTimesamp(analyze.getBelongDate())));
                        }
                    }
                    //背景：目前分析迟到时长时如果签到正常就不分析迟到，但是当签到时间属于弹性区间并且当天有请假，应再次分析迟到时长（即使签到时间正常也要再次分析迟到时长）
                    boolean isAnalyzeLateFlag = false;
                    Boolean leaveFlag = this.checkApplyLeaveByDate(dto, shiftDef.getEmpid(), belongDate, isFlexibleWorking, paramsMap);
                    if (leaveFlag && BooleanUtils.isTrue(isFlexibleWorking)) {
                        isAnalyzeLateFlag = true;
                    }
                    if ((isAnalyzeLateFlag || ((StringUtils.isBlank(reg.getResultDesc()) || reg.getResultDesc().contains("TIME_ERR")))) && shiftDef.getStartTime() != null && isAnalyzeLateEarly(analyzeInfo)) {
                        // 如果中间有休息时间，迟到时间不包含休息时间 即 如果签到时间在休息时间范围内则迟到时间＝休息开始时间－上班开始时间
                        lateTime = this.getLateTime(dto, regtime * 1000, shiftDef, isFlexibleWorking, paramsMap);
                        if (lateTime > shiftDef.getWorkTotalTime()) {
                            lateTime = shiftDef.getWorkTotalTime();
                        }
                        //如果迟到则说明异常
                        if (lateTime > 0) {
                            analyze.setIsExp(1);
                        }
                    }
                    //新逻辑,弹性班次设置开关
                    assert analyzeInfo != null;
                    if (analyzeInfo.getFlexibleWorkSwitch() != null && FlexibleEnum.OPEN.getIndex().equals(analyzeInfo.getFlexibleWorkSwitch()) && isAnalyzeLateEarly(analyzeInfo)) {
                        leaveFlag = this.checkApplyLeaveByDate(dto, shiftDef.getEmpid(), belongDate, Boolean.TRUE, paramsMap);
                        //晚到晚走，早到早走
                        if (FlexibleEnum.OPEN.getIndex().equals(shiftDef.getFlexibleShiftSwitch())) {
                            if (FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(shiftDef.getFlexibleWorkRule())) {
                                lateTime = getFlexibleLateTime(regtime * 1000, shiftDef, analyzeInfo, leaveFlag);
                                if (lateTime > shiftDef.getWorkTotalTime()) {
                                    lateTime = shiftDef.getWorkTotalTime();
                                }
                                //如果迟到则说明异常
                                if (lateTime > 0) {
                                    analyze.setIsExp(1);
                                }
                            }
                            //前一天晚走,第二天晚到
                            if (FlexbleWorkTypeEnum.GO_LATE_LATE.getIndex().equals(shiftDef.getFlexibleWorkRule())) {
                                //前一天的签退记录
                                Long yesterday = DateUtil.addDate(belongDate * 1000, -1);
                                String[] arr = key.split("_");
                                String key1 = String.format("%s_%s", arr[0], yesterday);
                                WaRegisterRecord yesterdayRecord = singOffs.get(key1);
                                //前一天有签退
                                if (yesterdayRecord != null) {
                                    EmpShiftInfo yesterdayShift = getEmpShiftDefByInfo(yesterdayRecord.getEmpid(), null, yesterdayRecord.getBelongDate(), dto);
                                    Long yesterdayRegDateTime = yesterdayRecord.getRegDateTime();
                                    lateTime = getLateTimeForGoLate(yesterdayRegDateTime * 1000, regtime * 1000, yesterdayShift, shiftDef, lateTime, dto);
                                }
                                if (lateTime > shiftDef.getWorkTotalTime()) {
                                    lateTime = shiftDef.getWorkTotalTime();
                                }
                                //如果迟到则说明异常
                                if (lateTime > 0) {
                                    analyze.setIsExp(1);
                                }
                            }
                        }
                    }
                    analyze.setWorkTime(shiftDef.getWorkTotalTime());
                }
                analyze.setLateTime(lateTime);//迟到小时数
                if (lateTime > 0 && !StringUtils.isBlank(reg.getReason())) {
                    analyze.setErrMsg(reg.getReason());
                }
                //  根据时间 去匹配签退数据
                if (singOffs.containsKey(key)) {
                    WaRegisterRecord reg2 = singOffs.get(key);
                    analyze.setSignoffId(reg2.getRecordId());
                    analyze.setRegSignoffTime(reg2.getRegDateTime());
                    regMap.put("signOffId", reg2.getRecordId());
                    regMap.put("signOffTime", reg2.getRegDateTime());
                    if (reg2.getType() != null && reg2.getType() == 6) {
                        analyze.setBdkCount(analyze.getBdkCount() == null ? 1 : analyze.getBdkCount() + 1); // 如果是补打卡类型则记录一次
                    }
                    float earlyTime = 0;
                    if (shiftDef != null) {
                        //TODO 考勤分析是否计算休息日实际工作时间--1.0 是通过开关配置来控制的，腾讯无配置页面，默认不开启
                        //String isCalRestDay = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + dto.getBelongid() + RedisKeyDefine.IS_ANALYSE_RESTDAY_WORKTIME);
                        //String isCalRestDay = "0";
                        Boolean leaveFlag = this.checkApplyLeaveByDate(dto, shiftDef.getEmpid(), belongDate, Boolean.TRUE, paramsMap);
                        if (shiftDef.getDateType() == 1) {
                            if (((StringUtils.isBlank(reg2.getResultDesc()) || reg2.getResultDesc().contains("TIME_ERR"))) && shiftDef.getEndTime() != null && isAnalyzeLateEarly(analyzeInfo)) {
                                //计算早退分钟数
                                Boolean isFlexibleWorking = false;
                                if (analyzeInfo != null) {
                                    isFlexibleWorking = BooleanUtils.toBoolean(analyzeInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
                                }
                                earlyTime = getEarlyTime(dto, analyze, shiftDef, isFlexibleWorking, paramsMap);
                                if (earlyTime > shiftDef.getWorkTotalTime().longValue()) {
                                    earlyTime = shiftDef.getWorkTotalTime();
                                }
                                //如果早退则说明异常
                                if (earlyTime > 0) {
                                    analyze.setIsExp(1);
                                }
                            }
                            //获取实际（有效）工作时长
                            Float actualWorkTime = calculateActualWorkTime(analyze, shiftDef, analyzeInfo, dto.getBelongid(), leaveFlag);
                            analyze.setActualWorkTime(actualWorkTime);
                            //获取早退时长 - 弹性设置
                            if (FlexibleEnum.OPEN.getIndex().equals(analyzeInfo.getFlexibleWorkSwitch()) && isAnalyzeLateEarly(analyzeInfo) && FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(shiftDef.getFlexibleWorkRule())) {
                                earlyTime = getEarlyTimeFlexible(dto, analyze, shiftDef, paramsMap, analyzeInfo);
                            }
                        } else if (shiftDef.getDateType() == 3 || shiftDef.getDateType() == 5) {
                            //法定节假日计算实际工作时长
                            Float actualWorkTime = calculateActualWorkTimeForRestDay(analyze, shiftDef, analyzeInfo);
                            actualWorkTime = actualWorkTime < 0 ? 0f : actualWorkTime;
                            analyze.setHolidayWorkTime(actualWorkTime.intValue());
                        }
                    } else {
                        log.info("未找到排班记录 empid=" + reg.getEmpid() + ",date=" + reg.getRegDateTime());
                    }

                    Integer regTime = (analyze.getRegSignoffTime().intValue() - analyze.getRegSigninTime().intValue()) / 60;
                    analyze.setRegisterTime(regTime);

                    analyze.setEarlyTime(earlyTime);
                    if (earlyTime > 0 && !StringUtils.isBlank(reg2.getReason())) {
                        String errMsg;
                        if (!StringUtils.isBlank(analyze.getErrMsg())) {
                            errMsg = analyze.getErrMsg() + "     " + reg2.getReason();
                        } else {
                            errMsg = reg2.getReason();
                        }
                        analyze.setErrMsg(errMsg);
                    }
                    //移除map中的签退纪录，剩下的就是只有签退纪录，没有签到的纪录
                    singOffs.remove(key);
                } else {
                    // 员工当天对应的考勤分组及分析分组
                    WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), belongDate);
                    if (shiftDef != null && shiftDef.getDateType() == 1 && empAnalyzeInfo != null) {
                        //打卡记录缺失的情况处理逻辑 0 不处理 2 当旷工
                        if (empAnalyzeInfo.getRegister_miss() != null && empAnalyzeInfo.getRegister_miss() == 2) {
                            String allowedDateType = empAnalyzeInfo.getAllowedDateType();
                            List<Integer> allowedDateTypes = new ArrayList<>();
                            if (StringUtils.isNotBlank(allowedDateType)) {
                                allowedDateTypes = Arrays.stream(allowedDateType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                            }
                            if (!allowedDateTypes.contains(shiftDef.getDateType())) {
                                analyze.setIsKg(0);
                            } else {
                                analyze.setIsKg(1);
                                analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                                analyze.setErrMsg(StringUtil.appendStr(analyze.getErrMsg(), "缺失签退记录当旷工处理", "   "));
                                analyze.setLateTime(0f);
                                analyze.setEarlyTime(0f);
                            }
                        }
                    }
                }
                excludeFieldRecordMap.put(key, regMap);
                resultWa.add(analyze);
            }
        }
        log.info("analyzeResultForMultiClock For SignIn end time={}", System.currentTimeMillis());

        //签退
        log.info("analyzeResultForMultiClock For SignOff start time={}", System.currentTimeMillis());
        if (MapUtils.isNotEmpty(singOffs)) {
            for (Map.Entry<String, WaRegisterRecord> map : singOffs.entrySet()) {
                WaAnalyze analyze = new WaAnalyze();
                WaRegisterRecord reg = map.getValue();
                Long belongDate = reg.getBelongDate();
                Integer regType = reg.getType();
                if (belongDate == null) {
                    belongDate = DateUtil.getDateLong(reg.getRegDateTime() * 1000, "yyyy-MM-dd", true);
                }
                analyze.setClockType(ParseGroupClockTypeEnum.SIGN_TWICE.getIndex());
                analyze.setBelongDate(belongDate);
                analyze.setBelongOrgId(dto.getBelongid());
                analyze.setEmpid(reg.getEmpid());
                Map<String, Object> regMap = new HashMap<>();
                regMap.put("signOffId", reg.getRecordId());
                regMap.put("signOffTime", reg.getRegDateTime());
                excludeFieldRecordMap.put(map.getKey(), regMap);
                analyze.setSignoffId(reg.getRecordId());
                analyze.setRegSignoffTime(reg.getRegDateTime());
                if (regType != null && regType == 6) {
                    analyze.setBdkCount(analyze.getBdkCount() == null ? 1 : analyze.getBdkCount() + 1); // 如果是补打卡类型则记录一次
                }
                float earlyTime = 0;
                EmpShiftInfo shiftDef = this.getEmpShiftDefByInfo(reg.getEmpid(), null, reg.getBelongDate(), dto);
                WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                if (shiftDef == null || shiftDef.getShiftDefId() == null) {
                    analyze.setIsShift(1);// 是否缺少班次
                    if (!StringUtil.isNullOrTrimEmpty(analyze.getErrMsg()) && !analyze.getErrMsg().contains(noShiftMsg)) {
                        analyze.setErrMsg(noShiftMsg);
                    }
                } else {
                    analyze.setIsShift(0);
                    analyze.setShiftDefId(shiftDef.getShiftDefId());
                    analyze.setWorkTime(shiftDef.getWorkTotalTime());//应工作时间
                }
                // AD-7 20170828 只有当异常是时间异常时才计算早退小时数
                Integer resultType = reg.getResultType() == null ? 2 : reg.getResultType();
                if ((resultType == 2 && (reg.getResultDesc() == null || reg.getResultDesc().contains("TIME_ERR"))) && shiftDef != null && shiftDef.getDateType() == 1 && shiftDef.getEndTime() != null && isAnalyzeLateEarly(analyzeInfo)) {
                    Boolean isFlexibleWorking = BooleanUtils.toBoolean(analyzeInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
                    //计算早退分钟数
                    earlyTime = this.getEarlyTime(dto, analyze, shiftDef, isFlexibleWorking, paramsMap);
                    if (earlyTime > shiftDef.getWorkTotalTime()) {
                        earlyTime = shiftDef.getWorkTotalTime();
                    }
                    //如果早退则说明异常
                    if (earlyTime > 0) {
                        analyze.setIsExp(1);
                    }
                }
                //获取早退时长 - 弹性设置
                if (shiftDef != null) {
                    if (FlexibleEnum.OPEN.getIndex().equals(analyzeInfo.getFlexibleWorkSwitch()) && isAnalyzeLateEarly(analyzeInfo) && FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(shiftDef.getFlexibleWorkRule())) {
                        earlyTime = getEarlyTimeFlexible(dto, analyze, shiftDef, paramsMap, analyzeInfo);
                    }
                }
                analyze.setEarlyTime(earlyTime);
                if (earlyTime > 0 && !StringUtils.isBlank(reg.getReason())) {
                    analyze.setErrMsg(reg.getReason());
                }
                // 员工当天对应的考勤分组及分析分组
                WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), belongDate);
                if (empAnalyzeInfo != null && shiftDef != null && shiftDef.getDateType() == 1) {
                    //打卡记录缺失的情况处理逻辑 0 不处理 2 当旷工
                    if (empAnalyzeInfo.getRegister_miss() != null && empAnalyzeInfo.getRegister_miss() == 2) {
                        String allowedDateType = empAnalyzeInfo.getAllowedDateType();
                        List<Integer> allowedDateTypes = new ArrayList<>();
                        if (StringUtils.isNotBlank(allowedDateType)) {
                            allowedDateTypes = Arrays.stream(allowedDateType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                        }
                        if (!allowedDateTypes.contains(shiftDef.getDateType())) {
                            analyze.setIsKg(0);
                        } else {
                            analyze.setIsKg(1);
                            analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                            analyze.setErrMsg(StringUtil.appendStr(analyze.getErrMsg(), "缺失签到记录当旷工处理", "   "));
                            analyze.setLateTime(0f);
                            analyze.setEarlyTime(0f);
                        }
                    }
                }
                resultWa.add(analyze);
            }
        }
        log.info("analyzeResultForMultiClock For SignOff end time={}", System.currentTimeMillis());
        log.info("analyzeResultForMultiClock end time={}", System.currentTimeMillis());
        return resultWa;
    }

    /**
     * 一次卡分析逻辑
     *
     * @param signOnceRecs
     * @param dto
     * @return
     */
    private List<WaAnalyze> analyzeOneCardResult(Map<String, List<WaRegisterRecord>> signOnceRecs, WaAnalyzCalDTO dto, Map<String, Map<String, Object>> excludeFieldRecordMap) {
        List<WaAnalyze> resultWa = new ArrayList<>();
        if (MapUtils.isEmpty(signOnceRecs)) {
            return resultWa;
        }
        //一次卡逻辑处理
        for (Map.Entry<String, List<WaRegisterRecord>> entry : signOnceRecs.entrySet()) {
            String empIdAndBelongDateKey = entry.getKey();
            String[] keyArray = empIdAndBelongDateKey.split("_");
            Long empId = Long.valueOf(keyArray[0]);
            Long belongDate = Long.valueOf(keyArray[1]);
            WaAnalyze analyze = new WaAnalyze();
            analyze.setClockType(ParseGroupClockTypeEnum.SIGN_ONCE.getIndex());
            analyze.setBelongOrgId(dto.getBelongid());
            analyze.setEmpid(empId);
            analyze.setBelongDate(belongDate);
            long curTime = System.currentTimeMillis() / 1000;
            analyze.setUpdtime(curTime);
            analyze.setUpduser(dto.getUserId());
            analyze.setCrtuser(dto.getUserId());
            analyze.setCrttime(curTime);
            //查询班次
            EmpShiftInfo shiftdef = this.getEmpShiftDefByInfo(empId, null, belongDate, dto);
            if (shiftdef == null || shiftdef.getShiftDefId() == null) {
                analyze.setErrMsg("缺少对应班次");
                analyze.setIsShift(1);
            } else {
                analyze.setIsShift(0);
                analyze.setActualWorkTime(0f);
                analyze.setRegisterTime(0);
                analyze.setShiftDefId(shiftdef.getShiftDefId());
                analyze.setWorkTime(shiftdef.getWorkTotalTime());
            }
            //当天打卡记录
            List<WaRegisterRecord> regList = entry.getValue().stream().sorted(Comparator.comparing(WaRegisterRecord::getRegDateTime)).collect(Collectors.toList());
            //当班次为工作日时才去分析工作时长
            if (shiftdef != null && DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftdef.getDateType())) {
                WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(empId, belongDate);
                WaRegisterRecord normalRegisterRecord = null;
                if (analyzeInfo != null && analyzeInfo.getClock_rule() != null) {
                    JSONObject json = JSONObject.parseObject(analyzeInfo.getClock_rule());
                    Integer clockRule = Integer.valueOf(json.get("clockRule").toString());
                    //计算正常取卡区间
                    Integer normalRegStartTime;
                    Integer normalRegEndTime;
                    //班次跨夜或者打卡区间跨夜
                    boolean isKy = CdWaShiftUtil.checkCrossNight(shiftdef.getStartTime(), shiftdef.getEndTime(), shiftdef.getDateType()) || shiftdef.getOffDutyStartTime() > shiftdef.getOffDutyEndTime();
                    if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(clockRule)) {
                        //在班次打卡时段内存在打卡、补卡记录
                        normalRegEndTime = shiftdef.getOffDutyEndTime();
                        normalRegStartTime = shiftdef.getOnDutyStartTime();
                    } else {
                        //在班次上班时间-下班时间内存在打卡、补卡记录
                        normalRegStartTime = shiftdef.getStartTime();
                        normalRegEndTime = shiftdef.getEndTime();
                    }
                    for (WaRegisterRecord registerRecord : regList) {
                        boolean flag = isMissClock(registerRecord, normalRegStartTime, normalRegEndTime, isKy);
                        if (flag) {
                            normalRegisterRecord = registerRecord;
                            break;
                        }
                    }
                }
                //存在正常范围内的打卡数据
                if (normalRegisterRecord != null) {
                    analyze.setActualWorkTime(shiftdef.getWorkTotalTime().floatValue());
                    analyze.setRegisterTime(shiftdef.getWorkTotalTime());
                    analyze.setSigninId(normalRegisterRecord.getRecordId());
                    analyze.setRegSigninTime(normalRegisterRecord.getRegDateTime());
                    Map<String, Object> regMap = new HashMap<>();
                    regMap.put("signInId", normalRegisterRecord.getRecordId());
                    regMap.put("signInTime", normalRegisterRecord.getRegDateTime());
                    excludeFieldRecordMap.put(empIdAndBelongDateKey, regMap);
                    // 用于统计补打卡次数 regType = 6 代表补打卡类型
                    if (ClockWayEnum.FILLCLOCK.getIndex().equals(normalRegisterRecord.getType())) {
                        analyze.setBdkCount(1);
                    }
                    //在考勤分析中，按天分析内进行如果员工缺少班次信息，则提示员工缺少对应班次，在按月分析内，扩展一列”缺少班次天数”。进行缺少班次数据统计。（KEN）
                    if (normalRegisterRecord.getShiftDefId() == null) {
                        normalRegisterRecord.setShiftDefId(shiftdef.getShiftDefId());
                        waRegisterRecordMapper.updateByPrimaryKeySelective(normalRegisterRecord);
                    }
                } else {
                    //当天在正常区间内无打卡记录当旷工处理
                    if (analyzeInfo != null && analyzeInfo.getClock_rule() != null) {
                        String allowedDateType = analyzeInfo.getAllowedDateType();
                        List<Integer> allowedDateTypes = new ArrayList<>();
                        if (StringUtils.isNotBlank(allowedDateType)) {
                            allowedDateTypes = Arrays.stream(allowedDateType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                        }
                        boolean isAllowedDateType = allowedDateTypes.contains(shiftdef.getDateType());
                        JSONObject clockJson = JSONObject.parseObject(analyzeInfo.getClock_rule());
                        Integer missingClockRule = (Integer) clockJson.get("missingClockRule");
                        if (ParseGroupMissingClockRuleEnum.ABSENTEEISM.getIndex().equals(missingClockRule)) {
                            if (isAllowedDateType) {
                                //缺卡 视为旷工
                                if (shiftdef.getWorkTotalTime() > 0) {
                                    analyze.setIsKg(1);
                                } else {
                                    analyze.setIsKg(0);
                                }
                                analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                            } else {
                                analyze.setIsKg(0);
                            }
                        } else {
                            if (isAllowedDateType) {
                                //缺卡 视为缺卡 此逻辑c产品暂未提供，默认旷工
                                if (shiftdef.getWorkTotalTime() > 0) {
                                    analyze.setIsKg(1);
                                } else {
                                    analyze.setIsKg(0);
                                }
                                analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                            } else {
                                analyze.setIsKg(0);
                            }
                        }
                    } else {
                        //未配置缺卡规则默认旷工
                        if (shiftdef.getWorkTotalTime() > 0) {
                            analyze.setIsKg(1);
                        } else {
                            analyze.setIsKg(0);
                        }
                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                    }
                }
            }
            resultWa.add(analyze);
        }
        return resultWa;
    }

    /**
     * 外勤打卡分析逻辑
     *
     * @param registerMap
     * @param dto
     * @return
     */
    private List<WaAnalyze> analyzeOutSignResult(Map<String, List<WaRegisterRecord>> registerMap, WaAnalyzCalDTO dto) {
        //此种情况，仅判断当天存在大于等于1次的外勤打卡即为出勤正常，如存在出差单，出差单仅参与出差时长地计算，但不参与迟到早退旷工的抵扣
        //外勤分析规为外勤打卡时，不管当天是否有其他类型的卡（例如：签到，签退），实际工作时长和出勤时长始终等于班次工作时长，考勤结果也按照外勤打卡逻辑进行分析
        List<WaAnalyze> outSignWaResult = new ArrayList<>();
        if (MapUtils.isEmpty(registerMap)) {
            return outSignWaResult;
        }
        for (Map.Entry<String, List<WaRegisterRecord>> entry : registerMap.entrySet()) {
            String empIdAndBelongDateKey = entry.getKey();
            String[] keyArray = empIdAndBelongDateKey.split("_");
            Long empId = Long.valueOf(keyArray[0]);
            Long belongDate = Long.valueOf(keyArray[1]);
            //查询当天考勤分析规则
            WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(empId, belongDate);
            //外勤分析规则，如未设置默认外勤打卡
            Integer outParseRule = analyzeInfo == null || analyzeInfo.getOutParseRule() == null ? TravelTypeParseRuleEnum.TRAVEL_REG.getIndex() : analyzeInfo.getOutParseRule();
            if (TravelTypeParseRuleEnum.TRAVEL_REG.getIndex().equals(outParseRule)) {
                //外勤打卡
                WaAnalyze analyze = new WaAnalyze();
                //出勤规则，如未设置则默认二次卡出勤规则
                Integer clockType = analyzeInfo == null || analyzeInfo.getClock_type() == null ? ParseGroupClockTypeEnum.SIGN_TWICE.getIndex() : analyzeInfo.getClock_type();
                analyze.setClockType(clockType);
                analyze.setBelongOrgId(dto.getBelongid());
                analyze.setEmpid(empId);
                analyze.setBelongDate(belongDate);
                long curTime = System.currentTimeMillis() / 1000;
                analyze.setCrtuser(dto.getUserId());
                analyze.setCrttime(curTime);
                analyze.setUpdtime(curTime);
                analyze.setUpduser(dto.getUserId());
                //查询班次
                EmpShiftInfo shiftDef = this.getEmpShiftDefByInfo(empId, null, belongDate, dto);
                if (shiftDef == null || shiftDef.getShiftDefId() == null) {
                    analyze.setIsShift(1);
                    analyze.setErrMsg("缺少对应班次");
                } else {
                    analyze.setIsShift(0);
                    analyze.setShiftDefId(shiftDef.getShiftDefId());
                    analyze.setWorkTime(shiftDef.getWorkTotalTime());
                    analyze.setActualWorkTime(0f);
                    analyze.setRegisterTime(0);
                }
                //当班次为工作日时才去分析工作时长
                if (shiftDef != null && DateTypeEnum.DATE_TYP_1.getIndex().equals(shiftDef.getDateType())) {
                    analyze.setActualWorkTime(shiftDef.getWorkTotalTime().floatValue());
                    analyze.setRegisterTime(shiftDef.getWorkTotalTime());
                    //当天打卡记录
                    List<WaRegisterRecord> regList = entry.getValue();
                    if (CollectionUtils.isNotEmpty(regList)) {
                        regList.sort(Comparator.comparing(WaRegisterRecord::getRegDateTime));
                        analyze.setSigninId(regList.get(0).getRecordId());
                        analyze.setRegSigninTime(regList.get(0).getRegDateTime());
                        if (regList.size() > 1) {
                            analyze.setSigninId(regList.get(regList.size() - 1).getRecordId());
                            analyze.setRegSigninTime(regList.get(regList.size() - 1).getRegDateTime());
                        }
                    }
                }
                outSignWaResult.add(analyze);
            }
        }
        return outSignWaResult;
    }

    private void checkAndAnalyzeRegisterRecord(String tenantId, List<WaRegisterRecord> regList, WaAnalyzCalDTO dto) {
        if (!enableclockparse) {
            log.info("checkAndAnalyzeRegisterRecord fail cause: no enable");
            return;
        }
        log.info("checkAndAnalyzeRegisterRecord start time={}", System.currentTimeMillis());
        if (null == tenantId || CollectionUtils.isEmpty(regList)) {
            log.info("checkAndAnalyzeRegisterRecord fail cause: params error");
            return;
        }
        for (WaRegisterRecord record : regList) {
            Long empId = record.getEmpid();
            Long belongDate = DateUtil.getOnlyDate(new Date(record.getRegDateTime() * 1000));
            EmpShiftInfo currentShiftDef = this.getEmpShiftDefByInfo(empId, null, belongDate, dto);
            if (null == currentShiftDef) {
                continue;
            }
            if (record.getShiftDefId() != null && !record.getShiftDefId().equals(currentShiftDef.getShiftDefId())) {
                clockSignService.analyseRegisterRecord(tenantId, Collections.singletonList(empId), belongDate, ClockAnalyseDataCacheDto.doBuild());
                WaRegisterRecord analyzeRecord = waRegisterRecordMapper.selectByPrimaryKey(record.getRecordId());
                if (null == analyzeRecord) {
                    continue;
                }
                record = analyzeRecord;
            }
        }
        log.info("checkAndAnalyzeRegisterRecord end time={}", System.currentTimeMillis());
    }

    /**
     * 一次卡判断是否缺卡
     *
     * @param registerRecord
     * @param startTime
     * @param endTime
     * @param isKy
     * @return
     */
    private boolean isMissClock(WaRegisterRecord registerRecord, Integer startTime, Integer endTime, boolean isKy) {
        long belongDate = registerRecord.getBelongDate();
        long s = belongDate + (startTime * 60);
        long e = belongDate + (endTime * 60);
        if (isKy) {
            e = DateUtil.addDate(belongDate * 1000, 1) + (endTime * 60);
        }
        Long regTime = registerRecord.getRegDateTime();
        return regTime >= s && regTime <= e;
    }

    /**
     * 考勤分析-生成旷工记录
     *
     * @param paramsMap
     * @param startDate
     * @param endDate
     * @param dto
     */
    private void absentEmpRecord(Map<String, Object> paramsMap, Long startDate, Long endDate, WaAnalyzCalDTO dto) {
        List<Long> empIds = (List<Long>) paramsMap.get("empIdList");
        if (CollectionUtils.isEmpty(empIds)) {
            return;
        }
        String tenantId = (String) paramsMap.get("belongid");
        if (startDate == null && endDate == null) {
            endDate = DateUtil.getCurrentTime(false) - (24 * 60 * 60);
            startDate = DateUtil.addDate(endDate, -365);
            endDate = (endDate / 1000) + (23 * 60 * 60) + (59 * 60) + 59;//除以1000转成unix时间＋23:59:59秒
        } else {
            Long currtime = DateUtil.getCurrentTime(true);
            if (endDate.intValue() > currtime.intValue()) {
                endDate = currtime;
            }
        }
        //如果开始时间截止时间都等于空，插入所有人的纪录
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        //查询已经分析过的日期数据
        List<String> empDateList = waAnalyzeMapper.getAnalyseEmpDate(paramsMap);
        //遍历每个人每个日期去生成对应的考勤分析数据（排除生成过的日期）
        Integer registerMiss = (Integer) paramsMap.get("registerMiss");
        String allowedDateType = (String) paramsMap.get("allowedDateType");
        List<Integer> allowedDateTypes = new ArrayList<>();
        if (StringUtils.isNotBlank(allowedDateType)) {
            allowedDateTypes = Arrays.stream(allowedDateType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        List<WaAnalyze> analyzeAddList = new ArrayList<>();
        //查询员工入离职信息
        List<SysEmpInfo> empInfoList = getEmpinfoListByEmpids(tenantId, empIds);
        if (CollectionUtils.isEmpty(empInfoList)) {
            return;
        }
        // 员工考勤分析数据删除集合
        List<EmpAnalyzeResultDeleteDto> empAnalyzeResultDelList = Lists.newArrayList();
        for (SysEmpInfo empInfo : empInfoList) {
            Long empId = empInfo.getEmpid();
            long start = DateUtilExt.getTimeByPattern(startDate, "yyyy-MM-dd");
            long end = DateUtilExt.getTimeByPattern(endDate, "yyyy-MM-dd");
            // 入离职日期判断
            long hireDate = Optional.ofNullable(empInfo.getHireDate()).orElse(0L);
            long terminationDate = Optional.ofNullable(empInfo.getTerminationDate()).orElse(0L);
            Long internshipDate = empInfo.getInternshipDate();
            if (null != internshipDate && internshipDate > 0) {
                start = start <= internshipDate ? internshipDate : start;
            } else {
                start = hireDate > 0 && start <= hireDate ? hireDate : start;
            }
            if (terminationDate > 0) {
                if (hireDate > terminationDate) {
                    // 离职再入职
                    // 删除离职日期到入职日期之间的数据，不包括离职和入职当天
                    empAnalyzeResultDelList.add(new EmpAnalyzeResultDeleteDto(empId,
                            DateUtil.addDate(terminationDate * 1000, 1),
                            DateUtil.addDate(hireDate * 1000, -1)));
                } else {
                    // 离职
                    end = Math.min(end, terminationDate);
                    // 删除离职日期后面所有的考勤分析数据，不包括离职当天
                    empAnalyzeResultDelList.add(new EmpAnalyzeResultDeleteDto(empId,
                            DateUtil.addDate(terminationDate * 1000, 1),
                            253402185600L));
                }
            }
            while (start <= end) {
                if (!empDateList.contains(empId + "_" + start)) {
                    //1、查询员工排班信息
                    EmpShiftInfo shiftdef = dto.getEmpShiftByDate(empId, start);
                    if (shiftdef != null) {
                        boolean isAllowedDateType = allowedDateTypes.contains(shiftdef.getDateType());
                        WaAnalyze analyze = new WaAnalyze();
                        analyze.setBelongOrgId(tenantId);
                        analyze.setEmpid(empId);
                        analyze.setBelongDate(start);
                        analyze.setWorkTime(shiftdef.getWorkTotalTime());
                        analyze.setCrtuser((Long) paramsMap.get("crtuser"));
                        analyze.setCrttime(DateUtil.getCurrentTime(true));
                        analyze.setShiftDefId(shiftdef.getShiftDefId());
                        //查询员工考勤分析分组配置的出勤规则（考勤方案-分析规则-出勤规则），如未查到则默认二次卡出勤规则
                        Integer clockType = ParseGroupClockTypeEnum.SIGN_TWICE.getIndex();
                        WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                        if (analyzeInfo != null && analyzeInfo.getClock_type() != null) {
                            clockType = analyzeInfo.getClock_type();
                        }
                        analyze.setClockType(clockType);
                        if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(clockType)) {
                            //一次卡
                            if (analyzeInfo != null && analyzeInfo.getClock_rule() != null) {
                                JSONObject clockJson = JSONObject.parseObject(analyzeInfo.getClock_rule());
                                Integer missingClockRule = (Integer) clockJson.get("missingClockRule");
                                if (ParseGroupMissingClockRuleEnum.ABSENTEEISM.getIndex().equals(missingClockRule)) {
                                    if (isAllowedDateType) {
                                        //缺卡 视为旷工
                                        if (shiftdef.getWorkTotalTime() > 0) {
                                            analyze.setIsKg(1);
                                        } else {
                                            analyze.setIsKg(0);
                                        }
                                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                    } else {
                                        analyze.setIsKg(0);
                                        analyze.setKgWorkTime(0);
                                    }
                                } else {
                                    if (isAllowedDateType) {
                                        //缺卡 视为缺卡 此逻辑c产品暂未提供，默认旷工
                                        if (shiftdef.getWorkTotalTime() > 0) {
                                            analyze.setIsKg(1);
                                        } else {
                                            analyze.setIsKg(0);
                                        }
                                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                    } else {
                                        analyze.setIsKg(0);
                                        analyze.setKgWorkTime(0);
                                    }
                                }
                            } else {
                                if (isAllowedDateType) {
                                    //未配置缺卡规则默认旷工
                                    if (shiftdef.getWorkTotalTime() > 0) {
                                        analyze.setIsKg(1);
                                    } else {
                                        analyze.setIsKg(0);
                                    }
                                    analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                } else {
                                    analyze.setIsKg(0);
                                    analyze.setKgWorkTime(0);
                                }
                            }
                        } else if (ParseGroupClockTypeEnum.SIGN_TWICE.getIndex().equals(clockType)) {
                            //两次卡
                            if (registerMiss != null) {
                                if (registerMiss == 0) {
                                    //缺卡 视为正常
                                    analyze.setIsKg(0);
                                    analyze.setKgWorkTime(0);
                                } else {
                                    if (isAllowedDateType) {
                                        //缺卡 视为旷工
                                        if (shiftdef.getWorkTotalTime() > 0) {
                                            analyze.setIsKg(1);
                                        } else {
                                            analyze.setIsKg(0);
                                        }
                                        analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                    } else {
                                        analyze.setIsKg(0);
                                        analyze.setKgWorkTime(0);
                                    }
                                }
                            } else {
                                if (isAllowedDateType) {
                                    if (shiftdef.getWorkTotalTime() > 0) {
                                        analyze.setIsKg(1);
                                    } else {
                                        analyze.setIsKg(0);
                                    }
                                    analyze.setKgWorkTime(shiftdef.getWorkTotalTime());
                                } else {
                                    analyze.setIsKg(0);
                                    analyze.setKgWorkTime(0);
                                }
                            }
                        } else {
                            //不打卡
                            analyze.setActualWorkTime(shiftdef.getWorkTotalTime().floatValue());
                            analyze.setRegisterTime(shiftdef.getWorkTotalTime());
                            analyze.setIsKg(0);
                            analyze.setKgWorkTime(0);
                        }

                        analyzeAddList.add(analyze);
                    }
                }
                start += 86400;
            }
        }
        // 删除员工考勤分析数据
        if (!CollectionUtils.isEmpty(empAnalyzeResultDelList)) {
            for (EmpAnalyzeResultDeleteDto deleteDto : empAnalyzeResultDelList) {
                WaAnalyzeExample delExample = new WaAnalyzeExample();
                WaAnalyzeExample.Criteria delCriteria = delExample.createCriteria();
                delCriteria.andBelongOrgIdEqualTo(tenantId)
                        .andEmpidEqualTo(deleteDto.getEmpid())
                        .andBelongDateBetween(deleteDto.getStartDate(), deleteDto.getEndDate());
                waAnalyzeMapper.deleteByExample(delExample);
            }
            // 同步删除薪资考勤数据
            paySyncWaPublish.syncDelWaAnalyze(tenantId, empAnalyzeResultDelList);
        }
        if (CollectionUtils.isNotEmpty(analyzeAddList)) {
            List<List<WaAnalyze>> lists = ListTool.split(analyzeAddList, 350);
            for (List<WaAnalyze> list : lists) {
                importService.fastInsertList(WaAnalyze.class, "analyzeId", list);
            }
        }
    }

    private List<SysEmpInfo> getEmpinfoListByEmpids(String belongid, List<Long> empids) {
        SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
        empInfoExample.createCriteria().andBelongOrgIdEqualTo(belongid).andEmpidIn(empids);
        return sysEmpInfoMapper.selectByExample(empInfoExample);
    }

    /**
     * 获取员工的所属的考勤分组级考勤分组对应的考勤分析分组
     * 获取假期类型
     * 获取加班类型
     *
     * @param paramsMap
     * @return
     */
    @SuppressWarnings("unchecked")
    public WaAnalyzCalDTO getWaAnalyzeInfo(Map<String, Object> paramsMap) throws Exception {
        String belongId = (String) paramsMap.get("belongid");
        WaAnalyzCalDTO dto = new WaAnalyzCalDTO();

        // 查询公司所有的班次
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongId);
        dto.setCorpShiftDefMap(shiftDefMap);

        //查询员工分配的考勤分析分组
        List<Map> empWaGroupMaps = waParseGroupMapper.getEmpWaGroupAndParseGrops(paramsMap);
        if (empWaGroupMaps != null) {
            try {
                String json = objectMapper.writeValueAsString(empWaGroupMaps);
                List<WaAnalyzInfo> empAnalyzeInfos = objectMapper.readValue(json, new TypeReference<List<WaAnalyzInfo>>() {
                });
                for (int i = 0; i < empWaGroupMaps.size(); i++) {
                    Map empAnalyze = empWaGroupMaps.get(i);
                    convertAnalyzeRule(empAnalyzeInfos.get(i), empAnalyze);
                }
                dto.setEmpAnalyzInfos(empAnalyzeInfos);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //查询默认的考勤分析分组
        Map defaultWaGroup = waParseGroupMapper.getDefaultEmpWaGroupAndParseGrop(paramsMap);
        if (defaultWaGroup != null) {
            String json = objectMapper.writeValueAsString(defaultWaGroup);
            WaAnalyzInfo defaultAnalyze = objectMapper.readValue(json, new TypeReference<WaAnalyzInfo>() {
            });
            convertAnalyzeRule(defaultAnalyze, defaultWaGroup);
            dto.setDefaultAnalyz(defaultAnalyze);
        }

        //查询公司假期类型（当逻辑为非导入逻辑时）
        if (!paramsMap.containsKey("import")) {
            List<WaLeaveType> leaveTypes = waConfigService.getLeaveTypes(belongId);
            if (CollectionUtils.isNotEmpty(leaveTypes)) {
                leaveTypes.forEach(lt -> dto.getLtMaps().put(lt.getLeaveTypeId(), lt));
            }
        }

        //查询公司所有的出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(belongId);
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            Map<Long, WaTravelTypeDo> typeDoMap = travelTypeDoList.stream().collect(Collectors.toMap(WaTravelTypeDo::getTravelTypeId, Function.identity()));
            dto.setTravelTypeMaps(typeDoMap);
        }

        //加班类型
        List<WaOvertimeType> otTypes = waConfigService.getOtTypes(belongId);
        if (CollectionUtils.isNotEmpty(otTypes)) {
            otTypes.forEach(ot -> dto.getOtMaps().put(ot.getOvertimeTypeId(), ot));
            List<Long> otTransferRuleIds = otTypes.stream().map(WaOvertimeType::getRuleId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            QueryWrapper<WaOvertimeTransferRulePo> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("rule_id", otTransferRuleIds);
            List<WaOvertimeTransferRulePo> transferRules = overtimeTransferRuleMapper.selectList(queryWrapper);
            dto.setOtTransferRuleMaps(transferRules.stream().collect(Collectors.toMap(WaOvertimeTransferRulePo::getRuleId, Function.identity(), (k1, k2) -> k2)));
        }

        //查询每个人的排班数据
        Map<String, EmpShiftInfo> empShiftInfoByDateMap = new HashMap<>();
        String anyEmpIds = (String) paramsMap.get("anyEmpids");
        List<Long> empIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(anyEmpIds)) {
            List<String> empidList = Arrays.asList(anyEmpIds.replace("'{", "").replace("}'", "").split(","));
            empidList.forEach(id -> empIdList.add(Long.valueOf(id)));
        }
        long endDate = (Long) paramsMap.get("endDate");
        paramsMap.put("endDate", endDate + 1440 * 60);
        Map<String, EmpShiftInfo> empShift = getEmpShiftInfoListMaps(paramsMap, empShiftInfoByDateMap, empIdList, shiftDefMap);
        paramsMap.put("endDate", endDate);
        dto.setEmpShift(empShift);
        dto.setEmpShiftInfoByDateMap(empShiftInfoByDateMap);

        //查询员工信息
        List<SysEmpInfo> empInfoList = getEmpInfoList(belongId, empIdList);
        if (CollectionUtils.isNotEmpty(empInfoList)) {
            Map<Long, SysEmpInfo> empInfoMap = new HashMap<>();
            empInfoList.forEach(row -> empInfoMap.put(row.getEmpid(), row));
            dto.setEmpInfoMap(empInfoMap);
        }
        return dto;
    }

    private void convertAnalyzeRule(WaAnalyzInfo analyzeInfo, Map analyzeMap) throws Exception {
        String key = "absent_condition_jsonb";
        if (analyzeMap.containsKey(key)) {
            PGobject pGobject = (PGobject) analyzeMap.get(key);
            List<AbsenceAnalyzeRule> absenceAnalyzeRules = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<AbsenceAnalyzeRule>>() {
            });
            analyzeInfo.setAbsentConditionRules(absenceAnalyzeRules);
        }
        key = "ot_pase_jsonb";
        if (analyzeMap.containsKey(key)) {
            PGobject pGobject = (PGobject) analyzeMap.get(key);
            List<OtAnalyzeRule> otAnalyzeRules = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<OtAnalyzeRule>>() {
            });
            analyzeInfo.setOtParseRules(otAnalyzeRules);
        }
    }

    private List<SysEmpInfo> getEmpInfoList(String belongId, List<Long> empids) {
        SysEmpInfoExample example = new SysEmpInfoExample();
        SysEmpInfoExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgIdEqualTo(belongId);
        if (empids.size() <= 1000) {
            criteria.andEmpidIn(empids);
        }
        return sysEmpInfoMapper.selectByExample(example);
    }

    private EmpShiftInfo convertWaShiftDef(Long empId, Long workDate, WaShiftDef shiftDef) {
        EmpShiftInfo shift = new EmpShiftInfo();
        BeanUtils.copyProperties(shiftDef, shift);
        shift.setWorkDate(workDate);
        shift.setEmpid(empId);
        Map<String, Object> shiftObj = new HashMap<>();
        shiftObj.put("rest_periods", shiftDef.getRestPeriods());
        shiftObj.put("overtime_rest_periods", shiftDef.getOvertimeRestPeriods());
        shiftObj.put("midwayClockTimes", shiftDef.getMidwayClockTime());
        try {
            convertRestPeriods(shift, shiftObj);
        } catch (Exception e) {
            log.error("AnalyzeResultCalculateService.convertWaShiftDef error msg {}", e.getMessage(), e);
        }
        return shift;
    }

    /**
     * 查询员工排班
     *
     * @param paramsMap
     * @param empShiftInfoByDateMap
     * @param empIdList
     * @param corpShiftDefMap
     * @return
     */
    public Map<String, EmpShiftInfo> getEmpShiftInfoListMaps(
            Map<String, Object> paramsMap, Map<String, EmpShiftInfo> empShiftInfoByDateMap, List<Long> empIdList, Map<Integer, WaShiftDef> corpShiftDefMap) {
        Map<String, EmpShiftInfo> empShift = new HashMap<>();

        String belongid = (String) paramsMap.get("belongid");
        Long startdate = (Long) paramsMap.get("startDate");
        Long preStartDate = startdate - (24 * 60 * 60);
        Long endDate = (Long) paramsMap.get("endDate");

        //班次调整查询
        Map<String, Integer> empChangeShiftMap = waCommonService.getEmpChangeShiftMapByTimeStamp(belongid, empIdList, preStartDate, endDate);

        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, empIdList, preStartDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                for (WorkTimeCalendar ec : calendarList) {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    Long empid = ec.getEmpid();
                    //班次替换
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey) && empChangeShiftMap.get(shiftKey) != null) {
                        ec.setId(empChangeShiftMap.get(shiftKey));
                        empChangeShiftMap.remove(shiftKey);
                    }
                    if (corpShiftDefMap.containsKey(ec.getId()) && corpShiftDefMap.get(ec.getId()) != null) {
                        EmpShiftInfo shift = convertWaShiftDef(empid, workDate, corpShiftDefMap.get(ec.getId()));
                        String key = shift.getEmpid() + "_" + shift.getShiftDefId() + "_" + shift.getWorkDate();
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    }
                }
            }
        } else {
            paramsMap.put("startDate", preStartDate);
            List<Map> shiftinfos = waRegisterRecordMapper.getEmpShiftRecords2(paramsMap);
            paramsMap.put("startDate", startdate);

            if (CollectionUtils.isNotEmpty(shiftinfos)) {
                ObjectMapper objectMapper = new ObjectMapper();
                for (Map<String, Object> shiftObj : shiftinfos) {
                    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    String json = null;
                    EmpShiftInfo shift = null;
                    try {
                        json = objectMapper.writeValueAsString(shiftObj);
                        shift = objectMapper.readValue(json, new TypeReference<EmpShiftInfo>() {
                        });
                        convertRestPeriods(shift, shiftObj);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    if (shift != null) {
                        Long empid = shift.getEmpid();
                        Long workDate = shift.getWorkDate();
                        //班次替换
                        if (empChangeShiftMap.containsKey(empid + "_" + workDate)) {
                            Integer changeShiftId = empChangeShiftMap.get(empid + "_" + workDate);
                            WaShiftDef shiftDef = corpShiftDefMap.get(changeShiftId);
                            if (shiftDef != null) {
                                BeanUtils.copyProperties(shiftDef, shift);
                                convertRestPeriods(shiftDef, shift);
                            }
                            empChangeShiftMap.remove(empid + "_" + workDate);
                        }
                        String key = String.format("%s_%s_%s", shift.getEmpid(), shift.getShiftDefId(), shift.getWorkDate());
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    }
                }
            }
        }

        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            for (Map.Entry<String, Integer> entry : empChangeShiftMap.entrySet()) {
                EmpShiftInfo shift = new EmpShiftInfo();
                WaShiftDef source = corpShiftDefMap.get(entry.getValue());
                if (null == source) {
                    continue;
                }
                BeanUtils.copyProperties(source, shift);
                convertRestPeriods(source, shift);
                String empIdAndWorkDateKey = entry.getKey();
                String[] keyArray = empIdAndWorkDateKey.split("_");
                shift.setEmpid(Long.valueOf(keyArray[0]));
                shift.setWorkDate(Long.valueOf(keyArray[1]));
                empShift.put(String.format("%s_%s_%s", shift.getEmpid(), shift.getShiftDefId(), shift.getWorkDate()), shift);
                empShiftInfoByDateMap.put(entry.getKey(), shift);
            }
        }
        return empShift;
    }

    private void convertRestPeriods(WaShiftDef source, EmpShiftInfo target) {
        if (null == source || null == target) {
            return;
        }
        try {
            if (source.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) source.getRestPeriods();
                List<ShiftRestPeriods> restPeriods = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                });
                target.setRestPeriods(restPeriods);
            }
            if (source.getOvertimeRestPeriods() != null) {
                PGobject overtimeRestPeriodsPGobject = (PGobject) source.getOvertimeRestPeriods();
                List<ShiftRestPeriods> overRest = objectMapper.readValue(overtimeRestPeriodsPGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                });
                target.setOvertimeRestPeriods(overRest);
            }
            if (StringUtils.isNotBlank(source.getMidwayClockTime())) {
                target.setMidwayClockTimeList(FastjsonUtil.toArrayList(source.getMidwayClockTime(), TimeSlot.class));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void convertRestPeriods(EmpShiftInfo empShiftInfo, Map map) throws Exception {
        String key = "rest_periods";
        if (map.containsKey(key) && map.get(key) != null) {
            PGobject pGobject = (PGobject) map.get(key);
            List<ShiftRestPeriods> restPeriods = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
            });
            empShiftInfo.setRestPeriods(restPeriods);
        }
        key = "overtime_rest_periods";
        if (map.containsKey(key) && map.get(key) != null) {
            PGobject pGobject = (PGobject) map.get(key);
            List<ShiftRestPeriods> overRest = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
            });
            empShiftInfo.setOvertimeRestPeriods(overRest);
        }
        key = "midwayClockTimes";
        if (map.containsKey(key) && map.get(key) != null) {
            empShiftInfo.setMidwayClockTimeList(FastjsonUtil.toArrayList(map.get(key).toString(), TimeSlot.class));
        }
    }

    private List<Map> getEmpLeaveList(Map<String, Object> map) {
        List<Integer> approvalStatusList = Lists.newArrayList(2);
        if (waanalyzeAll || (map.get("includeInProgress") != null && (boolean) map.get("includeInProgress"))) {
            approvalStatusList.add(1);
        }
        map.put("approvalStatusList", approvalStatusList);

        //TODO 跨夜班休假自动归属到前一天--1.0 是通过开关配置来控制的，腾讯无配置页面，默认开启
//        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + map.get("belongid") + "_KY_LT_AUTOMATICALLY_ATTRIBUTED_TO_PREVIOUSDAY");
        String isOpen = "1";
        if ("1".equals(isOpen)) {
            Map<String, Object> params = new HashMap<>();
            params.putAll(map);
            Long endDate = (Long) params.get("endDate");
            params.put("endDate", endDate + 86400);
            return empLeaveMapper.getEmpLeaveByEmpid(params);
        } else {
            return empLeaveMapper.getEmpLeaveByEmpid(map);
        }
    }

    private List<WaEmpTravelDaytimeDo> getEmpTravelList(String tenantId, String anyEmpIds, Long startDate, Long endDate) {
        List<Integer> approvalStatusList = Lists.newArrayList(2);
        if (waanalyzeAll) {
            approvalStatusList.add(1);
        }
        long searchEndDate = endDate + 86400;//跨夜班休假自动归属到前一天
        return waEmpTravelDaytimeDo.getEmpTravelDaytimeDetailList(tenantId, anyEmpIds, startDate, searchEndDate, approvalStatusList);
    }

    /**
     * 根据员工id，签到时间，班次 查询 （班次信息 日期类型）
     *
     * @param dto
     * @return
     */
    public EmpShiftInfo getEmpShiftDefByInfo(Long empid, Integer shiftDefId, Long workDate, WaAnalyzCalDTO dto) {
        EmpShiftInfo shiftdef = null;
        //如果对应的班次不存在，则去查询当前所属班次
        if (shiftDefId == null) {
            shiftdef = dto.getEmpShiftByDate(empid, workDate);
            if (shiftdef != null) {
                return shiftdef;
            }
            /*WaShiftDef def = this.getEmpShiftInfo(workDate, empid);
            if (def != null) {
                shiftdef = new EmpShiftInfo();
                BeanUtils.copyProperties(def, shiftdef);
                convertRestPeriods(def, shiftdef);
                shiftdef.setEmpid(empid);
                shiftdef.setWorkDate(workDate);
                String key = empid + "_" + def.getShiftDefId() + "_" + workDate;
                dto.getEmpShift().put(key, shiftdef);
            }*/
        } else {
            shiftdef = dto.getEmpShift(empid, shiftDefId, workDate);
            if (shiftdef == null) {
                shiftdef = dto.getEmpShiftByDate(empid, workDate);
            }
        }
        return shiftdef;
    }

    /**
     * 根据签到时间 empid 查询员工对应的排班，包括非门店，门店排班
     *
     * @param regTime
     * @param empid
     * @return
     */
    private WaShiftDef getEmpShiftInfo(Long regTime, Long empid) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("empid", empid);
        Long belongDate = DateUtil.getDateLong(regTime * 1000, "yyyy-MM-dd", true);
        String date = DateUtil.convertDateTimeToStr(regTime, "yyyy-MM-dd", true);
        Integer ym = Integer.valueOf(date.substring(0, date.lastIndexOf("-")).replaceAll("-", ""));
        Integer day = Integer.valueOf(date.substring(date.lastIndexOf("-") + 1));
        params.put("workDate", belongDate);// 年月日的秒
        params.put("startdate", ym);//年月
        params.put("day", day);//日

        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);

        WaShiftDef shiftdef = null;
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {
            shiftdef = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empid, belongDate);
            if (shiftdef != null && shiftdef.getDateType() != null && DateTypeEnum.DATE_TYP_4.getIndex().equals(shiftdef.getDateType())) {
                shiftdef.setDateType(DateTypeEnum.DATE_TYP_2.getIndex());
            }
        } else {
            List<Map> shiftinfos = waRegisterRecordMapper.getEmpShiftRecord(params);
            if (CollectionUtils.isNotEmpty(shiftinfos) && shiftinfos.size() > 0) {

                Map map = shiftinfos.get(0);
                Integer shift_def_id = (Integer) map.get("shift_def_id");
                if (shift_def_id != null) {
                    shiftdef = new WaShiftDef();
                } else {
                    return shiftdef;
                }
                shiftdef.setShiftDefId(shift_def_id);
                //签到打卡开始时间
                Integer on_duty_start_time = (Integer) map.get("on_duty_start_time");
                shiftdef.setOnDutyStartTime(on_duty_start_time);
                //打卡截止
                Integer on_duty_end_time = (Integer) map.get("on_duty_end_time");
                shiftdef.setOnDutyEndTime(on_duty_end_time);
                //签退打卡开始时间
                Integer off_duty_start_time = (Integer) map.get("off_duty_start_time");
                shiftdef.setOffDutyStartTime(off_duty_start_time);
                Integer off_duty_end_time = (Integer) map.get("off_duty_end_time");
                shiftdef.setOffDutyEndTime(off_duty_end_time);
                //1、工作日，2休息日，3法定假日 4 公司特殊假日
                Integer dateType = (Integer) map.get("date_type");
                //如果是公司特殊假日，则按休息日类型计算
                if (DateTypeEnum.DATE_TYP_4.getIndex().equals(dateType)) {
                    dateType = DateTypeEnum.DATE_TYP_2.getIndex();
                }
                shiftdef.setDateType(dateType);
                Integer rest_total_time = (Integer) map.get("rest_total_time");
                shiftdef.setRestTotalTime(rest_total_time);
                Integer work_total_time = (Integer) map.get("work_total_time");
                shiftdef.setWorkTotalTime(work_total_time);
                Integer start_time = (Integer) map.get("start_time");
                shiftdef.setStartTime(start_time);
                Integer end_time = (Integer) map.get("end_time");
                shiftdef.setEndTime(end_time);
                Boolean is_noon_rest = (Boolean) map.get("is_noon_rest");
                shiftdef.setIsNoonRest(is_noon_rest);
                Integer noon_rest_start = (Integer) map.get("noon_rest_start");
                shiftdef.setNoonRestStart(noon_rest_start);
                Integer noon_rest_end = (Integer) map.get("noon_rest_end");
                shiftdef.setNoonRestEnd(noon_rest_end);

                // 是否启用了半天时间定义
                Boolean isHalfdayTime = (Boolean) map.get("isHalfdayTime");
                shiftdef.setIsHalfdayTime(isHalfdayTime);

                Integer halfdayTime = (Integer) map.get("halfdayTime");
                shiftdef.setHalfdayTime(halfdayTime);

                Boolean is_special = (Boolean) map.get("is_special");
                shiftdef.setIsSpecial(is_special);

                Integer special_work_time = (Integer) map.get("special_work_time");
                shiftdef.setSpecialWorkTime(special_work_time);

                Integer flexibleWorkRule = (Integer) map.get("flexibleWorkRule");
                shiftdef.setFlexibleWorkRule(flexibleWorkRule);

                BigDecimal flexibleWorkLate = map.get("flexibleWorkLate") == null ? new BigDecimal(0) : (BigDecimal) map.get("flexibleWorkLate");
                shiftdef.setFlexibleWorkLate(flexibleWorkLate);

                String flexibleOffWorkRule = (String) map.get("flexibleOffWorkRule");
                shiftdef.setFlexibleOffWorkRule(flexibleOffWorkRule);

            }
        }
        return shiftdef;
    }

    /**
     * 检查当天是否有请假数据
     *
     * @param dto
     * @param empId
     * @param date
     * @return
     */
    private Boolean checkApplyLeaveByDate(WaAnalyzCalDTO dto, Long empId, Long date, Boolean isFlexibleWorking, Map<String, Object> params) {
        if (BooleanUtils.isTrue(isFlexibleWorking)) {
            if (dto.getEmpLeaveInfoList() == null) {
                dto.setEmpLeaveInfo(getEmpLeaveList(params));
            }
            //组合员工的请假单 key ： empid+"_"+leave_date;
            String key = empId + "_" + date;
            Map<String, List<EmpLeaveInfo>> empLeaveAfterMap = new HashMap<>();
            Map<String, List<EmpLeaveInfo>> empLeaveMap = this.getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empLeaveAfterMap);
            boolean leaveFlag = (empLeaveMap.containsKey(key) && CollectionUtils.isNotEmpty(empLeaveMap.get(key).stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList())))
                    || (empLeaveAfterMap.containsKey(key) && CollectionUtils.isNotEmpty(empLeaveAfterMap.get(key).stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList())));
            if (dto.getEmpTravelInfoList() == null && params.get("anyEmpids2") != null) {
                Long startDate = (Long) params.get("startDate");
                Long endDate = (Long) params.get("endDate");
                String tenantId = (String) params.get("belongid");
                List<WaEmpTravelDaytimeDo> empTravelList = getEmpTravelList(tenantId, (String) params.get("anyEmpids2"), startDate, endDate);
                dto.setEmpTravelInfo(empTravelList);
            }
            Map<String, List<WaEmpTravelDaytimeDo>> empTravelAfterMap = new HashMap<>();
            Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap = this.getEmpTravelInfos(dto.getEmpTravelInfoList(), dto, empTravelAfterMap);
            boolean travelFlag = (empTravelMap.containsKey(key) && CollectionUtils.isNotEmpty(empTravelMap.get(key))) || (empTravelAfterMap.containsKey(key) && CollectionUtils.isNotEmpty(empTravelAfterMap.get(key)));
            return leaveFlag || travelFlag;
        }
        return false;
    }

    @SuppressWarnings("rawtypes")
    private Map<String, List<EmpLeaveInfo>> getEmpLeaveInfos(List<EmpLeaveInfo> empLeaves, WaAnalyzCalDTO dto, Map<String, List<EmpLeaveInfo>> empleaveAfterMap) {
        Map<String, List<EmpLeaveInfo>> empleaveMap = new HashMap<>();
        if (empLeaves != null && empLeaves.size() > 0) {
            for (EmpLeaveInfo lf : empLeaves) {
                Long empid = lf.getEmpid();
                //请假时间归属在那一天上，以审批时间为准，如果审批时间小于请假时间以请假时间为准，这个判断在审批时已经判断过
                Long real_date = lf.getReal_date();
                Long leave_date = lf.getLeave_date();
                if (real_date == null) {
                    real_date = leave_date;
                }
                //求每一天对应的班次信息
                EmpShiftInfo empShiftInfo = this.getEmpShiftDefByInfo(empid, null, lf.getLeave_date(), dto);
                lf.setEmpShiftInfo(empShiftInfo);
                if (real_date.equals(leave_date)) {
                    //先判断休假日期前一天是否为跨夜班，如果前一天为跨夜班并且休假类型为小时，则判断休假但是否归属到前一天，判断条件为：休假时间在前一天上班区间之内
                    if (lf.getPeriod_type() != null && lf.getPeriod_type() == 3) {
                        Long preDay = DateUtil.addDate(lf.getLeave_date() * 1000, -1);
                        EmpShiftInfo preDayShift = this.getEmpShiftDefByInfo(empid, null, preDay, dto);
                        if (preDayShift != null && preDayShift.getDateType() == 1 &&
                                CdWaShiftUtil.checkCrossNight(preDayShift.getStartTime(), preDayShift.getEndTime(), preDayShift.getDateType())) {
                            Long leaveStart = lf.getLeave_date() + lf.getStart_time() * 60;
                            Long leaveEnd = lf.getLeave_date() + lf.getEnd_time() * 60;
                            if (lf.getEnd_time() < lf.getStart_time()) {
                                leaveEnd += 86400;
                            }
                            Long shiftStart = preDay + preDayShift.getStartTime() * 60;
                            Long shiftEnd = lf.getLeave_date() + preDayShift.getEndTime() * 60;
                            if (leaveStart >= shiftStart && leaveEnd <= shiftEnd) {
                                real_date = preDay;
                            }
                        }
                    }
                }
                // 1.如果最后审批完成时间大于请假日期 则把请假时间计算在审批完成日期上
                //empleaveAfterMap 返回 审批时间 大于 请假时间的请假记录
                if (real_date > leave_date) {
                    String key = empid + "_" + leave_date;
                    if (empleaveAfterMap.containsKey(key)) {
                        empleaveAfterMap.get(key).add(lf);
                    } else {
                        List<EmpLeaveInfo> list = new ArrayList<>();
                        list.add(lf);
                        empleaveAfterMap.put(key, list);
                    }
                }
                // 以审批时间为key 返回请假记录
                String key = empid + "_" + real_date;
                if (!empleaveMap.containsKey(key)) {
                    List<EmpLeaveInfo> list = new ArrayList<>();
                    list.add(lf);
                    empleaveMap.put(key, list);
                } else {
                    empleaveMap.get(key).add(lf);
                }
            }
        }
        return empleaveMap;
    }

    @SuppressWarnings("rawtypes")
    private Map<String, List<WaEmpTravelDaytimeDo>> getEmpTravelInfos(List<WaEmpTravelDaytimeDo> empTravels, WaAnalyzCalDTO dto,
                                                                      Map<String, List<WaEmpTravelDaytimeDo>> empTravelAfterMap) {
        if (CollectionUtils.isEmpty(empTravels)) {
            return new HashMap<>();
        }
        Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap = new HashMap<>();

        for (WaEmpTravelDaytimeDo lf : empTravels) {
            Long empid = lf.getEmpId();
            //请假时间归属在那一天上，以审批时间为准，如果审批时间小于请假时间以请假时间为准，这个判断在审批时已经判断过
            Long real_date = lf.getRealDate();
            Long leave_date = lf.getTravelDate();
            if (real_date == null) {
                real_date = leave_date;
            }
            //求每一天对应的班次信息
            EmpShiftInfo empShiftInfo = this.getEmpShiftDefByInfo(empid, null, lf.getTravelDate(), dto);
            lf.setEmpShiftInfo(empShiftInfo);
            if (real_date.equals(leave_date)) {
                //先判断休假日期前一天是否为跨夜班，如果前一天为跨夜班并且休假类型为小时，则判断休假但是否归属到前一天，判断条件为：休假时间在前一天上班区间之内
                if (lf.getPeriodType() != null && lf.getPeriodType() == 3) {
                    Long preDay = DateUtil.addDate(lf.getTravelDate() * 1000, -1);
                    EmpShiftInfo preDayShift = this.getEmpShiftDefByInfo(empid, null, preDay, dto);
                    if (preDayShift != null && preDayShift.getDateType() == 1 && CdWaShiftUtil.checkCrossNight(preDayShift.getStartTime(), preDayShift.getEndTime(), preDayShift.getDateType())) {
                        Long leaveStart = lf.getTravelDate() + lf.getStartTime() * 60;
                        Long leaveEnd = lf.getTravelDate() + lf.getEndTime() * 60;
                        if (lf.getEndTime() < lf.getStartTime()) {
                            leaveEnd += 86400;
                        }
                        Long shiftStart = preDay + preDayShift.getStartTime() * 60;
                        Long shiftEnd = lf.getTravelDate() + preDayShift.getEndTime() * 60;
                        if (leaveStart >= shiftStart && leaveEnd <= shiftEnd) {
                            real_date = preDay;
                        }
                    }
                }
            }

            // 1.如果最后审批完成时间大于请假日期 则把请假时间计算在审批完成日期上
            //empleaveAfterMap 返回 审批时间 大于 请假时间的请假记录
            if (real_date > leave_date) {
                String key = empid + "_" + leave_date;
                if (empTravelAfterMap.containsKey(key)) {
                    empTravelAfterMap.get(key).add(lf);
                } else {
                    List<WaEmpTravelDaytimeDo> list = new ArrayList<>();
                    list.add(lf);
                    empTravelAfterMap.put(key, list);
                }
            }
            // 以审批时间为key 返回请假记录
            String key = empid + "_" + real_date;
            if (!empTravelMap.containsKey(key)) {
                List<WaEmpTravelDaytimeDo> list = new ArrayList<>();
                list.add(lf);
                empTravelMap.put(key, list);
            } else {
                empTravelMap.get(key).add(lf);
            }
        }
        return empTravelMap;
    }

    /**
     * 判断是否需要进行签到签退分析
     *
     * @param analyzeInfo
     * @return
     */
    private boolean isAnalyzeLateEarly(WaAnalyzInfo analyzeInfo) {
        if (analyzeInfo != null) {
            return analyzeInfo.getIs_analyze_late_early() == null || analyzeInfo.getIs_analyze_late_early();
        }
        return true;
    }

    // 获得迟到时间
    private float getLateTime(WaAnalyzCalDTO dto, Long regTime, EmpShiftInfo shift, Boolean isFlexibleWorking, Map<String, Object> paramsMap) {
        if (shift == null || shift.getStartTime() == null || shift.getEndTime() == null) {
            return 0;
        }
        Calendar c = Calendar.getInstance();
        c.clear();
        c.setTimeInMillis(regTime);
        int h = c.get(Calendar.HOUR_OF_DAY);
        int m2 = c.get(Calendar.MINUTE);
        Integer seconds = c.get(Calendar.SECOND);
        float regMinute = h * 60 + m2 + (seconds.floatValue() / 60f);
        Long yqdsj = shift.getWorkDate();
        yqdsj = (yqdsj + (shift.getStartTime() * 60)) * 1000;
        Long regDate = DateUtil.getDateLong(regTime, "yyyy-MM-dd", true);
        //CLOUD-8623
        Boolean leaveFlag = checkApplyLeaveByDate(dto, shift.getEmpid(), regDate, isFlexibleWorking, paramsMap);
        //CLOUD-3848 考勤分析选择“按弹性时间分析” 并且 班次上也启用了 弹性工作时间”
        if (!leaveFlag && isFlexibleWorking && BooleanUtils.isTrue(shift.getIsFlexibleWork()) && shift.getFlexibleWorkType() == 1) {
            Integer restStartTime = shift.getNoonRestStart();
            Integer restEndTime = shift.getNoonRestEnd();
            // 超过弹性上班结束时间的签到，以弹性上班结束时间为标准计算迟到分钟数
            if (regMinute > shift.getFlexibleOnDutyEndTime()) {
                float result = regMinute - shift.getFlexibleOnDutyEndTime();
                // 判断是否在休息时间段内，如在区间范围内或超过了休息截止时间，则扣除中午休息时间
                if ((regMinute <= restEndTime && regMinute >= restStartTime) || regMinute > restEndTime) {
                    result -= Math.min(regMinute, restEndTime) - restStartTime;
                }
                return this.getFloatTime(String.valueOf(result));
            }
        } else {
            return getLateTime(regTime, shift, regMinute, yqdsj);
        }
        return 0;
    }

    /**
     * 获取迟到时间 -弹性设置  早到早走,晚到晚走
     *
     * @param regTime
     * @param shift
     * @return
     */
    private float getFlexibleLateTime(Long regTime, EmpShiftInfo shift, WaAnalyzInfo analyzeInfo, Boolean leaveFlag) {
        if (shift == null || shift.getEndTime() == null || shift.getStartTime() == null) {
            return 0f;
        }
        long belongDate = shift.getWorkDate();
        Long startTime = shift.getWorkDate();
        startTime = (startTime + (shift.getStartTime() * 60)) * 1000;
        //上班时间
        Long clockInTime = startTime;
        //上班时间(秒)
        Long workTimeMinute = shift.getStartTime() * 60L;
        //弹性时间(秒)
        BigDecimal workLate = shift.getFlexibleWorkLate() == null ? BigDecimal.ZERO : shift.getFlexibleWorkLate();
        long workLateMin = workLate.multiply(BigDecimal.valueOf(3600)).longValue();
        long flexibleMin = workTimeMinute + workLateMin;
        //弹性上班时间
        long clockInTimeForFlexible = startTime + workLateMin * 1000L;
        Integer flexibleWorkType = analyzeInfo.getFlexibleWorkType();
        Integer flexibleWorkRule = shift.getFlexibleWorkRule();
        //按弹性区间分析
        if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
            workTimeMinute = flexibleMin;
            clockInTime = clockInTimeForFlexible;
        } else {
            if (!leaveFlag) {
                clockInTime = clockInTimeForFlexible;
            }
        }
        long workTimeSecond = (belongDate + workTimeMinute) * 1000;
        long lateTime = 0;
        Integer noonRestStartMin = shift.getNoonRestStart();
        Integer noonRestEndMin = shift.getNoonRestEnd();
        ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
        noonRestStartMin = restPeriod.getNoonRestStart();
        noonRestEndMin = restPeriod.getNoonRestEnd();
        if (BooleanUtils.isTrue(shift.getIsHalfdayTime()) && shift.getHalfdayTime() != null && shift.getHalfdayTime() >= 0) {
            long halfDayTime = (belongDate + shift.getHalfdayTime() * 60) * 1000;
            if (shift.getHalfdayTime() < shift.getStartTime()) {
                halfDayTime = DateUtil.addDate(halfDayTime, 1) * 1000;
            }
            if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                //设置了中午休息时间
                long noonRestStart = (belongDate + noonRestStartMin * 60) * 1000;
                long noonRestEnd = (belongDate + noonRestEndMin * 60) * 1000;
                /*if (noonRestEnd < noonRestStart) {
                    noonRestEnd = DateUtil.addDate(noonRestEnd, 1) * 1000;
                }*/
                if (halfDayTime < noonRestStart) {
                    if (regTime > halfDayTime) {
                        if (regTime <= noonRestStart) {
                            //签到时间在半天时间点和休息开始时间之间
                            lateTime = regTime - workTimeSecond;
                        } else if (regTime < noonRestEnd) {
                            //签到时间在休息时间段
                            lateTime = noonRestStart - workTimeSecond;
                        } else {
                            //签到时间超出休息时间段结束时间
                            lateTime = (noonRestStart - workTimeSecond) + (regTime - noonRestEnd);
                        }
                    } else {
                        if (regTime > clockInTime) {
                            if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                                lateTime = regTime - clockInTime;
                            } else {
                                lateTime = regTime - startTime;
                            }
                        }
                    }
                } else if (halfDayTime <= noonRestEnd) {
                    //签到时间在休息时间范围内
                    if (regTime >= noonRestStart && regTime <= noonRestEnd) {
                        lateTime = noonRestStart - workTimeSecond;
                    } else if (regTime > noonRestEnd) {
                        lateTime = (noonRestStart - workTimeSecond) + (regTime - noonRestEnd);
                    } else {
                        if (regTime > clockInTime) {
                            if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                                lateTime = regTime - clockInTime;
                            } else {
                                lateTime = regTime - startTime;
                            }
                        }
                    }
                } else {
                    if (regTime < halfDayTime) {
                        if (regTime > noonRestEnd) {
                            lateTime = (noonRestStart - workTimeSecond) + (regTime - noonRestEnd);
                        } else if (regTime >= noonRestStart) {
                            if (noonRestStart > workTimeSecond) {
                                lateTime = noonRestStart - workTimeSecond;
                            }
                        } else {
                            if (regTime > clockInTime) {
                                if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                                    lateTime = regTime - clockInTime;
                                } else {
                                    lateTime = regTime - startTime;
                                }
                            }
                        }
                    } else {
                        lateTime = (noonRestStart - workTimeSecond) + (regTime - noonRestEnd);
                    }
                }
            } else {
                if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                    if (FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(flexibleWorkRule)) {
                        halfDayTime += workLateMin * 1000;
                    }
                }
                if (regTime > halfDayTime) {
                    lateTime = regTime - workTimeSecond;
                } else {
                    if (regTime > clockInTime) {
                        if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                            lateTime = regTime - clockInTime;
                        } else {
                            lateTime = regTime - startTime;
                        }
                    }
                }
            }
        } else if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
            //判断是否有休息时间
            long noonRestStart = (belongDate + noonRestStartMin * 60) * 1000;
            long noonRestEnd = (belongDate + noonRestEndMin * 60) * 1000;
            //弹性时间超过了休息时间
            if (workTimeSecond > noonRestStart) {
                long diff = workLateMin - (noonRestStartMin - shift.getStartTime()) * 60L;
                long lateHour = noonRestEndMin * 60L + diff;
                clockInTime = (shift.getWorkDate() + lateHour) * 1000;
                if (regTime > clockInTime) {
                    if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                        lateTime = regTime - clockInTime;
                    } else {
                        lateTime = regTime - startTime;
                    }
                }
            } else {
                //签到处在休息时间范围内
                if (regTime >= noonRestStart && regTime <= noonRestEnd) {
                    lateTime = noonRestStart - workTimeSecond;
                } else if (regTime > noonRestEnd) {
                    //在休息时间以后
                    lateTime = (noonRestStart - workTimeSecond) + (regTime - noonRestEnd);
                } else {
                    if (regTime > clockInTime) {
                        if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                            lateTime = regTime - clockInTime;
                        } else {
                            lateTime = regTime - startTime;
                        }
                    }
                }
            }
        } else {
            if (regTime > clockInTime) {
                if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(flexibleWorkType)) {
                    lateTime = regTime - clockInTime;
                } else {
                    lateTime = regTime - startTime;
                }
            }
        }
        return BigDecimal.valueOf(lateTime / 1000).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue();
    }

    /**
     * 获取迟到时间 -弹性设置  前一天晚走，第二天晚到
     *
     * @param regTime
     * @param shift
     * @return
     */
    private float getLateTimeForGoLate(Long regTime, Long todayRegTime, EmpShiftInfo yesterdayShift, EmpShiftInfo shift, float num, WaAnalyzCalDTO dto) {
        if (yesterdayShift == null || yesterdayShift.getStartTime() == null || yesterdayShift.getEndTime() == null) {
            return num;
        }
        Long belongDate = DateUtil.getDateLong(regTime, "yyyy-MM-dd", true);
        Long today = DateUtil.getDateLong(todayRegTime, "yyyy-MM-dd", true);
        if (today.equals(belongDate)) {
            belongDate = DateUtil.addDate(belongDate * 1000, -1);
        }
        Long yesterdayStartTime = (belongDate + (yesterdayShift.getStartTime() * 60)) * 1000;
        Long yesterdayEndTime = (belongDate + (yesterdayShift.getEndTime() * 60)) * 1000;
        if (yesterdayEndTime < yesterdayStartTime) {
            yesterdayEndTime += 24 * 3600 * 1000;
        }
        if (regTime > yesterdayEndTime) {
            String jsonStr = shift.getFlexibleOffWorkRule();
            if (StringUtils.isNotBlank(jsonStr)) {
                List<FlexibleDto> list = JsonTool.json2list(jsonStr, FlexibleDto.class);
                list.sort(new Comparator<FlexibleDto>() {
                    @Override
                    public int compare(FlexibleDto o1, FlexibleDto o2) {
                        return o2.getSignOffLateTime().compareTo(o1.getSignOffLateTime());
                    }
                });
                Double min = BigDecimal.valueOf((regTime - yesterdayEndTime) / 1000).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).doubleValue();
                Double sigInLateTime = 0d;
                for (FlexibleDto flexibleDto : list) {
                    BigDecimal lateTime = flexibleDto.getSignOffLateTime() == null ? BigDecimal.valueOf(0f) : flexibleDto.getSignOffLateTime();
                    double maxTime = lateTime.doubleValue() * 60;
                    if (min >= maxTime) {
                        BigDecimal sigIn = flexibleDto.getSignInLateTime() == null ? BigDecimal.valueOf(0f) : flexibleDto.getSignInLateTime();
                        Double v = sigIn.doubleValue() * 60;
                        sigInLateTime = v;
                        dto.setExemptDurationMaps(shift.getEmpid(), shift.getWorkDate(), v);
                        break;
                    }
                }
                long startTime = (today + (shift.getStartTime() * 60)) * 1000;
                Double clockInTimeForFlexible = startTime + (sigInLateTime * 60 * 1000);
                Double lateTime = 0d;
                Integer noonRestStartMin = shift.getNoonRestStart();
                Integer noonRestEndMin = shift.getNoonRestEnd();
                ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
                noonRestStartMin = restPeriod.getNoonRestStart();
                noonRestEndMin = restPeriod.getNoonRestEnd();
                if (BooleanUtils.isTrue(shift.getIsHalfdayTime()) && shift.getHalfdayTime() != null && shift.getHalfdayTime() > 0) {
                    Double halfDayTime = (today + shift.getHalfdayTime().doubleValue() * 60) * 1000;
                    if (halfDayTime < startTime) {
                        halfDayTime += 24 * 3600 * 1000;
                    }
                    if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                        //设置了中午休息时间
                        Double noonRestStartTime = (today + noonRestStartMin.doubleValue() * 60) * 1000;
                        Double noonRestEndTime = (today + noonRestEndMin.doubleValue() * 60) * 1000;
                        /*if (noonRestEndTime < noonRestStartTime) {
                            noonRestEndTime += 24 * 3600 * 1000;
                        }*/
                        if (halfDayTime < noonRestStartTime) {
                            if (todayRegTime > halfDayTime) {
                                if (todayRegTime <= noonRestStartTime) {
                                    lateTime = todayRegTime - clockInTimeForFlexible;
                                } else if (todayRegTime < noonRestEndTime) {
                                    lateTime = noonRestStartTime - clockInTimeForFlexible;
                                } else {
                                    lateTime = (noonRestStartTime - clockInTimeForFlexible) + (todayRegTime - noonRestEndTime);
                                }
                            } else {
                                if (todayRegTime > clockInTimeForFlexible) {
                                    lateTime = todayRegTime - clockInTimeForFlexible;
                                }
                            }
                        } else if (halfDayTime <= noonRestEndTime) {
                            if (todayRegTime >= noonRestStartTime && todayRegTime <= noonRestEndTime) {
                                lateTime = noonRestStartTime - clockInTimeForFlexible;
                            } else if (todayRegTime > noonRestEndTime) {
                                lateTime = (noonRestStartTime - clockInTimeForFlexible) + (todayRegTime - noonRestEndTime);
                            } else {
                                if (todayRegTime > clockInTimeForFlexible) {
                                    lateTime = todayRegTime - clockInTimeForFlexible;
                                }
                            }
                        } else {
                            if (todayRegTime < halfDayTime) {
                                if (todayRegTime > noonRestEndTime) {
                                    lateTime = (noonRestStartTime - clockInTimeForFlexible) + (todayRegTime - noonRestEndTime);
                                } else if (todayRegTime >= noonRestStartTime) {
                                    lateTime = noonRestStartTime - clockInTimeForFlexible;
                                } else {
                                    if (todayRegTime > clockInTimeForFlexible) {
                                        lateTime = todayRegTime - clockInTimeForFlexible;
                                    }
                                }
                            } else {
                                lateTime = (noonRestStartTime - clockInTimeForFlexible) + (todayRegTime - noonRestEndTime);
                            }
                        }
                    } else {
                        if (todayRegTime > clockInTimeForFlexible) {
                            lateTime = todayRegTime - clockInTimeForFlexible;
                        }
                    }
                } else if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                    //是否有休息时间
                    Long noonRestStartTime = (today + (noonRestStartMin * 60)) * 1000;
                    Long noonRestEndTime = (today + noonRestEndMin * 60) * 1000;
                    /*if (noonRestEndTime < noonRestStartTime) {
                        noonRestEndTime += 24 * 3600 * 1000;
                    }*/
                    //在休息时间范围内
                    if (clockInTimeForFlexible >= noonRestStartTime && clockInTimeForFlexible <= noonRestEndTime) {
                        if (todayRegTime > noonRestEndTime) {
                            lateTime = BigDecimal.valueOf(todayRegTime - noonRestEndTime).doubleValue();
                        }
                    } else if (clockInTimeForFlexible < noonRestStartTime) {
                        if (todayRegTime >= noonRestStartTime && todayRegTime <= noonRestEndTime) {
                            lateTime = noonRestStartTime - clockInTimeForFlexible;
                        } else if (todayRegTime > noonRestEndTime) {
                            lateTime = (todayRegTime - clockInTimeForFlexible - (noonRestEndTime - noonRestStartTime));
                        } else {
                            lateTime = todayRegTime - clockInTimeForFlexible;
                        }
                    } else {
                        lateTime = todayRegTime - clockInTimeForFlexible;
                    }
                }
                if (lateTime > 0) {
                    return BigDecimal.valueOf(lateTime / 1000).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue();
                }
            }
        }
        return num;
    }

    private float getLateTime(Long regTime, EmpShiftInfo shift, float regMinute, Long yqdsj) {
        if (shift.getStartTime() == 0 || shift.getEndTime() == 0) {
            return 0;
        }
        Long lateTime = 0L;
        long belongDate = shift.getWorkDate();
        //上班时间点
        long shiftStartTime = (belongDate + shift.getStartTime() * 60) * 1000;
        Integer noonRestStart = shift.getNoonRestStart();
        Integer noonRestEnd = shift.getNoonRestEnd();
        ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
        noonRestStart = restPeriod.getNoonRestStart();
        noonRestEnd = restPeriod.getNoonRestEnd();
        // 定义了半天时间点
        if (BooleanUtils.isTrue(shift.getIsHalfdayTime()) && shift.getHalfdayTime() != null && shift.getHalfdayTime() >= 0) {
            long halfDayTime = (belongDate + shift.getHalfdayTime() * 60) * 1000;
            if (halfDayTime < shiftStartTime) {
                halfDayTime = DateUtil.addDate(halfDayTime, 1) * 1000;
            }

            if (BooleanUtils.isTrue(shift.getIsNoonRest()) && noonRestStart != null && noonRestEnd != null) {
                //设置了中午休息时间
                long noonRestStartTime = (belongDate + noonRestStart * 60) * 1000;
                long noonRestEndTime = (belongDate + noonRestEnd * 60) * 1000;
                /*if (noonRestEndTime < noonRestStartTime) {
                    noonRestEndTime = DateUtil.addDate(noonRestEndTime, 1) * 1000;
                }*/
                if (halfDayTime < noonRestStartTime) {
                    if (regTime > halfDayTime) {
                        if (regTime <= noonRestStartTime) {
                            lateTime = regTime - shiftStartTime;
                        } else if (regTime < noonRestEndTime) {
                            lateTime = noonRestStartTime - shiftStartTime;
                        } else {
                            lateTime = (noonRestStartTime - shiftStartTime) + (regTime - noonRestEndTime);
                        }
                    } else {
                        if (regTime > yqdsj) {
                            lateTime = regTime - yqdsj;
                        }
                    }
                } else if (halfDayTime <= noonRestEndTime) {
                    if (regTime >= noonRestStartTime && regTime <= noonRestEndTime) {
                        lateTime = noonRestStartTime - shiftStartTime;
                    } else if (regTime > noonRestEndTime) {
                        lateTime = (noonRestStartTime - shiftStartTime) + (regTime - noonRestEndTime);
                    } else {
                        if (regTime > yqdsj) {
                            lateTime = regTime - yqdsj;
                        }
                    }
                } else {
                    if (regTime < halfDayTime) {
                        if (regTime > noonRestEndTime) {
                            lateTime = (noonRestStartTime - shiftStartTime) + (regTime - noonRestEndTime);
                        } else if (regTime >= noonRestStartTime) {
                            lateTime = noonRestStartTime - shiftStartTime;
                        } else {
                            if (regTime > yqdsj) {
                                lateTime = regTime - yqdsj;
                            }
                        }
                    } else {
                        lateTime = (noonRestStartTime - shiftStartTime) + (regTime - noonRestEndTime);
                    }
                }
            } else {
                if (regTime > halfDayTime) {
                    lateTime = regTime - shiftStartTime;
                } else {
                    if (regTime > yqdsj) {
                        lateTime = regTime - yqdsj;
                    }
                }
            }
        } else if (BooleanUtils.isTrue(shift.getIsNoonRest()) && noonRestStart != null && noonRestEnd != null) {
            long noonRestStartTime = (belongDate + noonRestStart * 60) * 1000;
            long noonRestEndTime = (belongDate + noonRestEnd * 60) * 1000;
            /*if (noonRestEndTime < noonRestStartTime) {
                noonRestEndTime = DateUtil.addDate(noonRestEndTime, 1) * 1000;
            }*/
            // 签到时间处在休息时间范围内
            if (regTime >= noonRestStartTime && regTime <= noonRestEndTime) {
                lateTime = noonRestStartTime - shiftStartTime;
            } else if (regTime > noonRestEndTime) {
                // 签到时间在休息截止时间后应减去休息时间
                lateTime = (noonRestStartTime - shiftStartTime) + (regTime - noonRestEndTime);
            } else {
                if (regTime > yqdsj) {
                    // 返回迟到分钟数
                    lateTime = regTime - yqdsj;
                }
            }
        } else {
            //签到时间在应签到时间之后
            if (regTime > yqdsj) {
                // 返回迟到分钟数
                lateTime = regTime - yqdsj;
            }
        }
        return BigDecimal.valueOf(lateTime / 1000).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue();
    }

    private float getFloatTime(String value) {
        return new BigDecimal(value).setScale(2, RoundingMode.HALF_UP).floatValue();
    }

    /**
     * 根据弹性打卡区间计算早退时长
     *
     * @param reBelongDate
     * @param regtime
     * @param shift
     * @return
     */
    private Integer getEarlyTimeForFlexible(Long reBelongDate, Long regtime, EmpShiftInfo shift) {
        Integer minute = 0;
        if (shift.getFlexibleOffDutyStartTime() != null) {
            //在弹性下班开始时间前的签退，以弹性下班开始时间为标准计算早退分钟数
            Long flexibleOffDutyStartTime = reBelongDate + (shift.getFlexibleOffDutyStartTime() * 60);
            if (regtime < flexibleOffDutyStartTime) {
                Long result = flexibleOffDutyStartTime - regtime;
                result = decRestTime(reBelongDate, regtime, shift, result);
                minute = result.intValue();
            }
        }
        return minute;
    }

    /**
     * 分析早退时长
     *
     * @param dto
     * @param analyze
     * @param shift
     * @param isFlexibleWorking
     * @param paramsMap
     * @return
     */
    private float getEarlyTime(WaAnalyzCalDTO dto, WaAnalyze analyze, EmpShiftInfo shift, Boolean isFlexibleWorking, Map<String, Object> paramsMap) {
        if (shift == null || shift.getStartTime() == null || shift.getEndTime() == null || shift.getWorkDate() == null) {
            return 0;
        }
        Long regtime = analyze.getRegSignoffTime();
        Long belongDate = shift.getWorkDate();
        Long reBelongDate = shift.getWorkDate();
        boolean isShiftKy = CdWaShiftUtil.checkCrossNight(shift.getStartTime(), shift.getEndTime(), shift.getDateType());
        if (isShiftKy) {
            // 班次跨夜了所以日期加一天
            belongDate = DateUtil.addDate(belongDate * 1000, 1);
        }
        Integer minute = 0;
        //CLOUD-8623
        Boolean leaveFlag = checkApplyLeaveByDate(dto, shift.getEmpid(), reBelongDate, isFlexibleWorking, paramsMap);
        //CLOUD-3848
        if (!leaveFlag && isFlexibleWorking && BooleanUtils.isTrue(shift.getIsFlexibleWork()) && shift.getFlexibleWorkType() == 1) {
            //标准逻辑
            if (analyze.getRegSigninTime() != null) {//有签到
                //判断签到时间是否在标准区间还是弹性区间
                Integer onDutyType = getOnDutyTimeRangeType(reBelongDate, analyze.getRegSigninTime(), shift);
                if (onDutyType == 1) {
                    //签到在标准打卡时间范围内，早退=标准下班打卡开始时间-签退时间
                    log.info("标准逻辑-早退分析逻辑,getEarlyTimeForStandard");
                    minute = getEarlyTimeForStandard(reBelongDate, regtime, shift);
                } else {
                    //签到在弹性打卡时间范围内，早退=弹性下班打卡开始时间-签退时间
                    //签到时间超出弹性打卡结束时间，早退=弹性下班打卡开始时间-签退时间
                    log.info("标准逻辑-早退分析逻辑,getEarlyTimeForFlexible");
                    minute = getEarlyTimeForFlexible(reBelongDate, regtime, shift);
                }
            } else {//无签到
                minute = getEarlyTimeForFlexible(reBelongDate, regtime, shift);
            }
        } else {
            Long yqtsj = (belongDate + (shift.getEndTime() * 60));
            // 如果 签退时间早于 应签退时间 则说明 提前下班了认为是早退了
            // 如果在休息时间范围内签退则早退时间＝0，如果在休息开始时间之前签退 早退时间＝下班时间－签退时间－休息时间
            Integer shiftEnd = belongDate.intValue() + (shift.getEndTime() * 60);
            Integer noonRestStartMin = shift.getNoonRestStart();
            Integer noonRestEndMin = shift.getNoonRestEnd();
            ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
            noonRestStartMin = restPeriod.getNoonRestStart();
            noonRestEndMin = restPeriod.getNoonRestEnd();
            if (BooleanUtils.isTrue(shift.getIsHalfdayTime()) && shift.getHalfdayTime() != null && shift.getHalfdayTime() >= 0) {
                Integer halfdayTime = (belongDate.intValue() + (shift.getHalfdayTime() * 60));
                if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {//设置了中午休息时间
                    Integer noonStart = reBelongDate.intValue() + (noonRestStartMin * 60);
                    Integer noonEnd = reBelongDate.intValue() + (noonRestEndMin * 60);
                    if (shiftEnd > halfdayTime && shiftEnd > noonEnd) {
                        if (halfdayTime < noonStart) {
                            if (regtime < halfdayTime) {
                                minute = (shiftEnd - regtime.intValue()) - (noonEnd - noonStart);
                            } else if (regtime >= halfdayTime && regtime < noonStart) {
                                minute = (shiftEnd - regtime.intValue()) - (noonEnd - noonStart);
                            } else if (regtime >= noonStart && regtime <= noonEnd) {
                                minute = shiftEnd - noonEnd;
                            } else {
                                if (regtime < yqtsj) {
                                    minute = (yqtsj.intValue() - regtime.intValue());
                                }
                            }
                        } else if (halfdayTime <= noonEnd) {
                            if (regtime < noonStart) {
                                minute = (shiftEnd - noonEnd) + (noonStart - regtime.intValue());
                            } else if (regtime >= noonStart && regtime <= noonEnd) {
                                minute = shiftEnd - noonEnd;
                            } else {
                                if (regtime < yqtsj) {
                                    minute = (yqtsj.intValue() - regtime.intValue());
                                }
                            }
                        } else {
                            if (regtime >= halfdayTime) {
                                if (regtime < yqtsj) {
                                    minute = (yqtsj.intValue() - regtime.intValue());
                                }
                            } else if (regtime >= noonEnd) {
                                minute = shiftEnd - regtime.intValue();
                            } else if (regtime >= noonStart) {
                                minute = shiftEnd - noonEnd;
                            } else {
                                minute = (shiftEnd - noonEnd) + (noonStart - regtime.intValue());
                            }
                        }
                    }
                } else {
                    if (shiftEnd > halfdayTime) {
                        if (regtime < halfdayTime) {
                            minute = shiftEnd - regtime.intValue();
                        } else {
                            if (regtime < yqtsj) {
                                minute = (yqtsj.intValue() - regtime.intValue());
                            }
                        }
                    }
                }
            } else if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                Integer noonStart = reBelongDate.intValue() + (noonRestStartMin * 60);
                Integer noonEnd = reBelongDate.intValue() + (noonRestEndMin * 60);
                if (shiftEnd > noonEnd) { // 只有当 班次截止时间大于班次休息截止时间时，才需要考虑中午休息时间的情况
                    if (regtime < noonStart) {
                        minute = (shiftEnd - noonEnd) + (noonStart - regtime.intValue());
                    } else if (regtime >= noonStart && regtime <= noonEnd) {
                        minute = shiftEnd - noonEnd;
                    } else {
                        if (regtime < yqtsj) {
                            // 返回早退分钟数
                            minute = (yqtsj.intValue() - regtime.intValue());
                        }
                    }
                }
            } else {
                if (regtime < yqtsj) {
                    // 返回早退分钟数
                    minute = (yqtsj.intValue() - regtime.intValue());
                }
            }
        }
        return minute > 0 ? BigDecimal.valueOf(minute).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).floatValue() : 0f;
    }

    /**
     * 获取早退时间 - 弹性班次设置
     *
     * @param dto
     * @param analyze
     * @param shift
     * @param paramsMap
     * @return
     */
    private float getEarlyTimeFlexible(WaAnalyzCalDTO dto, WaAnalyze analyze, EmpShiftInfo shift, Map<String, Object> paramsMap, WaAnalyzInfo analyzeInfo) {
        if (shift == null || shift.getStartTime() == null || shift.getEndTime() == null || shift.getWorkDate() == null) {
            return 0;
        }
        List<EmpLeaveInfo> leaveList = dto.getEmpLeaveInfoByDateEmpId(shift.getEmpid(), shift.getWorkDate());
        Long regSignTime = analyze.getRegSigninTime();//签到时间
        Long regTime = analyze.getRegSignoffTime();//签退时间
        Long reBelongDate = shift.getWorkDate();
        Integer minute = 0;
        //上班时间
        Long clockInTime = (reBelongDate + (shift.getStartTime() * 60));
        //最多早走多少小时
        BigDecimal earlyTime = shift.getFlexibleWorkEarly() == null ? BigDecimal.ZERO : shift.getFlexibleWorkEarly();
        Double workEarLyMin = earlyTime.doubleValue() * 3600;
        //最多晚到多少小时
        BigDecimal lateTime = shift.getFlexibleWorkLate() == null ? BigDecimal.ZERO : shift.getFlexibleWorkLate();
        Double workLateMin = lateTime.doubleValue() * 3600;
        //弹性上班时间(晚到)
        Long clockInLateTime = clockInTime + workLateMin.intValue();
        //弹性上班时间(早到)
        Long clockInTimeForFlexible = clockInTime - workEarLyMin.intValue();
        //下班时间
        Long clockOffTime = (reBelongDate + (shift.getEndTime() * 60));
        //弹性下班时间(早到)
        Long clockOffTimeForFlexible = clockOffTime - workEarLyMin.intValue();
        //弹性下班时间（晚到）
        Long clockOffLateTime = clockOffTime + workLateMin.intValue();
        //有签到
        if (regSignTime != null) {
            Integer noonRestStartMin = shift.getNoonRestStart();
            Integer noonRestEndMin = shift.getNoonRestEnd();
            ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
            noonRestStartMin = restPeriod.getNoonRestStart();
            noonRestEndMin = restPeriod.getNoonRestEnd();
            if (regSignTime < clockInTime) {
                //签到时间 < 上班时间
                //打卡时间在弹性区间
                //下班时间 - (上班时间 - 打卡时间)
                if (regSignTime > clockInTimeForFlexible) {
                    //签到时间 > 弹性早到时间
                    if (CollectionUtils.isNotEmpty(leaveList)) {
                        for (EmpLeaveInfo leaveInfo : leaveList) {
                            if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(leaveInfo.getPeriod_type().intValue())) {
                                Integer startTime = leaveInfo.getStart_time();
                                Long leaveStartTime = leaveInfo.getReal_date() + startTime * 60;
                                if (leaveStartTime >= clockOffTimeForFlexible) {
                                    clockOffTimeForFlexible = clockOffTime;
                                    break;
                                }
                            } else {
                                clockOffTimeForFlexible = clockOffTime - (clockInTime - regSignTime);
                            }
                        }
                    } else {
                        clockOffTimeForFlexible = clockOffTime - (clockInTime - regSignTime);
                    }
                }
            } else {
                if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                    Long noonRestEnd = reBelongDate + noonRestEndMin * 60;
                    int noon = (noonRestStartMin - shift.getStartTime()) * 60;
                    if (workLateMin > noon) {
                        clockInLateTime = noonRestEnd + (workLateMin.intValue() - noon);
                    }
                }
                if (regSignTime < clockInLateTime) {
                    if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                        Long noonStart = reBelongDate + noonRestStartMin * 60;
                        Long noonRestEnd = reBelongDate + noonRestEndMin * 60;
                        int noon = (noonRestStartMin - shift.getStartTime()) * 60;
                        if (workLateMin > noon) {
                            if (regSignTime > noonRestEnd) {
                                int diffHour = noonRestEndMin - noonRestStartMin;
                                clockOffTimeForFlexible = clockOffTime + (regSignTime - clockInTime - diffHour * 60L);
                            } else if (regSignTime >= noonStart) {
                                clockOffTimeForFlexible = clockOffTime + (noonStart - clockInTime);
                            } else {
                                clockOffTimeForFlexible = clockOffTime + (regSignTime - clockInTime);
                            }
                        } else {
                            clockOffTimeForFlexible = clockOffTime + (regSignTime - clockInTime);
                        }
                    } else {
                        clockOffTimeForFlexible = clockOffTime + (regSignTime - clockInTime);
                    }
                } else {
                    clockOffTimeForFlexible = clockOffLateTime;
                    if (CollectionUtils.isNotEmpty(leaveList)) {
                        for (EmpLeaveInfo leaveInfo : leaveList) {
                            if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(leaveInfo.getPeriod_type().intValue())) {
                                int halfWorkTime = shift.getWorkTotalTime() / 2;
                                Integer endTime = leaveInfo.getEnd_time();
                                Long leaveEndTime = leaveInfo.getReal_date() + endTime * 60;
                                if (leaveEndTime <= clockInLateTime) {
                                    clockOffTimeForFlexible = clockOffTime;
                                    break;
                                } else {
                                    //上半天
                                    int halfTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shift, halfWorkTime);
                                    if (leaveInfo.getTime_duration().intValue() >= halfTime) {
                                        clockOffTimeForFlexible = clockOffTime;
                                        break;
                                    }
                                }
                            } else {
                                if (!"BRJ".equals(leaveInfo.getLeave_type_def_code())) {
                                    clockOffTimeForFlexible = clockOffTime;
                                    break;
                                }
                            }
                        }
                    }
                }
                //按班次分析,有请假不享受弹性
                if (FlexibleRuleEnum.SHIFT_ANALYZE.getIndex().equals(analyzeInfo.getFlexibleWorkType())) {
                    if (CollectionUtils.isNotEmpty(leaveList)) {
                        clockOffTimeForFlexible = clockOffTime;
                    }
                }
            }
            //是否有休息时间
            Integer shiftEnd = reBelongDate.intValue() + (shift.getEndTime() * 60);
            if (BooleanUtils.isTrue(shift.getIsNoonRest()) && shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
                Integer noonStart = reBelongDate.intValue() + (noonRestStartMin * 60);
                Integer noonEnd = reBelongDate.intValue() + (noonRestEndMin * 60);
                //只有当 班次截止时间大于班次休息截止时间时，才需要考虑中午休息时间的情况
                if (shiftEnd > noonEnd) {
                    if (regTime < noonStart) {
                        minute = (clockOffTimeForFlexible.intValue() - noonEnd) + (noonStart - regTime.intValue());
                    } else if (regTime >= noonStart && regTime <= noonEnd) {
                        minute = clockOffTimeForFlexible.intValue() - noonEnd;
                    } else {
                        if (regTime < clockOffTimeForFlexible) {
                            minute = (clockOffTimeForFlexible.intValue() - regTime.intValue());
                        }
                    }
                }
            } else {
                log.info("===================44444444==========================");
                //返回早退分钟数
                if (regTime < clockOffTimeForFlexible) {
                    minute = (clockOffTimeForFlexible.intValue() - regTime.intValue());
                }
            }
        }
        return minute > 0 ? BigDecimal.valueOf(minute.floatValue() / 60f).setScale(2, RoundingMode.HALF_DOWN).floatValue() : 0f;
    }

    /**
     * 获取签到打卡区间类型
     *
     * @param belongDate
     * @param regTime
     * @param shift
     * @return
     */
    private Integer getOnDutyTimeRangeType(Long belongDate, Long regTime, EmpShiftInfo shift) {
        Integer onDutyType = 1;//1 标准时间 2 弹性时间
        Long onDutyEndTime = belongDate + (shift.getOnDutyEndTime() * 60);
        if (regTime <= onDutyEndTime) {
            onDutyType = 1;
        } else {
            onDutyType = 2;
        }
        return onDutyType;
    }

    /**
     * 根据标准打卡区间计算早退时长
     *
     * @param reBelongDate
     * @param regtime
     * @param shift
     * @return
     */
    private Integer getEarlyTimeForStandard(Long reBelongDate, Long regtime, EmpShiftInfo shift) {
        Integer minute = 0;
        if (shift.getOffDutyStartTime() != null) {
            //在标准下班开始时间前的签退，以标准下班开始时间为标准计算早退分钟数
            Long offDutyStartTime = reBelongDate + (shift.getOffDutyStartTime() * 60);
            if (regtime < offDutyStartTime) {
                Long result = offDutyStartTime - regtime;
                result = decRestTime(reBelongDate, regtime, shift, result);
                minute = result.intValue();
            }
        }
        return minute;
    }

    /**
     * 计算早退时长时，扣减休息时间
     *
     * @param reBelongDate
     * @param regtime
     * @param shift
     * @param result
     * @return
     */
    private Long decRestTime(Long reBelongDate, Long regtime, EmpShiftInfo shift, Long result) {
        //判断是否在休息时间段内，如在区间范围内或小于了休息开始时间，则扣除中午休息时间
        if (shift.getNoonRestStart() != null && shift.getNoonRestEnd() != null) {
            Integer noonRestStart = shift.getNoonRestStart();
            Integer noonRestEnd = shift.getNoonRestEnd();

            ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
            noonRestStart = restPeriod.getNoonRestStart();
            noonRestEnd = restPeriod.getNoonRestEnd();

            Long restStartTime = reBelongDate + (noonRestStart * 60);
            Long restEndTime = reBelongDate + (noonRestEnd * 60);
            if ((regtime <= restEndTime && regtime >= restStartTime) || regtime < restStartTime) {
                result -= restEndTime - Math.max(regtime, restStartTime);
            }
        }
        // 扣除休息时间段内的时长
        List<ShiftRestPeriods> restPeriods = shift.getRestPeriods();
        if (CollectionUtils.isNotEmpty(restPeriods)) {
            for (ShiftRestPeriods rest : restPeriods) {
                Integer noonRestStart = rest.getNoonRestStart();
                Integer noonRestEnd = rest.getNoonRestEnd();

                ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
                noonRestStart = restPeriod.getNoonRestStart();
                noonRestEnd = restPeriod.getNoonRestEnd();

                Long restStart = reBelongDate + noonRestStart * 60;
                Long restEnd = reBelongDate + noonRestEnd * 60;
                if ((regtime <= restEnd && regtime >= restStart) || regtime < restStart) {
                    Long tm = restEnd - Math.max(regtime, restStart);
                    result -= tm;
                }
            }
        }
        return result;
    }

    /**
     * 计算实际工作时长
     *
     * @param analyze
     * @param empShiftDef
     * @param analyzeInfo
     * @return
     */
    private Float calculateActualWorkTime(WaAnalyze analyze, EmpShiftInfo empShiftDef, WaAnalyzInfo analyzeInfo, String belongid, Boolean leaveFlag) {
        Float actualWorkTime = 0f;
        //CLOUD-3848
        Boolean isFlexibleWorking = BooleanUtils.toBoolean(analyzeInfo.getIs_flexible_working()); // 是否按弹性时间分析迟到分钟数
        //TODO 按照系统标准逻辑计算实际工作时长--1.0 是通过开关配置来控制的，腾讯无配置页面，默认不开启
        //String decByStandardRule = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + "_CALCULATE_ACTUALWORKTIME_BYSTANDARDRULE");
        String decByStandardRule = "0";
        if ("1".equals(decByStandardRule)) {
            isFlexibleWorking = false;
        }
        Integer noonRestStartMin = empShiftDef.getNoonRestStart();
        Integer noonRestEndMin = empShiftDef.getNoonRestEnd();
        ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType());
        noonRestStartMin = restPeriod.getNoonRestStart();
        noonRestEndMin = restPeriod.getNoonRestEnd();
        if (isFlexibleWorking && BooleanUtils.isTrue(empShiftDef.getIsFlexibleWork()) && empShiftDef.getFlexibleWorkType() == 1) {
            //实际工作时长 = 弹性上班开始时间-设置的加班开始时间（7:30-21:30）【为弹性上班开始时间-加班开始时间】这段时间范围内有效的出勤时长(需扣除中午休息时间)
            long start = analyze.getBelongDate() + empShiftDef.getFlexibleOnDutyStartTime() * 60;
            long end = analyze.getBelongDate() + (empShiftDef.getOvertimeStartTime() == null ? empShiftDef.getFlexibleOffDutyEndTime() * 60 : empShiftDef.getOvertimeStartTime() * 60);
            Long start_time = Math.max(analyze.getRegSigninTime(), start);
            Long end_time = Math.min(analyze.getRegSignoffTime(), end);
            long timehour = end_time - start_time;
            if (empShiftDef.getIsNoonRest()) {
                long restStartTime = analyze.getBelongDate() + noonRestStartMin * 60;
                long restEndTime = analyze.getBelongDate() + noonRestEndMin * 60;
                if (start_time <= restEndTime && end_time >= restStartTime) {
                    timehour -= Math.min(end_time, restEndTime) - Math.max(start_time, restStartTime);
                }
            }
            actualWorkTime = BigDecimal.valueOf(timehour).floatValue();
            if (actualWorkTime < 0) {
                actualWorkTime = 0f;
            } else {
                actualWorkTime /= 60f;
            }
        } else {
            // 计算实际工作分钟数(上下班时间)
            long s = analyze.getBelongDate() + empShiftDef.getStartTime() * 60;
            long e = analyze.getBelongDate() + empShiftDef.getEndTime() * 60;
            boolean isShiftKy = CdWaShiftUtil.checkCrossNight(empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType());
            if (isShiftKy) {//跨夜
                e = DateUtil.addDate(empShiftDef.getWorkDate() * 1000, 1) + empShiftDef.getEndTime() * 60;
            }
            Long regSignInTime = analyze.getRegSigninTime();
            Long regSignOffTime = analyze.getRegSignoffTime();
            //弹性设置
            if (FlexibleEnum.OPEN.getIndex().equals(analyzeInfo.getFlexibleWorkSwitch())) {
                if (FlexibleEnum.OPEN.getIndex().equals(empShiftDef.getFlexibleShiftSwitch())) {
                    if (FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(empShiftDef.getFlexibleWorkRule())) {
                        BigDecimal flexibleWorkLate = empShiftDef.getFlexibleWorkLate();//晚到多少小时
                        long late = BigDecimal.valueOf(flexibleWorkLate.doubleValue()).multiply(BigDecimal.valueOf(3600)).longValue();
                        BigDecimal flexibleWorkEarly = empShiftDef.getFlexibleWorkEarly();//早走多少小时
                        long early = BigDecimal.valueOf(flexibleWorkEarly.doubleValue()).multiply(BigDecimal.valueOf(3600)).longValue();
                        if (regSignInTime < s) {
                            long fs = s - early;
                            if (regSignInTime > fs) {
                                e = e - (s - regSignInTime);
                                s = regSignInTime;
                            } else {
                                e = e - early;
                                s = fs;
                            }
                        } else if (regSignInTime > s) {
                            long fs = s + late;
                            if (regSignInTime < fs) {
                                long diff = 0;
                                if (empShiftDef.getIsNoonRest()) {
                                    long restStartTime = analyze.getBelongDate() + noonRestStartMin * 60;
                                    long restEndTime = analyze.getBelongDate() + noonRestEndMin * 60;
                                    if (regSignInTime > restEndTime) {
                                        diff = regSignInTime - (restEndTime - restStartTime) - s;
                                    } else if (regSignInTime > restStartTime && regSignInTime < restEndTime) {
                                        diff = restStartTime - s;
                                    } else {
                                        diff = regSignInTime - s;
                                    }
                                }
                                s = regSignInTime;
                                e = e + diff;
                            } else {
                                e = e + late;
                                s = fs;
                            }
                        }
                    }
                }
            }
            if (regSignInTime < s) {
                regSignInTime = s;
            } else if (regSignInTime > e) {
                regSignInTime = e;
            }
            if (regSignOffTime < s) {
                regSignOffTime = s;
            } else if (regSignOffTime > e) {
                regSignOffTime = e;
            }
            float workTime = BigDecimal.valueOf((regSignOffTime - regSignInTime) / 60f).setScale(2, RoundingMode.HALF_UP).floatValue();
            if (empShiftDef.getIsNoonRest()) {
                long noonRestStartTime = analyze.getBelongDate() + noonRestStartMin * 60;
                long noonRestEndTime = analyze.getBelongDate() + noonRestEndMin * 60;
                /*if (noonRestStartTime > noonRestEndTime) {
                    noonRestEndTime += 24 * 3600;
                }*/
                Integer nonetime = 0;
                //当签到时间在休息时间段内的情况，不计算工作时间
                if (regSignInTime >= noonRestStartTime && regSignOffTime <= noonRestEndTime) {
                    workTime = 0L;
                } else if (regSignInTime <= noonRestStartTime && regSignOffTime >= noonRestEndTime) {// 签到时间小于或等于休息开始时间 并且 签退时间大于等于休息截止时间的情况 时间工作小时数＝签退－签到－中午休息时间
                    nonetime = empShiftDef.getRestTotalTime();// 应减去的休息时间
                } else if (regSignInTime <= noonRestStartTime && regSignOffTime > noonRestStartTime) {//签退时间在休息区间内的情况  把签退时间赋予为休息开始时间
                    regSignOffTime = noonRestStartTime;
                } else if (regSignOffTime >= noonRestEndTime && regSignInTime <= noonRestEndTime) {// 签到时间在休息区间内的情况 把签到时间赋予到休息截止时间
                    regSignInTime = noonRestEndTime;
                } else {
                    nonetime = 0;
                }
                workTime = BigDecimal.valueOf((regSignOffTime - regSignInTime) / 60f - nonetime).setScale(2, RoundingMode.HALF_UP).floatValue();
            }
            // 实际工作分钟数 ＝ 签退－签退时间－中午休息时间
            actualWorkTime = workTime;
            if (actualWorkTime > empShiftDef.getWorkTotalTime()) {
                actualWorkTime = empShiftDef.getWorkTotalTime().floatValue();
            }

            if (actualWorkTime < 0) {
                actualWorkTime = 0f;
            }
        }
        return actualWorkTime;
    }

    /**
     * 计算实际工作时长
     *
     * @param analyze
     * @param empShiftDef
     * @param analyzeInfo
     * @return
     */
    private Float calculateActualWorkTimeForRestDay(WaAnalyze analyze, EmpShiftInfo empShiftDef, WaAnalyzInfo analyzeInfo) {
        // 计算实际工作分钟数
        Long regtime = analyze.getRegSigninTime();
        Long regtime2 = analyze.getRegSignoffTime();
        Float workTime = (regtime2 - regtime) / 60f;
        if (empShiftDef.getIsNoonRest()) {
            Integer noonRestStartMin = empShiftDef.getNoonRestStart();
            Integer noonRestEndMin = empShiftDef.getNoonRestEnd();
            ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType());
            noonRestStartMin = restPeriod.getNoonRestStart();
            noonRestEndMin = restPeriod.getNoonRestEnd();
            long nStart = analyze.getBelongDate() + noonRestStartMin * 60;
            long nEnd = analyze.getBelongDate() + noonRestEndMin * 60;
            Integer nonetime = 0;
            //当签到时间在休息时间段内的情况，不计算工作时间
            if (regtime >= nStart && regtime2 <= nEnd) {
                return 0f;
            } else if (regtime <= nStart && regtime2 >= nEnd && empShiftDef.getRestTotalTime() != null) {// 签到时间小于或等于休息开始时间 并且 签退时间大于等于休息截止时间的情况 时间工作小时数＝签退－签到－中午休息时间
                nonetime = empShiftDef.getRestTotalTime();// 应减去的休息时间
            } else if (regtime <= nStart && regtime2 > nStart && regtime2 <= nEnd) {//签退时间在休息区间内的情况  把签退时间赋予为休息开始时间
                regtime2 = nStart;
            } else if (regtime2 >= nEnd && regtime > nStart && regtime <= nEnd) {// 签到时间在休息区间内的情况 把签到时间赋予到休息截止时间
                regtime = nEnd;
            } else {
                nonetime = 0;
            }
            workTime = (regtime2 - regtime) / 60f - nonetime;
        }
        return BigDecimal.valueOf(workTime).setScale(2, RoundingMode.HALF_DOWN).floatValue();
    }

    /**
     * @param paramsMap 需要传递的参数： startDate，endDate ，belongid，[anyEmpids2（员工empid)]
     * @param dto
     * @param resultWa
     */
    private void analyzeLeaveOtData(Map<String, Object> paramsMap, WaAnalyzCalDTO dto, List<WaAnalyze> resultWa) throws Exception {
        if (newLeaveDataAnalyzeLogic) {//新计算逻辑
            analyzeLeaveDataNew(resultWa, paramsMap, dto);
        } else {//原始逻辑
            analyzeLeaveData(resultWa, paramsMap, dto);
        }
        analyzeOverTimeData(resultWa, paramsMap, dto);
    }

    /**
     * 休假单据分析--废弃
     *
     * @param resultWa
     * @param params
     * @param dto
     */
    @Deprecated
    @SuppressWarnings("unchecked")
    private void analyzeLeaveData(List<WaAnalyze> resultWa, Map params, WaAnalyzCalDTO dto) {
        String belongId = (String) params.get("belongid");
        Long startDate = (Long) params.get("startDate");
        Long endDate = (Long) params.get("endDate");
        if (dto == null) {
            dto = new WaAnalyzCalDTO();
            // 假期类型
            List<WaLeaveType> leaveTypes = waConfigService.getLeaveTypes(belongId);
            if (CollectionUtils.isNotEmpty(leaveTypes)) {
                Map<Integer, WaLeaveType> leaveTypeMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity(), (v1, v2) -> v1));
                dto.getLtMaps().putAll(leaveTypeMap);
            }
        }
        // 根据empid去查询某天的排班纪录，统计请假小时数 如果有请假，则需要抵消迟到早退小时数 按请假迟到有重合的段进行折算
        if (CollectionUtils.isNotEmpty(resultWa)) {
            if (dto.getEmpLeaveInfoList() == null) {
                dto.setEmpLeaveInfo(getEmpLeaveList(params));
            }
            // 默认正常的请假数据
            // 审批时间在请假时间后的请假数据
            Map<String, List<EmpLeaveInfo>> empleaveAfterMap = new HashMap<>();
            //组合员工的请假单 key ： empid+"_"+leave_date;
            Map<String, List<EmpLeaveInfo>> empleaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empleaveAfterMap);
            // 计算请假小时数
            for (WaAnalyze analyze : resultWa) {
                Long belongDate = analyze.getBelongDate();
                String key = analyze.getEmpid() + "_" + belongDate;
                WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                // 去查找请假日期的数据
                EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                if (null == currentShiftDef) {
                    continue;
                }
                List<EmpLeaveInfo> empLeaveInfos = null;
                // 查找这一天，是否有请假记录
                if (empleaveMap.containsKey(key)) {
                    List<EmpLeaveInfo> leaveInfos = empleaveMap.get(key);
                    //处理哺乳假
                    leaveInfos = dealBRJLeaveInfo(leaveInfos, currentShiftDef);
                    //1 先判断需不需要进行抵扣（如果迟到早退异常时才需要抵扣）
                    if (leaveInfos != null && leaveInfos.size() > 0 && empAnalyzeInfo != null) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            //扣减迟到早退小时数
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnalyzeInfo, 1, belongId);
                        } else if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnalyzeInfo, 2, belongId);
                        } else if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnalyzeInfo, 3, belongId);
                        }
                    }
                    analyze.setLevelColumnJsonb(getLevelColumnJson(leaveInfos, dto, false));
                    //查找 leaveInfos 中 real_date == 申请日期的数据 存入 原始记录中 在拿请假单数据做分析
                    empLeaveInfos = leaveInfos;
                    empleaveMap.remove(key);
                }
                //2 获取请假当天的数据 做扣减
                List<EmpLeaveInfo> empLeaveInfos2 = null;
                if (empleaveAfterMap.containsKey(key)) {
                    List<EmpLeaveInfo> leaveInfos = empleaveAfterMap.get(key);
                    //1 先判断需不需要进行抵扣（如果迟到早退异常时才需要抵扣）
                    if (leaveInfos != null && leaveInfos.size() > 0 && empAnalyzeInfo != null) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            //扣减迟到早退小时数
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnalyzeInfo, 1, belongId);
                        } else if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnalyzeInfo, 2, belongId);
                        } else if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            deductionLateAndEarlyHours2(analyze, leaveInfos, empAnalyzeInfo, 3, belongId);
                        }
                    }
                    // 只用请假记录扣减迟到早退小时数，不记录请假申请时间<审批时间的请假记录
                    //查找 leaveInfos 中 real_date == 申请日期的数据 存入 原始记录中 在拿请假单数据做分析
                    empLeaveInfos2 = leaveInfos;
                    //旷工  员工当天没上班，请一天假，假期单据由9:00-18:00 旷工	考勤异常，旷工和请假单对冲—>当旷工时间=0
                    empleaveAfterMap.remove(key);
                }
                //计算当天的请假单数据
                if (currentShiftDef != null) {
                    Map<String, Object> orginLevelJson = calculateOriginLevel(dto, empLeaveInfos, empLeaveInfos2);
                    analyze.setOriginLevelColumnJsonb(orginLevelJson);
                    analyze.setShiftDefId(currentShiftDef.getShiftDefId());
                }
                // 4 判断是否有请假记录，如果有则抵消矿工记录
                cancelKgRecord(analyze, empLeaveInfos, empLeaveInfos2, null, null, null);
                // 新的旷工逻辑，旷工时长判断逻辑
                if (currentShiftDef != null && currentShiftDef.getDateType() == 1) {
                    absentAnalyzeRule(analyze, empAnalyzeInfo, currentShiftDef);
                }
            }
            // 记录剩余的请假单数据
            calculateEmpLeave(resultWa, empleaveMap, dto);
            //审批时间大于请假申请时间的记录，默认添加一个条分析记录
            addEmpLeaveAfterWaAnalyze(resultWa, empleaveAfterMap, dto, startDate, endDate);
        } else {
            if (dto.getEmpLeaveInfoList() == null) {
                dto.setEmpLeaveInfo(getEmpLeaveList(params));
            }
            // 默认正常的请假数据
            // 审批时间在请假时间后的请假数据
            Map<String, List<EmpLeaveInfo>> empleaveAfterMap = new HashMap<>();
            //组合员工的请假单 key ： empid+"_"+leave_date;
            Map<String, List<EmpLeaveInfo>> empleaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empleaveAfterMap);
            // 记录剩余的请假单数据
            calculateEmpLeave(resultWa, empleaveMap, dto);
            //审批时间大于请假申请时间的记录，默认添加一个条分析记录
            addEmpLeaveAfterWaAnalyze(resultWa, empleaveAfterMap, dto, startDate, endDate);
        }
    }

    @SuppressWarnings("unchecked")
    private void analyzeLeaveDataNew(List<WaAnalyze> resultWa, Map params, WaAnalyzCalDTO dto) {
        String belongId = (String) params.get("belongid");
        Long startDate = (Long) params.get("startDate");
        Long endDate = (Long) params.get("endDate");
        if (dto == null) {
            dto = new WaAnalyzCalDTO();
            // 假期类型
            List<WaLeaveType> leaveTypes = waConfigService.getLeaveTypes(belongId);
            if (CollectionUtils.isNotEmpty(leaveTypes)) {
                Map<Integer, WaLeaveType> leaveTypeMap = leaveTypes.stream().collect(Collectors.toMap(WaLeaveType::getLeaveTypeId, Function.identity(), (v1, v2) -> v1));
                dto.getLtMaps().putAll(leaveTypeMap);
            }
        }
        //查询设置销假记录
        setLeaveCancelMap(belongId, startDate, endDate, dto);
        if (dto.getEmpLeaveInfoList() == null) {
            dto.setEmpLeaveInfo(getEmpLeaveList(params));
        }

        // 外出数据
        Map<String, List<WaEmpTravelDaytimeDo>> empTravelAfterMap = new HashMap<>();
        Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap = getEmpTravelInfos(dto.getEmpTravelInfoList(), dto, empTravelAfterMap);

        // 休假数据抵扣迟到早退旷工时长
        if (CollectionUtils.isNotEmpty(resultWa)) {
            Map<String, List<EmpLeaveInfo>> empLeaveAfterMap = new HashMap<>();
            Map<String, List<EmpLeaveInfo>> empLeaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empLeaveAfterMap);

            for (WaAnalyze analyze : resultWa) {
                Long belongDate = analyze.getBelongDate();
                String key = analyze.getEmpid() + "_" + belongDate;

                WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                boolean isFlexibleWork = empAnalyzeInfo != null && empAnalyzeInfo.getFlexibleWorkSwitch() != null && FlexibleEnum.OPEN.getIndex().equals(empAnalyzeInfo.getFlexibleWorkSwitch());

                EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                if (null == currentShiftDef) {
                    continue;
                }
                WaShiftDef shiftDef = ObjectConverter.convert(currentShiftDef, WaShiftDef.class);

                // 当天考勤信息
                int workTime = Optional.ofNullable(analyze.getWorkTime()).orElse(0);
                float actualWorkTime = Optional.ofNullable(analyze.getActualWorkTime()).orElse(0f);
                float lateTime = Optional.ofNullable(analyze.getLateTime()).orElse(0f);
                float originalLateTime = Optional.ofNullable(analyze.getLateTime()).orElse(0f);
                float earlyTime = Optional.ofNullable(analyze.getEarlyTime()).orElse(0f);
                long regSignInTime = Optional.ofNullable(analyze.getRegSigninTime()).orElse(0L);

                // 当天休假单
                List<EmpLeaveInfo> allEmpLeaveInfo = new ArrayList<>();
                List<EmpLeaveInfo> empLeaveInfos = null;
                List<EmpLeaveInfo> empLeaveInfos2 = null;
                if (empLeaveMap.containsKey(key)) {
                    List<EmpLeaveInfo> leaveInfos = empLeaveMap.get(key);
                    //处理哺乳假
                    leaveInfos = dealBRJLeaveInfo(leaveInfos, currentShiftDef);
                    analyze.setLevelColumnJsonb(getLevelColumnJson(leaveInfos, dto, false));
                    empLeaveInfos = leaveInfos;
                    allEmpLeaveInfo.addAll(leaveInfos.stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList()));
                    empLeaveMap.remove(key);
                }
                if (empLeaveAfterMap.containsKey(key)) {
                    List<EmpLeaveInfo> leaveInfos = empLeaveAfterMap.get(key);
                    empLeaveInfos2 = leaveInfos;
                    allEmpLeaveInfo.addAll(leaveInfos.stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList()));
                    empLeaveAfterMap.remove(key);
                }

                // 查询当天的外出记录
                List<WaLeaveDaytimeExtDto> travelDaytimeList = new ArrayList<>();
                // 判断外出单是否已经抵扣了迟到早退
                Integer outParseRule = (empAnalyzeInfo == null || empAnalyzeInfo.getOutParseRule() == null)
                        ? TravelTypeParseRuleEnum.TRAVEL_REG.getIndex() : empAnalyzeInfo.getOutParseRule();
                CheckTravelIfValidResultDto checkTravelIfValidResultDto = checkTravelIfValid(analyze, outParseRule, dto);
                if (checkTravelIfValidResultDto.isIfCalculateKgForTravel()
                        && null != empAnalyzeInfo && empAnalyzeInfo.doCheckCoexistForLeaveOut()) {
                    travelDaytimeList = getTravelDaytimeExtDtoList(empTravelMap, empTravelAfterMap, key, shiftDef);
                }

                // 计算休假时间并抵扣迟到
                float totalLeaveTimeDuration = 0f;
                float brjLeaveTimeDuration = 0f;
                if (CollectionUtils.isNotEmpty(allEmpLeaveInfo)) {
                    // 计算可以进行抵扣的休假数据和休假时长
                    CalLeaveTimeDurationResultDto timeResultDto =
                            calLeaveTimeDuration(allEmpLeaveInfo, belongDate, workTime, currentShiftDef);
                    totalLeaveTimeDuration = timeResultDto.getTotalLeaveTimeDuration();
                    brjLeaveTimeDuration = timeResultDto.getBrjLeaveTimeDuration();
                    List<Integer> notDeductionLeaveIds = timeResultDto.getNotDeductionLeaveIds();
                    List<Integer> brjLeaveIds = timeResultDto.getBrjLeaveIds();

                    // 非哺乳假需做时间重叠校验，哺乳假直接进行抵扣计算
                    allEmpLeaveInfo = allEmpLeaveInfo.stream().filter(l -> !notDeductionLeaveIds.contains(l.getLeave_id()) && !brjLeaveIds.contains(l.getLeave_id())).collect(Collectors.toList());
                    if (totalLeaveTimeDuration > 0 && CollectionUtils.isNotEmpty(allEmpLeaveInfo) && (lateTime > 0 || earlyTime > 0)) {
                        // 计算实际休假时间
                        List<WaLeaveDaytimeExtPo> leaveDayTimes = getLeaveDayTimes(allEmpLeaveInfo);
                        List<WaLeaveDaytimeExtDto> ltExtDtoList = leaveDayTimes.stream().map(ltDay -> mobileV16Service.calLeaveDayRealTimeSlot(ltDay, shiftDef)).collect(Collectors.toList());

                        // 计算实际销假时间
                        List<WaLeaveCancelDayTime> leaveCancelDayTimes = dto.getLeaveCancelMap().get(key);
                        List<WaLeaveDaytimeExtDto> leaveCancelDaytimeExtDtoList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(leaveCancelDayTimes)) {
                            leaveCancelDaytimeExtDtoList = leaveCancelDayTimes.stream().map(leaveCancelDayTime -> {
                                WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
                                waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
                                WaLeaveDaytimeExtDto extDto = mobileV16Service.calLeaveDayRealTimeSlot(waLeaveCancelDayTime, shiftDef);
                                extDto.setXj(true);
                                return extDto;
                            }).collect(Collectors.toList());
                        }

                        // 计算有效休假时间段
                        List<WaLeaveDaytimeExtDto> allDaytimeExtDtoList = new ArrayList<>();
                        allDaytimeExtDtoList.addAll(ltExtDtoList);
                        allDaytimeExtDtoList.addAll(leaveCancelDaytimeExtDtoList);
                        allDaytimeExtDtoList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));
                        if (CollectionUtils.isNotEmpty(travelDaytimeList)) {
                            travelDaytimeList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));
                            allDaytimeExtDtoList.addAll(travelDaytimeList);
                        }

                        List<TimeRangeCheckUtil.ChangeData> leaveTimeRangeList = allDaytimeExtDtoList.stream()
                                .map(lt -> TimeRangeCheckUtil.ChangeData.from(!lt.isXj(), lt.getLeaveStartTime(), lt.getLeaveEndTime())).collect(Collectors.toList());
                        List<Pair<Long, Long>> leaveTimes = TimeRangeCheckUtil.asks(leaveTimeRangeList);

                        // 抵扣迟到
                        float overlapLate = 0f;//迟到重叠时长
                        totalLeaveTimeDuration = 0f;
                        if (CollectionUtils.isNotEmpty(leaveTimes)) {
                            float leaveDuration = 0f;
                            for (Pair<Long, Long> leaveTime : leaveTimes) {
                                long start = leaveTime.getKey();
                                long end = leaveTime.getValue();
                                long validLeaveDuration = end - start;
                                if (validLeaveDuration > 0) {
                                    validLeaveDuration = deductionRestTime(belongDate, validLeaveDuration, start, end, currentShiftDef);
                                    leaveDuration += (BigDecimal.valueOf(validLeaveDuration).divide(BigDecimal.valueOf(60), 0, RoundingMode.HALF_DOWN).floatValue());
                                }
                                //计算迟到时间是否与休假时间段重叠，累加计算重叠的时间段
                                if (lateTime > 0) {
                                    long onDutyStart = belongDate + currentShiftDef.getStartTime() * 60;
                                    boolean flexibleAnalyze = false;
                                    long workLate = shiftDef.getFlexibleWorkLate() == null ? 0L : shiftDef.getFlexibleWorkLate().multiply(BigDecimal.valueOf(3600)).longValue();
                                    if (isFlexibleWork && FlexibleEnum.OPEN.getIndex().equals(shiftDef.getFlexibleShiftSwitch())) {
                                        if (FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(shiftDef.getFlexibleWorkRule())) {
                                            if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(empAnalyzeInfo.getFlexibleWorkType())) {
                                                flexibleAnalyze = true;
                                            } else {
                                                boolean leaveFlag = this.checkApplyLeaveByDate(dto, analyze.getEmpid(), belongDate, Boolean.TRUE, params);
                                                flexibleAnalyze = !leaveFlag;
                                            }
                                        }
                                    }
                                    if (flexibleAnalyze) {
                                        onDutyStart += workLate;
                                        start += workLate;
                                        end += workLate;
                                    }
                                    if (mobileV16Service.checkTimeRangeOverlap(onDutyStart, regSignInTime, start, end)) {
                                        //迟到时间与休假时间重叠
                                        long s = Math.max(onDutyStart, start);
                                        long e = Math.min(regSignInTime, end);
                                        long diff = e - s;
                                        if (diff > 0) {
                                            //迟到与休假重叠时间与休息时间重叠，扣除重叠地休息时间
                                            if (flexibleAnalyze) {
                                                diff = flexibleDeductionRestTime(belongDate, diff, s, e, currentShiftDef, workLate);
                                            } else {
                                                diff = deductionRestTime(belongDate, diff, s, e, currentShiftDef);
                                            }
                                        }
                                        overlapLate += BigDecimal.valueOf(diff).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).floatValue();
                                    }
                                }
                            }
                            if (leaveDuration > 0) {
                                totalLeaveTimeDuration = leaveDuration;
                            }
                            if (totalLeaveTimeDuration == shiftDef.getWorkTotalTime()) {
                                overlapLate = shiftDef.getWorkTotalTime();
                            }
                            //迟到时长抵扣休假时间，计算实际迟到时长
                            lateTime -= overlapLate;
                            if (lateTime < 0) {
                                lateTime = 0f;
                            }
                        }
                    }
                }

                // 抵扣早退
                if (actualWorkTime < 0) {
                    actualWorkTime = 0;
                }
                if (isAnalyzeLateEarly(empAnalyzeInfo) || originalLateTime > 0 || earlyTime > 0) {
                    float early = (float) workTime - actualWorkTime;
                    if (lateTime > 0 && brjLeaveTimeDuration > 0) {
                        if (lateTime > brjLeaveTimeDuration) {
                            lateTime -= brjLeaveTimeDuration;
                        } else {
                            lateTime = 0;
                        }
                    }
                    if (brjLeaveTimeDuration > 0) {
                        totalLeaveTimeDuration += brjLeaveTimeDuration;
                    }
                    Map<String, Float> travelDurationMap = dto.getTravelDurationMap();
                    if (travelDurationMap.containsKey(key)) {
                        early -= travelDurationMap.get(key);
                    }
                    early -= totalLeaveTimeDuration;
                    early -= lateTime;
                    early = early < 0 ? 0f : early;
                    if (dto.getExemptDuration(analyze.getEmpid(), belongDate) > 0) {
                        early -= dto.getExemptDuration(analyze.getEmpid(), belongDate);
                    }
                    analyze.setActualWorkTime(actualWorkTime);
                    analyze.setLateTime(BigDecimal.valueOf(lateTime).setScale(2, RoundingMode.HALF_UP).floatValue());
                    analyze.setEarlyTime(BigDecimal.valueOf(early).setScale(2, RoundingMode.HALF_UP).floatValue());
                    lateAndEarlyLeaveExemption(totalLeaveTimeDuration, analyze, empAnalyzeInfo, allEmpLeaveInfo);
                }
                // 考勤异常标记
                if ((analyze.getLateTime() != null && analyze.getLateTime() > 0) || analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                    analyze.setIsExp(1);
                }
                if (analyze.getIsKg() != null && analyze.getIsKg() == 1) {
                    analyze.setLateTime(0f);
                    analyze.setEarlyTime(0f);
                }

                // 计算当天的请假单数据
                Map<String, Object> originLevelJson = calculateOriginLevel(dto, empLeaveInfos, empLeaveInfos2);
                analyze.setOriginLevelColumnJsonb(originLevelJson);
                analyze.setShiftDefId(currentShiftDef.getShiftDefId());
                if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
                    empLeaveInfos = empLeaveInfos.stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
                    empLeaveInfos2 = empLeaveInfos2.stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList());
                }

                // 旷工分析逻辑
                cancelKgRecord(analyze, empLeaveInfos, empLeaveInfos2, travelDaytimeList, currentShiftDef, dto);
                if (currentShiftDef.getDateType() == 1) {
                    absentAnalyzeRule(analyze, empAnalyzeInfo, currentShiftDef);
                }
            }

            calculateEmpLeave(resultWa, empLeaveMap, dto);
            addEmpLeaveAfterWaAnalyze(resultWa, empLeaveAfterMap, dto, startDate, endDate);
        } else {
            Map<String, List<EmpLeaveInfo>> empLeaveAfterMap = new HashMap<>();
            Map<String, List<EmpLeaveInfo>> empLeaveMap = getEmpLeaveInfos(dto.getEmpLeaveInfoList(), dto, empLeaveAfterMap);

            calculateEmpLeave(resultWa, empLeaveMap, dto);
            addEmpLeaveAfterWaAnalyze(resultWa, empLeaveAfterMap, dto, startDate, endDate);
        }
    }

    private void lateAndEarlyLeaveExemption(float totalLeaveTimeDuration, WaAnalyze analyze, WaAnalyzInfo empAnalyzeInfo, List<EmpLeaveInfo> empLeaveInfo) {
        if (null == empAnalyzeInfo) {
            return;
        }
        if (checkLeaveExemption(totalLeaveTimeDuration, empAnalyzeInfo, empLeaveInfo)) {
            if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && !checkMinLateTime(analyze.getLateTime(), empAnalyzeInfo)) {
                analyze.setLateTime(0f);
            }
            if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0 && !checkMinEarlyTime(analyze.getEarlyTime(), empAnalyzeInfo)) {
                analyze.setEarlyTime(0f);
            }
        }
    }

    /**
     * 迟到早退豁免条件判断
     *
     * @param totalLeaveTimeDuration 休假时长
     * @param analyzeInfo            分析规则
     * @param empLeaveInfo           休假单
     * @return boolean
     */
    private boolean checkLeaveExemption(float totalLeaveTimeDuration, WaAnalyzInfo analyzeInfo, List<EmpLeaveInfo> empLeaveInfo) {
        // 1. 快速空值/无效判断
        if (analyzeInfo == null) {
            return false;
        }
        // 2. 解析豁免类型（提取为独立方法，提高可读性）
        Set<Integer> exemptionTypes = parseExemptionTypes(analyzeInfo.getLeaveExemptionType());
        // 3. 检查各豁免条件（使用短路逻辑，任一满足即返回）
        if (exemptionTypes.contains(LeaveExemptionTypeEnum.NON_LEAVE.getIndex()) && handleNormalWorkExemption(totalLeaveTimeDuration)) {
            return true;
        }
        if (exemptionTypes.contains(LeaveExemptionTypeEnum.DAY_LEAVE.getIndex()) && handleHalfDayLeaveExemption(totalLeaveTimeDuration, empLeaveInfo)) {
            return true;
        }
        return exemptionTypes.contains(LeaveExemptionTypeEnum.HOUR_LEAVE.getIndex()) && handleHourLeaveExemption(totalLeaveTimeDuration, empLeaveInfo);
    }

    /**
     * 解析休假豁免类型字符串为去重的整数集合
     *
     * @param leaveExemptionType 豁免类型字符串（如"1,2,3"）
     * @return 去重后的豁免类型集合，空字符串/Null返回空集合
     */
    private Set<Integer> parseExemptionTypes(String leaveExemptionType) {
        // 处理空值或空白字符串
        if (leaveExemptionType == null || leaveExemptionType.trim().isEmpty()) {
            return Collections.emptySet();
        }
        // 分割、转换、去重并收集为Set
        return Arrays.stream(leaveExemptionType.trim().split(",")).map(this::safeParseInt).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    /**
     * 安全地将字符串转换为Integer（处理格式错误）
     *
     * @param str 待转换的字符串
     * @return 转换后的Integer，失败则返回null
     */
    private Integer safeParseInt(String str) {
        try {
            return Integer.valueOf(str.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 无休假
     *
     * @param totalLeaveTimeDuration 休假时长
     * @return boolean
     */
    private boolean handleNormalWorkExemption(float totalLeaveTimeDuration) {
        return !(totalLeaveTimeDuration > 0);
    }

    /**
     * 半天假
     *
     * @param totalLeaveTimeDuration 休假时长
     * @param empLeaveInfo           休假单据
     * @return boolean
     */
    private boolean handleHalfDayLeaveExemption(float totalLeaveTimeDuration, List<EmpLeaveInfo> empLeaveInfo) {
        // 总休假时长不大于0，直接返回false
        if (totalLeaveTimeDuration <= 0) {
            return false;
        }
        // 休假信息列表为空，直接返回false
        if (CollectionUtils.isEmpty(empLeaveInfo)) {
            return false;
        }
        // 预定义需要匹配的假期类型（转为Short类型，与leave.getPeriod_type()类型一致）
        Short periodTypeOne = PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().shortValue();
        Short periodTypeNine = PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().shortValue();
        // 流处理：判断是否存在符合条件的休假记录
        return empLeaveInfo.stream().anyMatch(leave -> leave.getTime_duration() > 0
                && (periodTypeOne.equals(leave.getPeriod_type()) || periodTypeNine.equals(leave.getPeriod_type())));
    }

    /**
     * 小时假
     *
     * @param totalLeaveTimeDuration 休假时长
     * @param empLeaveInfo           休假单据
     * @return boolean
     */
    private boolean handleHourLeaveExemption(float totalLeaveTimeDuration, List<EmpLeaveInfo> empLeaveInfo) {
        // 总休假时长不大于0，直接返回false
        if (totalLeaveTimeDuration <= 0) {
            return false;
        }
        // 休假信息列表为空，直接返回false
        if (CollectionUtils.isEmpty(empLeaveInfo)) {
            return false;
        }
        // 预定义需要匹配的假期类型（转为Short类型，与leave.getPeriod_type()类型一致）
        Short periodTypeOne = PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().shortValue();
        Short periodTypeNine = PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().shortValue();
        return empLeaveInfo.stream().anyMatch(leave -> leave.getTime_duration() > 0
                && (periodTypeOne.equals(leave.getPeriod_type()) || periodTypeNine.equals(leave.getPeriod_type())));
    }

    private List<WaLeaveDaytimeExtDto> getTravelDaytimeExtDtoList(Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap,
                                                                  Map<String, List<WaEmpTravelDaytimeDo>> empTravelAfterMap,
                                                                  String key, WaShiftDef shiftDef) {
        List<WaLeaveDaytimeExtDto> travelDaytimeExtDtoList = new ArrayList<>();
        if (MapUtils.isNotEmpty(empTravelMap) || MapUtils.isNotEmpty(empTravelAfterMap)) {
            List<WaEmpTravelDaytimeDo> allEmpTravelInfos = new ArrayList<>();
            if (empTravelMap.containsKey(key) && null != empTravelMap.get(key)) {
                allEmpTravelInfos.addAll(empTravelMap.get(key));
            }
            if (empTravelAfterMap.containsKey(key) && null != empTravelAfterMap.get(key)) {
                allEmpTravelInfos.addAll(empTravelAfterMap.get(key));
            }
            List<DaytimeExtDto> travelDayTimes = getDayTimes(allEmpTravelInfos);
            List<WaTravelDaytimeExtDto> extDtoList = travelDayTimes.stream().map(ltDay -> calTravelDayRealTimeSlot(ltDay, shiftDef)).sorted(Comparator.comparing(WaTravelDaytimeExtDto::getTravelId)).collect(Collectors.toList());

            travelDaytimeExtDtoList = extDtoList.stream().map(travelDayTime -> {
                WaLeaveDaytimeExtDto extDto = new WaLeaveDaytimeExtDto();
                extDto.setCreateTime(travelDayTime.getCreateTime());
                extDto.setLeaveStartTime(travelDayTime.getStartTime());
                extDto.setLeaveEndTime(travelDayTime.getEndTime());
                extDto.setXj(true);
                return extDto;
            }).collect(Collectors.toList());
        }
        return travelDaytimeExtDtoList;
    }

    private CalLeaveTimeDurationResultDto calLeaveTimeDuration(List<EmpLeaveInfo> allEmpLeaveInfo, Long belongDate,
                                                               int workTime, EmpShiftInfo currentShiftDef) {
        float totalLeaveTimeDuration = 0f;
        float brjLeaveTimeDuration = 0f;
        List<Integer> notDeductionLeaveIds = new ArrayList<>();
        List<Integer> brjLeaveIds = new ArrayList<>();

        for (EmpLeaveInfo lf : allEmpLeaveInfo) {
            //非工作日等不抵扣
            if (lf.getDate_type() == null || !DateTypeEnum.DATE_TYP_1.getIndex().equals(lf.getDate_type())
                    || !lf.getLeave_date().equals(belongDate) || lf.getEmpShiftInfo() == null) {
                notDeductionLeaveIds.add(lf.getLeave_id());
                continue;
            }
            Integer periodType = Integer.valueOf(lf.getPeriod_type());
            float timeDuration = Optional.ofNullable(lf.getTime_duration()).orElse(0f);
            if (!lf.getLeave_type_def_code().equals("BRJ")) {
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
                    //休假为整天，则休假时长为应出勤时长
                    timeDuration = workTime;
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    //休假为上下半天，则休假时长需根据是否设置半天时间点等计算实际休假时长（单位分钟）
                    int halfWorkTime = workTime / 2;
                    if (timeDuration == 1) {
                        //假期类型为上下半天，实际休假时长为1，则转换为应出勤时长（单位分钟）
                        timeDuration = workTime;
                    } else {
                        if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                            //分析出来的上半天休假时长（单位分钟）
                            timeDuration = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, currentShiftDef, halfWorkTime);
                        }
                        if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                            //分析出来的下半天休假时长（单位分钟）
                            timeDuration = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, currentShiftDef, halfWorkTime);
                        }
                    }
                }
                //累加非哺乳假休假时长
                totalLeaveTimeDuration += timeDuration;
            } else {
                brjLeaveIds.add(lf.getLeave_id());
                //累加哺乳假休假时长
                brjLeaveTimeDuration += timeDuration;
            }
        }

        CalLeaveTimeDurationResultDto resultDto = new CalLeaveTimeDurationResultDto();
        resultDto.setTotalLeaveTimeDuration(totalLeaveTimeDuration);
        resultDto.setBrjLeaveTimeDuration(brjLeaveTimeDuration);
        resultDto.setNotDeductionLeaveIds(notDeductionLeaveIds);
        resultDto.setBrjLeaveIds(brjLeaveIds);
        return resultDto;
    }

    private void setLeaveCancelMap(String tenantId, Long startDate, Long endDate, WaAnalyzCalDTO dto) {
        Map<String, List<WaLeaveCancelDayTime>> dayTimeMap = new HashMap<>();
        List<WaEmpLeaveCancelDaytimeDo> leaveCancels = empLeaveCancelService.getLeaveCancelDaytimeList(tenantId, startDate, endDate);
        if (CollectionUtils.isEmpty(leaveCancels)) {
            return;
        }
        Map<String, List<WaEmpLeaveCancelDaytimeDo>> empLeaveCancelMap = leaveCancels.stream().collect(Collectors.groupingBy(l -> String.format("%s_%s", l.getEmpid(), l.getLeaveCancelDate())));
        for (Map.Entry<String, List<WaEmpLeaveCancelDaytimeDo>> entry : empLeaveCancelMap.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                dayTimeMap.put(entry.getKey(), ObjectConverter.convertList(entry.getValue(), WaLeaveCancelDayTime.class));
            }
        }
        dto.setLeaveCancelMap(dayTimeMap);
    }

    private List<WaLeaveDaytimeExtPo> getLeaveDayTimes(List<EmpLeaveInfo> leaveInfos) {
        List<WaLeaveDaytimeExtPo> dayTimes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(leaveInfos)) {
            leaveInfos.forEach(l -> {
                WaLeaveDaytimeExtPo daytimeExt = new WaLeaveDaytimeExtPo();
                daytimeExt.setLeaveId(l.getLeave_id());
                daytimeExt.setLeaveDate(l.getLeave_date());
                daytimeExt.setShalfDay(l.getShalf_day());
                daytimeExt.setEhalfDay(l.getEhalf_day());
                daytimeExt.setStartTime(l.getStart_time());
                daytimeExt.setEndTime(l.getEnd_time());
                daytimeExt.setPeriodType(l.getPeriod_type());
                daytimeExt.setTimeUnit(l.getTime_unit().shortValue());
                daytimeExt.setTimeDuration(l.getTime_duration());
                daytimeExt.setDateType(l.getDate_type());
                daytimeExt.setCreateTime(l.getCreateTime());
                dayTimes.add(daytimeExt);
            });
        }
        return dayTimes;
    }

    /**
     * 出差单据分析--废弃
     *
     * @param resultWa
     * @param params
     * @param dto
     */
    @Deprecated
    private void analyzeTravelData(List<WaAnalyze> resultWa, Map params, WaAnalyzCalDTO dto) {
        String belongId = (String) params.get("belongid");
        Long startDate = (Long) params.get("startDate");
        Long endDate = (Long) params.get("endDate");
        String anyEmpIds2 = (String) params.get("anyEmpids2");
        //查询出差单据
        if (dto.getEmpTravelInfoList() == null) {
            List<WaEmpTravelDaytimeDo> empTravelList = getEmpTravelList(belongId, anyEmpIds2, startDate, endDate);
            dto.setEmpTravelInfo(empTravelList);
        }
        //审批时间在请假时间后的请假数据
        Map<String, List<WaEmpTravelDaytimeDo>> empTravelAfterMap = new HashMap<>();
        //组合员工的请假单key: empid + "_"+leave_date;
        Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap = getEmpTravelInfos(dto.getEmpTravelInfoList(), dto, empTravelAfterMap);
        if (CollectionUtils.isNotEmpty(resultWa)) {
            //计算出差小时数
            for (WaAnalyze analyze : resultWa) {
                Long belongDate = analyze.getBelongDate();
                String key = analyze.getEmpid() + "_" + belongDate;
                //出差单有效状态 默认有效
                Integer validStatus = ValidStatusEnum.VALID.getIndex();
                //出差时长是否抵扣迟到早退旷工时长
                boolean ifCalculateKgForTravel = false;
                WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                Integer outParseRule = empAnalyzeInfo == null || empAnalyzeInfo.getOutParseRule() == null ? TravelTypeParseRuleEnum.TRAVEL_REG.getIndex() : empAnalyzeInfo.getOutParseRule();
                if (!TravelTypeParseRuleEnum.TRAVEL_REG.getIndex().equals(outParseRule)) {
                    if (TravelTypeParseRuleEnum.TRAVEL_BILL_REG.getIndex().equals(outParseRule)) {
                        //判断当天是否有外勤打卡
                        List<WaRegisterRecord> outSignList = dto.getEmpOutRegByDateEmpId(analyze.getEmpid(), analyze.getBelongDate());
                        ifCalculateKgForTravel = CollectionUtils.isNotEmpty(outSignList);
                        if (!ifCalculateKgForTravel) {
                            validStatus = ValidStatusEnum.INVALID.getIndex();
                        }
                    } else {
                        ifCalculateKgForTravel = true;
                    }
                }
                //去查找出差日期的数据
                EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                List<WaEmpTravelDaytimeDo> empLeaveInfos = null;
                //查找这一天，是否有出差记录
                if (empTravelMap.containsKey(key)) {
                    List<WaEmpTravelDaytimeDo> leaveInfos = empTravelMap.get(key);
                    //先判断需不需要进行抵扣（如果迟到早退异常时才需要抵扣）
                    if (ifCalculateKgForTravel && leaveInfos != null && leaveInfos.size() > 0) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            //扣减迟到早退小时数
                            deductionLateAndEarlyHours2ForTravel(analyze, leaveInfos, 1);
                        } else if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            deductionLateAndEarlyHours2ForTravel(analyze, leaveInfos, 2);
                        } else if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            deductionLateAndEarlyHours2ForTravel(analyze, leaveInfos, 3);
                        }
                    }
                    Map travelColumnJsonMap = getTravelColumnJson(leaveInfos, dto, false);
                    if (MapUtils.isNotEmpty(travelColumnJsonMap)) {
                        //设置出差单有效状态
                        travelColumnJsonMap.put("valid_status", validStatus);
                    }
                    analyze.setTravelColumnJsonb(travelColumnJsonMap);
                    //查找leaveInfos中real_date==申请日期的数据存入原始记录中，再拿出差单数据做分析
                    empLeaveInfos = leaveInfos;
                    empTravelMap.remove(key);
                }
                //获取出差当天的数据做扣减
                List<WaEmpTravelDaytimeDo> empLeaveInfos2 = null;
                if (empTravelAfterMap.containsKey(key)) {
                    List<WaEmpTravelDaytimeDo> leaveInfos = empTravelAfterMap.get(key);
                    //先判断需不需要进行抵扣（如果迟到早退异常时才需要抵扣）
                    if (ifCalculateKgForTravel && leaveInfos != null && leaveInfos.size() > 0) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0 && analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            //扣减迟到早退小时数
                            deductionLateAndEarlyHours2ForTravel(analyze, leaveInfos, 1);
                        } else if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            deductionLateAndEarlyHours2ForTravel(analyze, leaveInfos, 2);
                        } else if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            deductionLateAndEarlyHours2ForTravel(analyze, leaveInfos, 3);
                        }
                    }
                    //只用出差记录扣减迟到早退小时数，不记录出差申请时间<审批时间的出差记录
                    //查找leaveInfos中real_date==申请日期的数据存入原始记录中，再拿出差单数据做分析
                    empLeaveInfos2 = leaveInfos;
                    //旷工，员工当天没上班，申请出差，出差单据由9:00-18:00 旷工	考勤异常，旷工和出差单对冲—>当旷工时间=0
                    empTravelAfterMap.remove(key);
                }
                //计算当天的出差单数据
                if (currentShiftDef != null) {
                    Map<String, Object> orginLevelJson = calculateOriginTravel(dto, empLeaveInfos, empLeaveInfos2);
                    if (MapUtils.isNotEmpty(orginLevelJson)) {
                        //设置出差单有效状态
                        orginLevelJson.put("valid_status", validStatus);
                    }
                    analyze.setOriginTravelColumnJsonb(orginLevelJson);
                    analyze.setShiftDefId(currentShiftDef.getShiftDefId());
                }
                //判断是否有出差记录，如果有则抵消旷工记录
                if (ifCalculateKgForTravel) {
                    cancelKgRecordForTravel(analyze, empLeaveInfos, empLeaveInfos2);
                }
                //新的旷工逻辑，旷工时长判断逻辑
                if (currentShiftDef != null && currentShiftDef.getDateType() == 1 && empLeaveInfos != null && empLeaveInfos2 != null) {
                    absentAnalyzeRule(analyze, empAnalyzeInfo, currentShiftDef);
                }
            }
        }
        //记录剩余的请假单数据
        calculateEmpTravel(resultWa, empTravelMap, dto);
        //审批时间大于请假申请时间的记录，默认添加一个条分析记录
        addEmpTravelAfterWaAnalyze(resultWa, empTravelAfterMap, dto, startDate, endDate);
    }

    /**
     * 出差单据分析
     *
     * @param resultWa
     * @param params
     * @param dto
     */
    private void analyzeTravelDataNew(List<WaAnalyze> resultWa, Map params, WaAnalyzCalDTO dto) {
        String belongId = (String) params.get("belongid");
        Long startDate = (Long) params.get("startDate");
        Long endDate = (Long) params.get("endDate");
        String anyEmpIds2 = (String) params.get("anyEmpids2");

        if (dto.getEmpTravelInfoList() == null) {
            List<WaEmpTravelDaytimeDo> empTravelList = getEmpTravelList(belongId, anyEmpIds2, startDate, endDate);
            dto.setEmpTravelInfo(empTravelList);
        }

        Map<String, List<WaEmpTravelDaytimeDo>> empTravelAfterMap = new HashMap<>();
        Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap = getEmpTravelInfos(dto.getEmpTravelInfoList(), dto, empTravelAfterMap);
        Map<String, Float> travelDurationMap = dto.getTravelDurationMap();

        // 外出分析
        if (CollectionUtils.isNotEmpty(resultWa)) {
            for (WaAnalyze analyze : resultWa) {
                Long belongDate = analyze.getBelongDate();
                String key = analyze.getEmpid() + "_" + belongDate;

                WaAnalyzInfo empAnalyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
                boolean isFlexibleWork = empAnalyzeInfo != null && empAnalyzeInfo.getFlexibleWorkSwitch() != null && FlexibleEnum.OPEN.getIndex().equals(empAnalyzeInfo.getFlexibleWorkSwitch());

                // 判断外出单是否可以进行抵扣
                Integer outParseRule = (empAnalyzeInfo == null || empAnalyzeInfo.getOutParseRule() == null)
                        ? TravelTypeParseRuleEnum.TRAVEL_REG.getIndex() : empAnalyzeInfo.getOutParseRule();
                CheckTravelIfValidResultDto checkTravelIfValidResultDto = checkTravelIfValid(analyze, outParseRule, dto);
                Integer validStatus = checkTravelIfValidResultDto.getValidStatus();
                boolean ifCalculateKgForTravel = checkTravelIfValidResultDto.isIfCalculateKgForTravel();

                // 当日排班
                EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                WaShiftDef shiftDef = ObjectConverter.convert(currentShiftDef, WaShiftDef.class);

                // 当日外出单据
                List<WaEmpTravelDaytimeDo> allEmpTravelInfos = new ArrayList<>();
                List<WaEmpTravelDaytimeDo> empTravelInfos = null;
                if (empTravelMap.containsKey(key)) {
                    List<WaEmpTravelDaytimeDo> travelInfos = empTravelMap.get(key);
                    if (ifCalculateKgForTravel && CollectionUtils.isNotEmpty(travelInfos)) {
                        allEmpTravelInfos.addAll(travelInfos);
                    }
                    Map travelColumnJsonMap = getTravelColumnJson(travelInfos, dto, false);
                    if (MapUtils.isNotEmpty(travelColumnJsonMap)) {
                        //设置出差单有效状态
                        travelColumnJsonMap.put("valid_status", validStatus);
                    }
                    analyze.setTravelColumnJsonb(travelColumnJsonMap);
                    empTravelInfos = travelInfos;
                    empTravelMap.remove(key);
                }
                List<WaEmpTravelDaytimeDo> empTravelInfos2 = null;
                if (empTravelAfterMap.containsKey(key)) {
                    List<WaEmpTravelDaytimeDo> travelInfos = empTravelAfterMap.get(key);
                    if (ifCalculateKgForTravel && CollectionUtils.isNotEmpty(travelInfos)) {
                        allEmpTravelInfos.addAll(travelInfos);
                    }
                    empTravelInfos2 = travelInfos;
                    empTravelAfterMap.remove(key);
                }

                // 当日考勤结果
                int workTime = Optional.ofNullable(analyze.getWorkTime()).orElse(0);
                float actualWorkTime = Optional.ofNullable(analyze.getActualWorkTime()).orElse(0f);
                float lateTime = Optional.ofNullable(analyze.getLateTime()).orElse(0f);
                float originalLateTime = Optional.ofNullable(analyze.getLateTime()).orElse(0f);
                Float earlyTime = Optional.ofNullable(analyze.getEarlyTime()).orElse(0f);
                long regSignInTime = Optional.ofNullable(analyze.getRegSigninTime()).orElse(0L);

                // 抵扣迟到
                float totalTravelTimeDuration = 0f;
                if (CollectionUtils.isNotEmpty(allEmpTravelInfos)) {
                    float overlapLate = 0f;// 迟到重叠时长

                    // 计算出差时长
                    List<Long> notDeductionTravelIds = new ArrayList<>();
                    for (WaEmpTravelDaytimeDo lf : allEmpTravelInfos) {
                        if (lf.getDateType() == null || !DateTypeEnum.DATE_TYP_1.getIndex().equals(lf.getDateType()) || !lf.getTravelDate().equals(belongDate) || lf.getEmpShiftInfo() == null) {
                            notDeductionTravelIds.add(lf.getTravelId());
                            continue;
                        }
                        Integer periodType = Integer.valueOf(lf.getPeriodType());
                        float timeDuration = Optional.ofNullable(lf.getTimeDuration()).orElse(0f);
                        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
                            timeDuration = workTime;
                        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                            int halfWorkTime = workTime / 2;
                            if (timeDuration == 1) {
                                timeDuration = workTime;
                            } else {
                                if (HalfDayTypeEnum.A.name().equals(lf.getShalfDay()) && HalfDayTypeEnum.A.name().equals(lf.getEhalfDay())) {
                                    timeDuration = getHalfTime(HalfDayTypeEnum.A.name(), currentShiftDef, halfWorkTime);
                                }
                                if (HalfDayTypeEnum.P.name().equals(lf.getShalfDay()) && HalfDayTypeEnum.P.name().equals(lf.getEhalfDay())) {
                                    timeDuration = getHalfTime(HalfDayTypeEnum.P.name(), currentShiftDef, halfWorkTime);
                                }
                            }
                        }
                        totalTravelTimeDuration += timeDuration;
                    }

                    allEmpTravelInfos = allEmpTravelInfos.stream().filter(l -> !notDeductionTravelIds.contains(l.getTravelId())).collect(Collectors.toList());
                    if (totalTravelTimeDuration > 0 && CollectionUtils.isNotEmpty(allEmpTravelInfos) && (lateTime > 0 || earlyTime > 0)) {
                        // 计算实际外出时间
                        List<DaytimeExtDto> travelDayTimes = getDayTimes(allEmpTravelInfos);
                        List<WaTravelDaytimeExtDto> extDtoList = travelDayTimes.stream().map(ltDay -> calTravelDayRealTimeSlot(ltDay, shiftDef)).sorted(Comparator.comparing(WaTravelDaytimeExtDto::getTravelId)).collect(Collectors.toList());

                        // 抵扣迟到
                        if (CollectionUtils.isNotEmpty(travelDayTimes)) {
                            float travelDuration = 0f;
                            for (WaTravelDaytimeExtDto travelTime : extDtoList) {
                                long start = travelTime.getStartTime();
                                long end = travelTime.getEndTime();
                                long validDuration = end - start;
                                if (validDuration > 0) {
                                    validDuration = deductionRestTime(belongDate, validDuration, start, end, currentShiftDef);
                                    travelDuration += (BigDecimal.valueOf(validDuration).divide(BigDecimal.valueOf(60), 0, RoundingMode.HALF_DOWN).floatValue());
                                }
                                if (lateTime > 0) {
                                    long onDutyStart = belongDate + currentShiftDef.getStartTime() * 60;
                                    boolean flexibleAnalyze = false;
                                    long workLate = shiftDef.getFlexibleWorkLate() == null ? 0L : shiftDef.getFlexibleWorkLate().multiply(BigDecimal.valueOf(3600)).longValue();
                                    if (isFlexibleWork && FlexibleEnum.OPEN.getIndex().equals(shiftDef.getFlexibleShiftSwitch())) {
                                        if (FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex().equals(shiftDef.getFlexibleWorkRule())) {
                                            if (FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex().equals(empAnalyzeInfo.getFlexibleWorkType())) {
                                                flexibleAnalyze = true;
                                            } else {
                                                boolean leaveFlag = this.checkApplyLeaveByDate(dto, analyze.getEmpid(), belongDate, Boolean.TRUE, params);
                                                flexibleAnalyze = !leaveFlag;
                                            }
                                        }
                                    }
                                    if (flexibleAnalyze) {
                                        onDutyStart += workLate;
                                        start += workLate;
                                        end += workLate;
                                    }
                                    if (mobileV16Service.checkTimeRangeOverlap(onDutyStart, regSignInTime, start, end)) {
                                        //迟到时间与休假时间重叠
                                        long s = Math.max(onDutyStart, start);
                                        long e = Math.min(regSignInTime, end);
                                        long diff = e - s;
                                        if (diff > 0) {
                                            //迟到与出差重叠时间与休息时间重叠，扣除重叠地休息时间
                                            if (flexibleAnalyze) {
                                                diff = flexibleDeductionRestTime(belongDate, diff, s, e, currentShiftDef, workLate);
                                            } else {
                                                diff = deductionRestTime(belongDate, diff, s, e, currentShiftDef);
                                            }
                                        }
                                        overlapLate += BigDecimal.valueOf(diff).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN).floatValue();
                                    }
                                }
                            }
                            if (travelDuration > 0) {
                                totalTravelTimeDuration = travelDuration;
                            }
                            if (totalTravelTimeDuration == shiftDef.getWorkTotalTime()) {
                                overlapLate = shiftDef.getWorkTotalTime();
                            }
                            //出差时间抵扣迟到时长，计算实际迟到时长
                            lateTime -= overlapLate;
                            if (lateTime < 0) {
                                lateTime = 0f;
                            }
                        }
                    }
                }
                // 抵扣早退
                if (actualWorkTime < 0) {
                    actualWorkTime = 0;
                }
                if (isAnalyzeLateEarly(empAnalyzeInfo) || earlyTime > 0 || originalLateTime > 0) {
                    float early = (float) workTime - actualWorkTime;
                    if (totalTravelTimeDuration > 0) {
                        if (travelDurationMap.containsKey(key)) {
                            travelDurationMap.put(key, travelDurationMap.get(key) + totalTravelTimeDuration);
                        } else {
                            travelDurationMap.put(key, totalTravelTimeDuration);
                        }
                    }
                    early -= totalTravelTimeDuration;
                    early -= lateTime;
                    early = early < 0 ? 0f : early;
                    analyze.setActualWorkTime(actualWorkTime);
                    analyze.setLateTime(BigDecimal.valueOf(lateTime).setScale(2, RoundingMode.HALF_DOWN).floatValue());
                    analyze.setEarlyTime(BigDecimal.valueOf(early).setScale(2, RoundingMode.HALF_DOWN).floatValue());
                }

                // 考勤异常标记
                if ((analyze.getLateTime() != null && analyze.getLateTime() > 0) || analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                    analyze.setIsExp(1);
                }
                if (analyze.getIsKg() != null && analyze.getIsKg() == 1) {
                    analyze.setLateTime(0f);
                    analyze.setEarlyTime(0f);
                }

                // 计算当天的出差单数据
                if (currentShiftDef != null) {
                    Map<String, Object> originTravelJson = calculateOriginTravel(dto, empTravelInfos, empTravelInfos2);
                    if (MapUtils.isNotEmpty(originTravelJson)) {
                        //设置出差单有效状态
                        originTravelJson.put("valid_status", validStatus);
                    }
                    analyze.setOriginTravelColumnJsonb(originTravelJson);
                    analyze.setShiftDefId(currentShiftDef.getShiftDefId());
                }

                // 旷工分析逻辑
                if (ifCalculateKgForTravel) {
                    cancelKgRecordForTravel(analyze, empTravelInfos, empTravelInfos2);
                }
                if (currentShiftDef != null && currentShiftDef.getDateType() == 1 && empTravelInfos != null && empTravelInfos2 != null) {
                    absentAnalyzeRule(analyze, empAnalyzeInfo, currentShiftDef);
                }
            }
        }
        // 剩余外出单分析
        calculateEmpTravel(resultWa, empTravelMap, dto);
        addEmpTravelAfterWaAnalyze(resultWa, empTravelAfterMap, dto, startDate, endDate);
    }

    /**
     * 判断外出单是否可以进行抵扣
     *
     * @param analyze
     * @param outParseRule
     * @param dto
     * @return
     */
    private CheckTravelIfValidResultDto checkTravelIfValid(WaAnalyze analyze, Integer outParseRule, WaAnalyzCalDTO dto) {
        Integer validStatus = ValidStatusEnum.VALID.getIndex();
        boolean ifCalculateKgForTravel = false;
        if (!TravelTypeParseRuleEnum.TRAVEL_REG.getIndex().equals(outParseRule)) {
            if (TravelTypeParseRuleEnum.TRAVEL_BILL_REG.getIndex().equals(outParseRule)) {
                List<WaRegisterRecord> outSignList = dto.getEmpOutRegByDateEmpId(analyze.getEmpid(), analyze.getBelongDate());
                ifCalculateKgForTravel = CollectionUtils.isNotEmpty(outSignList);
                if (!ifCalculateKgForTravel) {
                    validStatus = ValidStatusEnum.INVALID.getIndex();
                }
            } else {
                ifCalculateKgForTravel = true;
            }
        }
        CheckTravelIfValidResultDto resultDto = new CheckTravelIfValidResultDto();
        resultDto.setValidStatus(validStatus);
        resultDto.setIfCalculateKgForTravel(ifCalculateKgForTravel);
        return resultDto;
    }

    private List<DaytimeExtDto> getDayTimes(List<WaEmpTravelDaytimeDo> travelInfos) {
        List<DaytimeExtDto> dayTimes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(travelInfos)) {
            travelInfos.forEach(l -> {
                DaytimeExtDto daytimeExt = new DaytimeExtDto();
                daytimeExt.setTravelId(l.getTravelId());
                daytimeExt.setWorkDate(l.getTravelDate());
                daytimeExt.setSHalfDay(l.getShalfDay());
                daytimeExt.setEHalfDay(l.getEhalfDay());
                daytimeExt.setStartTime(l.getStartTime());
                daytimeExt.setEndTime(l.getEndTime());
                daytimeExt.setPeriodType(l.getPeriodType());
                daytimeExt.setTimeUnit(l.getTimeUnit());
                daytimeExt.setTimeDuration(l.getTimeDuration());
                daytimeExt.setDateType(l.getDateType());
                daytimeExt.setCreateTime(l.getCreateTime());
                dayTimes.add(daytimeExt);
            });
        }
        return dayTimes;
    }

    /**
     * 计算每日外出的实际休假时间段
     *
     * @param travelDaytime
     * @param shiftDef
     * @return
     */
    private WaTravelDaytimeExtDto calTravelDayRealTimeSlot(DaytimeExtDto travelDaytime, WaShiftDef shiftDef) {
        WaTravelDaytimeExtDto travelDaytimeExtDto = new WaTravelDaytimeExtDto();
        travelDaytimeExtDto.setTravelId(travelDaytime.getTravelId());
        travelDaytimeExtDto.setCreateTime(travelDaytime.getCreateTime());
        Integer periodType = Integer.valueOf(travelDaytime.getPeriodType());
        // 跨夜
        boolean isKy = CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
        long travelDate = travelDaytime.getWorkDate();
        long tomorrowDate = DateUtil.addDate(travelDate * 1000, 1);
        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
            // 整天
            travelDaytimeExtDto.setStartTime(travelDate + (shiftDef.getStartTime() * 60));
            long travelEndDate = isKy ? tomorrowDate : travelDate;
            travelDaytimeExtDto.setEndTime(travelEndDate + (shiftDef.getEndTime() * 60));
        } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
            // 半天
            if (HalfDayTypeEnum.A.name().equals(travelDaytime.getSHalfDay()) && HalfDayTypeEnum.P.name().equals(travelDaytime.getEHalfDay())) {
                // 一整天
                travelDaytimeExtDto.setStartTime(travelDate + (shiftDef.getStartTime() * 60));
                long travelEndDate = isKy ? tomorrowDate : travelDate;
                travelDaytimeExtDto.setEndTime(travelEndDate + (shiftDef.getEndTime() * 60));
            } else if (HalfDayTypeEnum.A.name().equals(travelDaytime.getSHalfDay()) && HalfDayTypeEnum.A.name().equals(travelDaytime.getEHalfDay())) {
                // 上半天
                travelDaytimeExtDto.setStartTime(travelDate + (shiftDef.getStartTime() * 60));
                // 计算半天定义时间点
                Integer halfHayTime = getHalfDayTime(shiftDef, HalfDayTypeEnum.A);
                if (null != halfHayTime) {
                    boolean travelKy = shiftDef.getStartTime() > halfHayTime;
                    long travelEndDate = travelKy ? tomorrowDate : travelDate;
                    travelDaytimeExtDto.setEndTime(travelEndDate + (halfHayTime * 60));
                } else {
                    long travelEndDate = isKy ? tomorrowDate : travelDate;
                    travelDaytimeExtDto.setEndTime(travelEndDate + (shiftDef.getEndTime() * 60));
                }
            } else if (HalfDayTypeEnum.P.name().equals(travelDaytime.getSHalfDay()) && HalfDayTypeEnum.P.name().equals(travelDaytime.getEHalfDay())) {
                // 下半天
                // 计算半天定义时间点
                Integer halfDayTime = getHalfDayTime(shiftDef, HalfDayTypeEnum.P);
                if (null != halfDayTime) {
                    boolean travelKy = shiftDef.getStartTime() > halfDayTime;
                    long travelStartDate = travelKy ? tomorrowDate : travelDate;
                    travelDaytimeExtDto.setStartTime(travelStartDate + (halfDayTime * 60));
                    long travelEndDate = isKy ? tomorrowDate : travelDate;
                    travelDaytimeExtDto.setEndTime(travelEndDate + (shiftDef.getEndTime() * 60));
                } else {
                    travelDaytimeExtDto.setStartTime(travelDate + (shiftDef.getStartTime() * 60));
                    long travelEndDate = isKy ? tomorrowDate : travelDate;
                    travelDaytimeExtDto.setEndTime(travelEndDate + (shiftDef.getEndTime() * 60));
                }
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(periodType)) {
            // 小时整天
            if (null != travelDaytime.getStartTime() && null != travelDaytime.getEndTime()) {
                travelDaytimeExtDto.setStartTime(travelDate + (travelDaytime.getStartTime() * 60));
                boolean travelKy = travelDaytime.getStartTime() > travelDaytime.getEndTime();
                long travelEndDate = travelKy ? tomorrowDate : travelDate;
                travelDaytimeExtDto.setEndTime(travelEndDate + (travelDaytime.getEndTime() * 60));
            } else {
                travelDaytimeExtDto.setStartTime(travelDate + (shiftDef.getStartTime() * 60));
                long travelEndDate = isKy ? tomorrowDate : travelDate;
                travelDaytimeExtDto.setEndTime(travelEndDate + (shiftDef.getEndTime() * 60));
            }
        } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(periodType)) {
            // 小时
            travelDaytimeExtDto.setStartTime(travelDate + (travelDaytime.getStartTime() * 60));
            boolean travelKy = travelDaytime.getStartTime() > travelDaytime.getEndTime();
            long travelEndDate = travelKy ? tomorrowDate : travelDate;
            travelDaytimeExtDto.setEndTime(travelEndDate + (travelDaytime.getEndTime() * 60));
        }
        return travelDaytimeExtDto;
    }

    private Integer getHalfDayTime(WaShiftDef shiftDef, HalfDayTypeEnum halfDay) {
        // 是否定义半天时间
        boolean isHalfDayDef = shiftDef.getIsHalfdayTime() != null && shiftDef.getIsHalfdayTime() && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0;
        if (isHalfDayDef) {
            return shiftDef.getHalfdayTime();
        }
        // 是否定义午休时间
        boolean isNoonDef = shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest() && null != shiftDef.getNoonRestStart() && null != shiftDef.getNoonRestEnd();
        if (isNoonDef) {
            return HalfDayTypeEnum.A == halfDay ? shiftDef.getNoonRestStart() : shiftDef.getNoonRestEnd();
        }
        return null;
    }

    /**
     * 休假-抵扣迟到早退
     *
     * @param wa
     * @param leaveInfos
     * @param empAnanlyzInfo
     * @param type           1 有迟到早退  2有迟到  3有早退
     * @param belongId
     * @return
     */
    private Map deductionLateAndEarlyHours2(WaAnalyze wa, List<EmpLeaveInfo> leaveInfos, WaAnalyzInfo empAnanlyzInfo, int type, String belongId) {
        for (EmpLeaveInfo lf : leaveInfos) {
            if (lf.getDate_type() == null || lf.getDate_type() != 1) {
                continue; // 当是工作日的请假才进行扣减
            }
            //如果有迟到早退才进行扣减
            if ((wa.getLateTime() != null && wa.getLateTime() > 0) || (wa.getEarlyTime() != null && wa.getEarlyTime() > 0)) {
                // 如果请假记录是等于当天的才进行扣减
                if (lf.getLeave_date().equals(wa.getBelongDate()) && lf.getEmpShiftInfo() != null) {
                    //扣减签到签退小时数
                    deductionLateAndEarly(wa, type, lf);
                }
            }
        }
        return null;
    }

    /**
     * 出差-抵扣迟到早退
     *
     * @param wa
     * @param leaveInfos
     * @param type
     * @return
     */
    private Map deductionLateAndEarlyHours2ForTravel(WaAnalyze wa, List<WaEmpTravelDaytimeDo> leaveInfos, int type) {
        for (WaEmpTravelDaytimeDo lf : leaveInfos) {
            if (lf.getDateType() == null || lf.getDateType() != 1) {
                continue; // 当是工作日的请假才进行扣减
            }
            //如果有迟到早退才进行扣减
            if ((wa.getLateTime() != null && wa.getLateTime() > 0) || (wa.getEarlyTime() != null && wa.getEarlyTime() > 0)) {
                // 如果请假记录是等于当天的才进行扣减
                if (lf.getTravelDate().equals(wa.getBelongDate()) && lf.getEmpShiftInfo() != null) {
                    //扣减签到签退小时数
                    deductionLateAndEarlyForTravel(wa, type, lf);
                }
            }
        }
        return null;
    }

    /**
     * 休假-迟到早退时长进行抵扣
     *
     * @param wa
     * @param type
     * @param lf
     */
    private void deductionLateAndEarly(WaAnalyze wa, int type, EmpLeaveInfo lf) {
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Integer workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();

        int halfWorkTime = workTotalTime / 2; // 半天的时长等于班次上工作时长的二分之一

        int periodType = lf.getPeriod_type();
        switch (type) {
            case 1:
                // 扣减迟到早退小时数
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                    wa.setIsExp(0);
                } else if (periodType == 9) {//时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    // 1 先得求出上半天 是几点到几点 下半天是几点到几点  A 代表上半天  P 代表下半天
                    if (lf.getTime_duration() == 1) {
                        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                            wa.setLateTime(0f);
                        }
                        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
                            wa.setEarlyTime(0f);
                        }
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null && lf.getEnd_time() > 0) {
                            //杰尼亚-CLOUD-8450
                            decLateAndEarlyForType1PeriodType3(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
                                log.info("case 1 ***************************请假单未设置 上半天 下半的标识会导致计算有误 leaveId=" + lf.getLeave_id());
                            }
                            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                                // 分析出来的上半天上班区间
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                                    if (wa.getLateTime() > halfWorkTime) {
                                        wa.setLateTime(wa.getLateTime() - halfWorkTime);
                                    } else {
                                        wa.setLateTime(0f);
                                    }
                                }
                            }
                            if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                                //抵扣迟到时长
                                if (wa.getLateTime() > 0) {
                                    decLateForPeriodType9(wa, lf, shiftDef);
                                }
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getEarlyTime() > halfWorkTime) {
                                    wa.setEarlyTime(wa.getEarlyTime() - halfWorkTime);
                                } else {
                                    wa.setEarlyTime(0f);
                                }
                            }
                        }
                    }
                } else if (periodType == 4) {
                    //哺乳假迟到早退哪边多算哪边
                    if ("BRJ".equals(lf.getLeave_type_def_code())) {
                        Float earlyTime = wa.getEarlyTime();
                        Float lateTime = wa.getLateTime();
                        //迟到多
                        if (lateTime > earlyTime) {
                            if (lf.getTime_duration() > lateTime) {
                                wa.setLateTime(0f);
                                Float diff = lf.getTime_duration() - lateTime;
                                if (diff > earlyTime) {
                                    wa.setEarlyTime(0f);
                                } else {
                                    BigDecimal early = new BigDecimal(earlyTime).subtract(new BigDecimal(diff)).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    wa.setEarlyTime(early.floatValue());
                                }
                            } else {
                                BigDecimal late = new BigDecimal(lateTime).subtract(new BigDecimal(lf.getTime_duration())).setScale(2, BigDecimal.ROUND_HALF_UP);
                                wa.setLateTime(late.floatValue());
                            }
                        } else if (lateTime < earlyTime) {
                            if (lf.getTime_duration() > earlyTime) {
                                wa.setEarlyTime(0f);
                                Float diff = lf.getTime_duration() - earlyTime;
                                if (diff > lateTime) {
                                    wa.setLateTime(0f);
                                } else {
                                    BigDecimal late = new BigDecimal(lateTime).subtract(new BigDecimal(diff)).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    wa.setLateTime(late.floatValue());
                                }
                            } else {
                                BigDecimal early = new BigDecimal(earlyTime).subtract(new BigDecimal(lf.getTime_duration())).setScale(2, BigDecimal.ROUND_HALF_UP);
                                wa.setEarlyTime(early.floatValue());
                            }
                        } else {
                            if (lf.getTime_duration() > earlyTime) {
                                wa.setEarlyTime(0f);
                                Float diff = lf.getTime_duration() - earlyTime;
                                if (diff > lateTime) {
                                    wa.setLateTime(0f);
                                } else {
                                    BigDecimal late = new BigDecimal(lateTime).subtract(new BigDecimal(diff)).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    wa.setLateTime(late.floatValue());
                                }
                            } else {
                                BigDecimal early = new BigDecimal(earlyTime).subtract(new BigDecimal(lf.getTime_duration())).setScale(2, BigDecimal.ROUND_HALF_UP);
                                wa.setEarlyTime(early.floatValue());
                            }
                        }
                    } else {
                        // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                        wa.setLateTime(0f);
                        wa.setEarlyTime(0f);
                        wa.setIsExp(0);
                    }
                } else if (periodType == 3) {
                    //时间单位为小时的非整天
                    decLateAndEarlyForType1PeriodType3(wa, lf, shiftDef);
                }
                break;
            case 2:
                // 扣减迟到小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setLateTime(0f);
                } else if (periodType == 9) { // 时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                            wa.setLateTime(0f);
                        }
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null && lf.getEnd_time() > 0) {//杰尼亚-CLOUD-8450
                            //时间单位为小时的非整天
                            decLateAndEarlyForType2PeriodType3(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
                                log.info("case 2***************************请假单未设置 上半天 下半的标识会导致计算有误 leaveId=" + lf.getLeave_id());
                            }
                            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                                //上半天
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                                    if (wa.getLateTime() > halfWorkTime) {
                                        wa.setLateTime(wa.getLateTime() - halfWorkTime);
                                    } else {
                                        wa.setLateTime(0f);
                                    }
                                }
                            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                                //下半天
                                decLateForPeriodType9(wa, lf, shiftDef);
                            }
                        }
                    }
                } else if (periodType == 4) {
                    //哺乳假
                    if ("BRJ".equals(lf.getLeave_type_def_code())) {
                        if (lf.getTime_duration() > wa.getLateTime()) {
                            wa.setLateTime(0f);
                        } else {
                            BigDecimal late = new BigDecimal(wa.getLateTime()).subtract(new BigDecimal(lf.getTime_duration())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            wa.setLateTime(late.floatValue());
                        }
                    } else {
                        // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                        wa.setLateTime(0f);
                    }
                } else if (periodType == 3) {
                    //时间单位为小时的非整天
                    decLateAndEarlyForType2PeriodType3(wa, lf, shiftDef);
                }
                break;
            case 3:
                //扣减早退小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setEarlyTime(0f);
                } else if (periodType == 9) {
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
                            wa.setEarlyTime(0f);
                        }
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null && lf.getEnd_time() > 0) {//杰尼亚-CLOUD-8450
                            //时间单位为小时的非整天 就是按小时请的假
                            decLateAndEarlyForType3PeriodType3(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalf_day()) || StringUtils.isBlank(lf.getEhalf_day())) {
                                log.info("case 3***************************请假单未设置 上半天 下半的标识会导致计算有误 leaveId=" + lf.getLeave_id());
                            }
                            if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getEarlyTime() > halfWorkTime) {
                                    wa.setEarlyTime(wa.getEarlyTime() - halfWorkTime);
                                } else {
                                    wa.setEarlyTime(0f);
                                }
                            }
                        }
                    }
                } else if (periodType == 4) {
                    //哺乳假
                    if ("BRJ".equals(lf.getLeave_type_def_code())) {
                        if (lf.getTime_duration() > wa.getEarlyTime()) {
                            wa.setEarlyTime(0f);
                        } else {
                            BigDecimal early = new BigDecimal(wa.getEarlyTime()).subtract(new BigDecimal(lf.getTime_duration())).setScale(2, BigDecimal.ROUND_HALF_UP);
                            wa.setEarlyTime(early.floatValue());
                        }
                    } else {
                        // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                        wa.setEarlyTime(0f);
                    }
                } else if (periodType == 3) {
                    //时间单位为小时的非整天 就是按小时请的假
                    decLateAndEarlyForType3PeriodType3(wa, lf, shiftDef);
                }
                break;
            default:
                break;
        }
    }


    /**
     * 出差-迟到早退时长进行抵扣
     *
     * @param wa
     * @param type
     * @param lf
     */
    private void deductionLateAndEarlyForTravel(WaAnalyze wa, int type, WaEmpTravelDaytimeDo lf) {
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Integer workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();

        int halfWorkTime = workTotalTime / 2; // 半天的时长等于班次上工作时长的二分之一

        int periodType = lf.getPeriodType();
        switch (type) {
            case 1:
                // 扣减迟到早退小时数
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                    wa.setIsExp(0);
                } else if (periodType == 9) {//时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    // 1 先得求出上半天 是几点到几点 下半天是几点到几点  A 代表上半天  P 代表下半天
                    if (lf.getTimeDuration() == 1) {
                        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                            wa.setLateTime(0f);
                        }
                        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
                            wa.setEarlyTime(0f);
                        }
                    } else {
                        if (lf.getStartTime() != null && lf.getEndTime() != null && lf.getEndTime() > 0) {
                            //杰尼亚-CLOUD-8450
                            decLateAndEarlyForType1PeriodType3ForTravel(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalfDay()) || StringUtils.isBlank(lf.getEhalfDay())) {
                                log.info("case 1 ***************************出差单未设置 上半天 下半的标识会导致计算有误 TravelId=" + lf.getTravelId());
                            }
                            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalfDay())) {
                                // 分析出来的上半天上班区间
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                                    if (wa.getLateTime() > halfWorkTime) {
                                        wa.setLateTime(wa.getLateTime() - halfWorkTime);
                                    } else {
                                        wa.setLateTime(0f);
                                    }
                                }
                            }
                            if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalfDay())) {
                                //抵扣迟到时长
                                if (wa.getLateTime() > 0) {
                                    decLateForPeriodType9ForTravel(wa, lf, shiftDef);
                                }
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getEarlyTime() > halfWorkTime) {
                                    wa.setEarlyTime(wa.getEarlyTime() - halfWorkTime);
                                } else {
                                    wa.setEarlyTime(0f);
                                }
                            }
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                    wa.setIsExp(0);
                } else if (periodType == 3) {
                    //时间单位为小时的非整天
                    decLateAndEarlyForType1PeriodType3ForTravel(wa, lf, shiftDef);
                }
                break;
            case 2:
                // 扣减迟到小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setLateTime(0f);
                } else if (periodType == 9) { // 时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTimeDuration() == 1) {
                        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                            wa.setLateTime(0f);
                        }
                    } else {
                        if (lf.getStartTime() != null && lf.getEndTime() != null && lf.getEndTime() > 0) {//杰尼亚-CLOUD-8450
                            //时间单位为小时的非整天
                            decLateAndEarlyForType2PeriodType3ForTravel(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalfDay()) || StringUtils.isBlank(lf.getEhalfDay())) {
                                log.info("case 2***************************出差单未设置 上半天 下半的标识会导致计算有误 TravelId=" + lf.getTravelId());
                            }
                            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalfDay())) {
                                //上半天
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getLateTime() != null && wa.getLateTime() > 0) {
                                    if (wa.getLateTime() > halfWorkTime) {
                                        wa.setLateTime(wa.getLateTime() - halfWorkTime);
                                    } else {
                                        wa.setLateTime(0f);
                                    }
                                }
                            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalfDay())) {
                                //下半天
                                decLateForPeriodType9ForTravel(wa, lf, shiftDef);
                            }
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    wa.setLateTime(0f);
                } else if (periodType == 3) {
                    //时间单位为小时的非整天
                    decLateAndEarlyForType2PeriodType3ForTravel(wa, lf, shiftDef);
                }
                break;
            case 3:
                //扣减早退小时数
                // 1。判断请假类型
                if (periodType == 1) {
                    //时间单位为天的整天
                    wa.setEarlyTime(0f);
                } else if (periodType == 9) {
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTimeUnit() == 1) {
                        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
                            wa.setEarlyTime(0f);
                        }
                    } else {
                        if (lf.getStartTime() != null && lf.getEndTime() != null && lf.getEndTime() > 0) {//杰尼亚-CLOUD-8450
                            //时间单位为小时的非整天 就是按小时请的假
                            decLateAndEarlyForType3PeriodType3ForTravel(wa, lf, shiftDef);
                        } else {
                            if (StringUtils.isBlank(lf.getShalfDay()) || StringUtils.isBlank(lf.getEhalfDay())) {
                                log.info("case 3***************************出差单未设置 上半天 下半的标识会导致计算有误 TravelId=" + lf.getTravelId());
                            }
                            if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalfDay())) {
                                halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                                if (wa.getEarlyTime() > halfWorkTime) {
                                    wa.setEarlyTime(wa.getEarlyTime() - halfWorkTime);
                                } else {
                                    wa.setEarlyTime(0f);
                                }
                            }
                        }
                    }
                } else if (periodType == 4) {

                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    wa.setEarlyTime(0f);
                } else if (periodType == 3) {
                    //时间单位为小时的非整天 就是按小时请的假
                    decLateAndEarlyForType3PeriodType3ForTravel(wa, lf, shiftDef);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 时间单位为小时的非整天-抵扣迟到、早退 type 1
     *
     * @param wa
     * @param lf
     * @param shiftDef
     */
    private void decLateAndEarlyForType1PeriodType3(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        //抵扣迟到时长
        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
            Long shiftStartTime = wa.getBelongDate() + (shiftDef.getStartTime() * 60);
            Float deductionTime = getDeductionTimeDuration(wa, lf, shiftDef, shiftStartTime, wa.getRegSigninTime(), wa.getLateTime());
            if (deductionTime > 0) {
                Float lateTime = wa.getLateTime() - deductionTime;
                lateTime = lateTime < 0 ? 0 : lateTime;
                wa.setLateTime(lateTime);
            }
        }
        //抵扣早退时长
        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
            Long shiftEndTime = wa.getBelongDate() + (shiftDef.getEndTime() * 60);
            Float deductionTime = getDeductionTimeDuration(wa, lf, shiftDef, wa.getRegSignoffTime(), shiftEndTime, wa.getEarlyTime());
            if (deductionTime > 0) {
                Float earlyTime = wa.getEarlyTime() - deductionTime;
                earlyTime = earlyTime < 0 ? 0 : earlyTime;
                wa.setEarlyTime(earlyTime);
            }
        }
    }

    /**
     * 时间单位为小时的非整天-抵扣迟到、早退 type 1
     *
     * @param wa
     * @param lf
     * @param shiftDef
     */
    private void decLateAndEarlyForType1PeriodType3ForTravel(WaAnalyze wa, WaEmpTravelDaytimeDo lf, EmpShiftInfo shiftDef) {
        //抵扣迟到时长
        if (wa.getLateTime() != null && wa.getLateTime() > 0) {
            Long shiftStartTime = wa.getBelongDate() + (shiftDef.getStartTime() * 60);
            Float deductionTime = getDeductionTimeDurationForTravel(wa, lf, shiftDef, shiftStartTime, wa.getRegSigninTime(), wa.getLateTime());
            if (deductionTime > 0) {
                Float lateTime = wa.getLateTime() - deductionTime;
                lateTime = lateTime < 0 ? 0 : lateTime;
                wa.setLateTime(lateTime);
            }
        }
        //抵扣早退时长
        if (wa.getEarlyTime() != null && wa.getEarlyTime() > 0) {
            Long shiftEndTime = wa.getBelongDate() + (shiftDef.getEndTime() * 60);
            Float deductionTime = getDeductionTimeDurationForTravel(wa, lf, shiftDef, wa.getRegSignoffTime(), shiftEndTime, wa.getEarlyTime());
            if (deductionTime > 0) {
                Float earlyTime = wa.getEarlyTime() - deductionTime;
                earlyTime = earlyTime < 0 ? 0 : earlyTime;
                wa.setEarlyTime(earlyTime);
            }
        }
    }

    /**
     * 休假时长抵扣迟到早退
     *
     * @param waAnalyze 考勤分析数据
     * @param lf        休假单
     * @param shiftDef  班次
     * @param startTime 需抵扣的开始时间点
     * @param endTime   需抵扣的结束时间点
     * @throws Exception
     */
    private Float getDeductionTimeDuration(WaAnalyze waAnalyze, EmpLeaveInfo lf, EmpShiftInfo shiftDef, Long startTime, Long endTime, Float timeDuration) {
        Long leaveStartTime = lf.getLeave_date() + lf.getStart_time() * 60;
        Long leaveEndTime = lf.getLeave_date() + lf.getEnd_time() * 60;
        if (leaveStartTime <= startTime && leaveEndTime >= endTime) {
            return timeDuration;
        } else if (leaveStartTime >= startTime && leaveEndTime <= endTime) {
            return lf.getTime_duration();
        } else if (leaveStartTime < endTime && leaveEndTime > startTime) {
            Long start = Math.max(leaveStartTime, startTime);
            Long end = Math.min(leaveEndTime, endTime);
            return deductLeaveDayNooRest(shiftDef, waAnalyze.getBelongDate(), start, end);
        }
        return 0f;
    }

    /**
     * 休假时长抵扣迟到早退
     *
     * @param waAnalyze 考勤分析数据
     * @param lf        休假单
     * @param shiftDef  班次
     * @param startTime 需抵扣的开始时间点
     * @param endTime   需抵扣的结束时间点
     * @throws Exception
     */
    private Float getDeductionTimeDurationForTravel(WaAnalyze waAnalyze, WaEmpTravelDaytimeDo lf, EmpShiftInfo shiftDef, Long startTime, Long endTime, Float timeDuration) {
        Long leaveStartTime = lf.getTravelDate() + lf.getStartTime() * 60;
        Long leaveEndTime = lf.getTravelDate() + lf.getEndTime() * 60;
        if (leaveStartTime <= startTime && leaveEndTime >= endTime) {
            return timeDuration;
        } else if (leaveStartTime >= startTime && leaveEndTime <= endTime) {
            return lf.getTimeDuration();
        } else if (leaveStartTime < endTime && leaveEndTime > startTime) {
            Long start = Math.max(leaveStartTime, startTime);
            Long end = Math.min(leaveEndTime, endTime);
            return deductLeaveDayNooRest(shiftDef, waAnalyze.getBelongDate(), start, end);
        }
        return 0f;
    }

    /**
     * 休假时长扣减午休时间
     *
     * @param shiftDef   班次
     * @param belongDate 所属日期
     * @param startTime  请假开始时间
     * @param endTime    请假结束时时间
     * @return
     * @throws Exception
     */
    private Float deductLeaveDayNooRest(EmpShiftInfo shiftDef, Long belongDate, Long startTime, Long endTime) {
        //计算扣除中午休息的时间，多段
        Long timeDuration = endTime - startTime;
        if (BooleanUtils.isTrue(shiftDef.getIsNoonRest())) {
            List<Map> restList = new ArrayList<>();
            //定义半天的休息时间段
            Integer restStart = shiftDef.getNoonRestStart();
            Integer restEnd = shiftDef.getNoonRestEnd();
            if (restStart != null && restEnd != null) {
                Map noonRestMap = new LinkedHashMap();
                noonRestMap.put("noonRestStart", restStart);
                noonRestMap.put("noonRestEnd", restEnd);
                restList.add(noonRestMap);
            }
            if (shiftDef.getRestPeriods() != null) {
                List<Map> restPeriods = null;
                try {
                    restPeriods = (List) JacksonJsonUtil.jsonToBean(shiftDef.getRestPeriods().toString(), List.class);
                } catch (Exception e) {
                    log.error("AnalyzeResultCalculateService.deductLeaveDayNooRest异常,{}", e.getMessage(), e);
                }
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    restList.addAll(restPeriods);
                }
            }
            restList = restList.stream().filter(periodMap -> periodMap.get("noonRestStart") != null && periodMap.get("noonRestEnd") != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(restList)) {
                for (Map periodMap : restList) {
                    Integer noonRestStart = Integer.valueOf(periodMap.get("noonRestStart").toString());
                    Integer noonRestEnd = Integer.valueOf(periodMap.get("noonRestEnd").toString());

                    ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
                    noonRestStart = restPeriod.getNoonRestStart();
                    noonRestEnd = restPeriod.getNoonRestEnd();

                    Long noonRestStartTime = belongDate + (noonRestStart * 60);
                    Long noonRestEndTime = belongDate + (noonRestEnd * 60);
                    if (startTime < noonRestEndTime && endTime > noonRestStartTime) {
                        timeDuration -= Math.min(noonRestEndTime, endTime) - Math.max(noonRestStartTime, startTime);
                    }
                }
            }
        }
        return timeDuration.floatValue() / 60f;
    }

    /**
     * 半天假抵扣迟到时长
     *
     * @param wa
     * @param lf
     * @param shiftDef
     */
    private void decLateForPeriodType9(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        if (shiftDef == null) {
            return;
        }
        //1、计算半天假时间请假时间
        Long s = null;
        Long e = null;
        String halfDay = "";
        int halfWorkTime = shiftDef.getWorkTotalTime() / 2;
        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() >= 0) {
            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                //上半天
                s = lf.getLeave_date() + (shiftDef.getStartTime() * 60);
                e = lf.getLeave_date() + (shiftDef.getHalfdayTime() * 60);
                if (e < s) {
                    e += 86400;
                }
                halfDay = BaseConst.LEAVE_HALF_SHALF_DAY;
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                //halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                halfWorkTime = (e.intValue() - s.intValue()) / 60;
                long halfDayTime = e;
                if (halfDayTime < s) {
                    halfDayTime += 86400;
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest())) {
                    long noonRestStart = lf.getLeave_date() + shiftDef.getNoonRestStart() * 60;
                    long noonRestEnd = lf.getLeave_date() + shiftDef.getNoonRestEnd() * 60;
                    if (noonRestStart < s) {
                        noonRestStart += 86400;
                        noonRestEnd += 86400;
                    } else if (noonRestEnd < noonRestStart) {
                        noonRestEnd += 86400;
                    }
                    if (halfDayTime >= noonRestStart) {
                        if (halfDayTime > noonRestEnd) {
                            halfWorkTime -= ((noonRestEnd - noonRestStart) / 60);
                        } else {
                            halfWorkTime -= ((halfDayTime - noonRestStart) / 60);
                        }
                    }
                }
            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                boolean kyShift = CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
                //下半天
                if (kyShift) {
                    //班次跨夜
                    s = lf.getLeave_date() + (shiftDef.getHalfdayTime() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60) + 86400;
                } else {
                    s = lf.getLeave_date() + (shiftDef.getHalfdayTime() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60);
                }
                halfDay = BaseConst.LEAVE_HALF_EHALF_DAY;
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (kyShift) {
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    if (shiftDef.getHalfdayTime() < shiftDef.getStartTime()) {
                        halfWorkTime = nextDaytime - (shiftDef.getHalfdayTime() + (24 * 60));
                    } else {
                        halfWorkTime = nextDaytime - shiftDef.getHalfdayTime();
                    }
                } else {
                    halfWorkTime = shiftDef.getEndTime() - shiftDef.getHalfdayTime();
                }
                long halfDayTime = s;
                if (shiftDef.getHalfdayTime() < shiftDef.getStartTime()) {
                    halfDayTime += 86400;
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest())) {
                    long noonRestStart = lf.getLeave_date() + shiftDef.getNoonRestStart() * 60;
                    long noonRestEnd = lf.getLeave_date() + shiftDef.getNoonRestEnd() * 60;
                    if (shiftDef.getNoonRestStart() < shiftDef.getStartTime()) {
                        noonRestStart += 86400;
                        noonRestEnd += 86400;
                    } else if (noonRestEnd < noonRestStart) {
                        noonRestEnd += 86400;
                    }
                    if (halfDayTime < noonRestEnd) {
                        if (halfDayTime < noonRestStart) {
                            halfWorkTime -= ((noonRestEnd - noonRestStart) / 60);
                        } else {
                            halfWorkTime -= ((noonRestEnd - halfDayTime) / 60);
                        }
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { //按中午休息时间来拆分
            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                s = lf.getLeave_date() + (shiftDef.getStartTime() * 60);
                e = lf.getLeave_date() + (shiftDef.getNoonRestStart() * 60);
                if (shiftDef.getNoonRestStart() < shiftDef.getStartTime()) {
                    e += 86400;
                }
                halfDay = BaseConst.LEAVE_HALF_SHALF_DAY;
                halfWorkTime = (e.intValue() - s.intValue()) / 60;
            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    s = lf.getLeave_date() + (shiftDef.getNoonRestEnd() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60) + 86400;
                } else {
                    s = lf.getLeave_date() + (shiftDef.getNoonRestEnd() * 60);
                    e = lf.getLeave_date() + (shiftDef.getEndTime() * 60);
                }
                if (shiftDef.getNoonRestEnd() < shiftDef.getStartTime()) {
                    s += 86400;
                }
                halfDay = BaseConst.LEAVE_HALF_EHALF_DAY;
                halfWorkTime = (e.intValue() - s.intValue()) / 60;
            }
        }
        if (s != null) {
            //判断迟到时间是否在 请假区间内
            if (wa.getRegSigninTime() > s && wa.getRegSigninTime() < e) {
                // 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
                long regtime = wa.getRegSigninTime();
                long noonRestTime = 0;
                if (shiftDef.getIsNoonRest()) {
                    long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                    long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                    if (CdWaShiftUtil.checkCrossNight(rs, re, shiftDef.getDateType())) {
                        re += 86400;
                    }
                    if (regtime >= rs && regtime <= re) {
                        // 如果签到时间在休息时间内，则指定regtime 为休息开始时间
                        regtime = rs;
                    } else if (regtime > re) {
                        noonRestTime = re - rs;
                    }
                }
                Float v = new BigDecimal(regtime - s - noonRestTime).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).floatValue();
                Float late = wa.getLateTime() - v;
                if (late < 0) {
                    late = 0f;
                }
                wa.setLateTime(late);
            } else if (wa.getRegSigninTime() >= e) {
                //清水定制需求，客户自定义半天上班时间
                Integer time = getQsLeaveTime(halfDay, shiftDef);
                if (time != null) {
                    halfWorkTime = time;
                }
                float remainHours = wa.getLateTime() - halfWorkTime;
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setLateTime(remainHours);
            }
        }
    }

    /**
     * 半天假抵扣迟到时长
     *
     * @param wa
     * @param lf
     * @param shiftDef
     */
    private void decLateForPeriodType9ForTravel(WaAnalyze wa, WaEmpTravelDaytimeDo lf, EmpShiftInfo shiftDef) {
        if (shiftDef == null) {
            return;
        }
        //1、计算半天假时间请假时间
        Long s = null;
        Long e = null;
        String halfDay = "";
        int halfWorkTime = shiftDef.getWorkTotalTime() / 2;
        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalfDay())) {
                //上半天
                s = lf.getTravelDate() + (shiftDef.getStartTime() * 60);
                e = lf.getTravelDate() + (shiftDef.getHalfdayTime() * 60);
                halfDay = BaseConst.LEAVE_HALF_SHALF_DAY;
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() >= shiftDef.getNoonRestStart()) {
                    if (shiftDef.getHalfdayTime() > shiftDef.getNoonRestEnd()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getHalfdayTime() - shiftDef.getNoonRestStart();
                    }
                }
            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalfDay())) {
                //下半天
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {
                    //班次跨夜
                    s = lf.getTravelDate() + (shiftDef.getHalfdayTime() * 60);
                    e = lf.getTravelDate() + (shiftDef.getEndTime() * 60) + 86400;
                } else {
                    s = lf.getTravelDate() + (shiftDef.getHalfdayTime() * 60);
                    e = lf.getTravelDate() + (shiftDef.getEndTime() * 60);
                }
                halfDay = BaseConst.LEAVE_HALF_EHALF_DAY;
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    halfWorkTime = nextDaytime - shiftDef.getHalfdayTime();
                } else {
                    halfWorkTime = shiftDef.getEndTime() - shiftDef.getHalfdayTime();
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest()) && shiftDef.getHalfdayTime() < shiftDef.getNoonRestEnd()) {
                    if (shiftDef.getHalfdayTime() < shiftDef.getNoonRestStart()) {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getNoonRestStart();
                    } else {
                        halfWorkTime -= shiftDef.getNoonRestEnd() - shiftDef.getHalfdayTime();
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { //按中午休息时间来拆分
            if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalfDay())) {
                s = lf.getTravelDate() + (shiftDef.getStartTime() * 60);
                e = lf.getTravelDate() + (shiftDef.getNoonRestStart() * 60);
                halfDay = BaseConst.LEAVE_HALF_SHALF_DAY;
                halfWorkTime = shiftDef.getNoonRestStart() - shiftDef.getStartTime();
            } else if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalfDay())) {
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    s = lf.getTravelDate() + (shiftDef.getNoonRestEnd() * 60);
                    e = lf.getTravelDate() + (shiftDef.getEndTime() * 60) + 86400;
                } else {
                    s = lf.getTravelDate() + (shiftDef.getNoonRestEnd() * 60);
                    e = lf.getTravelDate() + (shiftDef.getEndTime() * 60);
                }
                halfDay = BaseConst.LEAVE_HALF_EHALF_DAY;
                halfWorkTime = shiftDef.getEndTime() - shiftDef.getNoonRestEnd();
            }
        }
        if (s != null) {
            //判断迟到时间是否在 请假区间内
            if (wa.getRegSigninTime() > s && wa.getRegSigninTime() < e) {
                // 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
                long regtime = wa.getRegSigninTime();
                long noonRestTime = 0;
                if (shiftDef.getIsNoonRest()) {
                    long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                    long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                    if (regtime >= rs && regtime <= re) {
                        // 如果签到时间在休息时间内，则指定regtime 为休息开始时间
                        regtime = rs;
                    } else if (regtime > re) {
                        noonRestTime = re - rs;
                    }
                }
                Float late = wa.getLateTime() - (regtime - s - noonRestTime) / 60f;
                if (late < 0) {
                    late = 0f;
                }
                wa.setLateTime(late);
            } else if (wa.getRegSigninTime() >= e) {
                //清水定制需求，客户自定义半天上班时间
                Integer time = getQsLeaveTime(halfDay, shiftDef);
                if (time != null) {
                    halfWorkTime = time;
                }
                float remainHours = wa.getLateTime() - halfWorkTime;
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setLateTime(remainHours);
            }
        }
    }

    private void decLateAndEarlyForType2PeriodType3(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        //时间单位为小时的非整天
        Long s = lf.getLeave_date() + lf.getStart_time() * 60;
        Long e = lf.getLeave_date() + lf.getEnd_time() * 60;
        //迟到时 判断迟到时间是否在 请假区间内
        if (wa.getRegSigninTime() >= s && wa.getRegSigninTime() <= e) {
            // 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
            long regtime = wa.getRegSigninTime();
            if (shiftDef != null && shiftDef.getIsNoonRest()) {
                long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                // 如果签到时间在休息时间内，则指定regtime 为休息开始时间
                if (regtime >= rs && regtime <= re) {
                    regtime = rs;
                }
            }
            float v = new BigDecimal(regtime - s).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
            Float late = wa.getLateTime() - v;
            if (late < 0) {
                late = 0f;
            }
            wa.setLateTime(late);
        } else {
            //当签到签退时间段和请假时间段没有交集时，用请假的小时数减去迟到的小时数
            if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {
                //工时调整逻辑，CLOUD-8503
                float remainHours = wa.getLateTime() - lf.getBefore_adjust_time_duration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setLateTime(remainHours);
            } else {
                float remainHours = wa.getLateTime() - lf.getTime_duration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setLateTime(remainHours);
            }
        }
    }

    private void decLateAndEarlyForType2PeriodType3ForTravel(WaAnalyze wa, WaEmpTravelDaytimeDo lf, EmpShiftInfo shiftDef) {
        //时间单位为小时的非整天
        Long s = lf.getTravelDate() + lf.getStartTime() * 60;
        Long e = lf.getTravelDate() + lf.getStartTime() * 60;
        //迟到时 判断迟到时间是否在 请假区间内
        if (wa.getRegSigninTime() >= s && wa.getRegSigninTime() <= e) {
            // 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
            long regtime = wa.getRegSigninTime();
            if (shiftDef != null && shiftDef.getIsNoonRest()) {
                long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                // 如果签到时间在休息时间内，则指定regtime 为休息开始时间
                if (regtime >= rs && regtime <= re) {
                    regtime = rs;
                }
            }

            Float late = wa.getLateTime() - (regtime - s) / 60f;
            if (late < 0) {
                late = 0f;
            }
            wa.setLateTime(late);
        } else {
            //当签到签退时间段和请假时间段没有交集时，用请假的小时数减去迟到的小时数
            if (lf.getBeforeAdjustTimeDuration() != null && lf.getBeforeAdjustTimeDuration() > 0) {
                //工时调整逻辑，CLOUD-8503
                float remainHours = wa.getLateTime() - lf.getBeforeAdjustTimeDuration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setLateTime(remainHours);
            } else {
                float remainHours = wa.getLateTime() - lf.getTimeDuration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setLateTime(remainHours);
            }
        }
    }

    private void decLateAndEarlyForType3PeriodType3(WaAnalyze wa, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        Long s = lf.getLeave_date() + lf.getStart_time() * 60;
        Long e = lf.getLeave_date() + lf.getEnd_time() * 60;
        //迟到时 判断迟到时间是否在 请假区间内
        long regtime = wa.getRegSignoffTime();
        if (regtime >= s && regtime <= e) {
            //  时间时间 等于 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
            if (shiftDef != null && shiftDef.getIsNoonRest()) {
                long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                // 如果签到时间在休息时间内，则指定regtime 为休息截止时间
                if (regtime >= rs && regtime <= re) {
                    regtime = re;
                }
            }
            // eg: 1493352000 - 1493348557 12点整－11点2分37秒  精确到分钟
            Float early = wa.getEarlyTime() - (float) Math.ceil((e - regtime) / 60.0);
            if (early < 0) {
                early = 0f;
            }
            wa.setEarlyTime(early);
        } else {
            //当签到签退时间段和请假时间段没有交集时，用请假的小时数减去早退的小时数

            if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {
                //工时调整逻辑，CLOUD-8503
                float remainHours = wa.getEarlyTime() - lf.getBefore_adjust_time_duration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setEarlyTime(remainHours);
            } else {
                float remainHours = wa.getEarlyTime() - lf.getTime_duration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setEarlyTime(remainHours);
            }
        }
    }

    private void decLateAndEarlyForType3PeriodType3ForTravel(WaAnalyze wa, WaEmpTravelDaytimeDo lf, EmpShiftInfo shiftDef) {
        Long s = lf.getTravelDate() + lf.getStartTime() * 60;
        Long e = lf.getTravelDate() + lf.getEndTime() * 60;
        //迟到时 判断迟到时间是否在 请假区间内
        long regtime = wa.getRegSignoffTime();
        if (regtime >= s && regtime <= e) {
            //  时间时间 等于 迟到时间 － （签到时间－请假开始时间） ＝ 最终迟到小时数
            if (shiftDef != null && shiftDef.getIsNoonRest()) {
                long rs = wa.getBelongDate() + (shiftDef.getNoonRestStart() * 60);
                long re = wa.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
                // 如果签到时间在休息时间内，则指定regtime 为休息截止时间
                if (regtime >= rs && regtime <= re) {
                    regtime = re;
                }
            }
            // eg: 1493352000 - 1493348557 12点整－11点2分37秒  精确到分钟
            Float early = wa.getEarlyTime() - (float) Math.ceil((e - regtime) / 60.0);
            if (early < 0) {
                early = 0f;
            }
            wa.setEarlyTime(early);
        } else {
            //当签到签退时间段和请假时间段没有交集时，用请假的小时数减去早退的小时数

            if (lf.getBeforeAdjustTimeDuration() != null && lf.getBeforeAdjustTimeDuration() > 0) {
                //工时调整逻辑，CLOUD-8503
                float remainHours = wa.getEarlyTime() - lf.getBeforeAdjustTimeDuration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setEarlyTime(remainHours);
            } else {
                float remainHours = wa.getEarlyTime() - lf.getTimeDuration().intValue();
                if (remainHours < 0) {
                    remainHours = 0;
                }
                wa.setEarlyTime(remainHours);
            }
        }
    }

    /**
     * 休假-对迟到早退根据弹性规则计算--废弃了
     *
     * @param analyze
     * @param type
     * @param lf
     */
    @Deprecated
    private void deductionLateAndEarly2(WaAnalyze analyze, int type, EmpLeaveInfo lf) {
        EmpShiftInfo empShiftDef = lf.getEmpShiftInfo();
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        int periodType = lf.getPeriod_type();
        switch (type) {
            case 1:
                // 扣减迟到早退小时数
                if (periodType == 1) {
                    //时间单位为天的整天
                    analyze.setLateTime(0f);
                    analyze.setEarlyTime(0f);
                    analyze.setIsExp(0);
                } else if (periodType == 9) {//时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    // 1 先得求出上半天 是几点到几点 下半天是几点到几点  A 代表上半天  P 代表下半天
                    if (lf.getTime_duration() == 1) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            analyze.setLateTime(0f);
                        }
                        if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            analyze.setEarlyTime(0f);
                        }
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//CLOUD-8450
                            jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                        } else {
                            jsLateOrEarlyTime9(analyze, lf, empShiftDef);
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    analyze.setLateTime(0f);
                    analyze.setEarlyTime(0f);
                    analyze.setIsExp(0);
                } else if (periodType == 3) {
                    // 时间单位为小时的非整天
                    jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                }
                break;
            case 2:
                // 扣减迟到小时数
                if (periodType == 1) {
                    //时间单位为天的整天
                    analyze.setLateTime(0f);
                } else if (periodType == 9) { // 时间单位为天的非整天
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (analyze.getLateTime() != null && analyze.getLateTime() > 0) {
                            analyze.setLateTime(0f);
                        }
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//CLOUD-8450
                            jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                        } else {
                            jsLateOrEarlyTime9(analyze, lf, empShiftDef);
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    analyze.setLateTime(0f);
                } else if (periodType == 3) {
//                    休小时假的情况：
//                    1) 如果是休2小时假（休*班次的上班开始时间*后2小时和*班次下班时间*前2小时，），上午2小时：签到记录晚于“班次上班时间+2小时”，则为迟到，签退早于弹性下班开始时间则为早退；下午2小时：签到晚于弹性上班结束时间的，为迟到，签退早于“班次下班时间-2小时”，为早退；
//                    2) 如果是4小时的假期，按照（3）休半天假的逻辑去分析
//                    休小时假只有2小时和4小时这两种
                    // 判断是上午的假还是下午的假 如果请假时间在中午休息时间前的算上午的，否则算下午的
                    jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                }
                break;
            case 3:
                //扣减早退小时数
                if (periodType == 1) {
                    //时间单位为天的整天
                    analyze.setEarlyTime(0f);
                } else if (periodType == 9) {
                    // 需要判断是上半天下半天 时间段
                    if (lf.getTime_duration() == 1) {
                        if (analyze.getEarlyTime() != null && analyze.getEarlyTime() > 0) {
                            analyze.setEarlyTime(0f);
                        }
                    } else {
                        if (lf.getStart_time() != null && lf.getEnd_time() != null) {//CLOUD-8450
                            jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                        } else {
                            jsLateOrEarlyTime9(analyze, lf, empShiftDef);
                        }
                    }
                } else if (periodType == 4) {
                    // 为时间单位为小时的整天 与 1 逻辑类型 一个是按整天存储 一个是按小时存存储
                    analyze.setEarlyTime(0f);
                } else if (periodType == 3) {
                    // 时间单位为小时的非整天 就是按小时请的假
                    jsLateOrEarlyTime3(type, analyze, lf, shiftDef);
                }
                break;
            default:
                break;
        }
    }

    private void jsLateOrEarlyTime9(WaAnalyze analyze, EmpLeaveInfo lf, EmpShiftInfo empShiftDef) {
        this.jsLateOrEarlyTime9(analyze, empShiftDef, lf.getShalf_day(), lf.getEhalf_day());
    }

    private void jsLateOrEarlyTime9(WaAnalyze analyze, EmpShiftInfo empShiftDef, String shalfDay, String ehalfDay) {
        if (StringUtils.isBlank(shalfDay) || StringUtils.isBlank(ehalfDay)) {
            log.info("case 1 ***************************请假单未设置 上半天 下半天的标识会导致计算有误");
        }
        //休上半天的假，下午出勤无弹性（下班时间以班次定义的下班时间为标准，去分析是否早退）:中午签到有异常，此时判断是否有审批通过的上半天的假，
        // 下午的上班开始时间即为班次上定义的半天时间，签到时间晚于定义的半天时间的即为迟到，签退时间早于班次设置的下班时间则为早退。
        if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(ehalfDay)) {
            Long halftime = 0L;
            if (empShiftDef.getIsHalfdayTime() && empShiftDef.getHalfdayTime() != null && empShiftDef.getHalfdayTime() > 0) {
                halftime = analyze.getBelongDate() + (empShiftDef.getHalfdayTime() * 60);
            } else {
                halftime = analyze.getBelongDate() + (empShiftDef.getNoonRestEnd() * 60);
            }
            Long siginTime = analyze.getRegSigninTime();
            Long signoffTime = analyze.getRegSignoffTime();

            Long shiftEndTime = analyze.getBelongDate() + (empShiftDef.getEndTime() * 60);

            if (siginTime > halftime) {
                Float latetime = siginTime.floatValue() - halftime.floatValue();
                if (latetime < 0) {
                    latetime = 0f;
                } else {
                    latetime /= 60f;
                }
                analyze.setLateTime(latetime.floatValue());
            }
            if (signoffTime < shiftEndTime) {
                Float earlytime = shiftEndTime.floatValue() - signoffTime.floatValue();
                if (earlytime < 0) {
                    earlytime = 0f;
                } else {
                    earlytime /= 60f;
                }
                analyze.setEarlyTime(earlytime);
            }
        }
        // 1) 休下半天假，上午出勤有弹性：重新计算实际工作时长  20190525 add CLOUD-3848
        if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(shalfDay) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(ehalfDay)) {
            // 统计实际签到时间 至 实际签退时间 的时长为实际出勤时长，实际出勤时长>=“工作时长/2”小时，就可以签退
            if (analyze.getRegisterTime() >= empShiftDef.getWorkTotalTime() / 2) {
                analyze.setEarlyTime(0f);
                if (analyze.getLateTime() <= 0) {
                    analyze.setIsExp(0);
                }
            } else {//  如果 实际出勤时长<“工作时长/2”小时
                Float early = 0f;
//              如果 实际出勤时长<“工作时长/2”小时，则根据当天实际出勤时长与“正常签到+4小时”比较，得出早退分钟数；
                Long flexbleStartTime = analyze.getBelongDate() + (empShiftDef.getFlexibleOnDutyStartTime() * 60);
                Long flexbleEndTime = analyze.getBelongDate() + (empShiftDef.getFlexibleOnDutyEndTime() * 60);
                Long signinTime = analyze.getRegSigninTime();

//              如果签到时间超出弹性上班时间范围（7:30-9:30 ，如果7:00签到，实际是从弹性上班开始时间7:30去计算上午的出勤时长；如果签到时间大于弹性上班截止时间，
//              比如9:40签到，以9:30 + 4小时得出应签退时间，与实际签退时间比较，是否有早退情况，再计算9:30-9:40之间10分钟的迟到））CLOUD-5578

                if (signinTime < flexbleStartTime) {
                    signinTime = flexbleStartTime;
                    Long yqttime = signinTime + (empShiftDef.getWorkTotalTime() / 2) * 60;
                    early = yqttime.floatValue() - analyze.getRegSignoffTime().floatValue();
                } else if (signinTime > flexbleEndTime) {

                    Long yqttime = flexbleEndTime + (empShiftDef.getWorkTotalTime() / 2) * 60;
                    early = yqttime.floatValue() - analyze.getRegSignoffTime().floatValue();

                    Float latetime = signinTime.floatValue() - flexbleEndTime.floatValue();
                    if (latetime < 0) {
                        latetime = 0f;
                    } else {
                        latetime = latetime / 60f;
                    }
                    analyze.setLateTime(latetime);
                }
                if (early < 0) {
                    early = 0f;
                } else {
                    early = early / 60f;
                }
                analyze.setEarlyTime(early);
            }

            updateRegExpStatus(analyze);
        }
    }

    /**
     * 按小时申请的假期去抵扣迟到早退时长
     *
     * @param type
     * @param analyze
     * @param lf
     * @param shiftDef
     */
    private void jsLateOrEarlyTime3(int type, WaAnalyze analyze, EmpLeaveInfo lf, EmpShiftInfo shiftDef) {
        this.jsLateOrEarlyTime3(type, analyze, lf.getLeave_date(), lf.getStart_time(), lf.getEnd_time(), lf.getTime_duration(), lf.getBefore_adjust_time_duration(), shiftDef);
    }

    /**
     * 按小时申请的假期或者出差去抵扣迟到早退时长
     *
     * @param type                     1 有迟到早退  2有迟到  3有早退
     * @param analyze
     * @param leaveDate
     * @param startTime
     * @param endTime
     * @param timeDuration
     * @param beforeAdjustTimeDuration
     * @param shiftDef
     */
    private void jsLateOrEarlyTime3(int type, WaAnalyze analyze, Long leaveDate, Integer startTime, Integer endTime, Float timeDuration, Float beforeAdjustTimeDuration,
                                    EmpShiftInfo shiftDef) {
        //计算半天时间点
        long halftime = 0L;
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
            //设置了半天定义时间
            halftime = leaveDate + shiftDef.getHalfdayTime() * 60;
        } else if (BooleanUtils.isTrue(shiftDef.getIsNoonRest())) {
            halftime = leaveDate + (shiftDef.getNoonRestEnd() * 60);
        }

        // 默认上午的假
        boolean isAM = true;
        long e = leaveDate + endTime * 60;
        if (e > halftime) {
            isAM = false;
        }

        //休假时长
        Float realtimeDuration = timeDuration;
        //CLOUD-8503 调整之前的时长
        if (beforeAdjustTimeDuration != null && beforeAdjustTimeDuration > 0) {
            realtimeDuration = beforeAdjustTimeDuration;
        }
        realtimeDuration = realtimeDuration == null ? 0f : realtimeDuration;

        if (shiftDef.getWorkTotalTime() != null && realtimeDuration >= shiftDef.getWorkTotalTime()) {
            analyze.setEarlyTime(0f);
            analyze.setLateTime(0f);
        } else if (realtimeDuration >= (shiftDef.getWorkTotalTime() / 2)) {
            // 请假时长大于4小时按半天的逻辑分析
            if (isAM && (type == 1 || type == 2)) {
                analyze.setLateTime(0f);
            }
            if (!isAM && (type == 1 || type == 3)) {
                analyze.setEarlyTime(0f);
            }

            //计算迟到时长
            //CLOUD-5587
            if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() > 0) {
                halftime = analyze.getBelongDate() + (shiftDef.getHalfdayTime() * 60);
            } else {
                halftime = analyze.getBelongDate() + (shiftDef.getNoonRestEnd() * 60);
            }
            Long siginTime = analyze.getRegSigninTime();
            if (siginTime > halftime) {
                Long latetime = siginTime - halftime;
                if (latetime < 0) {
                    latetime = 0L;
                } else {
                    latetime /= 60;
                }
                analyze.setLateTime(latetime.floatValue());
            }

            //计算早退时长
            //请上午假无弹性，才需要重新计算早退小时数 CLOUD-5867
            Long signoffTime = analyze.getRegSignoffTime();
            Long shiftEndTime = analyze.getBelongDate() + (shiftDef.getEndTime() * 60);
            if (isAM && signoffTime < shiftEndTime) {
                Long earlytime = shiftEndTime - signoffTime;
                if (earlytime < 0) {
                    earlytime = 0L;
                } else {
                    earlytime /= 60;
                }
                analyze.setEarlyTime(earlytime.floatValue());
            }
        } else if (realtimeDuration < (shiftDef.getWorkTotalTime() / 2)) {
            // 请假时长小于4小时按小时假逻辑分析
            if (isAM && (type == 1 || type == 2)) {
                //1 如果有，签到记录晚于“班次上班时间+2小时”，则为迟到
                Long shiftStartTime = analyze.getBelongDate() + (shiftDef.getStartTime() * 60) + (realtimeDuration.longValue() * 60);
                if (analyze.getRegSigninTime() > shiftStartTime) {
                    Float late = (analyze.getRegSigninTime().intValue() - shiftStartTime.intValue()) / 60f;
                    if (late < 0) {
                        late = 0f;
                    }
                    analyze.setLateTime(late);
                } else {
                    analyze.setIsExp(0);
                    analyze.setLateTime(0f);
                }
            }
            if (!isAM && (type == 1 || type == 3)) {
                // 下午2小时：签退早于“班次下班时间-2小时”，为早退；
                Long shiftStartTime = analyze.getBelongDate() + (shiftDef.getEndTime() * 60) - (realtimeDuration.longValue() * 60);
                if (analyze.getRegSigninTime() < shiftStartTime) {
                    Float early = (shiftStartTime.intValue() - analyze.getRegSignoffTime().intValue()) / 60f;
                    if (early > shiftDef.getWorkTotalTime()) {
                        early = shiftDef.getWorkTotalTime().floatValue();
                    }
                    if (early < 0) {
                        early = 0f;
                    }
                    analyze.setEarlyTime(early);
                } else {
                    analyze.setIsExp(0);
                    analyze.setEarlyTime(0f);
                }
            }
        }
        updateRegExpStatus(analyze);
    }

    private void updateRegExpStatus(WaAnalyze wa) {
        if (wa != null) {
            if ((wa.getLateTime() != null && wa.getLateTime() > 0) || (wa.getEarlyTime() != null && wa.getEarlyTime() > 0)) {
                wa.setIsExp(1);
            } else {
                wa.setIsExp(0);
            }
        }
    }

    /**
     * 旷工逻辑分析
     *
     * @param wa
     * @param analyzeInfo
     * @param currentShiftDef
     */
    private void absentAnalyzeRule(WaAnalyze wa, WaAnalyzInfo analyzeInfo, EmpShiftInfo currentShiftDef) {
        if (null == analyzeInfo) {
            return;
        }
        // 新的规则
        float late = wa.getLateTime() == null ? 0 : wa.getLateTime();
        float early = wa.getEarlyTime() == null ? 0 : wa.getEarlyTime();
        Integer abnormalType = analyzeInfo.getAbnormalType();
        //float errorTime = late > early ? late : early; // 如果有迟到早退，则以大的为准
        float errorTime = 0;
        if (AbnormalTypeEnum.LATE_AND_EARLY.getIndex().equals(abnormalType)) {
            errorTime = late + early;
        } else {
            errorTime = Math.max(late, early);
        }
        if (errorTime <= 0) {
            return;
        }
        Integer statisticType = analyzeInfo.getStatisticType();
        if (statisticType != null) {
            //按比例，迟到早退转旷工
            if (statisticType == 2) {
                //迟到早退时间转旷工比例
                Integer convertTime = analyzeInfo.getConvertTime();
                //旷工转换时长
                Integer convertScale = analyzeInfo.getConvertScale();
                //迟到早退转旷工阈值，超出此范围则转旷工，否则，按照迟到早退比例转换迟到早退时长
                Integer convertTimeLimit = analyzeInfo.getConvertTimeLimit();
                //旷工转换时长(迟到早退值)
                Integer convertKgScale = analyzeInfo.getConvertKgScale();
                //转换后的迟到早退时长
                Integer expConvertScale = analyzeInfo.getExpConvertScale();
                if (convertTime == null || convertTime == 0 || convertScale == null ||
                        convertTimeLimit == null || expConvertScale == null || convertKgScale == null ||
                        convertKgScale == 0) {
                    return;
                }
                //迟到、早退转换比例
                BigDecimal convertRate = BigDecimal.valueOf(expConvertScale).divide(BigDecimal.valueOf(convertTime), 2, RoundingMode.HALF_DOWN);
                //转旷工比例
                BigDecimal convertKgRate = BigDecimal.valueOf(convertScale).divide(BigDecimal.valueOf(convertKgScale), 2, RoundingMode.HALF_DOWN);
                Double duration = 0d;
                //转换后迟到
                float convertLate = BigDecimal.valueOf(late).multiply(convertRate).floatValue();
                //转换后早退
                float convertEarly = BigDecimal.valueOf(early).multiply(convertRate).floatValue();
                if (AbnormalTypeEnum.LATE_AND_EARLY.getIndex().equals(abnormalType)) {
                    if (errorTime >= convertTimeLimit) {
                        duration += BigDecimal.valueOf(errorTime).multiply(convertKgRate).doubleValue();
                    }
                } else {
                    if (late > 0 && late >= convertTimeLimit) {
                        duration += BigDecimal.valueOf(late).multiply(convertKgRate).doubleValue();
                    }
                    if (early > 0 && early >= convertTimeLimit) {
                        duration += BigDecimal.valueOf(early).multiply(convertKgRate).doubleValue();
                    }
                }
                Integer kgWorkTime = 0;
                if (duration > 0) {
                    kgWorkTime = duration.intValue();
                }
                if (kgWorkTime > currentShiftDef.getWorkTotalTime()) {
                    kgWorkTime = currentShiftDef.getWorkTotalTime();
                }
                wa.setKgWorkTime(kgWorkTime);
                if (kgWorkTime > 0) {
                    wa.setIsKg(1);
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                } else {
                    wa.setLateTime(convertLate);
                    wa.setEarlyTime(convertEarly);
                }
            } else {
                if (CollectionUtils.isNotEmpty(analyzeInfo.getAbsentConditionRules())) {
                    List<AbsenceAnalyzeRule> absenceAnalyzeRules = analyzeInfo.getAbsentConditionRules();
                    if (AbnormalTypeEnum.LATE_AND_EARLY.getIndex().equals(abnormalType)) {
                        for (AbsenceAnalyzeRule absenceRule : absenceAnalyzeRules) {
                            Double start = absenceRule.getStart() * 60; // 目前只有按小时
                            Double end = absenceRule.getEnd() * 60;
                            if (errorTime >= start && errorTime <= end) {
                                wa.setIsKg(1);
                                switch (absenceRule.getDurationUnit()) {//1 小时/ 2 天
                                    case 1:
                                        Double duration = absenceRule.getDuration() * 60.0;
                                        wa.setKgWorkTime(duration.intValue());
                                        // CAIDAOM-134 考勤分析优化-旷工-只要旷工分析出数值，迟到、早退为0
                                        wa.setLateTime(0f);
                                        wa.setEarlyTime(0f);
                                        break;
                                    case 2:
                                        Double duration2 = absenceRule.getDuration() * currentShiftDef.getWorkTotalTime();
                                        wa.setKgWorkTime(duration2.intValue());
                                        // CAIDAOM-134 考勤分析优化-旷工-只要旷工分析出数值，迟到、早退为0
                                        wa.setLateTime(0f);
                                        wa.setEarlyTime(0f);
                                        break;
                                }
                                break;
                            }
                        }
                    } else {
                        Double duration = 0d;
                        Integer kgWorkTime = 0;
                        //迟到规则
                        List<AbsenceAnalyzeRule> lateRules = absenceAnalyzeRules.stream().filter(r -> r.getType() == 1).sorted(Comparator.comparing(AbsenceAnalyzeRule::getStart)).collect(Collectors.toList());
                        duration += this.getDuration(late, currentShiftDef.getWorkTotalTime(), lateRules);
                        //早退规则
                        List<AbsenceAnalyzeRule> earlyRules = absenceAnalyzeRules.stream().filter(r -> r.getType() == 2).sorted(Comparator.comparing(AbsenceAnalyzeRule::getStart)).collect(Collectors.toList());
                        duration += this.getDuration(early, currentShiftDef.getWorkTotalTime(), earlyRules);
                        if (duration > currentShiftDef.getWorkTotalTime()) {
                            kgWorkTime = currentShiftDef.getWorkTotalTime();
                        } else if (duration > 0) {
                            kgWorkTime = duration.intValue();
                        }
                        wa.setKgWorkTime(kgWorkTime);
                        if (kgWorkTime > 0) {
                            wa.setIsKg(1);
                            wa.setLateTime(0f);
                            wa.setEarlyTime(0f);
                        }
                    }
                }
            }
        } else {
            //兼容旧的规则 抵扣完成，再次计算 旷工判断 迟到/早退超过 超过多少算旷工
            if (currentShiftDef != null && currentShiftDef.getDateType() == 1 && analyzeInfo.getAbsent_limit() != null) {
                if (Math.round(errorTime / 60) > analyzeInfo.getAbsent_limit()) {
                    wa.setIsKg(1);
                    wa.setKgWorkTime(currentShiftDef.getWorkTotalTime());
                    // CLOUD-389
                    wa.setLateTime(0f);
                    wa.setEarlyTime(0f);
                }
            }
        }
    }

    private Double getDuration(float errTime, Integer workTotalTime, List<AbsenceAnalyzeRule> rules) {
        double duration = 0d;
        if (errTime > 0 && CollectionUtils.isNotEmpty(rules)) {
            for (AbsenceAnalyzeRule rule : rules) {
                // 目前只有按小时
                if (errTime >= rule.getStart() * 60 && errTime <= rule.getEnd() * 60) {
                    if (rule.getDurationUnit() == 1) {//1 小时
                        duration += rule.getDuration() * 60.0;
                    }
                    if (rule.getDurationUnit() == 2) {// 2 天
                        duration += (rule.getDuration() * workTotalTime);
                    }
                    break;
                }
            }
        }
        return duration;
    }

    /**
     * 考勤分析-分析休假数据
     *
     * @param resultWa
     * @param empLeaveMap
     * @param dto
     */
    private void calculateEmpLeave(List<WaAnalyze> resultWa, Map<String, List<EmpLeaveInfo>> empLeaveMap, WaAnalyzCalDTO dto) {
        if (MapUtils.isEmpty(empLeaveMap)) {
            return;
        }
        WaAnalyze analyze;
        for (Map.Entry<String, List<EmpLeaveInfo>> entry : empLeaveMap.entrySet()) {
            String key = entry.getKey();
            Long empId = Long.valueOf(key.split("_")[0]);
            Long leaveDate = Long.valueOf(key.split("_")[1]);
            analyze = new WaAnalyze();
            analyze.setEmpid(empId);
            analyze.setBelongDate(leaveDate);

            EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
            if (null == currentShiftDef) {
                continue;
            }

            //计算当天请假时长及是否旷工
            List<EmpLeaveInfo> empLeaveInfos = entry.getValue();

            //处理哺乳假
            empLeaveInfos = dealBRJLeaveInfo(empLeaveInfos, currentShiftDef);
            //计算是否旷工，如果请假小时大于等于了应该工作时长则抵消矿工，否则标识了旷工和旷工时长
            Map jsonbMap = getLevelColumnJson(empLeaveInfos, dto, false);
            analyze.setLevelColumnJsonb(jsonbMap);
            leaveCalculate(dto, analyze, empLeaveInfos, null, currentShiftDef);
            analyze.setWorkTime(currentShiftDef.getWorkTotalTime());
            resultWa.add(analyze);
        }
    }

    /**
     * 哺乳假
     *
     * @param empLeaveInfos
     * @return
     */
    private List<EmpLeaveInfo> dealBRJLeaveInfo(List<EmpLeaveInfo> empLeaveInfos, EmpShiftInfo shiftInfo) {
        List<EmpLeaveInfo> filterList = new ArrayList<>();
        for (EmpLeaveInfo leaveInfo : empLeaveInfos) {
            if ("BRJ".equals(leaveInfo.getLeave_type_def_code())) {
                if (DateTypeEnum.DATE_TYP_2.getIndex().equals(shiftInfo.getDateType()) && !leaveInfo.getIs_rest_day()) {//休息日连续计算
                    continue;
                } else if (DateTypeEnum.DATE_TYP_3.getIndex().equals(shiftInfo.getDateType()) && !leaveInfo.getIs_legal_holiday()) {//法定假日连续计算
                    continue;
                }
            }
            filterList.add(leaveInfo);
        }
        if (CollectionUtils.isEmpty(filterList)) {
            return filterList;
        }
        //非哺乳假
        List<EmpLeaveInfo> leaveList = filterList.stream().filter(e -> !"BRJ".equals(e.getLeave_type_def_code())).collect(Collectors.toList());
        //哺乳假
        List<EmpLeaveInfo> brjList = filterList.stream().filter(e -> "BRJ".equals(e.getLeave_type_def_code())).collect(Collectors.toList());

        //只有哺乳假
        if (CollectionUtils.isEmpty(leaveList)) {
            return brjList;
        }

        //如是休息日班次或者整天申请其他假已审批通过（请假时长=班次时长），那么这天请假时长=0
        Integer workTotalTime = shiftInfo.getWorkTotalTime();
        Float sumDuration = 0f;
        Integer halfWorkTime = workTotalTime / 2; // 半天的时长等于班次上工作时长的二分之一
        for (EmpLeaveInfo leaveInfo : leaveList) {
            if (leaveInfo.getStatus() != null && leaveInfo.getStatus() != 2) {
                continue;
            }
            Short period_type = leaveInfo.getPeriod_type();
            //整天
            Float time_duration = leaveInfo.getTime_duration();
            if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period_type.intValue())
                    || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period_type.intValue())) {
                sumDuration = time_duration * workTotalTime;
                break;
            } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period_type.intValue())) {
                //上半天时长
                if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(leaveInfo.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(leaveInfo.getEhalf_day())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftInfo, halfWorkTime);
                    time_duration = Float.valueOf(halfWorkTime);
                }
                if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(leaveInfo.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(leaveInfo.getEhalf_day())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftInfo, halfWorkTime);
                    time_duration = Float.valueOf(halfWorkTime);
                }
            }
            sumDuration += time_duration;
        }
        if (sumDuration >= workTotalTime) {
            filterList = leaveList;
        }
        return filterList;
    }

    /**
     * 考勤分析-分析出差数据(出差单为正常审批的单据)
     *
     * @param resultWa
     * @param empTravelMap
     * @param dto
     */
    private void calculateEmpTravel(List<WaAnalyze> resultWa, Map<String, List<WaEmpTravelDaytimeDo>> empTravelMap, WaAnalyzCalDTO dto) {
        if (MapUtils.isEmpty(empTravelMap)) {
            return;
        }
        WaAnalyze analyze;
        for (Map.Entry<String, List<WaEmpTravelDaytimeDo>> entry : empTravelMap.entrySet()) {
            analyze = new WaAnalyze();
            String key = entry.getKey();
            Long empid = Long.valueOf(key.split("_")[0]);
            Long travelDate = Long.valueOf(key.split("_")[1]);
            analyze.setEmpid(empid);
            analyze.setBelongDate(travelDate);
            analyze.setBelongOrgId(dto.getBelongid());
            EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
            if (currentShiftDef != null) {
                analyze.setWorkTime(currentShiftDef.getWorkTotalTime());
            }
            //计算出差时长
            List<WaEmpTravelDaytimeDo> empTravelInfos = entry.getValue();
            Map travelColumnJsonMap = getTravelColumnJson(empTravelInfos, dto, false);
            analyze.setTravelColumnJsonb(travelColumnJsonMap);
            //计算是否旷工，如果请假小时大于等于了应该工作时长则抵消矿工，否则标识了旷工和旷工时长
            Integer validStatus = travelCalculate(dto, analyze, empTravelInfos, null, currentShiftDef);
            if (MapUtils.isNotEmpty(travelColumnJsonMap)) {
                //设置出差有效状态
                travelColumnJsonMap.put("valid_status", validStatus);
                analyze.setTravelColumnJsonb(travelColumnJsonMap);
            }

            resultWa.add(analyze);
        }
    }

    private void addEmpLeaveAfterWaAnalyze(List<WaAnalyze> resultWa, Map<String, List<EmpLeaveInfo>> empleaveAfterMap, WaAnalyzCalDTO dto, Long startDate, Long endDate) {
        if (MapUtils.isEmpty(empleaveAfterMap)) {
            return;
        }
        WaAnalyze analyze;
        for (Map.Entry<String, List<EmpLeaveInfo>> entry : empleaveAfterMap.entrySet()) {
            String key = entry.getKey();
            Long empid = Long.valueOf(key.split("_")[0]);
            Long leaveDate = Long.valueOf(key.split("_")[1]);
            // 不在核算区间内的数据不进行核算
            if (!(leaveDate >= startDate && leaveDate <= endDate)) {
                continue;
            }
            // 查找是否已有存在的分析数据
            analyze = existsWaAnalyze(resultWa, empid, leaveDate);
            if (analyze == null) {
                analyze = new WaAnalyze();
                analyze.setEmpid(empid);
                analyze.setBelongDate(leaveDate);
                List<EmpLeaveInfo> empLeaveInfos = entry.getValue();
                Map jsonbMap = getLevelColumnJson(empLeaveInfos, dto, true);
                analyze.setLevelColumnJsonb(jsonbMap);
                EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                if (currentShiftDef == null) {
                    log.info("addEmpLeaveAfterWaAnalyze empid=" + analyze.getEmpid() + ",shifid=" + analyze.getSignoffId() + ",belongDate=" + analyze.getBelongDate());
                }
                //计算当天请假时长及是否旷工
                leaveCalculate(dto, analyze, null, empLeaveInfos, currentShiftDef);
                if (currentShiftDef != null) {
                    analyze.setWorkTime(currentShiftDef.getWorkTotalTime());
                }
                resultWa.add(analyze);
            }
        }
    }

    /**
     * 考勤分析-分析出差数据(出差单为延后审批的单据)
     *
     * @param resultWa
     * @param empleaveAfterMap
     * @param dto
     * @param startDate
     * @param endDate
     */
    private void addEmpTravelAfterWaAnalyze(List<WaAnalyze> resultWa, Map<String, List<WaEmpTravelDaytimeDo>> empleaveAfterMap,
                                            WaAnalyzCalDTO dto, Long startDate, Long endDate) {
        if (MapUtils.isEmpty(empleaveAfterMap)) {
            return;
        }
        WaAnalyze analyze = null;
        for (Map.Entry<String, List<WaEmpTravelDaytimeDo>> entry : empleaveAfterMap.entrySet()) {
            String key = entry.getKey();
            Long empid = Long.valueOf(key.split("_")[0]);
            Long travelDate = Long.valueOf(key.split("_")[1]);
            // 不在核算区间内的数据不进行核算
            if (!(travelDate >= startDate && travelDate <= endDate)) {
                continue;
            }
            // 查找是否已有存在的分析数据
            analyze = existsWaAnalyze(resultWa, empid, travelDate);
            if (analyze == null) {
                analyze = new WaAnalyze();
                analyze.setEmpid(empid);
                analyze.setBelongDate(travelDate);

                EmpShiftInfo currentShiftDef = getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                if (currentShiftDef == null) {
                    log.info("addEmpLeaveAfterWaAnalyze empid=" + analyze.getEmpid() + ",shifid=" + analyze.getSignoffId() + ",belongDate=" + analyze.getBelongDate());
                } else {
                    analyze.setWorkTime(currentShiftDef.getWorkTotalTime());
                }
                List<WaEmpTravelDaytimeDo> empLeaveInfos = entry.getValue();
                Map travelColumnJsonMap = getTravelColumnJson(empLeaveInfos, dto, true);
                analyze.setTravelColumnJsonb(travelColumnJsonMap);
                //计算当天请假时长及是否旷工
                Integer validStatus = travelCalculate(dto, analyze, null, empLeaveInfos, currentShiftDef);
                if (MapUtils.isNotEmpty(travelColumnJsonMap)) {
                    //设置出差有效状态
                    travelColumnJsonMap.put("valid_status", validStatus);
                    analyze.setTravelColumnJsonb(travelColumnJsonMap);
                }
                resultWa.add(analyze);
            }
        }
    }

    /**
     * 缺卡计算旷工逻辑
     *
     * @param analyzeInfo
     * @param analyze
     * @param shiftDef
     */
    private void missRegCalKg(WaAnalyzInfo analyzeInfo, WaAnalyze analyze, EmpShiftInfo shiftDef) {
        //出勤规则，如未设置则默认二次卡出勤规则
        Integer clockType = analyzeInfo == null || analyzeInfo.getClock_type() == null ? ParseGroupClockTypeEnum.SIGN_TWICE.getIndex() : analyzeInfo.getClock_type();
        List<Integer> allowedDateTypes = new ArrayList<>();
        if (analyzeInfo != null && StringUtils.isNotBlank(analyzeInfo.getAllowedDateType())) {
            allowedDateTypes = Arrays.stream(analyzeInfo.getAllowedDateType().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        boolean isAllowedDateType = allowedDateTypes.contains(shiftDef.getDateType());
        if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(clockType)) {
            //一次卡 缺卡默认视为旷工
            if (analyzeInfo != null && analyzeInfo.getClock_rule() != null) {
                JSONObject clockJson = JSONObject.parseObject(analyzeInfo.getClock_rule());
                Integer missingClockRule = (Integer) clockJson.get("missingClockRule");
                if (ParseGroupMissingClockRuleEnum.ABSENTEEISM.getIndex().equals(missingClockRule)) {
                    if (!isAllowedDateType) {
                        analyze.setIsKg(0);
                        analyze.setKgWorkTime(0);
                    } else {
                        //缺卡 视为旷工
                        if (shiftDef.getWorkTotalTime() > 0) {
                            analyze.setIsKg(1);
                        } else {
                            analyze.setIsKg(0);
                        }
                        analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                    }
                } else {
                    if (!isAllowedDateType) {
                        //缺卡 视为缺卡 此逻辑c产品暂未提供，默认旷工
                        if (shiftDef.getWorkTotalTime() > 0) {
                            analyze.setIsKg(1);
                        } else {
                            analyze.setIsKg(0);
                        }
                        analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                    } else {
                        analyze.setIsKg(0);
                        analyze.setKgWorkTime(0);
                    }
                }
            } else {
                if (!isAllowedDateType) {
                    analyze.setIsKg(0);
                    analyze.setKgWorkTime(0);
                } else {
                    //未配置缺卡规则默认旷工
                    if (shiftDef.getWorkTotalTime() > 0) {
                        analyze.setIsKg(1);
                    } else {
                        analyze.setIsKg(0);
                    }
                    analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                }
            }
        } else if (ParseGroupClockTypeEnum.SIGN_ZERO.getIndex().equals(clockType)) {
            analyze.setActualWorkTime(null != shiftDef.getWorkTotalTime() ? shiftDef.getWorkTotalTime().floatValue() : 0f);
            analyze.setRegisterTime(shiftDef.getWorkTotalTime());
            analyze.setIsKg(0);
            analyze.setKgWorkTime(0);
        } else {
            // 没有签到记录算旷工
            if (analyzeInfo != null && analyzeInfo.getRegister_miss() != null && analyzeInfo.getRegister_miss() == 2) {
                if (!isAllowedDateType) {
                    analyze.setIsKg(0);
                    analyze.setKgWorkTime(0);
                } else {
                    if (shiftDef.getWorkTotalTime() > 0) {
                        analyze.setIsKg(1);
                    } else {
                        analyze.setIsKg(0);
                    }
                    analyze.setKgWorkTime(shiftDef.getWorkTotalTime());
                }
            }
        }
    }

    /**
     * 计算当天请假时长及是否旷工
     *
     * @param dto
     * @param analyze
     * @param empLeaveInfos
     * @param shiftDef
     */
    private void leaveCalculate(WaAnalyzCalDTO dto, WaAnalyze analyze, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2, EmpShiftInfo shiftDef) {
        //计算当天请假时长
        Map<String, Object> originLevelJson = calculateOriginLevel(dto, empLeaveInfos, empLeaveInfos2);
        analyze.setOriginLevelColumnJsonb(originLevelJson);
        // 如果没有签到签退记录或者考勤分析设置上需要判断是否旷工，则需要把没有签到记录，但是请假或加班的数据标识为旷工
        if ((analyze.getSigninId() == null || analyze.getSignoffId() == null) && shiftDef != null && shiftDef.getDateType() == 1 && shiftDef.getWorkTotalTime() > 0) {
            WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
            this.missRegCalKg(analyzeInfo, analyze, shiftDef);
            //不打卡，有休假，处理实际出勤时长
            this.handleSignZeroRegisterTime(dto, analyze, analyzeInfo, empLeaveInfos, empLeaveInfos2, shiftDef);
        }
        //计算旷工时长 用请假小时数和班次上的时长比较
        calculateKg(analyze, shiftDef, empLeaveInfos, empLeaveInfos2, dto);
    }

    private void handleSignZeroRegisterTime(WaAnalyzCalDTO dto, WaAnalyze analyze, WaAnalyzInfo analyzeInfo, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2, EmpShiftInfo shiftDef) {
        Integer clockType = analyzeInfo == null || analyzeInfo.getClock_type() == null ? ParseGroupClockTypeEnum.SIGN_TWICE.getIndex() : analyzeInfo.getClock_type();
        if (ParseGroupClockTypeEnum.SIGN_ZERO.getIndex().equals(clockType)) {
            List<EmpLeaveInfo> allEmpLeaveInfos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
                allEmpLeaveInfos.addAll(empLeaveInfos);
            }
            if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
                allEmpLeaveInfos.addAll(empLeaveInfos2);
            }
            Float leaveTime = getValidLeaveDuration(analyze.getEmpid(), analyze.getBelongDate(), shiftDef, allEmpLeaveInfos, dto, null);
            BigDecimal leaveDec = new BigDecimal(leaveTime);
            BigDecimal workTime = new BigDecimal(shiftDef.getWorkTotalTime());
            BigDecimal diff = workTime.subtract(leaveDec);
            if (diff.doubleValue() > 0) {
                analyze.setRegisterTime(diff.intValue());
                analyze.setActualWorkTime(diff.floatValue());
            }
        }
    }

    /**
     * 计算当天出差时长及是否旷工且判断出差单是否有效
     *
     * @param dto
     * @param analyze
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @param shiftdef
     * @return 出差单有效状态 0 无效 1 有效
     */
    private Integer travelCalculate(WaAnalyzCalDTO dto, WaAnalyze analyze, List<WaEmpTravelDaytimeDo> empLeaveInfos,
                                    List<WaEmpTravelDaytimeDo> empLeaveInfos2, EmpShiftInfo shiftdef) {
        //计算出差时长
        Map<String, Object> orginLevelJson = calculateOriginTravel(dto, empLeaveInfos, empLeaveInfos2);
        Integer validStatus = ValidStatusEnum.VALID.getIndex();
        //出差时长是否抵扣旷工时长
        boolean ifCalculateKgForTravel = false;
        //查询当天考勤分析规则
        WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(analyze.getEmpid(), analyze.getBelongDate());
        //外勤分析规则，如未设置默认外勤打卡
        //外勤打卡：此种情况，仅判断当天存在大于等于1次的外勤打卡即为出勤正常，如存在出差单，出差单仅参与出差时长的计算，但不参与迟到早退旷工的抵扣
        //出差单联动外勤打卡：在出差单的事件日期当天存在外勤打卡，则出差单视为正常，出差单参与考勤分析，需抵扣迟到早退旷工。如出差单当天没有外勤打卡，则考勤分析出差单仅参与出差时长的计算，但不参与迟到早退旷工的抵扣
        //出差单：此种情况可把出差当成一种请假来处理。需计算出差单时长，且需抵扣迟到早退旷工
        Integer outParseRule = analyzeInfo == null || analyzeInfo.getOutParseRule() == null ? TravelTypeParseRuleEnum.TRAVEL_REG.getIndex() : analyzeInfo.getOutParseRule();
        if (!TravelTypeParseRuleEnum.TRAVEL_REG.getIndex().equals(outParseRule)) {
            if (TravelTypeParseRuleEnum.TRAVEL_BILL_REG.getIndex().equals(outParseRule)) {
                //判断当天是否有外勤打卡
                List<WaRegisterRecord> outSignList = dto.getEmpOutRegByDateEmpId(analyze.getEmpid(), analyze.getBelongDate());
                ifCalculateKgForTravel = CollectionUtils.isNotEmpty(outSignList);
                if (!ifCalculateKgForTravel) {
                    validStatus = ValidStatusEnum.INVALID.getIndex();
                }
            } else {
                ifCalculateKgForTravel = true;
            }
        }
        if (MapUtils.isNotEmpty(orginLevelJson)) {
            //设置出差单有效状态
            orginLevelJson.put("valid_status", validStatus);
        }
        analyze.setOriginTravelColumnJsonb(orginLevelJson);
        // 如果没有签到签退记录或者考勤分析设置上需要判断是否旷工，则需要把没有签到记录，但是请假或加班的数据标识为旷工
        if ((analyze.getSigninId() == null || analyze.getSignoffId() == null) && shiftdef != null && shiftdef.getDateType() == 1 && shiftdef.getWorkTotalTime() > 0) {
            this.missRegCalKg(analyzeInfo, analyze, shiftdef);
        }

        //出差时长抵扣旷工时长
        if (ifCalculateKgForTravel) {
            calculateKgForTravel(analyze, shiftdef, empLeaveInfos, empLeaveInfos2, dto);
        }

        return validStatus;
    }

    /**
     * 获取实际请假小时数
     *
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @return
     */
    private Float getRealLeaveTime(List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        Float total_time_duration = 0f;
        List<Integer> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (EmpLeaveInfo lf : empLeaveInfos) {
                if (lf.getLeave_date().equals(lf.getReal_date())) {
                    Float time_duration = lf.getTime_duration();

                    if (lf.getPeriod_type() == 3) {
                        if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {//CLOUD-8503
                            time_duration = lf.getBefore_adjust_time_duration();
                        }
                    }

                    if (time_duration != null && time_duration > 0) {
                        Integer time_unit = lf.getTime_unit();
                        if (time_unit == 1) {
                            time_duration = getRealLeaveHour(lf);
                        }
                        total_time_duration += time_duration;
                    }
                    levelIds.add(lf.getLeave_id());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (EmpLeaveInfo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getLeave_id())) {
                    Float time_duration = lf.getTime_duration();

                    if (lf.getPeriod_type() == 3) {
                        if (lf.getBefore_adjust_time_duration() != null && lf.getBefore_adjust_time_duration() > 0) {//CLOUD-8503
                            time_duration = lf.getBefore_adjust_time_duration();
                        }
                    }

                    if (time_duration != null && time_duration > 0) {
                        Integer time_unit = lf.getTime_unit();
                        if (time_unit == 1) {
                            time_duration = getRealLeaveHour(lf);
                        }
                        total_time_duration += time_duration;
                    }
                }
            }
        }
        return total_time_duration;
    }

    /**
     * 获取实际出差小时数
     *
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @return
     */
    private Float getRealTravelTime(List<WaEmpTravelDaytimeDo> empLeaveInfos, List<WaEmpTravelDaytimeDo> empLeaveInfos2) {
        Float total_time_duration = 0f;
        List<Long> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (WaEmpTravelDaytimeDo lf : empLeaveInfos) {
                if (lf.getTravelDate().equals(lf.getRealDate())) {
                    Float time_duration = lf.getTimeDuration();

                    if (lf.getPeriodType() == 3) {
                        if (lf.getBeforeAdjustTimeDuration() != null && lf.getBeforeAdjustTimeDuration() > 0) {//CLOUD-8503
                            time_duration = lf.getBeforeAdjustTimeDuration();
                        }
                    }

                    if (time_duration != null && time_duration > 0) {
                        Integer time_unit = Integer.valueOf(lf.getTimeUnit());
                        if (time_unit == 1) {
                            time_duration = getRealTravelHour(lf);
                        }
                        total_time_duration += time_duration;
                    }
                    levelIds.add(lf.getTravelId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (WaEmpTravelDaytimeDo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getTravelId())) {
                    Float time_duration = lf.getTimeDuration();

                    if (lf.getPeriodType() == 3) {
                        if (lf.getBeforeAdjustTimeDuration() != null && lf.getBeforeAdjustTimeDuration() > 0) {//CLOUD-8503
                            time_duration = lf.getBeforeAdjustTimeDuration();
                        }
                    }

                    if (time_duration != null && time_duration > 0) {
                        Integer time_unit = Integer.valueOf(lf.getTimeUnit());
                        if (time_unit == 1) {
                            time_duration = getRealTravelHour(lf);
                        }
                        total_time_duration += time_duration;
                    }
                }
            }
        }
        return total_time_duration;
    }

    private Integer getQsLeaveTime(String halfDay, EmpShiftInfo shiftDef) {
        //清水上半天休假定制逻辑
        if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY) && shiftDef.getShiftDefId() != null) {
            WaShiftCustomizedExample customizedExample = new WaShiftCustomizedExample();
            customizedExample.createCriteria().andShiftDefIdEqualTo(shiftDef.getShiftDefId());
            List<WaShiftCustomized> customizeds = waShiftCustomizedMapper.selectByExample(customizedExample);
            if (CollectionUtils.isNotEmpty(customizeds)) {
                WaShiftCustomized customized = customizeds.get(0);
                if (customized.getShalfTime() != null) {
                    //指定班次，请半天的假，上半天假默认时长为4个小时
                    return customized.getShalfTime();
                }
            }
        }
        return null;
    }

    /**
     * 求出是上半天或下半天的时长
     *
     * @param halfDay
     * @param shiftDef
     * @return
     */
    private int getHalfTime(String halfDay, EmpShiftInfo shiftDef, int halfWorkTime) {
        //清水定制逻辑
        Integer time = getQsLeaveTime(halfDay, shiftDef);
        if (time != null) {
            halfWorkTime = time;
            return halfWorkTime;
        }
        int startTime = shiftDef.getStartTime();
        int endTime = shiftDef.getEndTime();
        if (CdWaShiftUtil.checkCrossNight(startTime, endTime, shiftDef.getDateType())) {//跨夜
            endTime += 24 * 60;
        }
        Integer noonRestStartMin = shiftDef.getNoonRestStart();
        Integer noonRestEndMin = shiftDef.getNoonRestEnd();
        ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType());
        noonRestStartMin = restPeriod.getNoonRestStart();
        noonRestEndMin = restPeriod.getNoonRestEnd();
        // 如果定义了半天时间点，则按这个时间拆分上半天和下半天
        if (BooleanUtils.isTrue(shiftDef.getIsHalfdayTime()) && shiftDef.getHalfdayTime() != null && shiftDef.getHalfdayTime() >= 0) {
            int halfDayTime = shiftDef.getHalfdayTime();
            if (shiftDef.getHalfdayTime() < shiftDef.getStartTime()) {
                halfDayTime += 24 * 60;
            }
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                // 上半天(时间) = 定义的时间点 - 上班开始时间
                halfWorkTime = shiftDef.getHalfdayTime() - shiftDef.getStartTime();
                if (shiftDef.getHalfdayTime() < shiftDef.getStartTime()) {
                    halfWorkTime += 24 * 60;
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest())) {
                    int noonRestStartTime = noonRestStartMin;
                    int noonRestEndTime = noonRestEndMin;
                    /*if (noonRestEndTime < noonRestStartTime) {
                        noonRestEndTime += 24 * 60;
                    }*/
                    if (halfDayTime >= noonRestStartTime) {
                        if (halfDayTime > noonRestEndTime) {
                            halfWorkTime -= noonRestEndTime - noonRestStartTime;
                        } else {
                            halfWorkTime -= halfDayTime - noonRestStartTime;
                        }
                    }
                }
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                // 下半天(时间) = 下班截止时间 - 定义的时间点 【如果下班截止时间跨夜则设置为次日】
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    Integer nextDaytime = shiftDef.getEndTime() + (24 * 60);
                    halfWorkTime = nextDaytime - halfDayTime;
                } else {
                    halfWorkTime = endTime - halfDayTime;
                }
                // 如果定义的半天时间点在休息时间段内，则扣除休息时间
                if ((shiftDef.getIsNoonRest() != null && shiftDef.getIsNoonRest())) {
                    int noonRestStartTime = noonRestStartMin;
                    int noonRestEndTime = noonRestEndMin;
                    if (halfDayTime < noonRestEndTime) {
                        if (halfDayTime < noonRestStartTime) {
                            halfWorkTime -= noonRestEndTime - noonRestStartTime;
                        } else {
                            halfWorkTime -= noonRestEndTime - halfDayTime;
                        }
                    }
                }
            }
        } else if (shiftDef.getIsNoonRest()) { // 或者按 中午休息时间来拆分
            if (halfDay.equals(BaseConst.LEAVE_HALF_SHALF_DAY)) {
                halfWorkTime = noonRestStartMin - shiftDef.getStartTime();
            } else if (halfDay.equals(BaseConst.LEAVE_HALF_EHALF_DAY)) {
                if (CdWaShiftUtil.checkCrossNight(shiftDef.getStartTime(), shiftDef.getEndTime(), shiftDef.getDateType())) {//跨夜
                    long currentTime = DateUtil.getOnlyDate();
                    long start = currentTime + noonRestEndMin * 60;
                    long end = DateUtil.addDate(currentTime * 1000, 1) + shiftDef.getEndTime() * 60;
                    halfWorkTime = (int) ((end - start) / 60);
                } else {
                    halfWorkTime = shiftDef.getEndTime() - noonRestEndMin;
                }
            }
        } else {
            // 以上条件都没有满足，则按工作时长的一半进行拆分
        }
        return Math.abs(halfWorkTime);
    }

    /**
     * 单位为天的假-请假时长转换成小时
     *
     * @param lf
     * @return
     */
    private Float getRealLeaveHour(EmpLeaveInfo lf) {
        int periodType = lf.getPeriod_type();
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Float time_duration = lf.getTime_duration();
        Integer workTotalTime = getWorkTotalTime(lf);

        Float leaveTime = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
        if (periodType == 9 && time_duration != 1) {//半天假，需进行请假时长折算
            if (StringUtils.isNotBlank(lf.getShalf_day()) && StringUtils.isNotBlank(lf.getEhalf_day())) {
                Integer halfWorkTime = leaveTime.intValue();
                //上半天
                if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                }
                //下半天
                if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                }
                return Float.valueOf(halfWorkTime);
            }
        }
        return leaveTime;
    }

    /**
     * 单位为天的假-请假时长转换成小时
     *
     * @param lf
     * @return
     */
    private Float getRealTravelHour(WaEmpTravelDaytimeDo lf) {
        int periodType = lf.getPeriodType();
        EmpShiftInfo shiftDef = lf.getEmpShiftInfo();
        Float time_duration = lf.getTimeDuration();
        Integer workTotalTime = getWorkTotalTime(lf);

        Float leaveTime = getLeaveHour(lf.getEmpId(), lf.getTravelDate(), time_duration, workTotalTime);
        if (periodType == 9 && time_duration != 1) {//半天假，需进行请假时长折算
            if (StringUtils.isNotBlank(lf.getShalfDay()) && StringUtils.isNotBlank(lf.getEhalfDay())) {
                Integer halfWorkTime = leaveTime.intValue();
                //上半天
                if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalfDay())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shiftDef, halfWorkTime);
                }
                //下半天
                if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalfDay()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalfDay())) {
                    halfWorkTime = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shiftDef, halfWorkTime);
                }
                return Float.valueOf(halfWorkTime);
            }
        }
        return leaveTime;
    }

    /**
     * 抵扣休息时间
     *
     * @param belongDate         归属日期
     * @param validLeaveDuration 迟到与休假重叠时间
     * @param start              迟到与休假重叠时间与休息时间重叠开始时间
     * @param end                迟到与休假重叠时间与休息时间重叠结束时间
     * @param shift              班次
     * @return 返回重叠时长
     */
    private long deductionRestTime(long belongDate, long validLeaveDuration, long start, long end, EmpShiftInfo shift) {
        //班次休息开始时间
        Integer noonRestStart = Optional.ofNullable(shift.getNoonRestStart()).orElse(0);
        //班次休息结束时间
        Integer noonRestEnd = Optional.ofNullable(shift.getNoonRestEnd()).orElse(0);
        //是否开启休息
        boolean isNoonRest = Optional.ofNullable(shift.getIsNoonRest()).orElse(false);
        if (noonRestStart == 0 && noonRestEnd == 0) {
            return validLeaveDuration;
        }
        ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
        noonRestStart = restPeriod.getNoonRestStart();
        noonRestEnd = restPeriod.getNoonRestEnd();
        //休息开始时间
        long restStartTime = belongDate + (noonRestStart * 60);
        //休息结束时间
        long restEndTime = belongDate + (noonRestEnd * 60);
        if (isNoonRest && noonRestStart > 0 && noonRestEnd > 0
                && mobileV16Service.checkTimeRangeOverlap(restStartTime, restEndTime, start, end)) {
            //休假时间段与休息时间段重叠,扣除重叠时间
            long restStart = Math.max(restStartTime, start);
            long restEnd = Math.min(restEndTime, end);
            long restDiff = restEnd - restStart;
            if (restDiff > 0) {
                validLeaveDuration -= restDiff;
            }
        }
        //扣除休息时间段内的时长
        List<ShiftRestPeriods> restPeriods = shift.getRestPeriods();
        if (CollectionUtils.isNotEmpty(restPeriods)) {
            for (ShiftRestPeriods rest : restPeriods) {
                Integer noonRestStart1 = rest.getNoonRestStart();
                Integer noonRestEnd1 = rest.getNoonRestEnd();
                ShiftRestPeriods restPeriod1 = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart1, noonRestEnd1, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
                noonRestStart1 = restPeriod1.getNoonRestStart();
                noonRestEnd1 = restPeriod1.getNoonRestEnd();
                long restStart = belongDate + noonRestStart1 * 60;
                long restEnd = belongDate + noonRestEnd1 * 60;
                if (mobileV16Service.checkTimeRangeOverlap(restStart, restEnd, start, end)) {
                    long rStart = Math.max(restStartTime, start);
                    long rEnd = Math.min(restEndTime, end);
                    long restDiff = rEnd - rStart;
                    if (restDiff > 0) {
                        validLeaveDuration -= restDiff;
                    }
                }
            }
        }
        return validLeaveDuration;
    }

    /**
     * 弹性抵扣休息时间
     *
     * @param belongDate         归属日期
     * @param validLeaveDuration 重叠时间
     * @param start              迟到与休假重叠时间与休息时间重叠开始时间
     * @param end                迟到与休假重叠时间与休息时间重叠结束时间
     * @param shift              班次
     * @return 返回重叠时长
     */
    private long flexibleDeductionRestTime(long belongDate, long validLeaveDuration, long start, long end, EmpShiftInfo shift, long workLate) {
        Integer noonRestStartMin = shift.getNoonRestStart();
        Integer noonRestEndMin = shift.getNoonRestEnd();
        ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStartMin, noonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
        noonRestStartMin = restPeriod.getNoonRestStart();
        noonRestEndMin = restPeriod.getNoonRestEnd();
        //班次休息开始时间
        long noonRestStart = Optional.ofNullable(noonRestStartMin).orElse(0);
        //班次休息结束时间
        long noonRestEnd = Optional.ofNullable(noonRestEndMin).orElse(0);
        //是否开启休息
        boolean isNoonRest = Optional.ofNullable(shift.getIsNoonRest()).orElse(false);
        if (noonRestStart == 0 && noonRestEnd == 0) {
            return validLeaveDuration;
        }
        if (workLate > 0) {
            noonRestStart += workLate / 60;
            noonRestEnd += workLate / 60;
        }
        //休息开始时间
        long restStartTime = belongDate + (noonRestStart * 60);
        //休息结束时间
        long restEndTime = belongDate + (noonRestEnd * 60);
        if (isNoonRest && noonRestStart > 0 && noonRestEnd > 0 && mobileV16Service.checkTimeRangeOverlap(restStartTime, restEndTime, start, end)) {
            //休假时间段与休息时间段重叠,扣除重叠时间
            long restStart = Math.max(restStartTime, start);
            long restEnd = Math.min(restEndTime, end);
            long restDiff = restEnd - restStart;
            if (restDiff > 0) {
                validLeaveDuration -= restDiff;
            }
        }
        //扣除休息时间段内的时长
        List<ShiftRestPeriods> restPeriods = shift.getRestPeriods();
        if (CollectionUtils.isNotEmpty(restPeriods)) {
            for (ShiftRestPeriods rest : restPeriods) {
                Integer rNoonRestStartMin = rest.getNoonRestStart();
                Integer rNoonRestEndMin = rest.getNoonRestEnd();
                ShiftRestPeriods restTime = CdWaShiftUtil.getShiftWorkRestTime(rNoonRestStartMin, rNoonRestEndMin, shift.getStartTime(), shift.getEndTime(), shift.getDateType());
                rNoonRestStartMin = restTime.getNoonRestStart();
                rNoonRestEndMin = restTime.getNoonRestEnd();
                long restStart = belongDate + rNoonRestStartMin * 60;
                long restEnd = belongDate + rNoonRestEndMin * 60;
                if (workLate > 0) {
                    restStart += workLate;
                    restEnd += workLate;
                }
                if (mobileV16Service.checkTimeRangeOverlap(restStart, restEnd, start, end)) {
                    long rStart = Math.max(restStartTime, start);
                    long rEnd = Math.min(restEndTime, end);
                    long restDiff = rEnd - rStart;
                    if (restDiff > 0) {
                        validLeaveDuration -= restDiff;
                    }
                }
            }
        }
        return validLeaveDuration;
    }

    /**
     * 获取除哺乳假外的有效休假时长
     *
     * @param empId
     * @param belongDate
     * @param shift
     * @param empLeaveInfos
     * @param dto
     * @param travelDaytimeList
     * @return
     */
    public Float getValidLeaveDuration(Long empId, Long belongDate, EmpShiftInfo shift, List<EmpLeaveInfo> empLeaveInfos,
                                       WaAnalyzCalDTO dto, List<WaLeaveDaytimeExtDto> travelDaytimeList) {
        if (CollectionUtils.isEmpty(empLeaveInfos)) {
            return 0f;
        }
        float totalLeaveTimeDuration = 0f;
        float brjLeaveTimeDuration = 0f;
        int workTime = Optional.ofNullable(shift.getWorkTotalTime()).orElse(0);
        List<Integer> brjLeaveIds = new ArrayList<>();
        //计算休假时长
        for (EmpLeaveInfo lf : empLeaveInfos) {
            Integer periodType = Integer.valueOf(lf.getPeriod_type());
            float timeDuration = Optional.ofNullable(lf.getTime_duration()).orElse(0f);
            if (!lf.getLeave_type_def_code().equals("BRJ")) {
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
                    //休假为整天，则休假时长为应出勤时长
                    timeDuration = workTime;
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    //休假为上下半天，则休假时长需根据是否设置半天时间点等计算实际休假时长（单位分钟）
                    int halfWorkTime = workTime / 2;
                    if (timeDuration == 1) {
                        //假期类型为上下半天，实际休假时长为1，则转换为应出勤时长（单位分钟）
                        timeDuration = workTime;
                    } else {
                        if (BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_SHALF_DAY.equals(lf.getEhalf_day())) {
                            //分析出来的上半天休假时长（单位分钟）
                            timeDuration = getHalfTime(BaseConst.LEAVE_HALF_SHALF_DAY, shift, halfWorkTime);
                        }
                        if (BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getShalf_day()) && BaseConst.LEAVE_HALF_EHALF_DAY.equals(lf.getEhalf_day())) {
                            //分析出来的下半天休假时长（单位分钟）
                            timeDuration = getHalfTime(BaseConst.LEAVE_HALF_EHALF_DAY, shift, halfWorkTime);
                        }
                    }
                }
                //累加计算休假时长
                totalLeaveTimeDuration += timeDuration;
            } else {
                brjLeaveIds.add(lf.getLeave_id());
                brjLeaveTimeDuration += timeDuration;
            }
        }
        empLeaveInfos = empLeaveInfos.stream().filter(l -> !brjLeaveIds.contains(l.getLeave_id())).collect(Collectors.toList());
        if (totalLeaveTimeDuration > 0 && CollectionUtils.isNotEmpty(empLeaveInfos)) {
            List<WaLeaveDaytimeExtPo> leaveDayTimes = getLeaveDayTimes(empLeaveInfos);
            WaShiftDef shiftDef = ObjectConverter.convert(shift, WaShiftDef.class);
            //计算已经申请的休假单的实际休假开始、结束时间
            List<WaLeaveDaytimeExtDto> ltExtDtoList = leaveDayTimes.stream().map(ltDay -> mobileV16Service.calLeaveDayRealTimeSlot(ltDay, shiftDef)).collect(Collectors.toList());
            //查询当天的销假数据
            List<WaLeaveCancelDayTime> leaveCancelDayTimes = dto.getLeaveCancelMap().get(String.format("%s_%s", empId, belongDate));
            List<WaLeaveDaytimeExtDto> leaveCancelDaytimeExtDtoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(leaveCancelDayTimes)) {
                //计算销假单实际的销假开始、结束时间
                leaveCancelDaytimeExtDtoList = leaveCancelDayTimes.stream().map(leaveCancelDayTime -> {
                    WaLeaveDaytimeExtPo waLeaveCancelDayTime = ObjectConverter.convert(leaveCancelDayTime, WaLeaveDaytimeExtPo.class);
                    waLeaveCancelDayTime.setLeaveDate(leaveCancelDayTime.getLeaveCancelDate());
                    WaLeaveDaytimeExtDto extDto = mobileV16Service.calLeaveDayRealTimeSlot(waLeaveCancelDayTime, shiftDef);
                    extDto.setXj(true);
                    return extDto;
                }).collect(Collectors.toList());
            }
            //组合休假以及销假数据
            List<WaLeaveDaytimeExtDto> allDaytimeExtDtoList = new ArrayList<>();
            allDaytimeExtDtoList.addAll(ltExtDtoList);
            allDaytimeExtDtoList.addAll(leaveCancelDaytimeExtDtoList);
            allDaytimeExtDtoList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));
            if (CollectionUtils.isNotEmpty(travelDaytimeList)) {
                travelDaytimeList.sort(Comparator.comparing(WaLeaveDaytimeExtDto::getCreateTime));
                allDaytimeExtDtoList.addAll(travelDaytimeList);
            }

            //根据休假单、销假单 计算有效地休假时间段
            List<TimeRangeCheckUtil.ChangeData> leaveTimeRangeList = allDaytimeExtDtoList.stream()
                    .map(lt -> TimeRangeCheckUtil.ChangeData.from(!lt.isXj(), lt.getLeaveStartTime(), lt.getLeaveEndTime())).collect(Collectors.toList());
            //筛选有效地休假时间段
            List<Pair<Long, Long>> leaveTimes = TimeRangeCheckUtil.asks(leaveTimeRangeList);
            if (CollectionUtils.isNotEmpty(leaveTimes)) {
                float leaveDuration = 0f;
                for (Pair<Long, Long> leaveTime : leaveTimes) {
                    long start = (Long) leaveTime.getKey();
                    long end = (Long) leaveTime.getValue();
                    long validLeaveDuration = end - start;
                    if (validLeaveDuration > 0) {
                        validLeaveDuration = deductionRestTime(belongDate, validLeaveDuration, start, end, shift);
                        leaveDuration += (BigDecimal.valueOf(validLeaveDuration).divide(BigDecimal.valueOf(60), 0, RoundingMode.HALF_DOWN).floatValue());
                    }
                }
                if (leaveDuration > 0) {
                    totalLeaveTimeDuration = leaveDuration;
                }
            } else {
                totalLeaveTimeDuration = 0f;
            }
        }
        totalLeaveTimeDuration += brjLeaveTimeDuration;
        return totalLeaveTimeDuration;
    }

    /**
     * 获取有效出差时长
     *
     * @param belongDate
     * @param shift
     * @param empTravelInfos
     * @return
     */
    public Float getValidTravelDuration(Long belongDate, EmpShiftInfo shift, List<WaEmpTravelDaytimeDo> empTravelInfos) {
        float totalTravelTimeDuration = 0f;
        //先判断需不需要进行抵扣（有休假且迟到早退异常时才需要抵扣）
        if (CollectionUtils.isNotEmpty(empTravelInfos)) {
            int workTime = Optional.ofNullable(shift.getWorkTotalTime()).orElse(0);
            List<Long> notDeductionTravelIds = new ArrayList<>();
            //计算出差时长
            for (WaEmpTravelDaytimeDo lf : empTravelInfos) {
                //非工作日等不抵扣
                if (lf.getDateType() == null || !DateTypeEnum.DATE_TYP_1.getIndex().equals(lf.getDateType()) || !lf.getTravelDate().equals(belongDate) || lf.getEmpShiftInfo() == null) {
                    notDeductionTravelIds.add(lf.getTravelId());
                    continue;
                }
                Integer periodType = Integer.valueOf(lf.getPeriodType());
                float timeDuration = Optional.ofNullable(lf.getTimeDuration()).orElse(0f);
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(periodType)) {
                    //出差为整天，则时长为应出勤时长
                    timeDuration = workTime;
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(periodType)) {
                    //出差为上下半天，则出差时长需根据是否设置半天时间点等计算实际出差时长（单位分钟）
                    int halfWorkTime = workTime / 2;
                    if (timeDuration == 1) {
                        //出差类型为上下半天，实际出差时长为1，则转换为应出勤时长（单位分钟）
                        timeDuration = workTime;
                    } else {
                        if (HalfDayTypeEnum.A.name().equals(lf.getShalfDay()) && HalfDayTypeEnum.A.name().equals(lf.getEhalfDay())) {
                            //分析出来的上半天出差时长（单位分钟）
                            timeDuration = getHalfTime(HalfDayTypeEnum.A.name(), shift, halfWorkTime);
                        }
                        if (HalfDayTypeEnum.P.name().equals(lf.getShalfDay()) && HalfDayTypeEnum.P.name().equals(lf.getEhalfDay())) {
                            //分析出来的下半天出差时长（单位分钟）
                            timeDuration = getHalfTime(HalfDayTypeEnum.P.name(), shift, halfWorkTime);
                        }
                    }
                }
                //累加出差时长
                totalTravelTimeDuration += timeDuration;
            }
            empTravelInfos = empTravelInfos.stream().filter(l -> !notDeductionTravelIds.contains(l.getTravelId())).collect(Collectors.toList());
            //需做时间重叠校验
            if (totalTravelTimeDuration > 0 && CollectionUtils.isNotEmpty(empTravelInfos)) {
                List<DaytimeExtDto> travelDayTimes = getDayTimes(empTravelInfos);
                WaShiftDef shiftDef = ObjectConverter.convert(shift, WaShiftDef.class);
                //计算已经申请的出差单的实际休假开始、结束时间
                List<WaTravelDaytimeExtDto> extDtoList = travelDayTimes.stream().map(ltDay -> calTravelDayRealTimeSlot(ltDay, shiftDef)).sorted(Comparator.comparing(WaTravelDaytimeExtDto::getTravelId)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(travelDayTimes)) {
                    float travelDuration = 0f;
                    for (WaTravelDaytimeExtDto travelTime : extDtoList) {
                        long start = travelTime.getStartTime();
                        long end = travelTime.getEndTime();
                        long validDuration = end - start;
                        if (validDuration > 0) {
                            //抵扣休息时间
                            validDuration = deductionRestTime(belongDate, validDuration, start, end, shift);
                            travelDuration += (BigDecimal.valueOf(validDuration).divide(BigDecimal.valueOf(60), 0, RoundingMode.HALF_DOWN).floatValue());
                        }
                    }
                    if (travelDuration > 0) {
                        totalTravelTimeDuration = travelDuration;
                    }
                }
            }
        }
        return totalTravelTimeDuration;
    }

    /**
     * 旷工时长抵扣
     *
     * @param analyze
     * @param shiftDef
     * @param empLeaveInfos
     * @param empLeaveInfos2
     */
    private void calculateKg(WaAnalyze analyze, EmpShiftInfo shiftDef, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2, WaAnalyzCalDTO dto) {
        if (shiftDef != null && shiftDef.getDateType() != null && shiftDef.getDateType() == 1) {
            analyze.setShiftDefId(shiftDef.getShiftDefId());
            if (analyze.getKgWorkTime() != null && analyze.getKgWorkTime() > 0) {// 如果有旷工小时，则和请假小时进行抵消
                //获取实际请假时长
                //Float leaveTime = getRealLeaveTime(empLeaveInfos, empLeaveInfos2);
                List<EmpLeaveInfo> allEmpLeaveInfos = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
                    allEmpLeaveInfos.addAll(empLeaveInfos);
                }
                if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
                    allEmpLeaveInfos.addAll(empLeaveInfos2);
                }
                allEmpLeaveInfos = allEmpLeaveInfos.stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList());
                Float leaveTime = getValidLeaveDuration(analyze.getEmpid(), analyze.getBelongDate(), shiftDef, allEmpLeaveInfos, dto, null);
                BigDecimal leaveDec = new BigDecimal(leaveTime);
                BigDecimal kgDec = new BigDecimal(analyze.getKgWorkTime());
                BigDecimal diff = kgDec.subtract(leaveDec);
                if (diff.doubleValue() > 0) {
                    if (analyze.getWorkTime() == null) {
                        analyze.setWorkTime(shiftDef.getWorkTotalTime());
                    }
                    analyze.setIsKg(1);
                    analyze.setKgWorkTime(diff.intValue());
                } else {
                    analyze.setIsKg(0);
                    analyze.setKgWorkTime(0);
                }
            }
        }
    }

    /**
     * 旷工时长抵扣-出差
     *
     * @param analyze
     * @param shiftDef
     * @param empTravelInfos
     * @param empTravelInfos2
     */
    private void calculateKgForTravel(WaAnalyze analyze, EmpShiftInfo shiftDef, List<WaEmpTravelDaytimeDo> empTravelInfos, List<WaEmpTravelDaytimeDo> empTravelInfos2, WaAnalyzCalDTO dto) {
        if (shiftDef != null && shiftDef.getDateType() != null && shiftDef.getDateType() == 1) {
            analyze.setShiftDefId(shiftDef.getShiftDefId());
            if (analyze.getKgWorkTime() != null && analyze.getKgWorkTime() > 0) {
                //如果有旷工小时，则和请假小时进行抵消
                //获取实际出差时长
                Float leaveTime = getRealTravelTime(empTravelInfos, empTravelInfos2);
                /*List<WaEmpTravelDaytimeDo> allEmpTravelInfos = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(empTravelInfos)) {
                    allEmpTravelInfos.addAll(empTravelInfos);
                }
                if (CollectionUtils.isNotEmpty(empTravelInfos2)) {
                    allEmpTravelInfos.addAll(empTravelInfos2);
                }
                Float leaveTime = getValidTravelDuration(analyze.getBelongDate(), shiftDef, allEmpTravelInfos);*/
                if (leaveTime > 0) {
                    Map<String, Float> travelDurationMap = dto.getTravelDurationMap();
                    String key = String.format("%s_%s", analyze.getEmpid(), analyze.getBelongDate());
                    if (travelDurationMap.containsKey(key)) {
                        travelDurationMap.put(key, travelDurationMap.get(key) + leaveTime);
                    } else {
                        travelDurationMap.put(key, leaveTime);
                    }
                }
                BigDecimal leaveDec = new BigDecimal(leaveTime);
                BigDecimal kgDec = new BigDecimal(analyze.getKgWorkTime());
                BigDecimal diff = kgDec.subtract(leaveDec);
                if (diff.doubleValue() > 0) {
                    if (analyze.getWorkTime() == null) {
                        analyze.setWorkTime(shiftDef.getWorkTotalTime());
                    }
                    analyze.setIsKg(1);
                    analyze.setKgWorkTime(diff.intValue());
                } else {
                    analyze.setIsKg(0);
                    analyze.setKgWorkTime(0);
                }
            } else {
                Float leaveTime = getRealTravelTime(empTravelInfos, empTravelInfos2);
                if (leaveTime > 0) {
                    Map<String, Float> travelDurationMap = dto.getTravelDurationMap();
                    String key = String.format("%s_%s", analyze.getEmpid(), analyze.getBelongDate());
                    if (travelDurationMap.containsKey(key)) {
                        travelDurationMap.put(key, travelDurationMap.get(key) + leaveTime);
                    } else {
                        travelDurationMap.put(key, leaveTime);
                    }
                }
            }
        }
    }

    /**
     * 旷工时长抵扣逻辑2
     *
     * @param wa
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @param travelDaytimeList
     * @param shiftDef
     * @param dto
     */
    private void cancelKgRecord(WaAnalyze wa, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2,
                                List<WaLeaveDaytimeExtDto> travelDaytimeList, EmpShiftInfo shiftDef, WaAnalyzCalDTO dto) {
        if ((wa.getIsKg() != null && wa.getIsKg() == 1) || (wa.getKgWorkTime() != null && wa.getKgWorkTime() > 0)) {
            //获取实际休假时长
            Float leaveTime = 0f;
            if (CollectionUtils.isNotEmpty(travelDaytimeList)) {
                List<EmpLeaveInfo> allEmpLeaveInfos = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
                    allEmpLeaveInfos.addAll(empLeaveInfos);
                }
                if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
                    allEmpLeaveInfos.addAll(empLeaveInfos2);
                }
                allEmpLeaveInfos = allEmpLeaveInfos.stream().filter(l -> l.getStatus() == 2).collect(Collectors.toList());
                leaveTime = getValidLeaveDuration(wa.getEmpid(), wa.getBelongDate(), shiftDef, allEmpLeaveInfos, dto, travelDaytimeList);
            } else {
                leaveTime = getRealLeaveTime(empLeaveInfos, empLeaveInfos2);
            }

            BigDecimal leaveDec = new BigDecimal(leaveTime);
            BigDecimal kgDec = new BigDecimal(wa.getKgWorkTime());
            BigDecimal diff = kgDec.subtract(leaveDec);
            if (diff.doubleValue() > 0) {
                wa.setIsKg(1);
                wa.setKgWorkTime(diff.intValue());
            } else {
                wa.setIsKg(0);
                wa.setKgWorkTime(0);
            }
        }
    }

    /**
     * 旷工时长抵扣逻辑2
     *
     * @param wa
     * @param empLeaveInfos
     * @param empLeaveInfos2
     */
    private void cancelKgRecordForTravel(WaAnalyze wa, List<WaEmpTravelDaytimeDo> empLeaveInfos, List<WaEmpTravelDaytimeDo> empLeaveInfos2) {
        if ((wa.getIsKg() != null && wa.getIsKg() == 1) || (wa.getKgWorkTime() != null && wa.getKgWorkTime() > 0)) {
            //获取实际休假时长
            Float leaveTime = getRealTravelTime(empLeaveInfos, empLeaveInfos2);
            BigDecimal leaveDec = new BigDecimal(leaveTime);
            BigDecimal kgDec = new BigDecimal(wa.getKgWorkTime());
            BigDecimal diff = kgDec.subtract(leaveDec);
            if (diff.doubleValue() > 0) {
                wa.setIsKg(1);
                wa.setKgWorkTime(diff.intValue());
            } else {
                wa.setIsKg(0);
                wa.setKgWorkTime(0);
            }
        }
    }

    @Deprecated
    private Map<String, Object> calculateOriginLevel(WaAnalyzCalDTO dto, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        //TODO 考勤分析-休假分析新逻辑开启配置：true 开启 false 不开启
        if (ltanalyze) {
            return calculateOriginLevel2(dto, empLeaveInfos, empLeaveInfos2);
        }
        //TODO 以下逻辑为老的分析逻辑，暂时保留，上线后删除
        Map<String, Object> map = new HashMap<String, Object>();
        List<Integer> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (EmpLeaveInfo lf : empLeaveInfos) {
                if (lf.getLeave_date().equals(lf.getReal_date())) {

                    Float time_duration = lf.getTime_duration();
                    if (time_duration == null) time_duration = 0f;
                    Integer time_unit = lf.getTime_unit();
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        time_duration = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
                    String key1 = "lt_" + lf.getLeave_type_id() + "_key";
                    if (map.containsKey(key1)) {
                        Float f = (Float) map.get("time_duration");
                        Float leaveCount = (Float) map.get(key1);
                        map.put(key1, leaveCount + time_duration);
                        map.put("time_duration", f + time_duration);
                    } else {
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_key", time_duration);
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_name", leaveType.getLeaveName());
                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + time_duration);
                    }
                    levelIds.add(lf.getLeave_id());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (EmpLeaveInfo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getLeave_id())) {
                    Float time_duration = lf.getTime_duration();
                    if (time_duration == null) {
                        time_duration = 0f;
                    }
                    Integer time_unit = lf.getTime_unit();
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        time_duration = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
                    String key1 = "lt_" + lf.getLeave_type_id() + "_key";
                    if (map.containsKey(key1)) {
                        Float f = (Float) map.get("time_duration");
                        Float leaveCount = (Float) map.get(key1);
                        map.put(key1, leaveCount + time_duration);
                        map.put("time_duration", f + time_duration);
                    } else {
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_key", time_duration);
                        map.put("lt_" + leaveType.getLeaveTypeId() + "_name", leaveType.getLeaveName());
                        //map.put("lt_"+leaveType.getLeaveTypeId()+"_date", lf.getStart_time());
                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + time_duration);
                    }
                }
            }
        }
        return map;
    }

    @Deprecated
    private Map<String, Object> calculateOriginTravel(WaAnalyzCalDTO dto, List<WaEmpTravelDaytimeDo> empTravelInfos, List<WaEmpTravelDaytimeDo> empTravelInfos2) {
        Map<String, Object> map = new HashMap<>();
        List<Long> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empTravelInfos)) {
            for (WaEmpTravelDaytimeDo lf : empTravelInfos) {
                if (lf.getTravelDate().equals(lf.getRealDate())) {
                    Float originalTimeDuration = lf.getTimeDuration();
                    originalTimeDuration = originalTimeDuration == null ? 0 : originalTimeDuration;

                    Integer time_unit = Integer.valueOf(lf.getTimeUnit());
                    map.put("lt_" + lf.getTravelTypeId() + "_key_unit", time_unit);

                    Float timeDurationMinute = originalTimeDuration;
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        timeDurationMinute = getLeaveHour(lf.getEmpId(), lf.getTravelDate(), originalTimeDuration, workTotalTime);
                    }

                    WaTravelTypeDo leaveType = dto.getTravelTypeMaps().get(lf.getTravelTypeId());

                    String ltKey = "lt_" + lf.getTravelTypeId() + "_key";
                    String ltMinuteKey = "lt_" + lf.getTravelTypeId() + "_key_minute";

                    if (map.containsKey(ltMinuteKey)) {
                        Float ltTime = (Float) map.get(ltKey);
                        map.put(ltMinuteKey, ltTime + originalTimeDuration);

                        Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                        map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                        Float f = (Float) map.get("time_duration");
                        map.put("time_duration", f + timeDurationMinute);
                    } else {
                        map.put(ltKey, originalTimeDuration);
                        map.put(ltMinuteKey, timeDurationMinute);
                        map.put("lt_" + leaveType.getTravelTypeId() + "_name", leaveType.getTravelTypeName());

                        Float f = (Float) map.get("time_duration");
                        f = f == null ? 0f : f;
                        map.put("time_duration", f + timeDurationMinute);
                    }
                    levelIds.add(lf.getTravelId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empTravelInfos2)) {
            for (WaEmpTravelDaytimeDo lf : empTravelInfos2) {
                if (!levelIds.contains(lf.getTravelId())) {
                    Float originalTimeDuration = lf.getTimeDuration();
                    originalTimeDuration = originalTimeDuration == null ? 0f : originalTimeDuration;

                    Integer time_unit = Integer.valueOf(lf.getTimeUnit());

                    map.put("lt_" + lf.getTravelTypeId() + "_key_unit", time_unit);

                    Float timeDurationMinute = originalTimeDuration;
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        timeDurationMinute = getLeaveHour(lf.getEmpId(), lf.getTravelDate(), originalTimeDuration, workTotalTime);
                    }
                    WaTravelTypeDo leaveType = dto.getTravelTypeMaps().get(lf.getTravelTypeId());

                    String ltKey = "lt_" + lf.getTravelTypeId() + "_key";
                    String ltMinuteKey = "lt_" + lf.getTravelTypeId() + "_key_minute";

                    if (map.containsKey(ltMinuteKey)) {
                        Float ltTime = (Float) map.get(ltKey);
                        map.put(ltMinuteKey, ltTime + originalTimeDuration);

                        Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                        map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                        Float f = (Float) map.get("time_duration");
                        map.put("time_duration", f + timeDurationMinute);
                    } else {
                        map.put(ltKey, originalTimeDuration);
                        map.put(ltMinuteKey, timeDurationMinute);
                        map.put("lt_" + leaveType.getTravelTypeId() + "_name", leaveType.getTravelTypeName());

                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + timeDurationMinute);
                    }
                }
            }
        }
        if (MapUtils.isNotEmpty(map)) {
            //设置出差单有效状态
            map.put("valid_status", ValidStatusEnum.VALID.getIndex());
        }
        return map;
    }

    /**
     * 计算原始的休假时长
     *
     * @param dto
     * @param empLeaveInfos
     * @param empLeaveInfos2
     * @return
     */
    private Map<String, Object> calculateOriginLevel2(WaAnalyzCalDTO dto, List<EmpLeaveInfo> empLeaveInfos, List<EmpLeaveInfo> empLeaveInfos2) {
        Map<String, Object> map = new HashMap<>();
        List<Integer> levelIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empLeaveInfos)) {
            for (EmpLeaveInfo lf : empLeaveInfos) {
                if (lf.getLeave_date().equals(lf.getReal_date())) {
                    Float originalTimeDuration = lf.getTime_duration();
                    if (originalTimeDuration == null) {
                        originalTimeDuration = 0f;
                    }
                    Integer time_unit = lf.getTime_unit();
                    map.put("lt_" + lf.getLeave_type() + "_key_unit", time_unit);

                    Float timeDurationMinute = originalTimeDuration;
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        timeDurationMinute = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), originalTimeDuration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());

                    String ltKey = "lt_" + lf.getLeave_type() + "_key";
                    String ltMinuteKey = "lt_" + lf.getLeave_type() + "_key_minute";

                    if (map.containsKey(ltMinuteKey)) {
                        Float ltTime = (Float) map.get(ltKey);
                        map.put(ltMinuteKey, ltTime + originalTimeDuration);

                        Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                        map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                        Float f = (Float) map.get("time_duration");
                        map.put("time_duration", f + timeDurationMinute);
                    } else {
                        map.put(ltKey, originalTimeDuration);
                        map.put(ltMinuteKey, timeDurationMinute);
                        map.put("lt_" + leaveType.getLeaveType() + "_name", leaveType.getLeaveName());

                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + timeDurationMinute);
                    }
                    levelIds.add(lf.getLeave_id());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(empLeaveInfos2)) {
            for (EmpLeaveInfo lf : empLeaveInfos2) {
                if (!levelIds.contains(lf.getLeave_id())) {
                    Float originalTimeDuration = lf.getTime_duration();
                    if (originalTimeDuration == null) {
                        originalTimeDuration = 0f;
                    }
                    Integer time_unit = lf.getTime_unit();

                    map.put("lt_" + lf.getLeave_type() + "_key_unit", time_unit);

                    Float timeDurationMinute = originalTimeDuration;
                    if (time_unit == 1) {
                        Integer workTotalTime = getWorkTotalTime(lf);
                        timeDurationMinute = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), originalTimeDuration, workTotalTime);
                    }
                    WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());

                    String ltKey = "lt_" + lf.getLeave_type() + "_key";
                    String ltMinuteKey = "lt_" + lf.getLeave_type() + "_key_minute";

                    if (map.containsKey(ltMinuteKey)) {
                        Float ltTime = (Float) map.get(ltKey);
                        map.put(ltMinuteKey, ltTime + originalTimeDuration);

                        Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                        map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                        Float f = (Float) map.get("time_duration");
                        map.put("time_duration", f + timeDurationMinute);
                    } else {
                        map.put(ltKey, originalTimeDuration);
                        map.put(ltMinuteKey, timeDurationMinute);
                        map.put("lt_" + leaveType.getLeaveType() + "_name", leaveType.getLeaveName());

                        Float f = (Float) map.get("time_duration");
                        if (f == null) {
                            f = 0f;
                        }
                        map.put("time_duration", f + timeDurationMinute);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 统计请假记录
     *
     * @param leaveInfos           请假记录
     * @param dto
     * @param isApprovalLeaveAfter 是否把请假小时记录在这一天，并且在审批时间大于请假日期时。如果 true 比较审批时间和申请时间
     * @return
     */
    @Deprecated
    private Map getLevelColumnJson(List<EmpLeaveInfo> leaveInfos, WaAnalyzCalDTO dto, boolean isApprovalLeaveAfter) {
        //TODO 考勤分析-休假分析新逻辑开启配置：true 开启 false 不开启
        if (ltanalyze) {
            return getLevelColumnJson2(leaveInfos, dto, isApprovalLeaveAfter);
        }
        //TODO 以下逻辑为老的分析逻辑，暂时保留，上线后删除
        Map<String, Object> map = new HashMap<String, Object>();

        if (leaveInfos != null && leaveInfos.size() > 0) {
            for (EmpLeaveInfo lf : leaveInfos) {
                //审批时间大于请假时间 不记录请假记录
                if (isApprovalLeaveAfter) {
                    if (!(lf.getReal_date().equals(lf.getLeave_date()))) {
                        continue;
                    }
                }
                Float time_duration = lf.getTime_duration();
                if (time_duration == null) {
                    time_duration = 0f;
                }
                Integer time_unit = lf.getTime_unit();
                if (time_unit == 1) {
                    Integer workTotalTime = getWorkTotalTime(lf);
                    time_duration = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), time_duration, workTotalTime);
                }
                WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
                if (leaveType == null) {
                    log.info("假期类型不存在 ::ID=" + lf.getLeave_type_id());
                    continue;
                }
                String key1 = "lt_" + lf.getLeave_type_id() + "_key";
                if (map.containsKey(key1)) {
                    Float f = (Float) map.get("time_duration");
                    Float leaveCount = (Float) map.get(key1);
                    map.put(key1, leaveCount + time_duration);
                    map.put("time_duration", f + time_duration);
                } else {
                    map.put("lt_" + leaveType.getLeaveTypeId() + "_key", time_duration);
                    map.put("lt_" + leaveType.getLeaveTypeId() + "_name", leaveType.getLeaveName());
                    //map.put("lt_"+leaveType.getLeaveTypeId()+"_date", lf.getStart_time());
                    Float f = (Float) map.get("time_duration");
                    if (f == null) {
                        f = 0f;
                    }
                    map.put("time_duration", f + time_duration);
                }
            }
        }
        return map;
    }

    /**
     * 统计出差记录
     *
     * @param travelInfos
     * @param dto
     * @param isApprovlLeaveAfter 是否把请假小时记录在这一天，并且在审批时间大于请假日期时。如果 true 比较审批时间和申请时间
     * @return
     */
    private Map getTravelColumnJson(List<WaEmpTravelDaytimeDo> travelInfos, WaAnalyzCalDTO dto, boolean isApprovlLeaveAfter) {
        Map<String, Object> map = new HashMap<>();

        if (CollectionUtils.isEmpty(travelInfos)) {
            return map;
        }

        for (WaEmpTravelDaytimeDo lf : travelInfos) {
            //审批时间大于请假时间 不记录请假记录
            if (isApprovlLeaveAfter) {
                if (!(lf.getRealDate().equals(lf.getTravelDate()))) {
                    continue;
                }
            }

            WaTravelTypeDo travelType = dto.getTravelTypeMaps().get(lf.getTravelTypeId());
            if (travelType == null) {
                log.info("出差类型不存在 ::ID=" + lf.getTravelTypeId());
                continue;
            }

            Float originalTimeDuration = lf.getTimeDuration();
            originalTimeDuration = originalTimeDuration == null ? 0 : originalTimeDuration;

            //休假时长（分钟）
            Float timeDurationMinute = originalTimeDuration;
            Integer time_unit = Integer.valueOf(lf.getTimeUnit());

            map.put("lt_" + lf.getTravelTypeId() + "_key_unit", time_unit);

            if (time_unit == 1) {
                Integer workTotalTime = getWorkTotalTime(lf);
                timeDurationMinute = getLeaveHour(lf.getEmpId(), lf.getTravelDate(), originalTimeDuration, workTotalTime);
            }

            String ltKey = "lt_" + lf.getTravelTypeId() + "_key";
            String ltMinuteKey = "lt_" + lf.getTravelTypeId() + "_key_minute";

            if (map.containsKey(ltMinuteKey)) {
                Float ltTime = (Float) map.get(ltKey);
                map.put(ltKey, ltTime + originalTimeDuration);

                Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                Float f = (Float) map.get("time_duration");
                map.put("time_duration", f + timeDurationMinute);
            } else {
                map.put(ltKey, originalTimeDuration);
                map.put(ltMinuteKey, timeDurationMinute);
                map.put("lt_" + travelType.getTravelTypeId() + "_name", travelType.getTravelTypeName());

                Float f = (Float) map.get("time_duration");
                if (f == null) {
                    f = 0f;
                }
                map.put("time_duration", f + timeDurationMinute);
            }
        }
        if (MapUtils.isNotEmpty(map)) {
            //设置出差单有效状态
            map.put("valid_status", ValidStatusEnum.VALID.getIndex());
        }
        return map;
    }

    /**
     * 统计请假记录
     *
     * @param leaveInfos           请假记录
     * @param dto
     * @param isApprovalLeaveAfter 是否把请假小时记录在这一天，并且在审批时间大于请假日期时。如果 true 比较审批时间和申请时间
     * @return
     */
    private Map getLevelColumnJson2(List<EmpLeaveInfo> leaveInfos, WaAnalyzCalDTO dto, boolean isApprovalLeaveAfter) {
        Map<String, Object> map = new HashMap<>();

        if (CollectionUtils.isEmpty(leaveInfos)) {
            return map;
        }

        for (EmpLeaveInfo lf : leaveInfos) {
            //审批时间大于请假时间 不记录请假记录
            if (isApprovalLeaveAfter) {
                if (!(Objects.equals(lf.getReal_date(), lf.getLeave_date()))) {
                    continue;
                }
            }

            WaLeaveType leaveType = dto.getLtMaps().get(lf.getLeave_type_id());
            if (leaveType == null) {
                log.info("假期类型不存在 ::ID=" + lf.getLeave_type_id());
                continue;
            }

            Float originalTimeDuration = lf.getTime_duration();
            if (originalTimeDuration == null) {
                originalTimeDuration = 0f;
            }

            //休假时长（分钟）
            Float timeDurationMinute = originalTimeDuration;
            Integer time_unit = lf.getTime_unit();

            map.put("lt_" + lf.getLeave_type() + "_key_unit", time_unit);

            if (time_unit == 1) {
                Integer workTotalTime = getWorkTotalTime(lf);
                timeDurationMinute = getLeaveHour(lf.getEmpid(), lf.getLeave_date(), originalTimeDuration, workTotalTime);
            }

            String ltKey = "lt_" + lf.getLeave_type() + "_key";
            String ltMinuteKey = "lt_" + lf.getLeave_type() + "_key_minute";

            if (map.containsKey(ltMinuteKey)) {
                Float ltTime = (Float) map.get(ltKey);
                map.put(ltKey, ltTime + originalTimeDuration);

                Float ltTimeMinute = (Float) map.get(ltMinuteKey);
                map.put(ltMinuteKey, ltTimeMinute + timeDurationMinute);

                Float f = (Float) map.get("time_duration");
                map.put("time_duration", f + timeDurationMinute);
            } else {
                map.put(ltKey, originalTimeDuration);
                map.put(ltMinuteKey, timeDurationMinute);
                map.put("lt_" + leaveType.getLeaveType() + "_name", leaveType.getLeaveName());

                Float f = (Float) map.get("time_duration");
                if (f == null) {
                    f = 0f;
                }
                map.put("time_duration", f + timeDurationMinute);
            }
        }
        return map;
    }

    private Integer getWorkTotalTime(EmpLeaveInfo lf) {
        Integer workTotalTime = null;
        EmpShiftInfo empShiftInfo = lf.getEmpShiftInfo();
        Boolean isSpecial = false;//特殊班次工时调整
        if (empShiftInfo != null && empShiftInfo.getIsSpecial() != null && empShiftInfo.getIsSpecial() && empShiftInfo.getSpecialWorkTime() != null) {
            isSpecial = true;
        }

        if (lf.getEmpShiftInfo() != null) {
            if (isSpecial) {
                workTotalTime = lf.getEmpShiftInfo().getSpecialWorkTime();
            } else {
                workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();
            }
        }
        // 当是休息日的连续请假时，工作时长目前固定是8小时
        if (lf.getDate_type() != null && lf.getDate_type() == 2 && (workTotalTime == null || workTotalTime == 0)) {
            if (isSpecial) {
                workTotalTime = lf.getEmpShiftInfo().getSpecialWorkTime();
            } else {
                workTotalTime = 480;
            }
        }
        return workTotalTime;
    }

    private Integer getWorkTotalTime(WaEmpTravelDaytimeDo lf) {
        Integer workTotalTime = null;
        EmpShiftInfo empShiftInfo = lf.getEmpShiftInfo();
        Boolean isSpecial = false;//特殊班次工时调整
        if (empShiftInfo != null && empShiftInfo.getIsSpecial() != null && empShiftInfo.getIsSpecial() && empShiftInfo.getSpecialWorkTime() != null) {
            isSpecial = true;
        }

        if (lf.getEmpShiftInfo() != null) {
            if (isSpecial) {
                workTotalTime = lf.getEmpShiftInfo().getSpecialWorkTime();
            } else {
                workTotalTime = lf.getEmpShiftInfo().getWorkTotalTime();
            }
        }
        // 当是休息日的连续请假时，工作时长目前固定是8小时
        if (lf.getDateType() != null && lf.getDateType() == 2 && (workTotalTime == null || workTotalTime == 0)) {
            if (isSpecial) {
                workTotalTime = lf.getEmpShiftInfo().getSpecialWorkTime();
            } else {
                workTotalTime = 480;
            }
        }
        return workTotalTime;
    }

    /**
     * 查询请假所对应的班次,然后根据上班总小时，把天换算成小时数
     *
     * @param empid
     * @param leaveDate
     * @param time_duration
     * @return
     */
    private Float getLeaveHour(Long empid, Long leaveDate,
                               Float time_duration, Integer workTotalTime) {
        if (workTotalTime != null) {
            Float hourWork = workTotalTime * time_duration;
            return hourWork;
        } else {
            Map<String, Object> map = waEmpLeaveMapper.getWorkTimeDetail(empid, leaveDate);
            if (map != null && map.containsKey("work_total_time")) {
                Integer workTotalTime2 = (Integer) map.get("work_total_time");
                if (workTotalTime2 != null) {
                    Float hourWork = Float.valueOf(String.valueOf(workTotalTime2)) * time_duration;
                    return hourWork;
                }
            }
        }
        return 0f;
    }

    /**
     * 分析加班数据
     *
     * @param resultWa
     * @param params
     * @param dto
     */
    public void analyzeOverTimeData(List<WaAnalyze> resultWa, Map<String, Object> params, WaAnalyzCalDTO dto) throws Exception {
        if (dto == null) {
            dto = new WaAnalyzCalDTO();
        }
        String belongId = (String) params.get("belongid");
        List<Integer> approvalStatusList = Lists.newArrayList(2);
        if (waanalyzeAll) {
            approvalStatusList.add(1);
        }
        params.put("approvalStatusList", approvalStatusList);
        if (CollectionUtils.isNotEmpty(resultWa)) {
            List<Map> empOverList = otRecordMapper.getEmpOverTimeByEmpid(params);
            Map<String, List<EmpOverInfo>> empOverAfterMaps = new HashMap<>();
            Map<String, List<EmpOverInfo>> overListMaps = getEmpOvers(empOverList, empOverAfterMaps);
            Map<String, Object> orginOtMap;
            for (WaAnalyze wa : resultWa) {
                String key = wa.getEmpid() + "_" + wa.getBelongDate();
                if (overListMaps.containsKey(key)) {
                    WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(wa.getEmpid(), wa.getBelongDate());
                    if (analyzeInfo != null) {
                        analyzeInfo.setOtMaps(dto.getOtMaps());
                    }
                    EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(wa.getEmpid(), null, wa.getBelongDate(), dto);
                    List<EmpOverInfo> ofs = overListMaps.get(key);
                    orginOtMap = getOtColumnJsonB(ofs, analyzeInfo, wa, params, empShiftDef, dto);
                    wa.setOriginOtColumnJsonb(orginOtMap);
                    Map<String, Object> overMap = processEmpOver(wa, analyzeInfo, overListMaps, belongId, empShiftDef, dto);
                    if (overMap.size() == 0) {
                        wa.setOtColumnJsob(orginOtMap);
                    } else {
                        wa.setOtColumnJsob(overMap);
                    }
                    overListMaps.remove(key);
                }
            }
            overtimeAnalyze(resultWa, dto, overListMaps, params);
            overtimeAnalyze(resultWa, dto, empOverAfterMaps, params);
        } else {
            List<Map> empOverList = otRecordMapper.getEmpOverTimeByEmpid(params);
            Map<String, List<EmpOverInfo>> empOverAfterMaps = new HashMap<>();
            Map<String, List<EmpOverInfo>> overListMaps = getEmpOvers(empOverList, empOverAfterMaps);
            overtimeAnalyze(resultWa, dto, overListMaps, params);
            overtimeAnalyze(resultWa, dto, empOverAfterMaps, params);
        }
    }

    private void overtimeAnalyze(List<WaAnalyze> resultWa, WaAnalyzCalDTO dto, Map<String, List<EmpOverInfo>> empOverMaps, Map<String, Object> params) throws Exception {
        if (empOverMaps != null && empOverMaps.size() > 0) {
            WaAnalyze analyze = null;
            for (Map.Entry<String, List<EmpOverInfo>> entry : empOverMaps.entrySet()) {
                String key = entry.getKey();
                Long empid = Long.valueOf(key.split("_")[0]);
                Long overDate = Long.valueOf(key.split("_")[1]);
                WaAnalyzInfo analyzeInfo = dto.getEmpWaAnalyz(empid, overDate);
                analyzeInfo.setOtMaps(dto.getOtMaps());
                // 查找是否已有存在的分析数据
                analyze = existsWaAnalyze(resultWa, empid, overDate);
                if (analyze != null) {
                    EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(analyze.getEmpid(), null, analyze.getBelongDate(), dto);
                    Map<String, Object> otJsonbMap = getOtColumnJsonB(entry.getValue(), analyzeInfo, analyze, params, empShiftDef, dto);
                    Object otJsonb = mergeOtMap(analyze, otJsonbMap);
                    // 保存原始加班单数据
                    analyze.setOriginOtColumnJsonb(otJsonb);
                    // 保存分析后的加班数据
                    analyze.setOtColumnJsob(otJsonb);
                    if (empShiftDef != null && analyze.getWorkTime() == null) {
                        analyze.setWorkTime(empShiftDef.getWorkTotalTime());
                    }
                } else {
                    analyze = new WaAnalyze();
                    analyze.setEmpid(empid);
                    analyze.setBelongDate(overDate);
                    EmpShiftInfo empShiftDef = this.getEmpShiftDefByInfo(analyze.getEmpid(), analyze.getShiftDefId(), analyze.getBelongDate(), dto);
                    Map otJsonb = getOtColumnJsonB(entry.getValue(), analyzeInfo, analyze, params, empShiftDef, dto);
                    // 保存原始加班单数据
                    analyze.setOriginOtColumnJsonb(otJsonb);
                    if (empShiftDef != null) {
                        analyze.setWorkTime(empShiftDef.getWorkTotalTime());
                        analyze.setShiftDefId(empShiftDef.getShiftDefId());
                        if (DateTypeEnum.DATE_TYP_1.getIndex().equals(empShiftDef.getDateType())) {
                            // add 20180706  场景：没有签到签退，请假记录，有加班单的情况，记录是否旷工
                            WaAnalyzInfo analyzeConfig = dto.getEmpWaAnalyz(empid, analyze.getBelongDate());
                            this.missRegCalKg(analyzeInfo, analyze, empShiftDef);
                            if (analyzeConfig.getClock_type() == 3) {
                                analyze.setActualWorkTime(empShiftDef.getWorkTotalTime().floatValue());
                            }
                        }
                    }
                    analyze.setOtColumnJsob(otJsonb);
                    resultWa.add(analyze);
                }
            }
        }
    }

    private Object mergeOtMap(WaAnalyze an, Map<String, Object> otJsonbMap) throws Exception {
        Map<String, Object> originMap = null;
        if (an.getOtColumnJsob() == null) {
            return otJsonbMap;
        } else {
            if (otJsonbMap == null) {
                return an.getOtColumnJsob();
            }
            Object obj = an.getOtColumnJsob();
            if (obj instanceof Map) {
                originMap = (Map) obj;
            } else if (obj instanceof PGobject) {
                PGobject pg = (PGobject) obj;
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                String json = pg.getValue().toString();
                try {
                    originMap = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
                    });
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw e;
                }
            }
            if (originMap == null) {
                originMap = new HashMap<>();
            }
            // 把加班记录合并原先的map里
            for (Map.Entry<String, Object> entry : otJsonbMap.entrySet()) {
                String key = entry.getKey();
                if (key.contains("key") && originMap.containsKey(key)) {
                    Integer f = (Integer) originMap.get(key);
                    if (f == null) {
                        f = 0;
                    }
                    Integer value = (Integer) entry.getValue();
                    if (value == null) {
                        value = 0;
                    }
                    originMap.put(key, value + f);
                } else {
                    originMap.put(key, entry.getValue());
                }
            }
        }
        return originMap;
    }

    @SuppressWarnings("rawtypes")
    private Map<String, List<EmpOverInfo>> getEmpOvers(List<Map> empOverList, Map<String, List<EmpOverInfo>> empOverAfterMaps) {
        Map<String, List<EmpOverInfo>> overListMaps = new HashMap<String, List<EmpOverInfo>>();
        if (empOverList != null && empOverList.size() > 0) {
            Map<Object, List<Map>> overtimeMap = empOverList.stream().collect(Collectors.groupingBy(row -> row.get("ot_id")));
            overtimeMap.forEach((otId, otList) -> {
                if (otList.size() == 2) {
                    otList = otList.stream().sorted(Comparator.comparing(ot -> (Long) ot.get("end_time"))).collect(Collectors.toList());
                    Map ot = otList.get(1);
                    ot.put("zeroSplitting", true);
                }
            });
            ObjectMapper objectMapper = new ObjectMapper();
            for (Map<String, Object> empOver : empOverList) {
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                String json = null;
                EmpOverInfo of = null;
                try {
                    json = objectMapper.writeValueAsString(empOver);
                    of = objectMapper.readValue(json, new TypeReference<EmpOverInfo>() {
                    });
                } catch (Exception e) {
                    log.error("convert exception：{}", e.getMessage(), e);
                }
                Long empid = of.getEmpid();
                // date_type 1、工作日，2休息日，3法定假日
                Long real_date = of.getReal_date();
                Long over_date = of.getBelongDate();//加班单据申请日期
                over_date = DateUtil.getDateLong(over_date * 1000, "yyyy-MM-dd", true);
                real_date = DateUtil.getDateLong(real_date * 1000, "yyyy-MM-dd", true);
                of.setReal_date(real_date);

                // 1.如果最后审批完成时间大于加班期
                String key = empid + "_" + real_date;
                if (real_date > over_date) { // 如加班事件开始时间小于应归属时间 会包含跨夜加班的数据
                    String key1 = empid + "_" + real_date;
                    if (empOverAfterMaps.containsKey(key1)) {
                        empOverAfterMaps.get(key1).add(of);
                    } else {
                        List<EmpOverInfo> list = new ArrayList<EmpOverInfo>();
                        list.add(of);
                        empOverAfterMaps.put(key1, list);
                    }
                } else {
                    if (overListMaps.containsKey(key)) {
                        overListMaps.get(key).add(of);
                    } else {
                        List<EmpOverInfo> list = new ArrayList<EmpOverInfo>();
                        list.add(of);
                        overListMaps.put(key, list);
                    }
                }
            }
        }
        return overListMaps;
    }

    /**
     * 获取加班数据， 如果审批时间在申请时间之后的情况，要查询申请时间上的签到签退数据来分析有效的加班小时数 只有申请时间在核算区间之外的才需要查询
     *
     * @param otlist
     * @param analyzeInfo
     * @param waAnalyze
     * @param params
     * @param empShiftDef
     * @param dto
     * @return
     * @throws Exception
     */
    private Map<String, Object> getOtColumnJsonB(List<EmpOverInfo> otlist, WaAnalyzInfo analyzeInfo, WaAnalyze waAnalyze,
                                                 Map<String, Object> params, EmpShiftInfo empShiftDef, WaAnalyzCalDTO dto) throws Exception {
        Map<String, Object> otMap = new HashMap<>();
        if (CollectionUtils.isEmpty(otlist)) {
            return otMap;
        }
        String belongId = (String) params.get("belongid");

        Map<String, Float> relOtTimeDurationMap = dto.getRelOtTimeDurationMap();
        if (relOtTimeDurationMap == null) {
            relOtTimeDurationMap = new HashMap<>();
        }
        Map<String, String> transferMap = dto.getTransferMap();
        if (transferMap == null) {
            transferMap = new HashMap<>();
        }

        if (analyzeInfo != null && BooleanUtils.isTrue(analyzeInfo.getOt_sum_parse())) {
            doOtSumParse(otlist, analyzeInfo, waAnalyze, belongId, empShiftDef, dto,
                    relOtTimeDurationMap, transferMap, otMap);
        } else {
            for (EmpOverInfo of : otlist) {
                of.setBelongid(belongId);
                Integer dateType = of.getDate_type();
                Integer compensateType = of.getCompensate_type();
                if (dateType == null || compensateType == null) {
                    continue;
                }
                calOtByParseRule(of, waAnalyze, analyzeInfo, belongId, empShiftDef, dto, otMap, relOtTimeDurationMap, 1, transferMap);
            }
        }
        dto.setTransferMap(transferMap);
        dto.setRelOtTimeDurationMap(relOtTimeDurationMap);
        return otMap;
    }

    /**
     * 加班单日汇总分析（2.0 客户不再使用）
     *
     * @param otlist
     * @param analyzeInfo
     * @param waAnalyze
     * @param belongId
     * @param empShiftDef
     * @param dto
     * @param relOtTimeDurationMap
     * @param transferMap
     * @param otMap
     * @throws Exception
     */
    @Deprecated
    public void doOtSumParse(List<EmpOverInfo> otlist,
                             WaAnalyzInfo analyzeInfo,
                             WaAnalyze waAnalyze,
                             String belongId,
                             EmpShiftInfo empShiftDef,
                             WaAnalyzCalDTO dto,
                             Map<String, Float> relOtTimeDurationMap,
                             Map<String, String> transferMap,
                             Map<String, Object> otMap) throws Exception {
        WaSob waSob = dto.getWaSob();

        //根据考勤周期拆分加班单据，每个考勤周期的加班单据单独分析，最后分析结果合并在一起
        //判读逻辑：事件日期<考勤开始日期 && 实际加班日期在本次核算范围内（帐套开始日期<=审批日期<=帐套截止日期）
        //考勤开始日期：如果考勤帐套有值，取考勤帐套开始日期，反之：根据日期+考勤分组去反推考勤开始日期
        List<EmpOverInfo> otherPeriodOtList = new ArrayList<>();//其他考勤周期的加班单据（eg:事件日期是上月&审批日期是本月）
        List<EmpOverInfo> currentPeriodOtList = this.getOtListByCycle(otlist, otherPeriodOtList, analyzeInfo, waSob);//当前计算周期的加班单据
        //当前计算周期的加班单据分析
        if (CollectionUtils.isNotEmpty(currentPeriodOtList)) {
            Map<String, Object> currentPeriodOtMap = new HashMap<>();
            for (EmpOverInfo of : currentPeriodOtList) {
                of.setBelongid(belongId);
                //1、工作日，2休息日，3法定假日,4特殊日期
                Integer dateType = of.getDate_type();
                Integer compensateType = of.getCompensate_type();
                if (null == compensateType || dateType == null) {
                    continue;
                }
                calOtByParseRule(of, waAnalyze, analyzeInfo, belongId, empShiftDef, dto, currentPeriodOtMap, relOtTimeDurationMap, 2, transferMap);
            }
            if (MapUtils.isNotEmpty(currentPeriodOtMap)) {
                //根据加班分析规则计算加班时长
                final Float[] totalDuration = {0f};//加班总时长
                currentPeriodOtMap.forEach((key, v) -> {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    if (key.contains("_key")) {
                        Float duration = Float.valueOf(v.toString());
                        String[] keyArray = key.split("_");
                        Integer dateType = Integer.valueOf(keyArray[1]);
                        Integer compensateType = Integer.valueOf(keyArray[2]);
                        duration = analyzeOtRule(duration, analyzeInfo, dateType, compensateType);
                        totalDuration[0] += duration;
                        currentPeriodOtMap.put(key, duration);
                    }
                });
                currentPeriodOtMap.put("time_duration", totalDuration[0]);
                //数据组合
                otMap.putAll(currentPeriodOtMap);
            }
        }
        //上月加班单据分析
        if (CollectionUtils.isNotEmpty(otherPeriodOtList)) {
            Map<String, Object> otherPeriodOtMap = new HashMap<>();
            for (EmpOverInfo of : otherPeriodOtList) {
                of.setBelongid(belongId);
                Integer compensateType = of.getCompensate_type();
                //1、工作日，2休息日，3法定假日,4特殊日期
                Integer date_type = of.getDate_type();
                if (date_type == null || compensateType == null) {
                    continue;
                }
                calOtByParseRule(of, waAnalyze, analyzeInfo, belongId, empShiftDef, dto, otherPeriodOtMap, relOtTimeDurationMap, 2, transferMap);
            }
            if (MapUtils.isNotEmpty(otherPeriodOtMap)) {
                //根据加班分析规则计算加班时长
                final Float[] totalDuration = {0f};//加班总时长
                otherPeriodOtMap.forEach((key, v) -> {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    if (key.contains("_key")) {
                        Float duration = Float.valueOf(v.toString());
                        String[] keyArray = key.split("_");
                        Integer dateType = Integer.valueOf(keyArray[1]);
                        Integer compensateType = Integer.valueOf(keyArray[2]);
                        duration = analyzeOtRule(duration, analyzeInfo, dateType, compensateType);
                        totalDuration[0] += duration;
                        otherPeriodOtMap.put(key, duration);
                    }
                });
                otherPeriodOtMap.put("time_duration", totalDuration[0]);
                //数据组合
                otherPeriodOtMap.forEach((key, v) -> {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    if (key.contains("_key") || "time_duration".equals(key)) {
                        Integer duration = Integer.valueOf(String.valueOf(v));
                        if (otMap.containsKey(key)) {
                            int otminute = (Integer) otMap.get(key);
                            otMap.put(key, duration + otminute);
                        } else {
                            otMap.put(key, duration);
                        }
                    } else {
                        if (!otMap.containsKey(key)) {
                            otMap.put(key, v);
                        }
                    }
                });
            }
        }
        if (MapUtils.isNotEmpty(otMap)) {
            //法定假日加班汇总
            final Integer[] legalHolidayDuration = {0};//法定假日加班总时长
            List<String> delKeys = new ArrayList<>();
            otMap.forEach((k, v) -> {
                if (!"time_duration".equals(k)) {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    String[] keyArray = k.split("_");
                    Integer dateType = Integer.valueOf(keyArray[1]);

                    if (dateType == 3) {
                        legalHolidayDuration[0] += Integer.valueOf(v.toString());
                        delKeys.add(k);
                    }
                }
            });
            if (delKeys.size() > 0) {
                otMap.keySet().removeIf(k -> delKeys.indexOf(k) != -1);
                otMap.put("ot_3_key", legalHolidayDuration[0]);
                otMap.put("ot_3_name", BaseConst.WA_OT_COMPENSATE.get("3"));
            }
        }
    }

    /**
     * 根据考勤周期拆分加班单据
     *
     * @param otlist
     * @param otherPeriodOtList
     * @param analyzeInfo
     * @param waSob
     * @return
     * @throws Exception
     */
    private List<EmpOverInfo> getOtListByCycle(List<EmpOverInfo> otlist, List<EmpOverInfo> otherPeriodOtList, WaAnalyzInfo analyzeInfo, WaSob waSob) throws Exception {
        List<EmpOverInfo> currentPeriodOtList = new ArrayList<>();//当前计算周期的加班单据
        if (waSob != null) {
            otlist.forEach(of -> {
                Long over_date = of.getBelongDate();//加班单据申请日期
                over_date = DateUtil.getDateLong(over_date * 1000, "yyyy-MM-dd", true);
                if (over_date.compareTo(waSob.getStartDate()) < 0) {
                    otherPeriodOtList.add(of);
                } else {
                    currentPeriodOtList.add(of);
                }
            });
        } else {
            for (EmpOverInfo of : otlist) {
                Long real_date = of.getReal_date();
                Long over_date = of.getBelongDate();//加班单据申请日期
                over_date = DateUtil.getDateLong(over_date * 1000, "yyyy-MM-dd", true);
                real_date = DateUtil.getDateLong(real_date * 1000, "yyyy-MM-dd", true);

                //根据日期+考勤周期反推考勤开始日期
                //事件日期
                Map over_date_cycle = waCommonService.calculateCycleTime(over_date, analyzeInfo.getCyle_startdate());
                //实际加班日期
                Map real_date_cycle = waCommonService.calculateCycleTime(real_date, analyzeInfo.getCyle_startdate());

                boolean isOtherPeriod = false;
                if (MapUtils.isNotEmpty(over_date_cycle) && MapUtils.isNotEmpty(real_date_cycle)) {
                    Long curCycleBegin = (Long) real_date_cycle.get("cycleBegin");
                    Long cycleBegin = (Long) over_date_cycle.get("cycleBegin");

                    if (cycleBegin.compareTo(curCycleBegin) < 0) {//实际加班日期对用的考勤周期开始日期>事件日期对应的考勤周期开始日期
                        isOtherPeriod = true;
                    }
                }
                if (isOtherPeriod) {
                    otherPeriodOtList.add(of);
                } else {
                    currentPeriodOtList.add(of);
                }
            }
        }
        return currentPeriodOtList;
    }

    /**
     * 加班时长分析
     *
     * @param of
     * @param waAnalyze
     * @param analyzeInfo
     * @param belongId
     * @param empShiftDef
     * @param dto
     * @param otMap
     * @param relOtTimeDurationMap
     * @param legalHolidaysOtRule  法定假日加班分析规则 1 汇总分析 2 不汇总分析
     */
    private void calOtByParseRule(EmpOverInfo of,
                                  WaAnalyze waAnalyze,
                                  WaAnalyzInfo analyzeInfo,
                                  String belongId,
                                  EmpShiftInfo empShiftDef,
                                  WaAnalyzCalDTO dto, Map<String, Object> otMap,
                                  Map<String, Float> relOtTimeDurationMap,
                                  Integer legalHolidaysOtRule,
                                  Map<String, String> transferMap) {
        Float time_duration = of.getTime_duration().floatValue();
        analyzeInfo = dto.getEmpWaAnalyz(of.getEmpid(), of.getRegdate());
        if (analyzeInfo != null && analyzeInfo.getOtMaps() == null) {
            analyzeInfo.setOtMaps(dto.getOtMaps());
        }
        waAnalyze = getEmpAnalyze(belongId, of.getEmpid(), of.getBelongDate(), dto);
        if (empShiftDef != null && waAnalyze != null && waAnalyze.getShiftDefId() == null) {
            waAnalyze.setShiftDefId(empShiftDef.getShiftDefId());
        }

        Integer dateType = of.getDate_type();
        Integer compensateType = of.getCompensate_type();
        Integer overTypeId = of.getOverTypeId();
        String dateTypeCompensateTypeKey = String.format("%s_%s_%s", dateType, compensateType, overTypeId);
        Integer validTimeCalType = null;
        Float maxValidTimeMinute = null;
        WaOvertimeType overtimeType = null;
        Integer groupId = analyzeInfo.getWa_group_id();

        if (analyzeInfo != null) {
            Map<String, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongId, groupId, dto);
            if (otRuleMap.containsKey(dateTypeCompensateTypeKey)) {
                overtimeType = otRuleMap.get(dateTypeCompensateTypeKey);
                validTimeCalType = overtimeType.getValidTimeCalType();
                if (overtimeType.getMaxValidTime() != null) {
                    maxValidTimeMinute = overtimeType.getMaxValidTime() * 60f;
                }
            }
        } else {
            log.info("calOtByParseRule empId :" + of.getEmpid() + ",regDate:" + of.getRegdate() + " get analyzeInfo empty");
        }

        if (validTimeCalType != null && !OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
            processOt2(waAnalyze, null, otMap, of, analyzeInfo, empShiftDef, dto, relOtTimeDurationMap,
                    legalHolidaysOtRule, overtimeType, transferMap);
            return;
        }

        Long overtimeStartTime = of.getStart_time();
        Long overtimeEndTime = of.getEnd_time();
        if (null != overtimeStartTime && null != overtimeEndTime) {
            long jg = overtimeEndTime - overtimeStartTime;
            if (jg > 0 && empShiftDef != null) {
                long overtimeStartDate = DateUtil.getOnlyDate(new Date(overtimeStartTime * 1000));
                long overtimeEndDate = DateUtil.getOnlyDate(new Date(overtimeEndTime * 1000));
                if (overtimeEndDate - overtimeStartDate > 0) {
                    EmpShiftInfo secEmpShiftDef = this.getEmpShiftDefByInfo(of.getEmpid(), null, overtimeEndDate, dto);
                    jg -= waCommonService.calOtRestTotalTime(belongId, empShiftDef, overtimeStartTime, overtimeEndTime);
                    jg -= waCommonService.calOtRestTotalTime(belongId, secEmpShiftDef, overtimeStartTime, overtimeEndTime);
                } else {
                    Long preDate = overtimeStartDate - 1440L * 60;
                    EmpShiftInfo preEmpShiftDef = this.getEmpShiftDefByInfo(of.getEmpid(), null, preDate, dto);
                    jg -= waCommonService.calOtRestTotalTime(belongId, preEmpShiftDef, overtimeStartTime, overtimeEndTime);
                    jg -= waCommonService.calOtRestTotalTime(belongId, empShiftDef, overtimeStartTime, overtimeEndTime);
                }
                if (jg > 0) {
                    time_duration = BigDecimal.valueOf(jg / 60).setScale(0, RoundingMode.DOWN).floatValue();
                }
            }
        }

        if (maxValidTimeMinute != null && time_duration > maxValidTimeMinute) {
            time_duration = maxValidTimeMinute;
        }

        Float minOvertimeUnit = overtimeType == null ? null : overtimeType.getMinOvertimeUnit();
        Integer minOvertimeUnitType = Optional.ofNullable(overtimeType.getMinOvertimeUnitType()).orElse(2);
        time_duration = minOvertimeUnit == null ? time_duration : OvertimeUnitEnum.HOUR.getTime(null, time_duration * 60, minOvertimeUnit, minOvertimeUnitType) / 60;

        // 转换时长
        Float transferDuration = 0f;
        Integer transferUnit = 2;
        if (overtimeType != null) {
            WaOvertimeTransferRulePo transferRule = dto.getOtTransferRule(overtimeType.getRuleId());
            if (null != transferRule) {
                if (!TransferRuleEnum.OTHER.getIndex().equals(transferRule.getTransferRule())) {
                    TransferRuleEnum transferRuleEnum = TransferRuleEnum.getTransferRuleEnum(transferRule.getTransferRule());
                    assert transferRuleEnum != null;
                    Map<String, Object> map = transferRuleEnum.calTimeDuration(time_duration, transferRule.getTransferPeriods(), transferRule.getTransferTime());
                    transferDuration = new BigDecimal(map.get("duration").toString()).floatValue();
                    transferUnit = (Integer) map.get("unit");
                }
            } else {
                SysEmpInfo empInfo = dto.getEmpInfo(of.getEmpid());
                if (dto.getJob() || null == empInfo) {
                    log.error("transferRule is null, of:{},overtimeType:{}", JSONUtils.ObjectToJson(of), JSONUtils.ObjectToJson(overtimeType));
                } else {
                    String otDate = DateUtil.getDateStrByTimesamp(of.getReal_date());
                    throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_NOT_MATCH_TRANSFER_RULE, null).getMsg(), empInfo.getEmpName(), empInfo.getWorkno(), otDate));
                }
            }
        } else {
            SysEmpInfo empInfo = dto.getEmpInfo(of.getEmpid());
            if (dto.getJob() || null == empInfo) {
                log.error("overtimeType is null, of:{}", JSONUtils.ObjectToJson(of));
            } else {
                String otDate = DateUtil.getDateStrByTimesamp(of.getReal_date());
                throw new CDException(String.format("%s(%s)%s的加班单未查到匹配的加班类型", empInfo.getEmpName(), empInfo.getWorkno(), otDate));
            }
        }

        String ot_type_key1 = String.valueOf(of.getDate_type());
        ot_type_key1 += "_" + of.getCompensate_type();
        String transferKey = "ot_" + overTypeId + "_key";
        if (otMap.containsKey(transferKey)) {
            otMap.put(transferKey, (Float) otMap.get(transferKey) + transferDuration);
        } else {
            otMap.put(transferKey, transferDuration);
        }
        otMap.put("ot_" + overTypeId + "_key_unit", transferUnit);//转换单位
        String key1 = "ot_" + ot_type_key1 + "_key";
        String otName = BaseConst.WA_OT_COMPENSATE.get(ot_type_key1);
        if (otMap.containsKey(key1)) {
            float otMinute = (Float) otMap.get(key1);
            Float f = (Float) otMap.get("time_duration");
            if (f == null) {
                f = 0f;
            }
            otMap.put("time_duration", time_duration + f);
            otMap.put(key1, time_duration + otMinute);
        } else {
            otMap.put(key1, time_duration);
            otMap.put("ot_" + ot_type_key1 + "_name", otName);
            Float f = (Float) otMap.get("time_duration");
            if (f == null) {
                f = 0f;
            }
            otMap.put("time_duration", f + time_duration);
        }
        String otRelDurKey = of.getEmpid() + "_" + of.getOt_detail_id() + "_" + of.getOt_id();
        relOtTimeDurationMap.put(otRelDurKey, time_duration);
        transferMap.put(otRelDurKey, String.format("%s_%s", transferDuration, transferUnit));
    }

    /**
     * 加班联动签到分析
     *
     * @param wa
     * @param overListMaps
     * @param otMap
     * @param of
     * @param analyzeInfo
     * @param empShiftDef
     * @param dto
     * @param relOtTimeDurationMap
     * @param legalHolidaysOtRule  法定假日加班分析规则 1 汇总分析 2 不汇总分析
     * @param overtimeType
     */
    private void processOt2(WaAnalyze wa,
                            Map<String, List<EmpOverInfo>> overListMaps,
                            Map<String, Object> otMap,
                            EmpOverInfo of,
                            WaAnalyzInfo analyzeInfo,
                            EmpShiftInfo empShiftDef,
                            WaAnalyzCalDTO dto,
                            Map<String, Float> relOtTimeDurationMap,
                            Integer legalHolidaysOtRule,
                            WaOvertimeType overtimeType,
                            Map<String, String> transferMap) {
        if (wa == null) {
            return;
        }
        Long start_time = of.getStart_time();
        if (start_time == null) {
            start_time = 0L;
        }
        Long end_time = of.getEnd_time();
        if (end_time == null) {
            end_time = 0L;
        }
        Long signOffTime = wa.getRegSignoffTime();
        Long signTime = wa.getRegSigninTime();

        // 出差单
        List<WaEmpTravelDo> travelInfoList = waEmpTravelDo.getTravelInfoList(wa.getBelongOrgId(), wa.getEmpid(), wa.getBelongDate(), wa.getBelongDate() + 86399);
        List<WaEmpTravelDo> travelDoList = travelInfoList.stream().filter(e -> ApprovalStatusEnum.PASSED.getIndex().equals(e.getStatus())).collect(Collectors.toList());
        Boolean isOpenTravel = overtimeType.getIsOpenTravel();//是否关联出差申请
        Integer validTimeCalType = overtimeType.getValidTimeCalType();

        // 本次参与分析的打卡数据
        List<WaRegisterRecord> needAnalyzeRegList = new ArrayList<>();
        //查询员工当天所有的打卡记录（含外勤）
        List<WaRegisterRecord> currentDayRegList = dto.getEmpBelongDateRegListByDateEmpId(wa.getEmpid(), wa.getBelongDate());
        if (CollectionUtils.isNotEmpty(currentDayRegList)) {
            needAnalyzeRegList.addAll(currentDayRegList);
        }

        // 计算签到、签退时间
        if (CollectionUtils.isNotEmpty(needAnalyzeRegList)) {
            needAnalyzeRegList.sort(Comparator.comparing(WaRegisterRecord::getRegDateTime));

            // 有效打卡类型分析逻辑
            if (PunchTypeEnum.SAME.getIndex().equals(overtimeType.getValidPunchType())) {
                List<WaRegisterRecord> travelList = needAnalyzeRegList.stream().filter(e -> ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
                List<WaRegisterRecord> list = needAnalyzeRegList.stream().filter(e -> !ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(travelList) && CollectionUtils.isNotEmpty(list)) {
                    List<WaRegisterRecord> otherList = list.stream().filter(e -> !ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType())).collect(Collectors.toList());
                    List<WaRegisterRecord> bdkList = list.stream().filter(e -> ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(otherList) && CollectionUtils.isEmpty(bdkList)) {
                        needAnalyzeRegList = list;
                    }
                }
            }
            if (PunchTypeEnum.FIELD.getIndex().equals(overtimeType.getValidPunchType())) {
                needAnalyzeRegList = needAnalyzeRegList.stream().filter(e -> (ClockWayEnum.FIELD.getIndex().equals(e.getType()) || ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType()))).collect(Collectors.toList());
            }
            if (PunchTypeEnum.WORK.getIndex().equals(overtimeType.getValidPunchType())) {
                needAnalyzeRegList = needAnalyzeRegList.stream().filter(e -> !ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
            }

            // 计算当天的签到、签退时间
            if (CollectionUtils.isNotEmpty(needAnalyzeRegList)) {
                signTime = needAnalyzeRegList.get(0).getRegDateTime();
                if (needAnalyzeRegList.size() > 1) {
                    signOffTime = needAnalyzeRegList.get(needAnalyzeRegList.size() - 1).getRegDateTime();
                }
            } else {
                signTime = null;
                signOffTime = null;
            }
            if (isOpenTravel) {
                if (CollectionUtils.isEmpty(travelDoList)) {
                    signTime = null;
                    signOffTime = null;
                }
            }
        }

        // TODO roundingRule 不清楚为啥没有取值逻辑了，23/7/11 时还有
        Integer roundingRule = null;
        Integer overtimeCalType = overtimeType.getOvertimeCalType();

        // 加班联动打卡记录计算有效时长
        Float timeDuration = of.getTime_duration().floatValue();
        if (validTimeCalType != null && !OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
            if (OtValidTimeCalTypeEnum.REG_TIME.getIndex().equals(validTimeCalType)
                    || OtValidTimeCalTypeEnum.REG_TIME_NOT_DEDUCTION.getIndex().equals(validTimeCalType)) {
                if (signOffTime != null && signTime != null) {
                    BigDecimal bdTimeDuration = new BigDecimal(String.valueOf(timeDuration));
                    timeDuration = calRealOtTime(start_time, end_time, bdTimeDuration, signTime, signOffTime, empShiftDef, validTimeCalType, overtimeCalType, roundingRule);
                } else {
                    timeDuration = 0f;
                }
            } else {// 取加班时间段和打卡时间段的交集
                Boolean zeroSplitting = Optional.ofNullable(of.getZeroSplitting()).orElse(false); // 跨夜加班归属:按0点拆分
                if (zeroSplitting && (signTime == null || signOffTime == null)) {
                    if (signTime == null) {
                        signTime = of.getStart_time();
                    }
                    if (signOffTime == null) {
                        signOffTime = getZeroSplittingSignOffRegDate(of.getEmpid(), of.getStart_time() - 86400,
                                overtimeType, dto, travelDoList);
                    }
                }
                if (signOffTime != null && signTime != null) {
                    if (start_time <= signOffTime && end_time >= signTime) {
                        BigDecimal bdTimeDuration = new BigDecimal(String.valueOf(timeDuration));
                        timeDuration = calRealOtTime(start_time, end_time, bdTimeDuration, signTime, signOffTime, empShiftDef, validTimeCalType, overtimeCalType, roundingRule);
                    } else {// 跨夜加班分析
                        timeDuration = calRealOtTime2(of, empShiftDef, dto, validTimeCalType, overtimeCalType, roundingRule);
                    }
                } else {
                    timeDuration = 0f;
                }
            }
        }

        // 使用BigDecimal进行后续计算
        BigDecimal realTimeDurationBD = new BigDecimal(String.valueOf(timeDuration));
        // 需要修改方法签名以接受BigDecimal
        if (BooleanUtils.isFalse(analyzeInfo.getOt_sum_parse())) {//未开启加班单日汇总分析
            timeDuration = realTimeDurationBD.floatValue();
        }
        Float maxValidTimeMinute = null;
        if (overtimeType.getMaxValidTime() != null) {
            maxValidTimeMinute = overtimeType.getMaxValidTime() * 60f;
        }

        // 处理最大有效时长限制
        if (maxValidTimeMinute != null) {
            BigDecimal maxValidBD = new BigDecimal(String.valueOf(maxValidTimeMinute));
            if (realTimeDurationBD.compareTo(maxValidBD) > 0) {
                realTimeDurationBD = maxValidBD;
            }
            BigDecimal timeDurationBD = new BigDecimal(String.valueOf(timeDuration));
            if (timeDurationBD.compareTo(maxValidBD) > 0) {
                timeDuration = maxValidBD.floatValue();
            }
        }

        // 处理最小加班单位
        Float minOvertimeUnit = overtimeType.getMinOvertimeUnit();
        Integer minOvertimeUnitType = Optional.ofNullable(overtimeType.getMinOvertimeUnitType()).orElse(2);
        if (minOvertimeUnit != null) {
            // 转换为秒进行计算
            BigDecimal seconds = realTimeDurationBD.multiply(new BigDecimal("60"));
            BigDecimal minUnitBD = new BigDecimal(String.valueOf(minOvertimeUnit));
            // 需要修改OvertimeUnitEnum.HOUR.getTime方法以接受BigDecimal
            BigDecimal adjustedSeconds = BigDecimalOvertimeUnitEnum.HOUR.getTime(null, seconds, minUnitBD, minOvertimeUnitType);
            realTimeDurationBD = adjustedSeconds.divide(new BigDecimal("60"), 8, RoundingMode.HALF_UP);
        }

        // CLOUD-3398 加班分析规则的判断逻辑
        if (realTimeDurationBD.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal hours = realTimeDurationBD.divide(new BigDecimal("60"), 30, RoundingMode.HALF_UP);
            BigDecimal calculatedHours = calOtDurationByCarryRules(hours, overtimeType);
            realTimeDurationBD = calculatedHours.multiply(new BigDecimal("60"));
        }

        String otRelDurKey = of.getEmpid() + "_" + of.getOt_detail_id() + "_" + of.getOt_id();
        relOtTimeDurationMap.put(otRelDurKey, realTimeDurationBD.floatValue());

        // 转换时长
        Float transferDuration = 0f;
        Integer transferUnit = 2;
        WaOvertimeTransferRulePo transferRule = dto.getOtTransferRule(overtimeType.getRuleId());
        if (null != transferRule) {
            if (!TransferRuleEnum.OTHER.getIndex().equals(transferRule.getTransferRule())) {
                TransferRuleBDEnum transferRuleEnum = TransferRuleBDEnum.getTransferRuleEnum(transferRule.getTransferRule());
                assert transferRuleEnum != null;
                Map<String, Object> map = transferRuleEnum.calTimeDuration(realTimeDurationBD, transferRule.getTransferPeriods(), new BigDecimal(String.valueOf(transferRule.getTransferTime())));
                transferDuration = new BigDecimal(map.get("duration").toString()).floatValue();
                transferUnit = new BigDecimal(map.get("unit").toString()).intValue();
            }
        } else {
            SysEmpInfo empInfo = dto.getEmpInfo(of.getEmpid());
            if (dto.getJob() || null == empInfo) {
                log.error("transferRule is null, of:{},overtimeType:{}", JSONUtils.ObjectToJson(of), JSONUtils.ObjectToJson(overtimeType));
            } else {
                String otDate = DateUtil.getDateStrByTimesamp(of.getReal_date());
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_NOT_MATCH_TRANSFER_RULE, null).getMsg(), empInfo.getEmpName(), empInfo.getWorkno(), otDate));
            }
        }

        transferMap.put(otRelDurKey, String.format("%s_%s", transferDuration, transferUnit));
        String transferKey = "ot_" + of.getOverTypeId() + "_key";
        if (otMap.containsKey(transferKey)) {
            otMap.put(transferKey, (Float) otMap.get(transferKey) + transferDuration);
        } else {
            otMap.put(transferKey, transferDuration);
        }
        otMap.put("ot_" + of.getOverTypeId() + "_key_unit", transferUnit); //转换单位
        Float overTime = timeDuration;
        overTime = minOvertimeUnit == null ? overTime : OvertimeUnitEnum.HOUR.getTime(null, overTime * 60, minOvertimeUnit, minOvertimeUnitType) / 60;

        String ot_type_key1 = String.valueOf(of.getDate_type());
        ot_type_key1 += "_" + of.getCompensate_type();
        String otName = BaseConst.WA_OT_COMPENSATE.get(ot_type_key1);
        String key1 = "ot_" + ot_type_key1 + "_key";
        if (otMap.containsKey(key1)) {
            float otMinute = (Float) otMap.get(key1);
            Float f = (Float) otMap.get("time_duration");
            if (f == null) {
                f = 0f;
            }
            otMap.put("time_duration", overTime + f);
            otMap.put(key1, overTime + otMinute);
        } else {
            otMap.put(key1, overTime);
            if (of.getDate_type() != 3 || legalHolidaysOtRule != 2) {
                otMap.put("ot_" + ot_type_key1 + "_name", otName);
            }
            Float f = (Float) otMap.get("time_duration");
            if (f == null) {
                f = 0f;
            }
            otMap.put("time_duration", f + overTime);
        }
    }

    /**
     * 跨夜加班归属:按0点拆分时取卡逻辑
     *
     * @param empId
     * @param belongDate
     * @param overtimeType
     * @param dto
     * @param travelDoList
     * @return
     */
    private Long getZeroSplittingSignOffRegDate(Long empId,
                                                Long belongDate,
                                                WaOvertimeType overtimeType,
                                                WaAnalyzCalDTO dto,
                                                List<WaEmpTravelDo> travelDoList) {
        List<WaRegisterRecord> currentDayRegList = dto.getEmpBelongDateRegListByDateEmpId(empId, belongDate);
        if (CollectionUtils.isEmpty(currentDayRegList)) {
            return null;
        }

        // 本次参与分析的打卡数据
        List<WaRegisterRecord> needAnalyzeRegList = new ArrayList<>(currentDayRegList);
        needAnalyzeRegList.sort(Comparator.comparing(WaRegisterRecord::getRegDateTime));

        // 有效打卡类型分析逻辑
        if (PunchTypeEnum.SAME.getIndex().equals(overtimeType.getValidPunchType())) {
            List<WaRegisterRecord> travelList = needAnalyzeRegList.stream().filter(e -> ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
            List<WaRegisterRecord> list = needAnalyzeRegList.stream().filter(e -> !ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(travelList) && CollectionUtils.isNotEmpty(list)) {
                List<WaRegisterRecord> otherList = list.stream().filter(e -> !ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType())).collect(Collectors.toList());
                List<WaRegisterRecord> bdkList = list.stream().filter(e -> ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(otherList) && CollectionUtils.isEmpty(bdkList)) {
                    needAnalyzeRegList = list;
                }
            }
        }
        if (PunchTypeEnum.FIELD.getIndex().equals(overtimeType.getValidPunchType())) {
            needAnalyzeRegList = needAnalyzeRegList.stream().filter(e -> (ClockWayEnum.FIELD.getIndex().equals(e.getType()) || ClockWayEnum.FILLCLOCK.getIndex().equals(e.getType()))).collect(Collectors.toList());
        }
        if (PunchTypeEnum.WORK.getIndex().equals(overtimeType.getValidPunchType())) {
            needAnalyzeRegList = needAnalyzeRegList.stream().filter(e -> !ClockWayEnum.FIELD.getIndex().equals(e.getType())).collect(Collectors.toList());
        }

        // 计算签退时间
        Long signOffTime = null;
        if (CollectionUtils.isNotEmpty(needAnalyzeRegList) && needAnalyzeRegList.size() > 1) {
            signOffTime = needAnalyzeRegList.get(needAnalyzeRegList.size() - 1).getRegDateTime();
        }
        if (overtimeType.getIsOpenTravel() && CollectionUtils.isEmpty(travelDoList)) {
            signOffTime = null;
        }
        return signOffTime;
    }

    /**
     * 获取加班分析规则
     *
     * @param belongId
     * @param waGroupId
     * @param dto
     * @return
     */
    private Map<String, WaOvertimeType> getOtTypeAnalyseRuleMap(String belongId, Integer waGroupId, WaAnalyzCalDTO dto) {
        if (waGroupId == null) {
            return new HashMap<>();
        }
        Map<Integer, Map<String, WaOvertimeType>> waGroupOtRuleMap = dto.getWaGroupOtRuleMap();
        if (MapUtils.isEmpty(waGroupOtRuleMap) || !waGroupOtRuleMap.containsKey(waGroupId) || waGroupOtRuleMap.get(waGroupId) == null) {
            if (waGroupOtRuleMap == null) {
                waGroupOtRuleMap = new HashMap<>();
            }
            //List<WaOvertimeType> overtimeTypeList = waMapper.getOtTypeAnalyseRule(belongId, waGroupId);
            List<WaOvertimeType> overtimeTypeList = overTimeTypeDo.getOtTypeAnalyseRule(belongId, null);
            if (CollectionUtils.isNotEmpty(overtimeTypeList)) {
                Map<String, WaOvertimeType> overtimeTypeMap = new HashMap<>();
                overtimeTypeList.forEach(o -> {
                    String key = String.format("%s_%s_%s", o.getDateType(), o.getCompensateType(), o.getOvertimeTypeId());
                    overtimeTypeMap.put(key, o);
                });
                waGroupOtRuleMap.put(waGroupId, overtimeTypeMap);
                dto.setWaGroupOtRuleMap(waGroupOtRuleMap);
                return overtimeTypeMap;
            }
            return new HashMap<>();
        }
        return waGroupOtRuleMap.get(waGroupId);
    }

    private BigDecimal calOtDurationByCarryRules(BigDecimal timeDuration, WaOvertimeType overtimeType) {
        if (timeDuration == null || overtimeType == null || overtimeType.getRoundingRule() == null) {
            return timeDuration;
        }
        // 定义舍入精度为2位小数
        final int SCALE = 2;
        switch (overtimeType.getRoundingRule()) {
            case 1:
                timeDuration = getOtRoundingRule(timeDuration, true, new BigDecimal("0.25"));
                break;
            case 2:
                timeDuration = getOtRoundingRule(timeDuration, true, new BigDecimal("0.5"));
                break;
            case 3:
                timeDuration = getOtRoundingRule(timeDuration, true, new BigDecimal("1"));
                break;
            case 4:
                timeDuration = getOtRoundingRule(timeDuration, false, new BigDecimal("0.25"));
                break;
            case 5:
                timeDuration = getOtRoundingRule(timeDuration, false, new BigDecimal("0.5"));
                break;
            case 6:
                timeDuration = getOtRoundingRule(timeDuration, false, new BigDecimal("1"));
                break;
            case 7:
                timeDuration = timeDuration.setScale(0, RoundingMode.HALF_UP);
                break;
            case 8:
                timeDuration = timeDuration.setScale(2, RoundingMode.HALF_UP);
                break;
            default:
                break;
        }
        return timeDuration.setScale(SCALE, RoundingMode.HALF_UP);
    }

    private BigDecimal getOtRoundingRule(BigDecimal value, boolean roundUp, BigDecimal increment) {
        if (value == null || increment == null || increment.compareTo(BigDecimal.ZERO) <= 0) {
            return value;
        }
        // 获取整数部分
        BigDecimal integerPart = value.setScale(0, RoundingMode.FLOOR);
        // 获取小数部分
        BigDecimal fractionalPart = value.subtract(integerPart);
        // 计算余数
        BigDecimal remainder = fractionalPart.remainder(increment);
        if (roundUp) {
            // 向上舍入：如果有余数，增加一个增量
            if (remainder.compareTo(BigDecimal.ZERO) > 0) {
                fractionalPart = fractionalPart.add(increment.subtract(remainder));
            }
        } else {
            // 向下舍入：直接减去余数
            fractionalPart = fractionalPart.subtract(remainder);
        }
        // 合并整数部分和处理后的小数部分
        return integerPart.add(fractionalPart);
    }

    private Float analyzeOtRule(Float time_duration, WaAnalyzInfo analyzeInfo, Integer dateType, Integer compensateType) {
        List<OtAnalyzeRule> otParseRules = analyzeInfo.getOtParseRules();
        if (CollectionUtils.isNotEmpty(otParseRules)) {
            for1:
            for (int i = 0; i < otParseRules.size(); i++) {
                OtAnalyzeRule otAnalyzeRule = otParseRules.get(i);
                if (StringUtils.isNotBlank(otAnalyzeRule.getOtTypeIds())) {
                    String[] otTypeIds = otAnalyzeRule.getOtTypeIds().split(",");
                    for2:
                    for (int j = 0; j < otTypeIds.length; j++) {
                        Integer otTypeId = Integer.valueOf(otTypeIds[j]);
                        if (analyzeInfo != null && analyzeInfo.getOtMaps() != null && analyzeInfo.getOtMaps().containsKey(otTypeId)) {
                            WaOvertimeType overtimeType = analyzeInfo.getOtMaps().get(otTypeId);
                            if (overtimeType.getDateType().equals(dateType) && overtimeType.getCompensateType().equals(compensateType)) {
                                //规则 1=无, 2=向下取整15分钟 ,3 = 向上取整15分钟 ,4=向下取整 30分钟 ,5=向上取整30 分钟
                                //如果实际加班小时数小于（最小有效时长）则加班时长归零
                                if (otAnalyzeRule.getMinValidTime() != null && time_duration < otAnalyzeRule.getMinValidTime()) {
                                    // 如果小于最小时长，则跳出循环
                                    time_duration = 0f;
                                    break for1;
                                }
                                switch (otAnalyzeRule.getRule()) {
                                    case 1:
                                        break;
                                    case 2:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 15);
                                        break;
                                    case 3:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 15);
                                        break;
                                    case 4:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), false, 30);
                                        break;
                                    case 5:
                                        time_duration = getOtRoundingRule(time_duration.doubleValue(), true, 30);
                                        break;
                                    default:
                                        break;
                                }
                                break for1;
                            }
                        }
                    }
                }
            }
        }
        return time_duration;
    }

    private Float getOtRoundingRule(Double min, boolean isUp, Integer roundMin) {
        double mod = min % roundMin;
        //向上取整
        if (isUp) {
            min = min + (roundMin - mod);
        } else {
            //向下取整
            min = min - mod;
        }
        return min.floatValue();
    }

    /**
     * 加班单日汇总分析（2.0客户没有在使用）
     *
     * @param ovlist
     * @param analyzeInfo
     * @param dto
     * @param belongid
     * @param waAnalyze
     * @param empShiftDef
     * @param overListMaps
     * @param relOtTimeDurationMap
     * @param transferMap
     * @param otMap
     * @throws Exception
     */
    @Deprecated
    public void otSumParse(List<EmpOverInfo> ovlist,
                           WaAnalyzInfo analyzeInfo,
                           WaAnalyzCalDTO dto,
                           String belongid,
                           WaAnalyze waAnalyze,
                           EmpShiftInfo empShiftDef,
                           Map<String, List<EmpOverInfo>> overListMaps,
                           Map<String, Float> relOtTimeDurationMap,
                           Map<String, String> transferMap,
                           Map<String, Object> otMap) throws Exception {
        //根据考勤周期拆分加班单据，每个考勤周期的加班单据单独分析，最后分析结果合并在一起
        //判读逻辑：事件日期<考勤开始日期 && 实际加班日期在本次核算范围内（帐套开始日期<=审批日期<=帐套截止日期）
        //考勤开始日期：如果考勤帐套有值，取考勤帐套开始日期，反之：根据日期+考勤分组去反推考勤开始日期
        List<EmpOverInfo> otherPeriodOtList = new ArrayList<>();//其他考勤周期的加班单据（eg:事件日期是上月&审批日期是本月）
        List<EmpOverInfo> currentPeriodOtList = this.getOtListByCycle(ovlist, otherPeriodOtList, analyzeInfo, dto.getWaSob());//当前计算周期的加班单据
        //当前计算周期的加班单据分析
        if (CollectionUtils.isNotEmpty(currentPeriodOtList)) {
            Map<String, Object> currentPeriodOtMap = new HashMap<>();
            for (EmpOverInfo of : currentPeriodOtList) {
                of.setBelongid(belongid);
                //1、工作日，2休息日，3法定假日,4特殊日期
                Integer date_type = of.getDate_type();
                Integer compensateType = of.getCompensate_type();
                Integer overTypeId = of.getOverTypeId();
                if (compensateType == null || date_type == null) {
                    continue;
                }
                String dateTypeCompensateTypeKey = String.format("%s_%s_%s", date_type, compensateType, overTypeId);
                //查询加班分析规则
                Map<String, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzeInfo.getWa_group_id(), dto);
                Integer validTimeCalType = null;
                WaOvertimeType overtimeType = null;
                if (otRuleMap.containsKey(dateTypeCompensateTypeKey)) {
                    overtimeType = otRuleMap.get(dateTypeCompensateTypeKey);
                    validTimeCalType = overtimeType.getValidTimeCalType();
                }
                if (validTimeCalType == null || OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                    //按审批（申请）时长
                    calOtByParseRule(of, waAnalyze, analyzeInfo, belongid, empShiftDef, dto, currentPeriodOtMap, relOtTimeDurationMap, 2, transferMap);
                } else {
                    parseOtTime(of, waAnalyze, analyzeInfo, overListMaps, belongid, empShiftDef, dto, currentPeriodOtMap, relOtTimeDurationMap, 2, overtimeType, transferMap);
                }
            }
            if (MapUtils.isNotEmpty(currentPeriodOtMap)) {
                final Float[] totalDuration = {0f};//加班总时长
                currentPeriodOtMap.forEach((key, v) -> {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    if (key.contains("_key")) {
                        Float duration = Float.valueOf(v.toString());
                        String[] keyArray = key.split("_");
                        Integer dateType = Integer.valueOf(keyArray[1]);
                        Integer compensateType = Integer.valueOf(keyArray[2]);
                        //根据加班分析规则计算加班时长
                        duration = analyzeOtRule(duration, analyzeInfo, dateType, compensateType);
                        totalDuration[0] += duration;
                        currentPeriodOtMap.put(key, duration);
                    }
                });
                currentPeriodOtMap.put("time_duration", totalDuration[0]);
                //数据组合
                otMap.putAll(currentPeriodOtMap);
            }
        }
        //查询加班分析规则
        Map<String, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzeInfo.getWa_group_id(), dto);
        //上月加班单据分析
        if (CollectionUtils.isNotEmpty(otherPeriodOtList)) {
            Map<String, Object> otherPeriodOtMap = new HashMap<>();
            for (EmpOverInfo of : otherPeriodOtList) {
                of.setBelongid(belongid);
                //1、工作日，2休息日，3法定假日,4特殊日期
                Integer compensateType = of.getCompensate_type();
                Integer date_type = of.getDate_type();
                Integer overTypeId = of.getOverTypeId();
                if (date_type == null || compensateType == null) {
                    continue;
                }
                String dateTypeCompensateTypeKey = String.format("%s_%s_%s", date_type, compensateType, overTypeId);
                Integer validTimeCalType = null;
                WaOvertimeType overtimeType = null;
                if (otRuleMap.containsKey(dateTypeCompensateTypeKey)) {
                    overtimeType = otRuleMap.get(dateTypeCompensateTypeKey);
                    validTimeCalType = overtimeType.getValidTimeCalType();
                }
                if (validTimeCalType == null || OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                    //按审批（申请）时长
                    calOtByParseRule(of, waAnalyze, analyzeInfo, belongid, empShiftDef, dto, otherPeriodOtMap, relOtTimeDurationMap, 2, transferMap);
                } else {
                    parseOtTime(of, waAnalyze, analyzeInfo, overListMaps, belongid, empShiftDef, dto, otherPeriodOtMap, relOtTimeDurationMap, 2, overtimeType, transferMap);
                }
            }
            if (MapUtils.isNotEmpty(otherPeriodOtMap)) {
                final Float[] totalDuration = {0f};//加班总时长
                otherPeriodOtMap.forEach((key, v) -> {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    if (key.contains("_key")) {
                        Float duration = Float.valueOf(v.toString());
                        String[] keyArray = key.split("_");
                        Integer dateType = Integer.valueOf(keyArray[1]);
                        Integer compensateType = Integer.valueOf(keyArray[2]);
                        //根据加班分析规则计算加班时长
                        duration = analyzeOtRule(duration, analyzeInfo, dateType, compensateType);
                        totalDuration[0] += duration;
                        otherPeriodOtMap.put(key, duration);
                    }
                });
                otherPeriodOtMap.put("time_duration", totalDuration[0]);
                //数据组合
                otherPeriodOtMap.forEach((key, v) -> {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    if (key.contains("_key") || "time_duration".equals(key)) {
                        Integer duration = Integer.valueOf(v.toString());
                        if (otMap.containsKey(key)) {
                            int otminute = (Integer) otMap.get(key);
                            otMap.put(key, duration + otminute);
                        } else {
                            otMap.put(key, duration);
                        }
                    } else {
                        if (!otMap.containsKey(key)) {
                            otMap.put(key, v);
                        }
                    }
                });
            }
        }
        if (MapUtils.isNotEmpty(otMap)) {
            //法定假日加班汇总
            List<String> delKeys = new ArrayList<>();
            final Integer[] legalHolidayDuration = {0};//法定假日加班总时长
            otMap.forEach((k, v) -> {
                if (!"time_duration".equals(k)) {
                    //{"ot_1_1_key": 120, "ot_1_1_name": "工作日加班付现", "time_duration": 120}
                    String[] keyArray = k.split("_");
                    Integer dateType = Integer.valueOf(keyArray[1]);
                    if (dateType == 3) {
                        legalHolidayDuration[0] += Integer.valueOf(v.toString());
                        delKeys.add(k);
                    }
                }
            });
            if (delKeys.size() > 0) {
                otMap.keySet().removeIf(k -> delKeys.contains(k));
                otMap.put("ot_3_key", legalHolidayDuration[0]);
                otMap.put("ot_3_name", BaseConst.WA_OT_COMPENSATE.get("3"));
            }
        }
    }

    /**
     * 1.判断是否启用了加班联动分析
     * 2.没有启用联动分析的情况,直接记录加班小时数
     * 记录加班小时数分为5类，
     * 增加工作日加班付现小时数
     * 增加工作日加班调休小时数
     * 增加休息日加班付现小时数
     * 增加休息日加班调休小时数
     * 法定节假日加班小时数
     */
    private Map<String, Object> processEmpOver(WaAnalyze waAnalyze, WaAnalyzInfo analyzeInfo,
                                               Map<String, List<EmpOverInfo>> overListMaps,
                                               String belongid, EmpShiftInfo empShiftDef, WaAnalyzCalDTO dto) throws Exception {
        Map<String, Object> otMap = new HashMap<>();
        if (analyzeInfo == null) {
            return otMap;
        }
        //记录实际加班时长
        Map<String, Float> relOtTimeDurationMap = dto.getRelOtTimeDurationMap();
        if (relOtTimeDurationMap == null) {
            relOtTimeDurationMap = new HashMap<>();
        }
        //记录转换时长
        Map<String, String> transferMap = dto.getTransferMap();
        if (transferMap == null) {
            transferMap = new HashMap<>();
        }
        // 如果 1.启用加班联动分析 则需要判断 加班单是否有效 2.并判断加班单是否跨夜了，
        //跨夜的情况处理加班小时数，如果加班单上的加班小时小于了实际加班区间的小时数以加班单上的小时为准，反之则以实际小时为准
        String ot_key = waAnalyze.getEmpid() + "_" + waAnalyze.getBelongDate();
        List<EmpOverInfo> ovlist = overListMaps.get(ot_key);
        if (CollectionUtils.isEmpty(ovlist)) {
            return otMap;
        }
        if (BooleanUtils.isTrue(analyzeInfo.getOt_sum_parse())) {
            //开启加班单日汇总分析
            otSumParse(ovlist, analyzeInfo, dto, belongid, waAnalyze, empShiftDef, overListMaps,
                    relOtTimeDurationMap, transferMap, otMap);
        } else {
            for (EmpOverInfo of : ovlist) {
                of.setBelongid(belongid);
                Integer dateType = of.getDate_type();
                Integer compensateType = of.getCompensate_type();
                Integer overTypeId = of.getOverTypeId();
                if (dateType == null || null == compensateType) {
                    continue;
                }
                String dateTypeCompensateTypeKey = String.format("%s_%s_%s", dateType, compensateType, overTypeId);
                //查询加班分析规则
                Map<String, WaOvertimeType> otRuleMap = getOtTypeAnalyseRuleMap(belongid, analyzeInfo.getWa_group_id(), dto);
                Integer validTimeCalType = null;
                WaOvertimeType overtimeType = null;
                if (otRuleMap.containsKey(dateTypeCompensateTypeKey)) {
                    overtimeType = otRuleMap.get(dateTypeCompensateTypeKey);
                    validTimeCalType = overtimeType.getValidTimeCalType();
                }
                if (validTimeCalType == null || OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
                    //按审批（申请）时长
                    calOtByParseRule(of, waAnalyze, analyzeInfo, belongid, empShiftDef, dto, otMap, relOtTimeDurationMap, 1, transferMap);
                } else {
                    parseOtTime(of, waAnalyze, analyzeInfo, overListMaps, belongid, empShiftDef, dto, otMap, relOtTimeDurationMap, 1, overtimeType, transferMap);
                }
            }
        }
        dto.setTransferMap(transferMap);
        dto.setRelOtTimeDurationMap(relOtTimeDurationMap);
        return otMap;
    }

    /**
     * 加班时长分析计算
     *
     * @param of
     * @param waAnalyze
     * @param analyzeInfo
     * @param overListMaps
     * @param belongId
     * @param empShiftDef
     * @param dto
     * @param otMap
     * @param relOtTimeDurationMap
     * @param legalHolidaysOtRule
     * @param overtimeType
     */
    private void parseOtTime(EmpOverInfo of, WaAnalyze waAnalyze, WaAnalyzInfo analyzeInfo, Map<String, List<EmpOverInfo>> overListMaps,
                             String belongId, EmpShiftInfo empShiftDef, WaAnalyzCalDTO dto, Map<String, Object> otMap,
                             Map<String, Float> relOtTimeDurationMap, Integer legalHolidaysOtRule,
                             WaOvertimeType overtimeType, Map<String, String> transferMap) {
        of.setBelongid(belongId);
        analyzeInfo = dto.getEmpWaAnalyz(of.getEmpid(), of.getRegdate());
        if (analyzeInfo != null && analyzeInfo.getOtMaps() == null) {
            analyzeInfo.setOtMaps(dto.getOtMaps());
        }
        waAnalyze = getEmpAnalyze(belongId, of.getEmpid(), of.getBelongDate(), dto);
        processOt2(waAnalyze, overListMaps, otMap, of, analyzeInfo, empShiftDef, dto, relOtTimeDurationMap, legalHolidaysOtRule, overtimeType, transferMap);
    }

    /**
     * 加班联动打卡记录分析
     *
     * @param start_time       开始时间
     * @param end_time         结束时间
     * @param timeDuration     时长
     * @param signTime         签到时间 格式：yyyy-MM-dd HH:mm
     * @param signOffTime      签退时间 格式：yyyy-MM-dd HH:mm
     * @param empShiftDef      员工排班
     * @param validTimeCalType 计算类型
     * @param overtimeCalType  加班时长计算 1:申请时长与打卡时长取小值 2:以打卡时长为准
     * @param roundingRule     进位规则 1 向上取整0.25、2 向上取整0.5、3 向上取整1、4 向下取整0.25、5 向下取整0.5、 6 向下取整1、 7 四舍五入保留整数
     * @return
     */
    private Float calRealOtTime(Long start_time, Long end_time, BigDecimal timeDuration, Long signTime, Long signOffTime,
                                EmpShiftInfo empShiftDef, Integer validTimeCalType, Integer overtimeCalType, Integer roundingRule) {
        if (validTimeCalType == null || OtValidTimeCalTypeEnum.APPLY_TIME.getIndex().equals(validTimeCalType)) {
            // 按照申请时长计算
            return timeDuration.floatValue();
        }
        if (empShiftDef == null) {
            log.info("calRealOtTime get empShiftDef empty");
            return 0f;
        }
        if (start_time == null) {
            start_time = 0L;
        }
        if (end_time == null) {
            end_time = 0L;
        }
        Long realOtStartTime = 0L;
        Long realOtEndTime = 0L;
        BigDecimal validateTime = BigDecimal.ZERO;
        // 常量定义，提高代码可读性
        final BigDecimal HOURS_IN_SECONDS = new BigDecimal("3600");
        final BigDecimal MINUTES_IN_SECONDS = new BigDecimal("60");
        if (OtValidTimeCalTypeEnum.APPLY_REG_INTERSECTION.getIndex().equals(validTimeCalType)) {
            if (signOffTime != null && signTime != null && start_time <= signOffTime && end_time >= signTime) {
                if (signTime <= start_time && signOffTime >= end_time) {
                    realOtStartTime = start_time;
                    realOtEndTime = end_time;
                    validateTime = BigDecimal.valueOf(end_time - start_time);
                } else if (signTime <= start_time) {
                    realOtStartTime = start_time;
                    realOtEndTime = signOffTime;
                    validateTime = BigDecimal.valueOf(signOffTime - start_time);
                } else if (signOffTime <= end_time) {
                    realOtStartTime = signTime;
                    realOtEndTime = signOffTime;
                    validateTime = BigDecimal.valueOf(signOffTime - signTime);
                } else {
                    realOtStartTime = signTime;
                    realOtEndTime = end_time;
                    validateTime = BigDecimal.valueOf(end_time - signTime);
                }
            }
        } else {
            // 按打卡时长计算
            if (empShiftDef.getDateType() == 1) {
                Long shiftEndDate = empShiftDef.getWorkDate();
                if (CdWaShiftUtil.checkCrossNight(empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType())) {//跨夜
                    shiftEndDate += 86400;
                }
                Long shiftEndTime = shiftEndDate + (empShiftDef.getEndTime() * 60);
                if (signOffTime <= shiftEndTime) {
                    validateTime = BigDecimal.ZERO;
                } else {
                    realOtStartTime = Math.max(shiftEndTime, signTime);
                    realOtEndTime = signOffTime;
                    validateTime = BigDecimal.valueOf(signOffTime - Math.max(shiftEndTime, signTime));
                }
            } else {
                realOtStartTime = signTime;
                realOtEndTime = signOffTime;
                validateTime = BigDecimal.valueOf(signOffTime - signTime);
            }
        }
        BigDecimal validateTimeMinute = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(validateTime) < 0) {
            // 扣除加班休息时段的时长
            Integer shiftDate = empShiftDef.getWorkDate().intValue();
            List<ShiftRestPeriods> overtimeRestPeriods = empShiftDef.getOvertimeRestPeriods();
            if (CollectionUtils.isNotEmpty(overtimeRestPeriods)
                    && !OtValidTimeCalTypeEnum.REG_TIME_NOT_DEDUCTION.getIndex().equals(validTimeCalType)) {
                for (ShiftRestPeriods rest : overtimeRestPeriods) {
                    Integer restStart = shiftDate + rest.getOvertimeRestStartTime() * 60;
                    Integer restEnd;
                    // 跨夜
                    if (rest.getOvertimeRestStartTime() > rest.getOvertimeRestEndTime() ||
                            CdWaShiftUtil.checkCrossNight(empShiftDef.getStartTime(), empShiftDef.getEndTime(), empShiftDef.getDateType())) {
                        restEnd = shiftDate + 86400 + rest.getOvertimeRestEndTime() * 60;
                    } else {
                        restEnd = shiftDate + rest.getOvertimeRestEndTime() * 60;
                    }
                    if (realOtStartTime <= restEnd && realOtEndTime >= restStart) {
                        Long tm = Math.min(realOtEndTime, restEnd) - Math.max(realOtStartTime, restStart);
                        // 注意：这里原代码可能有误，应该是减去休息时间而不是加上
                        validateTime = validateTime.subtract(BigDecimal.valueOf(tm));
                        log.info("休息日和法定节假日休息时间扣减：员工ID={},日期={},排班={} 扣除了{}分钟",
                                empShiftDef.getEmpid(), empShiftDef.getWorkDate(),
                                empShiftDef.getShiftDefId(), tm / 60);
                    }
                }
            }
            // overtimeCalType 加班时长计算 1:申请时长与打卡时长取小值 2:以打卡时长为准
            BigDecimal applyTimeInSeconds = timeDuration.multiply(MINUTES_IN_SECONDS);
            if (OtValidTimeCalTypeEnum.REG_TIME.getIndex().equals(validTimeCalType) ||
                    OtValidTimeCalTypeEnum.REG_TIME_NOT_DEDUCTION.getIndex().equals(validTimeCalType)) {
                overtimeCalType = overtimeCalType == null ? OvertimeCalTypeEnum.MIN_APPLY_REGISTER.getIndex() : overtimeCalType;
                if (OvertimeCalTypeEnum.MIN_APPLY_REGISTER.getIndex().equals(overtimeCalType)) {
                    validateTime = validateTime.min(applyTimeInSeconds);
                }
            }
            if (OtValidTimeCalTypeEnum.APPLY_REG_INTERSECTION.getIndex().equals(validTimeCalType) &&
                    validateTime.compareTo(applyTimeInSeconds) > 0) {
                // 如果计算出的加班时长>申请的加班时长，两者取最小值
                validateTime = applyTimeInSeconds;
            }
            if (null != roundingRule) {
                // 进位
                BigDecimal validateTimeHour = validateTime.divide(HOURS_IN_SECONDS, 8, RoundingMode.HALF_UP);
                validateTimeHour = HandleMantissaUtil.handleMantissa(validateTimeHour, roundingRule);
                validateTimeMinute = validateTimeHour.multiply(MINUTES_IN_SECONDS);
            } else {
                validateTimeMinute = validateTime.divide(MINUTES_IN_SECONDS, 8, RoundingMode.HALF_UP);
            }
        }
        // 最终转换为float，确保使用String构造函数
        return validateTimeMinute.floatValue();
    }


    /**
     * 跨夜加班分析
     *
     * @param of               加班单
     * @param empShiftDef      班次
     * @param dto              基础配置
     * @param validTimeCalType 计算类型
     * @return
     */
    private Float calRealOtTime2(EmpOverInfo of, EmpShiftInfo empShiftDef, WaAnalyzCalDTO dto, Integer validTimeCalType, Integer overtimeCalType, Integer roundingRule) {
        Float time_duration = of.getTime_duration().floatValue();
        Long start_time = of.getStart_time();
        if (start_time == null) {
            start_time = 0L;
        }
        Long end_time = of.getEnd_time();
        if (end_time == null) {
            end_time = 0L;
        }
        Long preDate = DateUtil.addDate(of.getBelongDate() * 1000, -1);
        WaAnalyze preAnalyze = existsWaAnalyze(dto.getWaAnalyzeList(), of.getEmpid(), preDate);
        if (preAnalyze != null && preAnalyze.getRegSignoffTime() != null) {
            Long preSignOffTime = DateUtil.convertStringToDateTime(DateUtil.convertDateTimeToStr(preAnalyze.getRegSignoffTime(), "yyyy-MM-dd HH:mm", true), "yyyy-MM-dd HH:mm", true);
            if (start_time < preSignOffTime) {
                BigDecimal bdTimeDuration = new BigDecimal(String.valueOf(time_duration));
                time_duration = calRealOtTime(start_time, end_time, bdTimeDuration, 0L, preSignOffTime, empShiftDef, validTimeCalType, overtimeCalType, roundingRule);
            } else {
                time_duration = 0f;
            }
        } else {
            time_duration = 0f;
        }
        return time_duration;
    }

    private WaAnalyze existsWaAnalyze(List<WaAnalyze> resultWa, Long empid, Long leaveDate) {
        if (resultWa != null) {
            for (WaAnalyze waAnalyze : resultWa) {
                if (waAnalyze.getEmpid().intValue() == empid.intValue() && waAnalyze.getBelongDate().longValue() == leaveDate.longValue()) {
                    return waAnalyze;
                }
            }
        }
        return null;
    }

    private WaAnalyze getEmpAnalyze(String belongid, Long empid, long otBelongDate, WaAnalyzCalDTO dto) {

        WaAnalyze analyze = existsWaAnalyze(dto.getWaAnalyzeList(), empid, otBelongDate);
        if (analyze == null) {
            WaAnalyzeExample analyzeExample = new WaAnalyzeExample();
            analyzeExample.createCriteria().andEmpidEqualTo(empid).andBelongDateEqualTo(otBelongDate);
            List<WaAnalyze> analyzes = waAnalyzeMapper.selectByExample(analyzeExample);
            if (CollectionUtils.isNotEmpty(analyzes)) {
                return analyzes.get(0);
            }

            Map<String, Object> map = new HashMap<String, Object>();
            map.put("belongid", belongid);
            map.put("startDate", otBelongDate);
            map.put("endDate", otBelongDate + (23 * 60 * 60) + (59 * 60) + 59);
            map.put("empid", empid);
            // 查询请假当天的签到记录
            List<Map> reglist = waRegisterRecordMapper.getRegisterRecord(map);

            if (reglist != null && reglist.size() > 1) {
                List<WaRegisterRecord> registerRecords = new ArrayList<WaRegisterRecord>();
                for (Map regMap : reglist) {
                    WaRegisterRecord record2 = new WaRegisterRecord();
                    Integer recordId = (Integer) regMap.get("record_id");
                    Integer resultType = (Integer) regMap.get("result_type");
                    String resultDesc = (String) regMap.get("result_desc");
                    Long regDateTime = (Long) regMap.get("reg_date_time");
                    Long belongDate = (Long) regMap.get("belong_date");
                    Long empid2 = (Long) regMap.get("empid");
                    Integer shift_def_id = (Integer) regMap.get("shift_def_id");
                    Integer registerType = (Integer) regMap.get("register_type");

                    record2.setShiftDefId(shift_def_id);
                    record2.setRecordId(recordId);
                    record2.setEmpid(empid2);
                    record2.setRegisterType(registerType);
                    record2.setResultType(resultType);
                    record2.setResultDesc(resultDesc);
                    record2.setRegDateTime(regDateTime);
                    record2.setBelongDate(belongDate);
                    registerRecords.add(record2);
                }
                WaRegisterRecord singin = registerRecords.get(0);
                WaRegisterRecord singoff = registerRecords.get(1);
                Long siginOffTime = singoff.getRegDateTime();
                Long siginTime = singin.getRegDateTime();
                analyze = new WaAnalyze();
                analyze.setRegSigninTime(siginTime);
                analyze.setRegSignoffTime(siginOffTime);
                analyze.setBelongDate(DateUtil.getDateLong(siginTime * 1000, "yyyy-MM-dd", true));
            }
        }
        return analyze;
    }
}