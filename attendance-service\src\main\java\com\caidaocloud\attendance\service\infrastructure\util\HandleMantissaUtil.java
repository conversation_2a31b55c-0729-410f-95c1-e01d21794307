package com.caidaocloud.attendance.service.infrastructure.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 进位处理工具
 *
 * <AUTHOR>
 * @Date 2023/7/11
 */
public class HandleMantissaUtil {

    /**
     * 处理进位
     *
     * @param val  待处理数值
     * @param rule 进位规则 1 向上取整0.25、2 向上取整0.5、3 向上取整1、4 向下取整0.25、5 向下取整0.5、 6 向下取整1、 7 四舍五入保留整数
     * @return
     */
    public static BigDecimal handleMantissa(BigDecimal val, Integer rule) {
        if (null == rule) {
            return val;
        }
        switch (rule) {
            case 1:// 向上取整0.25
                val = handleMantissa(val.divide(BigDecimal.valueOf(0.25)), (short) 2, (short) 0).multiply(BigDecimal.valueOf(0.25));
                break;
            case 2:// 向上取整0.5
                val = handleMantissa(val.divide(BigDecimal.valueOf(0.5)), (short) 2, (short) 0).multiply(BigDecimal.valueOf(0.5));
                break;
            case 3:// 向上取整1
                val = handleMantissa(val, (short) 2, (short) 0);
                break;
            case 4:// 向下取整0.25
                val = handleMantissa(val.divide(BigDecimal.valueOf(0.25)), (short) 3, (short) 0).multiply(BigDecimal.valueOf(0.25));
                break;
            case 5:// 向下取整0.5
                val = handleMantissa(val.divide(BigDecimal.valueOf(0.5)), (short) 3, (short) 0).multiply(BigDecimal.valueOf(0.5));
                break;
            case 6:// 向下取整1
                val = handleMantissa(val, (short) 3, (short) 0);
                break;
            case 7:// 四舍五入保留整数
                val = handleMantissa(val, (short) 1, (short) 0);
                break;
            default:// 默认：四舍五入保留整数
                val = handleMantissa(val, (short) 1, (short) 0);
                break;
        }
        return val;
    }

    /**
     * 小数位处理
     *
     * @param value
     * @param type
     * @param scale
     * @return
     */
    public static BigDecimal handleMantissa(BigDecimal value, Short type, Short scale) {
        //四舍五入
        if (type != null) {
            if (scale == null || scale < 0) {
                scale = 0;
            }
            //四舍五入
            if (type == 1) {
                value = value.setScale(scale, BigDecimal.ROUND_HALF_UP);
            }
            //向上取整
            else if (type == 2) {
                value = value.setScale(scale, BigDecimal.ROUND_UP);
            }
            //向下取整
            else if (type == 3) {
                value = value.setScale(scale, BigDecimal.ROUND_DOWN);
            }
            //见分进角
            else if (type == 4) {
                DecimalFormat df = new DecimalFormat("#.00");
                value = new BigDecimal(df.format(value)).setScale(1, BigDecimal.ROUND_CEILING);
            }
            //见角进元
            else if (type == 5) {
                DecimalFormat df = new DecimalFormat("#.0");
                value = new BigDecimal(df.format(value)).setScale(0, BigDecimal.ROUND_CEILING);
            }
        }
        return value;
    }


    public static void main(String[] args) {
        // 1 向上取整0.25、2 向上取整0.5、3 向上取整1、4 向下取整0.25、5 向下取整0.5、 6 向下取整1、 7 四舍五入保留整数

        long time = BigDecimal.valueOf(1.735).multiply(BigDecimal.valueOf(3600)).longValue();
        Integer rule = 7;

        BigDecimal validateTimeHour = BigDecimal.valueOf(time).divide(BigDecimal.valueOf(3600), 4, BigDecimal.ROUND_HALF_UP);
        System.out.println("进位之前：" + validateTimeHour);

        validateTimeHour = HandleMantissaUtil.handleMantissa(validateTimeHour, rule);

        long validateTime = validateTimeHour.multiply(BigDecimal.valueOf(3600)).longValue();

        System.out.println(validateTimeHour + "----" + validateTime);
    }
}
