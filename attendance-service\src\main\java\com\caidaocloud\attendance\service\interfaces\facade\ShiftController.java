package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.ioc.util.GridUtil;
import com.caidao1.ioc.util.ListHelper;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.mobile.utils.JsonTool;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.WaShiftDto;
import com.caidaocloud.attendance.service.application.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.application.enums.FlexbleWorkTypeEnum;
import com.caidaocloud.attendance.service.application.enums.FlexibleEnum;
import com.caidaocloud.attendance.service.application.service.IShiftService;
import com.caidaocloud.attendance.service.application.service.IWorkRoundService;
import com.caidaocloud.attendance.service.domain.entity.WaWorkRoundDo;
import com.caidaocloud.attendance.service.interfaces.dto.RestPeriodDto;
import com.caidaocloud.attendance.service.interfaces.dto.ShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftRestDto;
import com.caidaocloud.attendance.service.interfaces.vo.FlexibleVo;
import com.caidaocloud.attendance.service.interfaces.vo.ShiftPageVo;
import com.caidaocloud.attendance.service.interfaces.vo.ShiftVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.TimeSlot;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j

@RestController
@RequestMapping("/api/attendance/shift/v1")
public class ShiftController {

    @Resource
    private WaAttendanceConfigService waConfigService;
    @Resource
    private IWorkRoundService workRoundService;
    @Resource
    private IShiftService shiftService;
    @Autowired
    private ISessionService sessionService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("查询班次分页列表")
    @PostMapping("/list")
    public Result<AttendancePageResult<ShiftPageVo>> getShiftList(@RequestBody ShiftPageDto dto) {
        try {
            AttendancePageResult<WaShiftDto> pageResult = shiftService.getShiftDefList(dto);
            if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
                List<ShiftPageVo> shiftDos = ObjectConverter.convertList(pageResult.getItems(), ShiftPageVo.class);
                shiftDos.forEach(row -> row.setDateType(DateTypeEnum.getName(Integer.parseInt(row.getDateType().toString()))));
                AttendancePageResult<ShiftPageVo> result = new AttendancePageResult<>(shiftDos, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
                return ResponseWrap.wrapResult(result);
            }
            return ResponseWrap.wrapResult(new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0));
        } catch (Exception ex) {
            log.error("ShiftController.getShiftList executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    /**
     * 保存班次时，检查班次休息时间段超出上下班时间
     *
     * @param dto
     * @return
     */
    private boolean checkRestTime(ShiftDto dto) {
        if (dto.getRestPeriods() == null) {
            return false;
        }
        List<ShiftRestDto> restTimes = JSON.parseArray(JSON.toJSONString(dto.getRestPeriods()), ShiftRestDto.class);
        if (CollectionUtils.isEmpty(restTimes)) {
            return false;
        }
        boolean isky = CdWaShiftUtil.checkCrossNight(dto.getStartTime(), dto.getEndTime(), dto.getDateType());
        Integer startTime = dto.getStartTime();
        Integer endTime = dto.getEndTime();
        if (isky) {
            endTime = endTime + 24 * 60;
        }
        for (ShiftRestDto r : restTimes) {
            Integer noonRestStart = r.getNoonRestStart();
            Integer noonRestEnd = r.getNoonRestEnd();
            if (!isky) {
                if (noonRestStart < startTime || noonRestEnd > endTime) {
                    return true;
                }
            } else {
                ShiftRestPeriods restPeriod = CdWaShiftUtil.getShiftWorkRestTime(noonRestStart, noonRestEnd, startTime, dto.getEndTime(), dto.getDateType());
                noonRestStart = restPeriod.getNoonRestStart();
                noonRestEnd = restPeriod.getNoonRestEnd();
                if (noonRestEnd < startTime || noonRestStart > endTime || noonRestStart < startTime || noonRestEnd > endTime) {
                    return true;
                }
            }
        }
        return false;
    }

    @ApiOperation("保存班次")
    @PostMapping("/save")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-班次设置-班次")
    public Result<Boolean> saveShiftDef(@Valid @RequestBody ShiftDto dto) {
        dto.initShiftDefName();
        dto.initI18nShiftDefName();
        try {
            if (null == dto.getDateType()) {
                return ResponseWrap.wrapResult(AttendanceCodes.DATE_TYPE_IS_NULL, Boolean.FALSE);
            }
            if (!StringUtil.isNotBlank(dto.getShiftDefName())) {
                return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_IS_NULL, Boolean.FALSE);
            }
            if (dto.getShiftDefName().length() > 100) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_IS_TOO_LONG, null).getMsg(), 100));
            }
            if (!StringUtil.isNotBlank(dto.getShiftDefCode())) {
                return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CODE_IS_NULL, Boolean.FALSE);
            }
            if (dto.getShiftDefCode().length() > 100) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CODE_IS_TOO_LONG, null).getMsg(), 100));
            }
            if (FlexibleEnum.OPEN.getIndex().equals(dto.getFlexibleShiftSwitch()) && dto.getStartTime() != null && dto.getEndTime() != null && dto.getStartTime() > dto.getEndTime()) {
                return ResponseWrap.wrapResult(AttendanceCodes.CROSS_NIGHT_SHIFT_NOT_FLEXIBILITY, Boolean.FALSE);
            }
            if (!((dto.getOvertimeStartTime() == null && dto.getOvertimeEndTime() == null) || (dto.getOvertimeStartTime() != null && dto.getOvertimeEndTime() != null))) {
                return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_PERIOD_NOT_SET, Boolean.FALSE);
            }
            if (checkRestTime(dto)) {
                return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_REST_EXCEEDS_COMMUTING, Boolean.FALSE);
            }
            if (shiftService.checkShiftDefReCode(dto.getShiftDefCode(), dto.getShiftDefId())) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SHIFT_CODE_DUPLICATED, Boolean.FALSE).getMsg(), dto.getShiftDefCode()));
            }
            if (shiftService.checkShiftDefReName(dto.getShiftDefName(), dto.getShiftDefId())) {
                return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NAME_EXIST, Boolean.FALSE);
            }
            if (dto.getFlexibleRules() != null) {
                String jsonStr = JsonTool.createJsonString(dto.getFlexibleRules());
                dto.setFlexibleOffWorkRule(jsonStr);
            }
            WaShiftDef record = ObjectConverter.convert(dto, WaShiftDef.class);
            if (null != dto.getI18nShiftDefName()) {
                record.setI18nShiftDefName(FastjsonUtil.toJson(dto.getI18nShiftDefName()));
            }
            if (CollectionUtils.isNotEmpty(dto.getRepairRestPeriods())) {
                if (dto.getRepairRestPeriods().stream().anyMatch(t -> t.getStartTime() == null || t.getEndTime() == null)) {
                    return ResponseWrap.wrapResult(AttendanceCodes.TIME_RULE_ERROR, Boolean.FALSE);
                }
                record.setClockTimeLimit(JSONUtils.ObjectToJson(dto.getRepairRestPeriods()));
            }
            if (CollectionUtils.isNotEmpty(dto.getMidwayClockTimes())) {
                if (dto.getMidwayClockTimes().stream().anyMatch(t -> t.getStartTime() == null || t.getEndTime() == null)) {
                    return ResponseWrap.wrapResult(AttendanceCodes.CLOCK_TIME_RULE_ERROR, Boolean.FALSE);
                }
                record.setMidwayClockTime(FastjsonUtil.toJson(dto.getMidwayClockTimes()));
            }
            record.setIsNight(false);
            shiftService.saveShiftDef(record, CommonConstant.LANG_CN);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("ShiftController.saveShiftDef executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("查询班次详情")
    @GetMapping(value = "/detail")
    public Result<ShiftVo> getShiftDef(@RequestParam(value = "id") Integer id) {
        try {
            UserInfo userInfo = this.getUserInfo();
            WaShiftDef shiftDef = waConfigService.getShiftDefObj(userInfo.getTenantId(), id);
            List<RestPeriodDto> allRestPeriodList = new ArrayList<>();
            if (shiftDef.getNoonRestStart() != null) {
                allRestPeriodList.add(new RestPeriodDto(shiftDef.getNoonRestStart(), shiftDef.getNoonRestEnd()));
            }
            if (shiftDef.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getRestPeriods();
                List<RestPeriodDto> restPeriods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<RestPeriodDto>>() {
                });
                if (CollectionUtils.isNotEmpty(restPeriods)) {
                    allRestPeriodList.addAll(restPeriods);
                }
            }
            shiftDef.setRestPeriods(allRestPeriodList);
            Map map = GridUtil.convertBean2Form(shiftDef);
            if (map.size() == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_NOT_EXIST, null);
            }
            Map dataMap = (Map) map.get("data");
            String i18nShiftDefName = null;
            if (dataMap.containsKey("i18nShiftDefName")) {
                i18nShiftDefName = (String) dataMap.get("i18nShiftDefName");
                dataMap.remove("i18nShiftDefName");
            }
            ShiftVo vo = JsonTool.json2povo(JSON.toJSONString(dataMap, SerializerFeature.WriteNullStringAsEmpty,
                    SerializerFeature.WriteNullNumberAsZero), ShiftVo.class);
            if (StringUtils.isNotBlank(i18nShiftDefName)) {
                vo.setI18nShiftDefName(FastjsonUtil.toObject(i18nShiftDefName, Map.class));
            } else if (StringUtils.isNotBlank(shiftDef.getShiftDefName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", shiftDef.getShiftDefName());
                vo.setI18nShiftDefName(i18nName);
            }
            if (vo.getFlexibleWorkRule() == null) {
                vo.setFlexibleWorkRule(FlexbleWorkTypeEnum.LATE_TO_LATE.getIndex());
            }
            String jsonStr = vo.getFlexibleOffWorkRule();
            List<FlexibleVo> list = null;
            if (StringUtils.isNotBlank(jsonStr)) {
                list = JsonTool.json2list(jsonStr, FlexibleVo.class);
            }
            vo.setFlexibleRules(list);
            if (StringUtil.isNotBlank(shiftDef.getClockTimeLimit())) {
                vo.setRepairRestPeriods(JsonTool.json2list(shiftDef.getClockTimeLimit(), TimeSlot.class));
            }
            if (StringUtils.isNotBlank(shiftDef.getMidwayClockTime())) {
                vo.setMidwayClockTimes(FastjsonUtil.toArrayList(shiftDef.getMidwayClockTime(), TimeSlot.class));
            }
            return ResponseWrap.wrapResult(vo);
        } catch (Exception ex) {
            log.error("ShiftController.saveShiftDef executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @ApiOperation("删除班次")
    @DeleteMapping(value = "/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-班次设置-班次")
    public Result<Boolean> deleteShiftDef(@RequestParam("id") Integer id) {
        try {
            List<WaWorkRoundDo> list = workRoundService.getWorkRoundListByShiftId(id);
            if (CollectionUtils.isNotEmpty(list)) {
                return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_DELETE_NOT_ALLOW, Boolean.FALSE);
            }
            waConfigService.deleteShiftDef(id);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("ShiftController.saveShiftDef executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("查询班次下拉列表")
    @GetMapping(value = "/selectShiftDefList")
    public Result selectShiftDefList() {
        try {
            List<Map> list = waConfigService.getShiftDefList(new PageBean(true));
            Map optMap = ListHelper.convertMapList(list, new ListHelper.LabelValueBeanCreator<Map>() {
                @Override
                public Object getValue(Map t) {
                    return t.get("shift_def_id");
                }

                @Override
                public String getLabel(Map t) {
                    return (String) t.get("shift_def_name");
                }
            }, false);
            Map<String, Object> map = new HashMap<>();
            map.put("items", optMap.get("options"));
            return ResponseWrap.wrapResult(map);
        } catch (Exception ex) {
            log.error("ShiftController.selectShiftDefList executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SELECT_OPTION_FAILED, new AttendancePageResult<>());
        }
    }
}
