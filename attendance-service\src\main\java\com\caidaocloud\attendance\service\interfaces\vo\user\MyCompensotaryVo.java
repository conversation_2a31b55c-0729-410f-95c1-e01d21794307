package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyCompensotaryVo {

    @ApiModelProperty("日期类型")
    private Integer overTimeType;
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("生效日期")
    private Long lastDate;
    @ApiModelProperty("调休额度")
    private Float quotaDay;
    @ApiModelProperty("已使用")
    private Float usedDay;
    @ApiModelProperty("流程中")
    private Float inTransitQuota;
    @ApiModelProperty("剩余")
    private Float leftDay;
    @ApiModelProperty("单位 1:天 2:小时")
    private Integer unit;
    @ApiModelProperty("加班日期")
    private Long overtimeDate;
    @ApiModelProperty("是否可申请调休转付现：true(是)/false(否)")
    private boolean applyCompensatoryCash;

    private Long empQuotaId;
}
