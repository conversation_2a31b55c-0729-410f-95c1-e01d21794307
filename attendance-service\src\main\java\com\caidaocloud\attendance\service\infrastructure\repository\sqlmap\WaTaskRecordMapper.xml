<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaTaskRecordMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTaskRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="progress" jdbcType="INTEGER" property="progress" />
    <result column="upload_files" jdbcType="VARCHAR" property="uploadFiles" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, type, progress, upload_files, status, reason, ext, create_by, create_time,
    update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_task_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_task_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTaskRecord">
    insert into wa_task_record (id, tenant_id, type, 
      progress, upload_files, status,
      reason, ext, create_by, 
      create_time, update_by, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{progress,jdbcType=INTEGER}, #{uploadFiles,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
      #{reason,jdbcType=VARCHAR}, #{ext,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTaskRecord">
    insert into wa_task_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="progress != null">
        progress,
      </if>
      <if test="uploadFiles != null">
        upload_files,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="progress != null">
        #{progress,jdbcType=INTEGER},
      </if>
      <if test="uploadFiles != null">
        #{uploadFiles,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTaskRecord">
    update wa_task_record
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="progress != null">
        progress = #{progress,jdbcType=INTEGER},
      </if>
      <if test="uploadFiles != null">
        upload_files = #{uploadFiles,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaTaskRecord">
    update wa_task_record
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      progress = #{progress,jdbcType=INTEGER},
      upload_files = #{uploadFiles,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>