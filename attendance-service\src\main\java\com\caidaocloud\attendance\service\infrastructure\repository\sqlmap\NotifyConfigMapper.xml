<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.NotifyConfigMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.NotifyConfigPo">
        <id column="notify_config_id" jdbcType="BIGINT" property="notifyConfigId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="clock_notify_switch" jdbcType="INTEGER" property="clockNotifySwitch"/>
        <result column="work_time" jdbcType="INTEGER" property="workTime"/>
        <result column="off_work_time" jdbcType="INTEGER" property="offWorkTime"/>
        <result column="daily_notify_switch" jdbcType="INTEGER" property="dailyNotifySwitch"/>
        <result column="daily_notify_rule" jdbcType="INTEGER" property="dailyNotifyRule"/>
        <result column="daily_notify_time" jdbcType="INTEGER" property="dailyNotifyTime"/>
        <result column="weekly_notify_switch" jdbcType="INTEGER" property="weeklyNotifySwitch"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>

        <result column="work_notify_content" jdbcType="VARCHAR" property="workNotifyContent"/>
        <result column="off_work_notify_content" jdbcType="VARCHAR" property="offWorkNotifyContent"/>
        <result column="leave_cancel_switch" jdbcType="INTEGER" property="leaveCancelSwitch"/>
        <result column="leave_cancel_content" jdbcType="VARCHAR" property="leaveCancelContent"/>
        <result column="leave_cancel_time" jdbcType="INTEGER" property="leaveCancelTime"/>
        <result column="leave_cancel_frequency" jdbcType="INTEGER" property="leaveCancelFrequency"/>
        <!-- CAIDAOM-1846 -->
        <result column="daily_notify_abnormal" jdbcType="INTEGER" property="dailyNotifyAbnormal"/>
        <result column="weekly_notify_frequency" jdbcType="INTEGER" property="weeklyNotifyFrequency"/>
        <result column="weekly_notify_time" jdbcType="INTEGER" property="weeklyNotifyTime"/>
        <result column="weekly_notify_type" jdbcType="INTEGER" property="weeklyNotifyType"/>
        <!-- CAIDAOM-2049 -->
        <result column="abnormal_switch" jdbcType="INTEGER" property="abnormalSwitch"/>
        <result column="abnormal_type" jdbcType="INTEGER" property="abnormalType"/>
        <result column="abnormal_time" jdbcType="VARCHAR" property="abnormalTime"/>
        <result column="abnormal_msg" jdbcType="VARCHAR" property="abnormalMsg"/>
        <!-- CAIDAOM-3321 -->
        <result column="attendance_detail_switch" jdbcType="INTEGER" property="attendanceDetailSwitch"/>
        <result column="attendance_detail_time" jdbcType="VARCHAR" property="attendanceDetailTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      notify_config_id,tenant_id,clock_notify_switch,work_time,off_work_time,
      daily_notify_switch,daily_notify_rule,daily_notify_time,weekly_notify_switch,
      deleted,create_by,create_time,update_by,update_time,work_notify_content,off_work_notify_content,
      leave_cancel_switch,leave_cancel_content,leave_cancel_time,leave_cancel_frequency,daily_notify_abnormal,
      weekly_notify_frequency,weekly_notify_time,weekly_notify_type,abnormal_switch,abnormal_type,abnormal_time,abnormal_msg,
      attendance_detail_switch, attendance_detail_time
    </sql>

    <select id="getNotifyConfigPageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wa_notify_config where deleted = 0
    </select>

</mapper>