package com.caidaocloud.attendance.service.interfaces.dto.group;

import com.caidaocloud.attendance.core.wa.enums.LeaveOutCheckRuleEnum;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class GroupDetailDto {
    @ApiModelProperty("方案id")
    private Integer waGroupId;
    @ApiModelProperty("方案名称")
    private String waGroupName;
    @ApiModelProperty("公司id")
    private String belongOrgid;
    @ApiModelProperty("加班类型id，多个以逗号分隔")
    private Object otTypeIds;
    @ApiModelProperty("加班类型名称，多个以逗号分隔")
    private String otTypeNames;
    @ApiModelProperty("休假类型id，多个以逗号分隔")
    private Object leaveTypeIds;
    @ApiModelProperty("休假类型名称，多个以逗号分隔")
    private String leaveTypeNames;
    @ApiModelProperty("true适用全部员工/false适用部分员工")
    private Boolean isDefault;
    @ApiModelProperty("分析规则id")
    private Integer parseGroupId;
    @ApiModelProperty("考勤周期：上月1，本月2")
    private Integer cyleMonth;
    @ApiModelProperty("考勤周期：起始日")
    private Integer cyleStartdate;
    @ApiModelProperty("是否超出上限控制:true/false")
    private boolean outLimitControl;
    @ApiModelProperty("请输入加班上限（单位小时）")
    private Integer outLimit;
    @ApiModelProperty("适用人员")
    private List<KeyValue> employees;
    @ApiModelProperty("适用员工id")
    private List<Long> empIds;
    @ApiModelProperty("标准时长字段，定义每天工作多长时间，单位为小时")
    private BigDecimal workingTime;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;

    private Boolean reasonMust;
    @ApiModelProperty("自动生成考勤周期开关，默认false(关闭)， true(开启)")
    private Boolean autoPeriod;
    @ApiModelProperty("自动生成周期月份，默认值1，本月，2下月")
    private Integer periodCycleMonth;
    @ApiModelProperty("自动生成周期截止日，默认1号")
    private Integer periodStartDate;
    @ApiModelProperty("同一天是否允许申请多条加班单  默认true(开启),false(关闭) ")
    private Boolean overtimeControl;
    @ApiModelProperty("加班时效性控制")
    private Boolean isOpenTimeControl;
    @ApiModelProperty("加班时效性类型 0:提前 1:延后")
    private Integer timeControlType;
    @ApiModelProperty("加班时效性时长")
    private Float controlTimeDuration;
    @ApiModelProperty("加班时效性单位 1:天 2:小时")
    private Integer controlTimeUnit;

    @ApiModelProperty("员工查看考勤范围:0 全部日期，1本月，2上月")
    private Integer calendarDataRange;
    @ApiModelProperty("当月计薪的休假数据:1审批通过的休假单据，2审批中及审批通过的休假单,默认1")
    private Integer leaveStatus;
    @ApiModelProperty("跨夜加班归属：1归属至加班开始日期 2根据零点拆分，默认值1")
    private Integer overtimeBelong;
    @ApiModelProperty("同一天是否可申请多种加班类型:true/false,默认true")
    private Boolean overtimeTypeControl;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nWaGroupName;
    @ApiModelProperty("自定义表达式条件")
    private ConditionTree groupExpCondition;

    @ApiModelProperty("批量申请休假时是否支持假期类型多选：true 多选、false 单选")
    private Boolean multiLeaveTypeOnBatch;

    @ApiModelProperty("休假外出申请校验规则：1 休假外出时间不允许重叠、2 休假外出允许时间重叠")
    private Integer leaveOutCheckRule;

    @ApiModelProperty("允许和出差共存：true 允许 、 false 不允许")
    private Boolean coexistForLeaveOut;

    public boolean doCheckCoexistForLeaveOut() {
        return LeaveOutCheckRuleEnum.LEAVE_OUT_COEXIST.getIndex().equals(this.leaveOutCheckRule);
    }
}
