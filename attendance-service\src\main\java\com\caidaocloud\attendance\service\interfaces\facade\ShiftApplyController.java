package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.impl.WaShiftApplyService;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ChangeShiftReqDto;
import com.caidaocloud.attendance.service.interfaces.vo.shift.ChangeShiftVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.RevokeEmpShiftDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR> Hanley
 * @Date 2022/3/16 11:36
 * @Version 1.0
 */
@Slf4j
@RestController
@Api(value = "/api/attendance/shiftApply/v1", description = "调班申请接口")
@RequestMapping("/api/attendance/shiftApply/v1")
public class ShiftApplyController {

    @Autowired
    private WaShiftApplyService waShiftApplyService;
    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @PostMapping("/list")
    @ApiOperation(value = "调班记录分页列表")
    public Result<PageResult<ChangeShiftVo>> getChangeShiftList(@RequestBody ChangeShiftReqDto reqDto, HttpServletRequest request) {
        UserInfo userInfo = this.getUserInfo();
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            reqDto.setDataScope(orgDataScope);
        }
        PageResult<ApplyShiftRecordDto> pageResult = waShiftApplyService.pageList(reqDto, userInfo);
        List<ChangeShiftVo> voList = ObjectConverter.convertList(pageResult.getItems(), ChangeShiftVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @PostMapping(value = "/revokeEmpShift")
    @ApiOperation(value = "撤销员工调班申请")
    @LogRecordAnnotation(success = "撤销了{empName{#empId}}", category = "撤销", menu = "排班管理-调班记录")
    public Result<Boolean> revokeEmpShift(@RequestBody RevokeEmpShiftDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        UserInfo userInfo = this.getUserInfo();
        try {
            return waShiftApplyService.revoke(dto.getRecId(), dto.getRevokeReason(), userInfo);
        } catch (Exception e) {
            log.error("ShiftApplyController.revokeEmpTravel executes exception, {}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, Boolean.FALSE);
        }
    }
}
