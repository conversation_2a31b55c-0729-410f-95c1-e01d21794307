package com.caidaocloud.attendance.service.interfaces.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * 出差申请
 *
 * <AUTHOR>
 */
@Data
public class EmpTravelSaveDto {
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("出差类型ID")
    private Long travelTypeId;
    @ApiModelProperty("开始日期")
    private Long startTime;
    @ApiModelProperty("结束日期")
    private Long endTime;
    @ApiModelProperty("是否选择半天 1 选择 0 不选择")
    private Integer showDay;
    @ApiModelProperty("是否选择小时 1 选择 0 不选择")
    private Integer showMin;
    @ApiModelProperty("半天开始 A 上半天 P 下半天")
    private String shalfDay;
    @ApiModelProperty("半天结束 A 上半天 P 下半天")
    private String ehalfDay;
    @ApiModelProperty("开始时间 类型分钟 ")
    private Integer stime;
    @ApiModelProperty("结束时间 类型分钟 ")
    private Integer etime;
    @ApiModelProperty("出差地省ID")
    private Long province;
    @ApiModelProperty("出差地市ID")
    private Long city;
    @ApiModelProperty("出差方式")
    private Integer[] travelMode;
    @ApiModelProperty("事由")
    private String reason;
    @ApiModelProperty("附件名称，多个使用逗号隔开")
    private String fileName;
    @ApiModelProperty("附件ID，多个使用逗号隔开")
    private String file;
    @ApiModelProperty("接口请求类型：1 仅校验 2 校验且保存")
    private Integer opt = 1;
    @ApiModelProperty("wa_emp_travel自定义字段信息")
    private String extCustomCol;
    @ApiModelProperty("批量流程ID，可选，批量发起时使用")
    private Long batchTravelId;
    @ApiModelProperty("wa_emp_travel_daytime自定义字段信息")
    private Map<Long, String> travelDayTimeExtInfoMap;
    @ApiModelProperty("是否为批量出差")
    private boolean ifBtchTravel;

    public String getDayTimeExtInfo(Long travelDate) {
        if (MapUtils.isEmpty(travelDayTimeExtInfoMap) || !travelDayTimeExtInfoMap.containsKey(travelDate)) {
            return null;
        }
        return travelDayTimeExtInfoMap.get(travelDate);
    }
}
