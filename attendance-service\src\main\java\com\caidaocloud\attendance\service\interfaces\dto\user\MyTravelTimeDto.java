package com.caidaocloud.attendance.service.interfaces.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyTravelTimeDto {
    @ApiModelProperty("单据ID")
    private Long travelId;
    @ApiModelProperty("出差类型")
    private String travelType;
    @ApiModelProperty("出差开始时间")
    private Long startTime;
    @ApiModelProperty("出差结束时间")
    private Long endTime;
    @ApiModelProperty("半天开始")
    private String shalfDay;
    @ApiModelProperty("半天结束")
    private String ehalfDay;
    @ApiModelProperty("申请时长")
    private Float timeDuration;
    @ApiModelProperty("申请单位")
    private Short timeUnit;
    @ApiModelProperty("出差单位")
    private Short periodType;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态")
    private String statusName;
    @ApiModelProperty("出差时间文本")
    private String travelTimeTxt;
    @ApiModelProperty("审批流程key")
    private String businessKey;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long createTime;
    private Integer funcType;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    @ApiModelProperty("申请类型")
    private String applyName;
    private String i18nTravelTypeName;
}
