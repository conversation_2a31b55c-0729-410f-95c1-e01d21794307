package com.caidaocloud.attendance.service.interfaces.dto.leaveExtension;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LeaveExtensionQuotaDto {
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("生效日期")
    private Long lastDate;
    @ApiModelProperty("额度")
    private Float quotaDay;
    @ApiModelProperty("已使用")
    private Float usedDay;
    @ApiModelProperty("流程中")
    private Float inTransitQuota;
    @ApiModelProperty("调整额度")
    private Float adjustQuota;
    @ApiModelProperty("剩余")
    private Float leftDay;
    @ApiModelProperty("单位 1:天 2:小时")
    private Integer unit;
    @ApiModelProperty("当前余额")
    private Float currentQuota;
    private Long configId;
    private Long empQuotaId;
    @ApiModelProperty("年份")
    private Integer periodYear;

    @ApiModelProperty("延期开始日期")
    private Long extensionStartDate;
    @ApiModelProperty("延期结束日期")
    private Long extensionEndDate;
}