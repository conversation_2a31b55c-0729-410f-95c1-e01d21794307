package com.caidaocloud.attendance.service.interfaces.dto.compensatory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CompensatoryCaseItemDto {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("员工")
    private String empName;
    @ApiModelProperty("员工")
    private String workNo;
    @ApiModelProperty("申请付现额度")
    private Float applyDuration;
    @ApiModelProperty("付现生效中有效额度")
    private Float validDuration;
    @ApiModelProperty("单位：1、天，2、小时")
    private Integer timeUnit;
    @ApiModelProperty("单位：1、天，2、小时")
    private String timeUnitName;
    @ApiModelProperty("审批状态:1、审批中，2、已通过，9、已撤销")
    private Integer status;
    @ApiModelProperty("审批状态:1、审批中，2、已通过，9、已撤销")
    private String statusName;
    @ApiModelProperty("审批日期")
    private Long lastApprovalTime;
    @ApiModelProperty("申请日期")
    private Long createTime;
    @ApiModelProperty("备注")
    private String note;
    private String businessKey;
}
