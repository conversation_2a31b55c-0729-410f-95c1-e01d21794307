<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper">

    <select id="getOvertimeList" parameterType="hashmap" resultType="hashmap">
        SELECT *
        FROM (
            SELECT a.ot_id AS "waidtype",
                a.ot_id AS waid,
                b.workno,
                b.emp_name,
                b.belong_org_id,
                a.crtuser,
                '' AS wa_name,
                sco.shortname,
                case
                when sco.full_path is not null and sco.full_path != ''
                then concat_ws('/', sco.full_path, sco.shortname)
                else sco.shortname
                end  as   "fullPath",
                b.orgid,
                to_timestamp(a.start_time)::timestamp without time zone || ' --&gt; ' || to_timestamp(a.end_time)::timestamp without time zone AS time_slot,
                d.ot_duration duration,
                2 AS time_unit,
                a.crttime,
                a.status,
                2 AS watype,
                ot.date_type,
                a.last_approval_time,
                ot.compensate_type,
                a.reason,
                d.start_time,
                d.end_time,
                d.overtime_type_id,
                b.employ_type,
                d.transfer_duration,
                d.transfer_unit,
                ot.rule_id,
                d.detail_id
            FROM wa_emp_overtime a
            JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted=0
            JOIN (SELECT min(weod.detail_id) detail_id,
                min(weod.start_time) start_time,
                max(weod.end_time) end_time,
                weod.overtime_id,
                weod.overtime_type_id,
                weod.transfer_unit,
                COALESCE(SUM(weod.transfer_duration), 0) AS transfer_duration,
                COALESCE(SUM(weod.time_duration), 0) AS ot_duration
                FROM wa_emp_overtime_detail weod
                GROUP BY weod.overtime_id,weod.overtime_type_id,weod.transfer_unit) d on d.overtime_id=a.ot_id
            LEFT JOIN sys_corp_org sco ON sco.orgid = b.orgid AND sco.deleted = 0
            LEFT JOIN wa_overtime_type ot on ot.overtime_type_id=d.overtime_type_id
            <where>
                <if test="datafilter != null and datafilter != ''">
                    ${datafilter}
                </if>
                <if test="belongOrgId != null">
                    AND b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
                </if>
                AND a.status <![CDATA[>]]> 0 AND (a.forgid IS NULL OR a.forgid != 0)
                <if test="filterStatus != null and filterStatus.length > 0">
                    <foreach collection="filterStatus" open=" and a.status in (" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="empId != null">
                    AND a.empid = #{empId}
                </if>
                <if test="keywords != null and keywords != ''">
                    AND (b.workno LIKE concat('%', #{keywords}, '%') OR b.emp_name LIKE concat('%', #{keywords}, '%'))
                </if>
            </where>
        ) AS t
        <where>
            <if test="filter != null">
                ${filter}
            </if>
            <if test="startDateTime != null">
                AND (
                    (t.start_time  <![CDATA[>=]]> #{startDateTime} AND t.start_time <![CDATA[<=]]> #{endDateTime})
                    OR (t.end_time <![CDATA[>=]]> #{startDateTime} AND t.end_time <![CDATA[<=]]> #{endDateTime})
                    OR (t.start_time <![CDATA[<=]]> #{startDateTime} AND t.end_time <![CDATA[>=]]> #{endDateTime})
                )
            </if>
        </where>
    </select>

    <select id="getTimeDurationByOtIds" parameterType="java.lang.String" resultType="hashmap">
        SELECT overtime_type_id,overtime_id,COALESCE(SUM(weod.rel_time_duration), 0) AS rel_time_duration
        FROM wa_emp_overtime_detail weod
        WHERE weod.overtime_id IN (${otIds}) GROUP BY overtime_id,overtime_type_id
    </select>

    <select id="getRelTimeDurationByOtIds" parameterType="java.lang.String" resultType="hashmap">
        SELECT overtime_id as overtimeId,COALESCE(SUM(weod.rel_time_duration), 0) AS relTimeDuration
        FROM wa_emp_overtime_detail weod
        WHERE weod.overtime_id IN (${otIds}) GROUP BY overtime_id
    </select>


    <select id="getRelTimeDurationByBatchIds" parameterType="java.util.List" resultType="hashmap">
        SELECT
        wbo.batch_id,
        COALESCE(SUM(weod.rel_time_duration), 0) AS rel_time_duration
        FROM
        wa_batch_overtime wbo
        JOIN
        wa_emp_overtime weo
        ON weo.batch_id = wbo.batch_id
        AND weo.status IN (1, 2)
        JOIN
        wa_emp_overtime_detail weod
        ON weod.overtime_id = weo.ot_id
        WHERE
        wbo.batch_id IN
        <foreach collection="batchIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        wbo.batch_id;

    </select>

    <select id="searchRegMonthList" parameterType="map" resultType="map">
        SELECT * FROM (
            SELECT
                a.empid,
                a.late_time,
                a.early_time,
                a.work_time,
                a.leave_time,
                a.is_exp,
                a.actual_work_time,
                a.is_kg,
                a.is_shift,
                a.shift_def_id,
                a.kg_work_time,
                a.register_time,
                a.ot_column_jsob,
                a.level_column_jsonb,
                a.belong_org_id,
                a.bdk_count,
                a.belong_date,
                tt.count AS "registerErrorCount",
                COALESCE(a.actual_work_time, 0) AS act_work_time,
                tt1.error_count
                <if test="isOpenWaAnalyseAdjust != null and isOpenWaAnalyseAdjust == true">
                    ,aj.late_time AS "late_time_adjust",
                    aj.early_time AS "early_time_adjust",
                    aj.actual_work_time AS "actual_work_time_adjust",
                    aj.kg_work_time AS "kg_work_time_adjust"
                </if>
                ${columns}
            FROM wa_analyze a
            INNER JOIN sys_emp_info e ON a.empid = e.empid and e.deleted = 0
            <if test="isOpenWaAnalyseAdjust != null and isOpenWaAnalyseAdjust == true">
                LEFT JOIN wa_analyze_adjust aj on aj.empid = a.empid AND aj.belong_date = a.belong_date
            </if>
            LEFT JOIN (
                SELECT count(1) AS error_count, a1.belong_date, a1.empid
                FROM wa_register_record a1
                WHERE result_type = 2 GROUP BY a1.empid, a1.belong_date
            ) tt1 ON tt1.empid = a.empid AND tt1.belong_date = a.belong_date AND NOT (a.late_time = 0 AND a.early_time = 0)
            LEFT JOIN (
                SELECT a.empid,
                belong_date,
                COUNT (1) count
                FROM wa_register_record a JOIN sys_emp_info e ON a.empid = e.empid and e.deleted = 0
                WHERE his_reg_time IS NOT NULL AND (approval_status IS NULL OR approval_status = 2 OR approval_status = -1)
                <choose>
                    <when test="isInterval==1">
                        AND belong_date BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <otherwise>
                        AND belong_date BETWEEN getEmpMonthStartDay(e.belong_org_id, #{ym}, e.empid)
                        AND getEmpMonthEndDay(e.belong_org_id, #{ym}, e.empid)
                    </otherwise>
                </choose>
                <if test="excludeDate != null and excludeDate.size > 0">
                    AND belong_date NOT IN <foreach collection="excludeDate" item="item" open="(" separator="," close=")">#{item}</foreach>
                </if>
                GROUP BY a.empid,belong_date
            ) tt ON tt.empid = a.empid AND tt.belong_date = a.belong_date
            <where>
                <choose>
                    <when test="isInterval==1">
                        a.belong_date BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <otherwise>
                        a.belong_date BETWEEN getEmpMonthStartDay(e.belong_org_id, #{ym}, e.empid)
                        AND getEmpMonthEndDay(e.belong_org_id, #{ym}, e.empid)
                    </otherwise>
                </choose>
                <if test="belongid != null">
                    AND e.belong_org_id = #{belongid}
                </if>
                <if test="anyEmpid != null">
                    AND e.empid = ANY(${anyEmpid})
                </if>
                <if test="excludeDate != null and excludeDate.size > 0">
                    AND a.belong_date NOT IN <foreach collection="excludeDate" item="item" open="(" separator="," close=")">#{item}</foreach>
                </if>
                <if test="empIds != null and empIds.size > 0">
                     and e.empid in
                     <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
                         #{empId}
                     </foreach>
                </if>
                <if test="keywords != null and keywords != ''">
                    AND (e.workno like concat('%', #{keywords}, '%') or e.emp_name like concat('%', #{keywords}, '%'))
                </if>
                <if test="datafilter != null and datafilter != ''">
                    ${datafilter}
                </if>
            </where>
        ) AS e
        <where>
            ${wafilter}
        </where>
        ORDER BY empid
    </select>
    <select id="getEmpOtList" resultType="java.util.Map">
        SELECT
        ot.ot_id "otId",
        ot.empid,
        od.detail_id                                      as "overtimeDetailId",
        od.start_time                                     as "startTime",
        od.end_time                                       as "endTime",
        od.time_duration                                  as "overtimeDuration",
        od.start_time - ((od.start_time + 28800) % 86400) AS "belongDate",
        od.date_type                                      as "dateType",
        ot.compensate_type                                as "compensateType",
        od.overtime_type_id                               as "overtimeTypeId",
        wot.rule_id                                       as "ruleId",
        otr.leave_type_id                                 as "leaveTypeId",
        coalesce(od.left_duration, 0)                     as "leftDuration",
        od.carried_forward                                as "carriedForward",
        coalesce(od.rel_time_duration, 0)                 as "relTimeDuration"
        FROM wa_emp_overtime_detail od
        JOIN wa_emp_overtime ot ON ot.ot_id = od.overtime_id
        JOIN sys_emp_info ei ON ei.empid = ot.empid and ei.deleted = 0
        LEFT JOIN wa_overtime_type wot on wot.overtime_type_id = od.overtime_type_id
        LEFT JOIN wa_overtime_transfer_rule otr on otr.rule_id=wot.rule_id
        WHERE ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR} AND (ot.data_source IS NULL OR ot.data_source <![CDATA[<>]]> 'HIS_IMPORT')
        AND ot.status = 2 AND ot.compensate_type = 2
        and ot.ot_id not in (select entity_id from wa_workflow_revoke where status=1 and module_name='OVERTIME_ABOLISH')
        <if test="empids != null and empids.size() > 0">
            AND ot.empid IN <foreach collection="empids" open="(" separator="," close=")" item="item">#{item}</foreach>
        </if>
        <if test="startDate!=null and endDate!=null">
            AND (od.start_time - ((od.start_time + 28800) % 86400)) BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="overtimeTypeIds != null and overtimeTypeIds.size() > 0">
            AND od.overtime_type_id IN <foreach collection="overtimeTypeIds" open="(" separator="," close=")" item="item">#{item}</foreach>
        </if>
        <if test="summary!=null">
            <choose>
                <when test="summary">
                    AND otr.transfer_rule=4
                </when>
                <otherwise>
                    AND otr.transfer_rule <![CDATA[!=]]> 4 and (od.carried_forward is null or od.carried_forward<![CDATA[!=]]>1) and (od.left_duration is null or od.left_duration <![CDATA[<=]]> 0)
                </otherwise>
            </choose>
        </if>
        ORDER BY ot.start_time
    </select>

    <select id="getOvertimes" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpOvertimeDo">
      SELECT a.ot_id,
       b.belong_org_id,
       a.empid,
       a.crtuser,
       a.crttime,
       a.upduser,
       a.updtime,
       a.status,
       a.start_time,
       a.end_time
      FROM wa_emp_overtime a
      JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted = 0
      WHERE b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </select>

    <select id="getOvertimeDetailList" resultType="hashmap">
        SELECT a.ot_id as "overtimeId",
        d.detail_id as "detailId",
        b.workno,
        b.emp_name as "empName",
        ot.type_name as "typeName",
        d.time_duration as "timeDuration",
        d.rel_time_duration "relTimeDuration",
        a.status,
        CASE
        WHEN a.status = 0
        THEN '暂存'
        WHEN a.status = 1
        THEN '审批中'
        WHEN a.status = 2
        THEN '已通过'
        WHEN a.status = 3
        THEN '已拒绝'
        WHEN a.status = 4
        THEN '已作废'
        WHEN a.status = 9
        THEN '已撤销' END AS "statusName",
        to_timestamp(a.start_time)::timestamp without time zone || ' --' || to_timestamp(a.end_time)::timestamp without time zone AS "timeSlot",
        to_char(to_timestamp(d.start_time), 'YYYY-MM-DD HH24:MI:SS') as "startTime",
        to_char(to_timestamp(d.end_time), 'YYYY-MM-DD HH24:MI:SS') as "endTime",
        to_char(to_timestamp(a.last_approval_time), 'YYYY-MM-DD HH24:MI:SS') as "lastApprovalTime",
        to_char(to_timestamp(a.crttime), 'YYYY-MM-DD HH24:MI:SS') as "applyTime"
        FROM wa_emp_overtime a
        JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted=0
        JOIN wa_emp_overtime_detail d on d.overtime_id =a.ot_id
        LEFT JOIN wa_overtime_type ot on ot.overtime_type_id=a.overtime_type_id
        <where>
            <if test="tenantId != null">
                AND b.belong_org_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            AND a.status <![CDATA[>]]> 0 AND (a.forgid IS NULL OR a.forgid != 0)
            <if test="empIds != null and empIds.size() > 0">
                <foreach collection="empIds" open=" and a.empid in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status.size() > 0">
                <foreach collection="status" open=" and a.status in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="startDate != null">
                AND (
                (d.start_time  <![CDATA[>=]]> #{startDate} AND d.start_time <![CDATA[<=]]> #{endDate})
                OR (d.end_time <![CDATA[>=]]> #{startDate} AND d.end_time <![CDATA[<=]]> #{endDate})
                OR (d.start_time <![CDATA[<=]]> #{startDate} AND d.end_time <![CDATA[>=]]> #{endDate})
                )
            </if>
            <if test="keywords != null and keywords != ''">
                AND (b.workno LIKE concat('%', #{keywords}, '%') OR b.emp_name LIKE concat('%', #{keywords}, '%'))
            </if>
        </where>
        order by a.ot_id desc,d.detail_id asc
    </select>
</mapper>