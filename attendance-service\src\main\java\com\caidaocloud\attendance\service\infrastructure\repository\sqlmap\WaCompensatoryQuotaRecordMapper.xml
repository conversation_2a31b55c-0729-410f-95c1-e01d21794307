<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaCompensatoryQuotaRecordMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="quota_id" jdbcType="BIGINT" property="quotaId" />
    <result column="detail_id" jdbcType="INTEGER" property="detailId" />
    <result column="carry_duration" jdbcType="REAL" property="carryDuration" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, quota_id, detail_id, carry_duration, status, deleted, create_by, create_time, 
    update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_compensatory_quota_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_compensatory_quota_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord">
    insert into wa_compensatory_quota_record (id, tenant_id, quota_id, 
      detail_id, carry_duration, status, 
      deleted, create_by, create_time, 
      update_by, update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{quotaId,jdbcType=BIGINT}, 
      #{detailId,jdbcType=INTEGER}, #{carryDuration,jdbcType=REAL}, #{status,jdbcType=INTEGER}, 
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord">
    insert into wa_compensatory_quota_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="carryDuration != null">
        carry_duration,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="detailId != null">
        #{detailId,jdbcType=INTEGER},
      </if>
      <if test="carryDuration != null">
        #{carryDuration,jdbcType=REAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord">
    update wa_compensatory_quota_record
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="quotaId != null">
        quota_id = #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="detailId != null">
        detail_id = #{detailId,jdbcType=INTEGER},
      </if>
      <if test="carryDuration != null">
        carry_duration = #{carryDuration,jdbcType=REAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaCompensatoryQuotaRecord">
    update wa_compensatory_quota_record
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      quota_id = #{quotaId,jdbcType=BIGINT},
      detail_id = #{detailId,jdbcType=INTEGER},
      carry_duration = #{carryDuration,jdbcType=REAL},
      status = #{status,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch">
    <foreach collection="records" item="record" separator=";">
      insert into wa_compensatory_quota_record
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="record.id != null">
          id,
        </if>
        <if test="record.tenantId != null">
          tenant_id,
        </if>
        <if test="record.quotaId != null">
          quota_id,
        </if>
        <if test="record.detailId != null">
          detail_id,
        </if>
        <if test="record.carryDuration != null">
          carry_duration,
        </if>
        <if test="record.status != null">
          status,
        </if>
        <if test="record.deleted != null">
          deleted,
        </if>
        <if test="record.createBy != null">
          create_by,
        </if>
        <if test="record.createTime != null">
          create_time,
        </if>
        <if test="record.updateBy != null">
          update_by,
        </if>
        <if test="record.updateTime != null">
          update_time,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="record.id != null">
          #{record.id,jdbcType=BIGINT},
        </if>
        <if test="record.tenantId != null">
          #{record.tenantId,jdbcType=VARCHAR},
        </if>
        <if test="record.quotaId != null">
          #{record.quotaId,jdbcType=BIGINT},
        </if>
        <if test="record.detailId != null">
          #{record.detailId,jdbcType=INTEGER},
        </if>
        <if test="record.carryDuration != null">
          #{record.carryDuration,jdbcType=REAL},
        </if>
        <if test="record.status != null">
          #{record.status,jdbcType=INTEGER},
        </if>
        <if test="record.deleted != null">
          #{record.deleted,jdbcType=INTEGER},
        </if>
        <if test="record.createBy != null">
          #{record.createBy,jdbcType=BIGINT},
        </if>
        <if test="record.createTime != null">
          #{record.createTime,jdbcType=BIGINT},
        </if>
        <if test="record.updateBy != null">
          #{record.updateBy,jdbcType=BIGINT},
        </if>
        <if test="record.updateTime != null">
          #{record.updateTime,jdbcType=BIGINT},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="updateBatch">
    <foreach collection="records" item="record" separator=";">
      update wa_compensatory_quota_record
      <set>
        <if test="record.tenantId != null">
          tenant_id = #{record.tenantId,jdbcType=VARCHAR},
        </if>
        <if test="record.quotaId != null">
          quota_id = #{record.quotaId,jdbcType=BIGINT},
        </if>
        <if test="record.detailId != null">
          detail_id = #{record.detailId,jdbcType=INTEGER},
        </if>
        <if test="record.carryDuration != null">
          carry_duration = #{record.carryDuration,jdbcType=REAL},
        </if>
        <if test="record.status != null">
          status = #{record.status,jdbcType=INTEGER},
        </if>
        <if test="record.deleted != null">
          deleted = #{record.deleted,jdbcType=INTEGER},
        </if>
        <if test="record.createBy != null">
          create_by = #{record.createBy,jdbcType=BIGINT},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime,jdbcType=BIGINT},
        </if>
        <if test="record.updateBy != null">
          update_by = #{record.updateBy,jdbcType=BIGINT},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{record.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>