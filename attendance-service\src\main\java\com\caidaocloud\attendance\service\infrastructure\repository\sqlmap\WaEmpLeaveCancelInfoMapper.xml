<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpLeaveCancelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelInfo" >
    <id column="leave_cancel_info_id" property="leaveCancelInfoId" jdbcType="BIGINT" />
    <result column="leave_cancel_id" property="leaveCancelId" jdbcType="BIGINT" />
    <result column="start_time" property="startTime" jdbcType="BIGINT" />
    <result column="end_time" property="endTime" jdbcType="BIGINT" />
    <result column="shalf_day" property="shalfDay" jdbcType="VARCHAR" />
    <result column="ehalf_day" property="ehalfDay" jdbcType="VARCHAR" />
    <result column="period_type" property="periodType" jdbcType="SMALLINT" />
    <result column="time_unit" property="timeUnit" jdbcType="INTEGER" />
    <result column="time_duration" property="timeDuration" jdbcType="REAL" />
    <result column="shift_start_time" property="shiftStartTime" jdbcType="BIGINT" />
    <result column="shift_end_time" property="shiftEndTime" jdbcType="BIGINT" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="BIGINT" />
    <result column="update_by" property="updateBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    leave_cancel_info_id, leave_cancel_id, start_time, end_time, shalf_day, ehalf_day, 
    period_type, time_unit, time_duration, shift_start_time, shift_end_time, deleted, 
    create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wa_emp_leave_cancel_info
    where leave_cancel_info_id = #{leaveCancelInfoId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wa_emp_leave_cancel_info
    where leave_cancel_info_id = #{leaveCancelInfoId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelInfo" >
    insert into wa_emp_leave_cancel_info (leave_cancel_info_id, leave_cancel_id, start_time, 
      end_time, shalf_day, ehalf_day, 
      period_type, time_unit, time_duration, 
      shift_start_time, shift_end_time, deleted, 
      create_by, create_time, update_by, 
      update_time)
    values (#{leaveCancelInfoId,jdbcType=BIGINT}, #{leaveCancelId,jdbcType=BIGINT}, #{startTime,jdbcType=BIGINT}, 
      #{endTime,jdbcType=BIGINT}, #{shalfDay,jdbcType=VARCHAR}, #{ehalfDay,jdbcType=VARCHAR}, 
      #{periodType,jdbcType=SMALLINT}, #{timeUnit,jdbcType=INTEGER}, #{timeDuration,jdbcType=REAL}, 
      #{shiftStartTime,jdbcType=BIGINT}, #{shiftEndTime,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelInfo" >
    insert into wa_emp_leave_cancel_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="leaveCancelInfoId != null" >
        leave_cancel_info_id,
      </if>
      <if test="leaveCancelId != null" >
        leave_cancel_id,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="shalfDay != null" >
        shalf_day,
      </if>
      <if test="ehalfDay != null" >
        ehalf_day,
      </if>
      <if test="periodType != null" >
        period_type,
      </if>
      <if test="timeUnit != null" >
        time_unit,
      </if>
      <if test="timeDuration != null" >
        time_duration,
      </if>
      <if test="shiftStartTime != null" >
        shift_start_time,
      </if>
      <if test="shiftEndTime != null" >
        shift_end_time,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="createBy != null" >
        create_by,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateBy != null" >
        update_by,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="leaveCancelInfoId != null" >
        #{leaveCancelInfoId,jdbcType=BIGINT},
      </if>
      <if test="leaveCancelId != null" >
        #{leaveCancelId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="shalfDay != null" >
        #{shalfDay,jdbcType=VARCHAR},
      </if>
      <if test="ehalfDay != null" >
        #{ehalfDay,jdbcType=VARCHAR},
      </if>
      <if test="periodType != null" >
        #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="timeUnit != null" >
        #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="timeDuration != null" >
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="shiftStartTime != null" >
        #{shiftStartTime,jdbcType=BIGINT},
      </if>
      <if test="shiftEndTime != null" >
        #{shiftEndTime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null" >
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelInfo" >
    update wa_emp_leave_cancel_info
    <set >
      <if test="leaveCancelId != null" >
        leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="shalfDay != null" >
        shalf_day = #{shalfDay,jdbcType=VARCHAR},
      </if>
      <if test="ehalfDay != null" >
        ehalf_day = #{ehalfDay,jdbcType=VARCHAR},
      </if>
      <if test="periodType != null" >
        period_type = #{periodType,jdbcType=SMALLINT},
      </if>
      <if test="timeUnit != null" >
        time_unit = #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="timeDuration != null" >
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="shiftStartTime != null" >
        shift_start_time = #{shiftStartTime,jdbcType=BIGINT},
      </if>
      <if test="shiftEndTime != null" >
        shift_end_time = #{shiftEndTime,jdbcType=BIGINT},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where leave_cancel_info_id = #{leaveCancelInfoId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpLeaveCancelInfo" >
    update wa_emp_leave_cancel_info
    set leave_cancel_id = #{leaveCancelId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      shalf_day = #{shalfDay,jdbcType=VARCHAR},
      ehalf_day = #{ehalfDay,jdbcType=VARCHAR},
      period_type = #{periodType,jdbcType=SMALLINT},
      time_unit = #{timeUnit,jdbcType=INTEGER},
      time_duration = #{timeDuration,jdbcType=REAL},
      shift_start_time = #{shiftStartTime,jdbcType=BIGINT},
      shift_end_time = #{shiftEndTime,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where leave_cancel_info_id = #{leaveCancelInfoId,jdbcType=BIGINT}
  </update>

  <select id="checkTimeRepeat" resultType="java.lang.Integer">
    SELECT count(0) from (
                           SELECT shift_start_time,
                                  shift_end_time
                           FROM wa_emp_leave_cancel_info welci
                                  join wa_emp_leave_cancel welc on welci.leave_cancel_id = welc.leave_cancel_id
                           WHERE welc.empid = #{empId} and welc.leave_id=#{leaveId}
                             AND welc.status in (1, 2, 8)
                         ) m WHERE
        <![CDATA[
      (
          (#{startTime} >= shift_start_time AND #{startTime} < shift_end_time)
          OR (#{endTime} > shift_start_time AND #{endTime} <= shift_end_time)
          OR (#{startTime} <= shift_start_time AND #{endTime} >= shift_end_time)
        )
    ]]>
  </select>
</mapper>