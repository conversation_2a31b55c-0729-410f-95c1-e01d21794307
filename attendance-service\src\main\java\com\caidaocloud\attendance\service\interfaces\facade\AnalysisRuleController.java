package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.AbsentConditionDto;
import com.caidaocloud.attendance.service.application.dto.AnalysisDetailDto;
import com.caidaocloud.attendance.service.application.enums.FlexibleRuleEnum;
import com.caidaocloud.attendance.service.application.enums.MinLateEarlyTimeUnitEnum;
import com.caidaocloud.attendance.service.application.enums.ParseGroupClockTypeEnum;
import com.caidaocloud.attendance.service.application.service.IAnalysisRuleService;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.impl.WaReportFieldRuleService;
import com.caidaocloud.attendance.service.infrastructure.util.NumberCheckUtil;
import com.caidaocloud.attendance.service.interfaces.dto.AnalysisRuleDto;
import com.caidaocloud.attendance.service.interfaces.vo.AnalysisDetailVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/analysisrule/v1")
@Api(value = "/api/attendance/analysisrule/v1", description = "分析规则接口")
public class AnalysisRuleController {
    @Resource
    private WaAttendanceConfigService waConfigService;
    @Resource
    private IAnalysisRuleService analysisRuleService;
    @Resource
    private IGroupService groupService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaReportFieldRuleService waReportFieldRuleService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "分析规则列表")
    @PostMapping(value = "/getParseGroupList")
    public Result getParseGroupList(@RequestBody AttendanceBasePage basePage) {
        AttendancePageResult list = analysisRuleService.getAnalysisList(getUserInfo().tenantId, basePage);
        return ResponseWrap.wrapResult(list);
    }

    @ApiOperation("考勤方案设置-保存出勤规则")
    @PostMapping(value = "/saveParseGroup")
    public Result saveParseGroup(@RequestBody @Valid AnalysisRuleDto dto) {
        //参数长度检查
        if (NumberCheckUtil.defaultCheckDigitAndValueIsError(dto.getLateCount()) ||
                NumberCheckUtil.defaultCheckDigitAndValueIsError(dto.getEarlyCount()) ||
                NumberCheckUtil.defaultCheckDigitAndValueIsError(dto.getLateAllowNumber()) ||
                NumberCheckUtil.defaultCheckDigitAndValueIsError(dto.getEarlyAllowNumber()) ||
                NumberCheckUtil.defaultCheckDigitAndValueIsError(dto.getAbsentLimit())) {
            return ResponseWrap.wrapResult(AttendanceCodes.EXIST_NUMBER_VALUE_EXCEEDS_UPPERLIMIT, Boolean.FALSE);
        }
        if (dto.getAbsentConditionJsonb() != null) {
            Object absentObj = dto.getAbsentConditionJsonb();
            List<AbsentConditionDto> absentList = JSON.parseArray(absentObj.toString()).toJavaList(AbsentConditionDto.class);
            if (CollectionUtils.isNotEmpty(absentList)) {
                for (AbsentConditionDto absentConditionDto : absentList) {
                    if (NumberCheckUtil.defaultCheckDigitAndValueIsError(new BigDecimal(absentConditionDto.getStart())) ||
                            NumberCheckUtil.defaultCheckDigitAndValueIsError(new BigDecimal(absentConditionDto.getEnd())) ||
                            NumberCheckUtil.defaultCheckDigitAndValueIsError(new BigDecimal(absentConditionDto.getDuration()))) {
                        return ResponseWrap.wrapResult(AttendanceCodes.EXIST_NUMBER_VALUE_EXCEEDS_UPPERLIMIT, Boolean.FALSE);
                    }
                }
            }
        }
        WaParseGroup record = ObjectConverter.convert(dto, WaParseGroup.class);
        if(null == record.getMinLateTimeUnit()){
            record.setMinLateTimeUnit(MinLateEarlyTimeUnitEnum.MINUTE.getIndex());
        }
        if(null == record.getMinEarlyTimeUnit()){
            record.setMinEarlyTimeUnit(MinLateEarlyTimeUnitEnum.MINUTE.getIndex());
        }
        if (record.getLvParse() == null) {
            //休假时长是否抵扣迟到早退
            record.setLvParse(true);
        }
        if (record.getFlexibleWorkType() == null) {
            record.setFlexibleWorkType(FlexibleRuleEnum.FLEXBLE_ANALYZE.getIndex());
        }
        record.setLateCycle("1");
        record.setEarlyCycle("1");
        record.setParseType(1);
        record.setIsFlexibleWorking(false);
        record.setClockRule(JSON.toJSONString(dto.getClockRuleDto()));
        if (!ParseGroupClockTypeEnum.SIGN_TWICE.getIndex().equals(record.getClockType())) {
            //此字段为二次卡缺卡处理逻辑，一次卡缺卡使用clock_rule中的missingClockRule字段
            record.setRegisterMiss(0);
        }
        try {
            Integer parseGroupId = waConfigService.saveParseGroup(record);
            groupService.updateGroupParseGroupId(dto.getWaGroupId(), parseGroupId);
            // 保存自定义分析规则
            waReportFieldRuleService.save(parseGroupId, dto.getCustomLatePaseRule(),
                    dto.getCustomEarlyPaseRule(), dto.getCustomAbsentPaseRule(), dto.getOpenCustomPaseRule());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //return Result.fail("保存失败");
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @ApiOperation("分析规则 取得详细信息")
    @GetMapping(value = "/getParseGroup")
    public Result<AnalysisDetailVo> getParseGroup(@RequestParam("id") Integer id) throws Exception {
        AnalysisDetailDto detailDto = new AnalysisDetailDto();
        WaGroup waGroup = waConfigService.getWaGroup(id);
        if (waGroup != null) {
            detailDto = analysisRuleService.getParseGroupById(waGroup.getParseGroupId());
        }
        AnalysisDetailVo data = ObjectConverter.convert(detailDto, AnalysisDetailVo.class);
        return ResponseWrap.wrapResult(data);
    }

    @DeleteMapping(value = "/deleteParseGroup")
    public Result deleteParseGroup(@RequestParam("id") Integer id) {
        try {
            waConfigService.deleteParseGroup(id);
        } catch (Exception e) {
//            return Result.fail("分析规则已被引用，不允许删除");
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_NOT_ALLOW, Boolean.FALSE);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }
}
