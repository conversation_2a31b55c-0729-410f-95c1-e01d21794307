package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.service.StartupServiceImpl;
import com.caidao1.commons.utils.ReturnMessage;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2021/3/16
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/cache/v1")
public class CacheController {
    @Autowired
    private StartupServiceImpl startupServiceImpl;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private TextAspect textAspect;

    @GetMapping(value = "/deleteCache")
    public ReturnMessage deleteCache(@RequestParam("key") String key) {
        try {
             textAspect.deleteCache(key);
        } catch (Exception e) {
            log.error("deleteCache err:{}", e.getMessage(), e);
        }
        return ReturnMessage.OK();
    }


    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @GetMapping(value = "/flushByType")
    public ReturnMessage flushByType(@RequestParam("type") String type) {
        ReturnMessage rt = new ReturnMessage(0, "success");
        try {
            UserInfo userInfo = getUserInfo();
            if ("all".equals(type)) {
                startupServiceImpl.init();
            } else if ("emp".equals(type)) {
                startupServiceImpl.initEmp(userInfo.getTenantId());
            } else if ("empall".equals(type)) {
                startupServiceImpl.initEmp(null);
            } else if ("orgpath".equals(type)) {
                startupServiceImpl.initSysOrgPath(userInfo.getTenantId());
            } else if ("esignsdk".equals(type)) {
//                startupServiceImpl.initEsignSdk(SessionHolder.getCorpId(), SessionHolder.getBelongOrgId());
            } else {
                rt.setMessage("没有匹配到刷新的类型");
                rt.setStatus(-1);
            }
        } catch (Exception e) {
            e.printStackTrace();
            rt.setMessage(e.getMessage());
            rt.setStatus(-1);
        } finally {
        }
        return rt;
    }
}
