package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class WaEmpTravel {
    private Long travelId;

    private String tenantId;

    private Long empId;

    private Long travelTypeId;

    private Long startTime;

    private Long endTime;

    private String shalfDay;

    private String ehalfDay;

    private Long shiftStartTime;

    private Long shiftEndTime;

    private Float timeDuration;

    private Short timeUnit;

    private Short periodType;

    private Long province;

    private Long city;

    private Integer county;

    private String travelMode;

    private Long lastApprovalTime;

    private Long lastEmpid;

    private String reason;

    private Integer status;

    private String revokeReason;

    private Integer revokeStatus;

    private String fileName;

    private String fileId;

    private Integer deleted;

    private Long createBy;

    private Long createTime;

    private Long updateBy;

    private Long updateTime;

    @TableField(exist = false)
    private String workNo;

    @TableField(exist = false)
    private String empName;

    @TableField(exist = false)
    private String orgName;

    @TableField(exist = false)
    private String fullPath;

    @TableField(exist = false)
    private String travelType;

    @TableField(exist = false)
    private Long hireDate;

    @TableField(exist = false)
    private String statusName;

    @TableField(exist = false)
    private String employType;

    @TableField(exist = false)
    private String workCity;

    private Long quotaId;

    private String businessKey;

    private String extCustomCol;

    private Long batchTravelId;

    @TableField(exist = false)
    private Long revokeId;
    private String processCode;
    @TableField(exist = false)
    private String i18nTravelTypeName;
}