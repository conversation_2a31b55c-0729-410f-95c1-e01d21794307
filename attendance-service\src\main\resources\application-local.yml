server:
  port: 8080
spring:
  application:
    name: caidaocloud-attendance-service
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.120.202:8848
        namespace: cd2pg
nacos:
  com:
    alibaba:
      nacos:
        naming:
          cache:
            dir: /tmp
  config:
    type: yaml
    server-addr: 192.168.120.202:8848
    data-id: caidaocloud-attendance-service-config
    auto-refresh: true
    group: INFRASTRUCTURE_GROUP
    namespace: cd2pg
    bootstrap:
      enable: true
      log:
        enable: true

