package com.caidaocloud.attendance.service.interfaces.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpTravelDto {

    @ApiModelProperty("出差记录ID")
    private Long travelId;

    @ApiModelProperty("员工工号")
    private String workNo;

    @ApiModelProperty("姓名")
    private String empName;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("部门全称")
    private String fullPath;

    @ApiModelProperty("出差类型")
    private String travelType;

    @ApiModelProperty("申请时长")
    private Float timeDuration;

    @ApiModelProperty("时间单位")
    private Short timeUnit;

    @ApiModelProperty("时间单位名称")
    private String timeUnitName;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    @ApiModelProperty("出行方式")
    private String travelMode;

    @ApiModelProperty("省")
    private Long province;

    @ApiModelProperty("市")
    private Long city;

    @ApiModelProperty("区")
    private Integer county;

    @ApiModelProperty("访问地")
    private String visitedPlace;

    @ApiModelProperty("申请理由")
    private String reason;

    @ApiModelProperty("申请时间")
    private Long createTime;

    @ApiModelProperty("审批状态")
    private Integer status;

    @ApiModelProperty("审批状态名称")
    private String statusName;

    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;

    @ApiModelProperty("时间类型")
    private Short periodType;

    @ApiModelProperty("撤销原因")
    private String revokeReason;

    @ApiModelProperty("撤销状态")
    private Integer revokeStatus;

    @ApiModelProperty("班次开始时间")
    private Long shiftStartTime;

    @ApiModelProperty("班次结束时间")
    private Long shiftEndTime;

    @ApiModelProperty("自定义字段")
    private String extCustomCol;

    //其他字段
    private Integer funType;
    private String businessKey;
    private String startDate;
    private String endDate;
    private String travelModeName;
    private String provinceName;
    private String cityName;
    private String countyName;
    private Long travelDate;

    private String shalfDay;
    private String ehalfDay;

    @ApiModelProperty("调休配额")
    private Float quotaDay;
    @ApiModelProperty("调休单位")
    private String quotaUnit;
    private Long quotaId;
    private Long batchTravelId;

    private Long revokeId;
    private String i18nTravelTypeName;
}
