package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.service.impl.CityTreeService;
import com.caidaocloud.attendance.service.interfaces.vo.CityTreeVo;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/8/11 15:42
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/city/v1")
public class CityTreeController {

    @Autowired
    private CityTreeService cityTreeService;

    @ApiOperation(value = "城市树形列表")
    @GetMapping(value = "/cityTree")
    public Result<List<CityTreeVo>> cityTree() {
        List<CityTreeVo> cityTree = null;
        try {
            cityTree = cityTreeService.getCityTree();
        } catch (Exception e) {
            log.error("cityTree error：{}", e.getMessage());
            return Result.fail("获取城市树形列表失败");
        }
        return ResponseWrap.wrapResult(cityTree);
    }

    @ApiOperation(value = "省份/市区 下拉框")
    @GetMapping(value = "/searchCity")
    public Result<Map> searchCity(@RequestParam(value = "isFirstNull", required = false) String isFirstNull,
                                  @RequestParam(value = "countryId", required = false) Integer countryId,
                                  @RequestParam(value = "cityType") Integer cityType,
                                  @RequestParam(value = "cityPid") Long cityPid,
                                  @RequestParam(value = "pid", required = false) Long pid,
                                  @RequestParam(value = "lang", required = false) String lang) throws Exception{
        Map map = null;
        try {
            List list = null;
            list = cityTreeService.searchCity(countryId, cityType, cityPid, pid, lang, isFirstNull);
            map = new HashMap();
            map.put("items", list);
        } catch (Exception e) {
            log.error("searchCity error：{}", e.getMessage());
            return Result.fail("获取城市列表失败");
        }
        return ResponseWrap.wrapResult(map);
    }
}
