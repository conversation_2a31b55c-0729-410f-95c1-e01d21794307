package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.util.GridUtil;
import com.caidao1.ioc.util.ListsHelper;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaCalendarGroupRelMapper;
import com.caidao1.wa.mybatis.mapper.WaHolidayCalendarMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveCalendarDetailMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.enums.DateTypeEnum;
import com.caidaocloud.attendance.service.application.service.IHolidayService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.vo.*;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 特殊日期
 * 
 * <AUTHOR>
 * @date 2021/2/18
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/holiday/v1")
@Api(value = "/api/attendance/holiday/v1", description = "特殊日期")
public class HolidayController {

    @Resource
    private WaLeaveCalendarDetailMapper waLeaveCalendarDetailMapper;
    @Resource
    private WaHolidayCalendarMapper waHolidayCalendarMapper;
    @Resource
    private WaCalendarGroupRelMapper calendarGroupRelMapper;
    @Resource
    private IHolidayService holidayService;
    @Resource
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "日期类型下拉列表")
    @GetMapping(value = "/getDateTypeOptions")
    public Result<ItemsResult<KeyValue>> getBaseDateTypeList() {
        List<KeyValue> items = new ArrayList<>();
        for (DateTypeEnum dateType : DateTypeEnum.values()) {
            items.add(new KeyValue(DateTypeEnum.getName(dateType.getIndex()), dateType.getIndex()));
        }
        return ResponseWrap.wrapResult(new ItemsResult<>(items));
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "新增修改特殊日期")
    public Result<Boolean> saveHolidayInfo(@RequestBody HolidayDto dto) {
        dto.initCalendarName();
        dto.initI18nCalendarName();
        if (dto.getCalendarName().length() >= 50) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.NAME_CHARACTER_LENGTH_LIMITED,null).getMsg(), 50));
        }
        try {
            UserInfo userInfo = this.getUserInfo();
            String belongId = userInfo.getTenantId();
            Long userId = userInfo.getUserId();
            WaHolidayCalendar holiday = ObjectConverter.convert(dto, WaHolidayCalendar.class);
            if (null != dto.getI18nCalendarName()) {
                holiday.setI18nCalendarName(FastjsonUtil.toJson(dto.getI18nCalendarName()));
            }
            int num = holidayService.checkName(belongId, dto.getCalendarName().trim(), dto.getHolidayCalendarId());
            if (num > 0) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_EXIST, Boolean.FALSE).getMsg(), dto.getCalendarName()));
            }
            int row = holidayService.saveOrUpdateOtherDateType(holiday, belongId, userId);
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.saveHolidayInfo executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("查询特殊日期列表")
    @PostMapping("/list")
    public Result<ItemsResult<HolidayVo>> getHolidayList() {
        try {
            UserInfo userInfo = this.getUserInfo();
            String belongOrgId = userInfo.getTenantId();
            List<WaHolidayCalendar> list = holidayService.getOtherDateList(belongOrgId);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(item -> {
                    item.setCalendarName(LangParseUtil.getI18nLanguage(item.getI18nCalendarName(), item.getCalendarName()));
                });
                list.sort((o1, o2) -> {
                    Long t1 = o1.getUpdtime() != null ? o1.getUpdtime() : o1.getCrttime();
                    Long t2 = o2.getUpdtime() != null ? o2.getUpdtime() : o2.getCrttime();
                    return t2.compareTo(t1);
                });
            }
            List<HolidayVo> items = ObjectConverter.convertList(list, HolidayVo.class);
            return ResponseWrap.wrapResult(new ItemsResult<>(items));
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayList executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new ItemsResult<>());
        }
    }

    @ApiOperation("查询特殊日期详情")
    @GetMapping("/detail")
    public Result<HolidayVo> getHolidayInfo(@RequestParam("id") Integer id) {
        if (null == id) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "日期的Id为空", null);
        }
        try {
            UserInfo userInfo = this.getUserInfo();
            String belongId = userInfo.getTenantId();
            WaHolidayCalendar holidayCalendar = holidayService.getHolidayCalendarInfo(id, belongId);
            HolidayVo vo = ObjectConverter.convert(holidayCalendar, HolidayVo.class);
            if (StringUtils.isNotBlank(holidayCalendar.getI18nCalendarName())) {
                vo.setI18nCalendarName(FastjsonUtil.toObject(holidayCalendar.getI18nCalendarName(), Map.class));
            } else if (StringUtils.isNotBlank(holidayCalendar.getCalendarName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", holidayCalendar.getCalendarName());
                vo.setI18nCalendarName(i18nName);
            }
            return ResponseWrap.wrapResult(vo);
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayInfo executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除特殊日期")
    public Result<Boolean> deleteHolidayById(@RequestParam("id") Integer id) {
        try {
            WaHolidayCalendar holidayCalendar = waHolidayCalendarMapper.selectByPrimaryKey(id);
            if (null == holidayCalendar) {
                return ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_NOT_EXIST, Boolean.FALSE);
            }
            int count = holidayService.getWaLeaveCalendarDetailCount(id);
            if (count > 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.DELETE_AFTER_DELETE_DATE_ITEM, Boolean.FALSE);
            }
            // 查询特殊日期是否被引用
            WaCalendarGroupRelExample example = new WaCalendarGroupRelExample();
            WaCalendarGroupRelExample.Criteria criteria = example.createCriteria();
            criteria.andHolidayCalendarIdEqualTo(id);
            List<WaCalendarGroupRel> relList = calendarGroupRelMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(relList)) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_DELETE_NOT_ALLOWED, Boolean.FALSE).getMsg(), holidayCalendar.getCalendarName()));
            }
            UserInfo userInfo = this.getUserInfo();
            int row = holidayService.delSetDateTypeById(id, userInfo.getTenantId());
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.deleteHolidayById executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "特殊日期下拉列表")
    @GetMapping(value = "/getHolidayOptions")
    public Result<ItemsResult> getHolidayOptions() {
        try {
            UserInfo userInfo = this.getUserInfo();
            List<WaHolidayCalendar> list = holidayService.getOtherDateList(userInfo.getTenantId());
            Map<String, List<Map<String, Object>>> listMap = ListsHelper.convertMapList(list, new ListsHelper.LabelValueBeanCreator<WaHolidayCalendar>() {
                @Override
                public Object getValue(WaHolidayCalendar t) {
                    return t.getHolidayCalendarId();
                }
                @Override
                public String getLabel(WaHolidayCalendar t) {
                    return LangParseUtil.getI18nLanguage(t.getI18nCalendarName(), t.getCalendarName());
                }
            }, false);
            return ResponseWrap.wrapResult(new ItemsResult<>(listMap.get("options")));
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayOptions executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SELECT_OPTION_FAILED, new ItemsResult<>());
        }
    }

    /*********************** 特殊日期日期项 ***********************/

    @PostMapping(value = "/saveHolidayEntry")
    @ApiOperation(value = "新增修改特殊日期日期项")
    public Result<Boolean> saveHolidayEntry(@RequestBody HolidayEntryDto dto) {
        try {
            if (null == dto.getHolidayCalendarId()) {
                return ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_EMPTY, Boolean.FALSE);
            }
            WaHolidayCalendar waHolidayCalendar = waHolidayCalendarMapper.selectByPrimaryKey(dto.getHolidayCalendarId());
            if (null == waHolidayCalendar) {
                return ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_NOT_EXIST, Boolean.FALSE);
            }
            if (dto.getCalendarDate() != null) {
                dto.setCalendarDate(DateUtil.getOnlyDate(new Date(dto.getCalendarDate() * 1000)));
            }
            if (dto.getSpecDate() != null) {
                dto.setSpecDate(DateUtil.getOnlyDate(new Date(dto.getSpecDate() * 1000)));
            }
            UserInfo userInfo = this.getUserInfo();
            WaLeaveCalendarDetailExample example = new WaLeaveCalendarDetailExample();
            WaLeaveCalendarDetailExample.Criteria criteria = example.createCriteria();
            criteria.andCalendarDateEqualTo(dto.getCalendarDate()).andHolidayCalendarEqualTo(dto.getHolidayCalendarId());
            if (dto.getCalendarDetailId() != null) {
                criteria.andCalendarDetailIdNotEqualTo(dto.getCalendarDetailId());
            }
            int count = waLeaveCalendarDetailMapper.countByExample(example);
            if (count > 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.DATE_EXIST, Boolean.FALSE);
            }
            WaLeaveCalendarDetail holiday = ObjectConverter.convert(dto, WaLeaveCalendarDetail.class);
            holiday.setHolidayCalendar(dto.getHolidayCalendarId());
            int row = holidayService.saveOtherDateDetailType(holiday, userInfo.getTenantId(), userInfo.getUserId());
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.saveHolidayEntry executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("查询特殊日期日期项分页列表")
    @PostMapping(value = "/getHolidayEntryPageList")
    public Result<AttendancePageResult<HolidayEntryPageVo>> getHolidayEntryPageList(@RequestBody HolidayEntryPageDto dto) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            if (dto.getHolidayCalendarId() == null) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>(new ArrayList<>(), dto.getPageNo(), dto.getPageSize(), 0));
            }
            PageList<Map> pageList = (PageList<Map>) holidayService.getOtherDateTypeDetailList(pageBean, dto.getHolidayCalendarId());
            List<HolidayEntryPageVo> list = JSON.parseArray(JSON.toJSONString(pageList), HolidayEntryPageVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(list, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayEntryPageList executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("查询特殊日期日期项详情")
    @GetMapping("/getHolidayEntryInfo")
    public Result<HolidayEntryVo> getHolidayEntryInfo(@RequestParam("id") Integer id) {
        if (id == null) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "日期项的detailId为空", null);
        }
        try {
            UserInfo userInfo = this.getUserInfo();
            WaLeaveCalendarDetail entry = holidayService.getLeaveDetailByInfo(id, userInfo.getTenantId());
            if (null == entry) {
                return ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_ITEM_NOT_EXIST, null);
            }
            HolidayEntryVo vo = JSON.parseObject(JSON.toJSONString(entry), HolidayEntryVo.class);
            return ResponseWrap.wrapResult(vo);
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayEntryInfo executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @DeleteMapping(value = "/deleteHolidayEntry")
    @ApiOperation("删除特定日期项")
    public Result<Boolean> deleteHolidayEntryById(@RequestParam("id") Integer id) {
        try {
            UserInfo userInfo = this.getUserInfo();
            int row = holidayService.delSetDateDetailById(id, userInfo.getTenantId());
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.deleteHolidayEntryById executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    /*********************** 特殊日期分组 ***********************/

    @ApiOperation(value = "特殊日期分组下拉列表")
    @GetMapping(value = "/selectCalendarGroupList")
    public Result<ItemsResult> selectCalendarGroupList() {
        try {
            UserInfo userInfo = this.getUserInfo();
            List<WaCalendarGroup> list = holidayService.getDateGroupList(userInfo.getTenantId());
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(row -> {
                    row.setGroupName(LangParseUtil.getI18nLanguage(row.getI18nGroupName(), row.getGroupName()));
                });
                list.sort((o1, o2) -> o2.getCrttime().compareTo(o1.getCrttime()));
            }
            Map<String, List<Map<String, Object>>> listMap = ListsHelper.convertMapList(list, new ListsHelper.LabelValueBeanCreator<WaCalendarGroup>() {
                @Override
                public Object getValue(WaCalendarGroup t) {
                    return t.getCalendarGroupId();
                }

                @Override
                public String getLabel(WaCalendarGroup t) {
                    return t.getGroupName();
                }
            }, false);
            return ResponseWrap.wrapResult(new ItemsResult<>(listMap.get("options")));
        } catch (Exception ex) {
            log.error("HolidayController.selectCalendarGroupList executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SELECT_OPTION_FAILED, new ItemsResult<>());
        }
    }

    @PostMapping(value = "/saveHolidayGroup")
    @ApiOperation("新增修改特殊日期分组")
    public Result<Boolean> saveHolidayGroup(@RequestBody HolidayGroupDto dto) {
        dto.initGroupName();
        dto.initI18nGroupName();
        if (StringUtils.isNotBlank(dto.getGroupName())) {
            dto.setGroupName(dto.getGroupName().trim());
        }
        if (StringUtils.isBlank(dto.getGroupName())) {
            return ResponseWrap.wrapResult(AttendanceCodes.DATE_GROUP_NAME_EMPTY, Boolean.FALSE);
        }
        if (dto.getGroupName().length() >= 100) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.DATE_GROUP_NAME_LENGTH_LIMIT, Boolean.FALSE).getMsg(), 100));
        }
        if (CollectionUtils.isEmpty(dto.getHolidayCalendarIds())) {
            return ResponseWrap.wrapResult(AttendanceCodes.SPECIAL_DATE_NOT_SELECTED, Boolean.FALSE);
        }
        try {
            UserInfo userInfo = this.getUserInfo();
            int count = holidayService.getCalendarGroupCountByName(userInfo.getTenantId(), dto.getCalendarGroupId(), dto.getGroupName());
            if (count > 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.DATE_GROUP_NAME_EXISTED, Boolean.FALSE);
            }
            WaCalendarGroup group = ObjectConverter.convert(dto, WaCalendarGroup.class);
            if (null != dto.getI18nGroupName()) {
                group.setI18nGroupName(FastjsonUtil.toJson(dto.getI18nGroupName()));
            }
            int row = holidayService.saveCalendarGroup(group, userInfo.getTenantId(), userInfo.getUserId());
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
            }
            List<HolidayGroupEntryDto> entries = new ArrayList<>();
            if (group.getCalendarGroupId() != null) {
                List<HolidayGroupEntryPageVo> oldEntries = this.getEntries(group.getCalendarGroupId());
                oldEntries.forEach(entry -> holidayService.delCalendarGroupRelById(entry.getGroupRel()));
                dto.getHolidayCalendarIds().forEach(holidayCalendarId -> entries.add(new HolidayGroupEntryDto(group.getCalendarGroupId(), holidayCalendarId, null)));
            } else {
                dto.getHolidayCalendarIds().forEach(holidayCalendarId -> entries.add(new HolidayGroupEntryDto(row, holidayCalendarId, null)));
            }
            List<WaCalendarGroupRel> list = ObjectConverter.convertList(entries, WaCalendarGroupRel.class);
            list.forEach(rel -> holidayService.saveGroupRel(rel, userInfo.getTenantId(), userInfo.getUserId()));
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.saveHolidayGroup executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取特殊日期分组列表")
    @PostMapping(value = "/getHolidayGroupList")
    public Result getHolidayGroupList(@RequestBody AttendanceBasePage basePage) {
        AttendancePageResult<HolidayGroupDto> pageResult = holidayService.getCalendarGroupPageList(PageUtil.getPageBean(basePage));
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<HolidayGroupVo> items = ObjectConverter.convertList(pageResult.getItems(), HolidayGroupVo.class);
            items.forEach(item -> item.setHolidayCalendars(this.getEntries(item.getCalendarGroupId())));
            return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
        }
        return ResponseWrap.wrapResult(new AttendancePageResult<>(new ArrayList<>(), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @GetMapping(value = "/getHolidayGroup")
    @ApiOperation("查询特殊日期分组详情")
    public Result<HolidayGroupVo> getHolidayGroupById(@RequestParam("id") Integer id) {
        if (id == null) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "日期分组的Id为空", null);
        }
        try {
            UserInfo userInfo = this.getUserInfo();
            WaCalendarGroup group = holidayService.getCalendarGroupById(id, userInfo.getTenantId());
            if (null == group) {
                return ResponseWrap.wrapResult(AttendanceCodes.DATE_GROUP_NOT_EXIST, null);
            }
            HolidayGroupVo item = ObjectConverter.convert(group, HolidayGroupVo.class);
            if (StringUtils.isNotBlank(group.getI18nGroupName())) {
                item.setI18nGroupName(FastjsonUtil.toObject(group.getI18nGroupName(), Map.class));
            } else if (StringUtils.isNotBlank(group.getGroupName())) {
                Map<String, String> i18nName = new HashMap<>();
                i18nName.put("default", group.getGroupName());
                item.setI18nGroupName(i18nName);
            }
            item.setHolidayCalendars(this.getEntries(item.getCalendarGroupId()));
            return ResponseWrap.wrapResult(item);
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayGroupById executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    private List<HolidayGroupEntryPageVo> getEntries(Integer calendarGroupId) {
        List<Map> maps = holidayService.getDateGroupReList(new PageBean(true), calendarGroupId, null);
        maps.forEach(item -> {
            if (null != item.get("i18nCalendarName")) {
                String i18n = LangParseUtil.getI18nLanguage(item.get("i18nCalendarName").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    item.put("calendarName", i18n);
                }
            }
        });
        return JSON.parseArray(JSON.toJSONString(maps), HolidayGroupEntryPageVo.class);
    }

    @ApiOperation("删除特殊日期分组")
    @DeleteMapping("/deleteHolidayGroup")
    public Result<Boolean> deleteHolidayGroupById(@RequestParam("id") Integer id) throws Exception {
        try {
            return holidayService.deleteHolidayGroupById(id);
        } catch (Exception ex) {
            log.error("HolidayController.deleteHolidayGroupById executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    /*********************** 特殊日期分组项 ***********************/

    @ApiOperation("查询特殊日期分组项分页列表")
    @PostMapping("/getHolidayGroupEntryList")
    public Result<AttendancePageResult<HolidayGroupEntryPageVo>> getHolidayGroupEntryList(@RequestBody HolidayGroupEntryPageDto dto) {

        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            if (dto.getCalendarGroupId() == null) {
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "特殊日期分组id为空", new AttendancePageResult<>());
            }
            PageList<Map> pageList = (PageList<Map>) holidayService.getDateGroupReList(pageBean, dto.getCalendarGroupId(), dto.getGroupRel());
            List<HolidayGroupEntryPageVo> list = JSON.parseArray(JSON.toJSONString(pageList), HolidayGroupEntryPageVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(list, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayGroupEntryList executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("查询特殊日期分组项详情")
    @GetMapping("/getHolidayGroupEntry")
    public Result<HolidayGroupEntryVo> getHolidayGroupEntryById(@RequestParam("id") Integer id) {
        if (null == id) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "特殊日期分组id为空", null);
        }
        try {
            UserInfo userInfo = this.getUserInfo();

            WaCalendarGroupRel entry = holidayService.getCalendarGroupRelById(id, userInfo.getTenantId());
            return ResponseWrap.wrapResult(ObjectConverter.convert(entry, HolidayGroupEntryVo.class));
        } catch (Exception ex) {
            log.error("HolidayController.getHolidayGroupEntryById executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @PostMapping(value = "/saveGroupEntry")
    @ApiOperation("新增修改特殊日期分组项")
    public Result<Boolean> saveHolidayGroupEntry(@RequestBody HolidayGroupEntryDto dto) {
        try {
            UserInfo userInfo = this.getUserInfo();

            WaCalendarGroupRel group = ObjectConverter.convert(dto, WaCalendarGroupRel.class);
            int row = holidayService.saveGroupRel(group, userInfo.getTenantId(), userInfo.getUserId());
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.saveHolidayGroupEntry executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("删除特殊日期分组项")
    @DeleteMapping("/deleteHolidayGroupEntry")
    public Result<Boolean> deleteHolidayGroupEntryById(@RequestParam("id") Integer id) {
        if (null == id) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "特殊日期分组项id为空", Boolean.FALSE);
        }
        try {
            int row = holidayService.delCalendarGroupRelById(id);
            if (row == 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("HolidayController.deleteHolidayGroupEntryById executes an exception {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }
}
