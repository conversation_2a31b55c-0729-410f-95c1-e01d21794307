package com.caidaocloud.attendance.service.metadata;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.attendance.core.annoation.feign.BccServiceFeignClient;
import com.caidaocloud.attendance.core.annoation.dto.dict.DictKeyValue;
import com.caidaocloud.attendance.service.AttendanceApplication;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
@Slf4j
public class DictDataTest {

    @Resource
    private BccServiceFeignClient bccServiceFeignClient;

    @Test
    public void testHrRepository() {
        String token = generateAccessToken();
        Map<String, String> result = new HashMap<>();
        initDictValueKeyIsName(token, "EmployType", result);
        System.out.println(JSON.toJSONString(result));
    }

    private void initDictValueKeyIsName(String token, String typeCode, Map<String, String> map) {
        String hrBelongModule = "Employee";
        Result<List<DictKeyValue>> dictList = bccServiceFeignClient.getEnableDictList(typeCode, hrBelongModule);
        if (dictList.isSuccess() && dictList.getData() != null) {
            Map<String, String> stringMap = dictList.getData().stream().collect(Collectors.toMap(DictKeyValue::getText, DictKeyValue::getValue));
            map.putAll(stringMap);
        }
    }

    String generateAccessToken() {
        String token = TokenGenerator.getToken("0", "11", 1);
        SecurityUserInfo securityUser = new SecurityUserInfo();
        securityUser.setEmpId(0L);
        securityUser.setTenantId("11");
        securityUser.setIsAdmin(true);
        securityUser.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(securityUser);
        return token;
    }
}
