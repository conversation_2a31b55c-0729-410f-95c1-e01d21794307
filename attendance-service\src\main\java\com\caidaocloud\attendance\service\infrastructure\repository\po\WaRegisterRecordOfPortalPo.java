package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordOfPortalDo;
import lombok.Data;
import lombok.var;
import org.springframework.beans.BeanUtils;

/**
 * 门户补打卡信息po
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
public class WaRegisterRecordOfPortalPo {
    @TableField(value = "record_id")
    private Long recordId;
    @TableField(value = "crttime")
    private Long crttime;
    @TableField(value = "shift_def_name")
    private String shiftDefName;
    @TableField(value = "register_type")
    private Integer registerType;
    @TableField(value = "reg_date_time")
    private Long regDateTime;
    @TableField(value = "reason")
    private String reason;
    @TableField(value = "approval_status")
    private Integer approvalStatus;
    @TableField(value = "last_approval_time")
    private Long lastApprovalTime;
    @TableField(value = "empid")
    private Long empid;
    @TableField(value = "bdk_record_id")
    private String bdkRecordId;

    public WaRegisterRecordOfPortalDo toEntity() {
        var waRegisterRecordOfPortalDo = new WaRegisterRecordOfPortalDo();
        BeanUtils.copyProperties(this, waRegisterRecordOfPortalDo);
        return waRegisterRecordOfPortalDo;
    }
}