package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WaClockInWifiDto extends AttendanceBasePage implements Serializable {
    @ApiModelProperty("wi-Fi的ssid 即Wi-Fi名称")
    private String ssid;

    @ApiModelProperty("wifi主键id")
    private Integer wifiId;

    @ApiModelProperty("Wi-Fi bssid")
    private String bssid;

    @ApiModelProperty("Wi-Fi地址")
    private String wifiAddr;
}
