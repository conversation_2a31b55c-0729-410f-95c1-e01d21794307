package com.caidaocloud.attendance.service.interfaces.dto.group;

import com.caidaocloud.attendance.core.wa.enums.LeaveOutCheckRuleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("考勤方案设置-保存休假规则参数DTO")
public class GroupLeaveRuleSaveDto {
    @ApiModelProperty("方案id")
    private Integer waGroupId;
    @ApiModelProperty("批量申请休假时是否支持假期类型多选：true 多选、false 单选")
    private Boolean multiLeaveTypeOnBatch;
    @ApiModelProperty("允许和出差共存：true 允许 、 false 不允许")
    private Boolean coexistForLeaveOut;

    public LeaveOutCheckRuleEnum getLeaveOutCheckRuleEnum() {
        if (this.coexistForLeaveOut != null && this.coexistForLeaveOut) {
            return LeaveOutCheckRuleEnum.LEAVE_OUT_COEXIST;
        }
        return LeaveOutCheckRuleEnum.LEAVE_OUT_NOTCOEXIST;
    }
}
