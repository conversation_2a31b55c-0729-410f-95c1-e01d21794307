package com.caidaocloud.attendance.service.infrastructure.util;

import com.caidao1.report.common.OpEnum;
import com.caidao1.report.dto.FilterBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class KeyToFilterUtil {
    public static List<FilterBean> KeyToFilterList(String keywords, String field,List<FilterBean> beans) {
        if (StringUtils.isNotBlank(keywords) && keywords != null) {
            List<FilterBean> list = new ArrayList<>();
            FilterBean bean = new FilterBean();
            bean.setField(field);
            bean.setOp(OpEnum.lk);
            bean.setMin(keywords);
            list.add(bean);
            if (CollectionUtils.isNotEmpty(beans) && beans != null) {
                beans.add(bean);
            } else {
                beans = list;
            }
        }
        return beans;
    }
}
