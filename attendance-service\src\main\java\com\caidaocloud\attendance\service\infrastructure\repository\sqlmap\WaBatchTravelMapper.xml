<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaBatchTravelMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchTravel">
    <id column="batch_travel_id" jdbcType="BIGINT" property="batchTravelId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="last_empid" jdbcType="BIGINT" property="lastEmpid" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="process_code" jdbcType="CHAR" property="processCode" />
  </resultMap>
  <sql id="Base_Column_List">
    batch_travel_id, tenant_id, business_key, remarks, status, last_approval_time, last_empid, 
    deleted, create_by, create_time, update_by, update_time, process_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_batch_travel
    where batch_travel_id = #{batchTravelId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_batch_travel
    where batch_travel_id = #{batchTravelId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchTravel">
    insert into wa_batch_travel (batch_travel_id, tenant_id, business_key, 
      remarks, status, last_approval_time, 
      last_empid, deleted, create_by, 
      create_time, update_by, update_time, process_code
      )
    values (#{batchTravelId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{businessKey,jdbcType=VARCHAR}, 
      #{remarks,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{lastApprovalTime,jdbcType=BIGINT}, 
      #{lastEmpid,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{processCode,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchTravel">
    insert into wa_batch_travel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchTravelId != null">
        batch_travel_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="lastEmpid != null">
        last_empid,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="processCode != null">
        process_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchTravelId != null">
        #{batchTravelId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="processCode != null">
        #{processCode,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchTravel">
    update wa_batch_travel
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        last_empid = #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="processCode != null">
        process_code = #{processCode,jdbcType=CHAR},
      </if>
    </set>
    where batch_travel_id = #{batchTravelId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchTravel">
    update wa_batch_travel
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      business_key = #{businessKey,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      process_code = #{processCode,jdbcType=CHAR}
    where batch_travel_id = #{batchTravelId,jdbcType=BIGINT}
  </update>
</mapper>