package com.caidaocloud.attendance.service.interfaces.vo.register;

import com.caidaocloud.attendance.service.interfaces.vo.RegisterRecordVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/09/08
 */
@Data
public class OutworkRegisterRecordVo extends RegisterRecordVo {
    @ApiModelProperty("审批状态编码")
    private Integer approvalStatus;
    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;
    @ApiModelProperty("审批时间")
    private Long approvalTime;
    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;
    @ApiModelProperty("查看的工作流id")
    private String businessKey;
    @ApiModelProperty("备注")
    private String owRmk;
    @ApiModelProperty("附件id")
    private String files;
    @ApiModelProperty("附件名包括扩展名")
    private String fileNames;
}
