package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.core.workflow.dto.LeaveTimeWfDetailDto;
import com.caidaocloud.dto.KeyValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("工作流程内容详情VO")
public class WfDetailVo {
    private List<KeyValue> data;
    private String files;
    private String fileNames;
    private String fullPath;
    @ApiModelProperty("销假单详情")
    private List<LeaveTimeWfDetailDto> leaveCancelList;
    @ApiModelProperty("休假单详情")
    private WfDetailVo leaveApplyInfo;
}
