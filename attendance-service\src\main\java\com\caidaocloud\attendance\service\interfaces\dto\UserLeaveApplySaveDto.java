package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserLeaveApplySaveDto {
    @ApiModelProperty("假期id")
    private Integer leaveId;
    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;
    @ApiModelProperty("开始日期")
    private Long startTime;
    @ApiModelProperty("结束日期")
    private Long endTime;
    @ApiModelProperty("是否选择半天 1 选择 0 不选择")
    private Integer showDay;
    @ApiModelProperty("是否选择小时 1 选择 0 不选择")
    private Integer showMin;
    @ApiModelProperty("半天开始 A 上半天 P 下半天")
    private String shalfDay;
    @ApiModelProperty("半天结束 A 上半天 P 下半天")
    private String ehalfDay;
    @ApiModelProperty("开始时间 类型分钟 ")
    private Integer stime;
    @ApiModelProperty("结束时间 类型分钟 ")
    private Integer etime;
    @ApiModelProperty("原因")
    private String reason;
    @ApiModelProperty("附件")
    private String file;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("持续时间")
    private String duration;
    @ApiModelProperty("每日申请时长")
    private Float dailyDuration;

    @ApiModelProperty("子女个数")
    private Integer childNum;
    @ApiModelProperty("产假类型：1顺产2难产")
    private Integer maternityLeaveType;
    @ApiModelProperty("子女出生日期")
    private Long manufactureDate;

    @ApiModelProperty("探亲事由")
    private String homeLeaveType;
    @ApiModelProperty("婚姻状态")
    private String marriageStatus;

    @ApiModelProperty("幂等校验key")
    private String secretKey;
}
