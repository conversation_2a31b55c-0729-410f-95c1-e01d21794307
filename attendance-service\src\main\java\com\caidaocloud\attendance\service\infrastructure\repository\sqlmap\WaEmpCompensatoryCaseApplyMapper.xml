<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseApplyMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="apply_duration" jdbcType="REAL" property="applyDuration" />
    <result column="valid_duration" jdbcType="REAL" property="validDuration" />
    <result column="time_unit" jdbcType="INTEGER" property="timeUnit" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, emp_id, apply_duration, valid_duration, time_unit, status, last_approval_time, note,revoke_reason, deleted, create_by,
    create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_emp_compensatory_case_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_emp_compensatory_case_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply">
    insert into wa_emp_compensatory_case_apply (id, tenant_id, emp_id,
      apply_duration, valid_duration, time_unit, status,
      last_approval_time, note,revoke_reason, deleted,
      create_by, create_time, update_by, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{empId,jdbcType=BIGINT},#{applyDuration,jdbcType=REAL},
      #{validDuration,jdbcType=REAL}, #{timeUnit,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
      #{lastApprovalTime,jdbcType=BIGINT}, #{note,jdbcType=VARCHAR},#{revokeReason,jdbcType=VARCHAR},
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply">
    insert into wa_emp_compensatory_case_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="applyDuration != null">
        apply_duration,
      </if>
      <if test="validDuration != null">
        valid_duration,
      </if>
      <if test="timeUnit != null">
        time_unit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="revokeReason != null">
        revokeReason,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=BIGINT},
      </if>
      <if test="applyDuration != null">
        #{applyDuration,jdbcType=REAL},
      </if>
      <if test="validDuration != null">
        #{validDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply">
    update wa_emp_compensatory_case_apply
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=BIGINT},
      </if>
      <if test="applyDuration != null">
        apply_duration = #{applyDuration,jdbcType=REAL},
      </if>
      <if test="validDuration != null">
        valid_duration = #{validDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        time_unit = #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply">
    update wa_emp_compensatory_case_apply
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=BIGINT},
      apply_duration = #{applyDuration,jdbcType=REAL},
      valid_duration = #{validDuration,jdbcType=REAL},
      time_unit = #{timeUnit,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      note = #{note,jdbcType=VARCHAR},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseApplyDo">
    select ecc.*,
           sei.workno workNo,
           sei.emp_name empName,
           case
             when co.full_path is not null and co.full_path != ''
           then concat_ws('/', co.full_path, co.shortname)
             else co.shortname
           end  as "fullPath",
           sei.workplace workCity,
           sei.employ_type employType,
           sei.hire_date hireDate
    from wa_emp_compensatory_case_apply ecc
        left join sys_emp_info sei on sei.empid=ecc.emp_id
        left join sys_corp_org co ON co.orgid = sei.orgid
    where ecc.tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id}
  </select>

  <select id="queryCompensatoryRecordList" parameterType="hashmap" resultType="com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseApplyDo">
    select * from(
    SELECT a.id,
    b.empid empId,
    b.workno workNo,
    b.emp_name empName,
    b.orgid,
    a.apply_duration applyDuration,
    a.valid_duration validDuration,
    a.time_unit timeUnit,
    a.create_time createTime,
    a.status,
    a.last_approval_time lastApprovalTime,
    a.note
    FROM wa_emp_compensatory_case_apply a
    JOIN sys_emp_info b ON a.emp_id = b.empid AND b.deleted = 0
    LEFT JOIN sys_corp_org sco on b.orgid = sco.orgid AND sco.deleted = 0
    <where>
      AND a.tenant_id=#{tenantId} AND a.deleted=0
      <if test="dataFilter != null and dataFilter != ''">
        ${dataFilter}
      </if>
      <if test="status != null">
        AND a.status = #{status}
      </if>
      <if test="empId != null">
        AND a.emp_id = #{empId}
      </if>
      <if test="empIds != null and empIds.size() > 0">
        AND a.emp_id in
        <foreach collection="empIds" item="empIdItem" open="(" close=")" separator=",">
          #{empIdItem}
        </foreach>
      </if>
      <if test="startDate != null and endDate != null">
        AND a.last_approval_time between #{startDate} AND #{endDate}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (b.workno like concat('%', #{keywords}, '%') or b.emp_name like concat('%', #{keywords}, '%'))
      </if>
    </where>) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
    order by createTime desc
  </select>

  <select id="batchInsert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply">
    <foreach collection="records" item="record" separator=";">
      insert into wa_emp_compensatory_case_apply (id, tenant_id, emp_id, apply_duration,
      valid_duration, time_unit, status, last_approval_time, note, deleted, create_by, create_time, update_by, update_time)
      values (#{record.id,jdbcType=BIGINT}, #{record.tenantId,jdbcType=VARCHAR}, #{record.empId,jdbcType=BIGINT},
      #{record.applyDuration,jdbcType=REAL}, #{record.validDuration,jdbcType=REAL}, #{record.timeUnit,jdbcType=INTEGER},
      #{record.status,jdbcType=INTEGER}, #{record.lastApprovalTime,jdbcType=BIGINT}, #{record.note,jdbcType=VARCHAR},
      #{record.deleted,jdbcType=INTEGER}, #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT}, #{record.updateBy,jdbcType=BIGINT},
      #{record.updateTime,jdbcType=BIGINT})
    </foreach>
  </select>

  <select id="selectCompensatoryCaseList" resultMap="BaseResultMap">
    select * from attendance.wa_emp_compensatory_case_apply wecca
    join attendance.wa_emp_compensatory_case wecc on wecc.apply_id=wecca.id
    where wecc.tenant_id=#{tenantId} and wecca.deleted=0
    <if test="ids!=null and ids.size()>0">
        and wecc.id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
        </foreach>
    </if>
    <if test="status!=null and status.size()>0">
        and wecca.status in
    <foreach collection="status" item="item" open="(" close=")" separator=",">
        #{item}
    </foreach>
    </if>
  </select>
</mapper>