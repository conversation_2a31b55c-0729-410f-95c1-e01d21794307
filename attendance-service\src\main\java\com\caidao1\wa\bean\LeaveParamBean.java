package com.caidao1.wa.bean;

import lombok.Data;

@Data
public class LeaveParamBean {
    private Long empid;
    private Integer leaveTypeId;
    private Integer timesize;
    private String reason;
    private String file;
    private Integer province;
    private Integer city;
    private Integer county;
    private Integer leaveId;//请假ID
    private Long weddingDate;//结婚日期
    private Long expectedDate;//预产期
    private Long manufactureDate;//生产日期

    //req params
    private Long startTime;
    private Long endTime;
    private String shalfDay;
    private String ehalfDay;
    private Integer stime;
    private Integer etime;
    private Integer showDay;
    private Integer showMin;
    private String fileName;
    private String period;
    private Integer funcType;
    private Float dailyDuration;

    private Integer childNum;
    private Integer maternityLeaveType;
    private Long birthday;
    /**
     * 探亲假
     */
    private String homeLeaveType;
    private String marriageStatus;

    /**
     * 是否为批量申请（可选，批量申请时使用）
     */
    private boolean ifBtch;
    /**
     * 批量单据ID（可选，批量申请时使用）
     */
    private Long batchId;
    /**
     * 批量申请时是否更新配额（可选，批量申请时使用）
     */
    private boolean updateQuotaWhenBatch;
}
