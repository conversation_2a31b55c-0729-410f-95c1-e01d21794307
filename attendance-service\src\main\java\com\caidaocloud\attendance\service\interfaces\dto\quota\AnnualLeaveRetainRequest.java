package com.caidaocloud.attendance.service.interfaces.dto.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AnnualLeaveRetainRequest {
    @ApiModelProperty("余额id")
    private Integer empQuotaId;
    @ApiModelProperty("员工id")
    private Long empid;
    @ApiModelProperty("留存配额")
    private Float quotaDay;
    @ApiModelProperty("留存配额已使用")
    private Float usedDay;
    @ApiModelProperty("留存有效期")
    private Long lastDate;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("上年留存来源假期额度主键")
    private Long configId;
    @ApiModelProperty("上年留存来源假期主键")
    private Integer leaveTypeId;
    @ApiModelProperty("年份")
    private Short periodYear;

    @ApiModelProperty("当前配额id")
    private Integer mergeEmpQuotaId;
}
