package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.application.enums.OtTypeEnum;
import com.caidaocloud.attendance.service.application.enums.OvertimeUnitEnum;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeTypeReqDto;
import com.caidaocloud.attendance.service.application.service.IOverTimeService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.OverTimeDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OverTimeInfoDto;
import com.caidaocloud.attendance.service.interfaces.vo.OverTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OverTimeEnumVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OverTimeInfoVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.plutext.jaxb.svg11.Mpath;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(description = "加班设置接口", value = "/api/attendance/overtime/v1")
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/overtime/v1")
public class OverTimeController {

    @Autowired
    private IOverTimeService overTimeService;

    @ApiOperation(value = "加班设置列表")
    @PostMapping(value = "/getOtTypeList")
    public Result<AttendancePageResult<OverTimeVo>> getOtTypeList(@RequestBody AttendanceBasePage basePage) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<Map> list = overTimeService.getOtTypeList(pageBean);
        PageList<Map> pageList = (PageList<Map>) list;
        List<OverTimeVo> overTimeVos = JSON.parseArray(JSON.toJSONString(list)).toJavaList(OverTimeVo.class);
        AttendancePageResult<OverTimeVo> pageResult = new AttendancePageResult<>(overTimeVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @ApiOperation(value = "加班设置保存")
    @PostMapping(value = "/saveOtType")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-加班设置-加班规则")
    public Result<Boolean> saveOtType(@RequestBody @Valid OverTimeDto dto) {
        dto.initTypeName();
        dto.initI18nTypeName();
        if (dto.getDateType() == null) {
            dto.setDateType(dto.getOvertimeType());
        }
        if (StringUtil.isEmpty(dto.getTypeName()) || StringUtil.isEmpty(dto.getTypeName().trim())) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_NAME_EMPTY, Boolean.FALSE);
        }
        if (dto.getMinOvertimeUnit() > 24) {
            return ResponseWrap.wrapResult(AttendanceCodes.EXCEEDS_MIN_OVERTIME_MAXIMUM, Boolean.FALSE);
        }
        dto.setTypeName(dto.getTypeName().trim());
        if (dto.getTypeName().length() > 64) {
            return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_NAME_TOO_LONG, Boolean.FALSE);
        }
        try {
            return overTimeService.saveOtType(dto);
        } catch (Exception e) {
            log.error("OverTimeController.saveOtType exception {}", e.getMessage(), e);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
    }

    @ApiOperation(value = "取得详细信息")
    @GetMapping(value = "/getOtType")
    public Result<OverTimeDto> getOtType(@RequestParam("id") Integer id) {
        return ResponseWrap.wrapResult(overTimeService.getOtType(id));
    }

    @ApiOperation(value = "删除加班类型")
    @DeleteMapping(value = "/deleteOtType")
    public Result<Boolean> deleteOtType(@RequestParam("id") Integer id) {
        if (overTimeService.checkOvertimeTypeUsed(id)) {
            return ResponseWrap.wrapResult(AttendanceCodes.RULE_USED, Boolean.FALSE);
        }
        try {
            overTimeService.deleteOtType(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("OverTimeController.deleteOtType exception {}", e.getMessage(), e);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
    }

    @ApiOperation(value = "删除加班类型，更新考勤方案")
    @DeleteMapping(value = "/deleteGroupOtType")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-加班设置-加班规则")
    public Result<Boolean> deleteGroupOtType(@RequestParam(value = "waGroupId", required = false) Integer waGroupId, @RequestParam("id") Integer id) {
        try {
            if (overTimeService.getRelBetweenTypeAndOt(id) > 0) {
                return ResponseWrap.wrapResult(AttendanceCodes.OVERTIME_TYPE_USED, Boolean.FALSE);
            }
            overTimeService.deleteGroupOtType(waGroupId, id);
            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception e) {
            log.error("OverTimeController.deleteGroupOtType exception {}", e.getMessage(), e);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
    }

    @ApiOperation("加班设置列表(不带分页)")
    @ApiImplicitParams({
         @ApiImplicitParam(name = "groupId",value = "考勤方案id 加班设置列表不需要传,加班规则需要传",dataType = "Integer"),
         @ApiImplicitParam(name = "status",value = "是否查看失效的加班设置,传0代表选中,不传代表没选中",dataType = "Integer")
    })
    @GetMapping("/getOtTypes")
    public Result<ItemsResult<OverTimeInfoVo>> getOtTypes(@RequestParam(value = "groupId",required = false) Integer groupId,
                                                          @RequestParam(value = "status",required = false) Integer status) {
        List<OverTimeInfoDto> list = overTimeService.getOtTypes(groupId,status);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseWrap.wrapResult(new ItemsResult<>());
        }
        return ResponseWrap.wrapResult(ItemsResult.of(ObjectConverter.convertList(list, OverTimeInfoVo.class)));
    }

    @ApiOperation("启用")
    @PostMapping("/enable")
    public Result<Boolean> enable(@RequestBody OverTimeTypeReqDto dto) {
        return overTimeService.enable(dto);
    }

    @ApiOperation("停用")
    @PostMapping("/disable")
    public Result<Boolean> disable(@RequestBody OverTimeTypeReqDto dto) {
        return overTimeService.disable(dto);
    }

    @ApiOperation("获取假期规则最小加班单位")
    @GetMapping("/getMinOvertimeUnit")
    public Result<ItemsResult<KeyValue>> getMinOvertimeUnit() {
        List<KeyValue> list = new ArrayList<>();
        for (OvertimeUnitEnum item : OvertimeUnitEnum.values()) {
            list.add(new KeyValue(item.getDesc(), item.ordinal()));
        }
        return ResponseWrap.wrapResult(new ItemsResult<>(list));
    }

    @ApiOperation(value = "加班类型下拉列表")
    @GetMapping(value = "/getOvertimeTypeOptions")
    public Result<ItemsResult<KeyValue>> getOvertimeTypeOptions() {
        List<KeyValue> items = new ArrayList<>();
        for (OtTypeEnum dateType : OtTypeEnum.values()) {
            items.add(new KeyValue(OtTypeEnum.getDescByIndex(dateType.getIndex()), dateType.getIndex()));
        }
        return ResponseWrap.wrapResult(new ItemsResult<>(items));
    }

    @ApiOperation("加班设置枚举值")
    @GetMapping("/getOtTypesEnum")
    public Result<ItemsResult<OverTimeEnumVo>> getOtTypes() {
        List<OverTimeInfoDto> list = overTimeService.getOtTypes(null,null);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseWrap.wrapResult(new ItemsResult<>());
        }
        List<OverTimeEnumVo> resultList=new ArrayList<>();
        for (OverTimeInfoDto overTimeInfoDto : list) {
            resultList.add(new OverTimeEnumVo(overTimeInfoDto.getOvertimeTypeId(),overTimeInfoDto.getTypeName()));
        }

        return ResponseWrap.wrapResult(ItemsResult.of(resultList));
    }
}
