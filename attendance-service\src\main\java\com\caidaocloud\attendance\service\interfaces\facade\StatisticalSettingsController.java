package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.IWaLeaveTypeDefService;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.WaLeaveTypeDefDto;
import com.caidaocloud.attendance.service.interfaces.vo.WaLeaveTypeDefVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.weibo.api.motan.util.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(value = "/api/attendance/statisticalSettings/v1", description = "考勤统计接口")
@RequestMapping("/api/attendance/statisticalSettings/v1")
public class StatisticalSettingsController {

    @Autowired
    private IWaLeaveTypeDefService waLeaveTypeDefService;
    @Autowired
    private ILeaveTypeService leaveTypeService;

    @ApiOperation(value = "考勤统计设置假期列表", tags = "v1.2")
    @GetMapping("/list")
    public Result searchList() {
        try {
            List<WaLeaveTypeDefDto> list = waLeaveTypeDefService.getWaLeaveTypeDefList();
            if(CollectionUtil.isEmpty(list)){
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "null data");
            }
            List<WaLeaveTypeDefVo> waLeaveTypeDefVoList = ObjectConverter.convertList(list,WaLeaveTypeDefVo.class);
            Map<String,Object> map = new HashMap<>();
            map.put("items", waLeaveTypeDefVoList);
            return ResponseWrap.wrapResult(map);
        } catch (Exception ex) {
            log.error("StatisticalSettingsController.searchList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, "");
        }
    }

    @ApiOperation("删除考勤统计设置")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-假期设置-统计设置")
    public Result<Boolean> delete(@RequestParam("id") Integer id) {
        try {
            if(id == null){
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "id is null", CommonConstant.FALSE);
            }
            WaLeaveTypeDefDto waLeaveTypeDefDto = waLeaveTypeDefService.getDetailList(id);
            if(waLeaveTypeDefDto != null){
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "System data cannot be deleted", CommonConstant.FALSE);
            }
            LeaveTypeDto leaveTypeDto =  leaveTypeService.getLeaveTypeByType(id);
            if(leaveTypeDto != null){
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.STATISTIC_SETTING_DELETE_NOT_ALLOW, null).getMsg(), leaveTypeDto.getLeaveName()));
            }
            waLeaveTypeDefService.delete(id);

            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception ex) {
            log.error("StatisticalSettingsController.delete executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("新增/更新考勤统计设置")
    @PostMapping("/save")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-假期设置-统计设置")
    public Result<Boolean> save(@RequestBody WaLeaveTypeDefDto dto) {
        try {
            if (dto == null) {
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "无数据", CommonConstant.FALSE);
            }
            dto.initLeaveTypeDefName();
            dto.initI18nLeaveTypeDefName();
            WaLeaveTypeDefDto waLeaveTypeDefDto = waLeaveTypeDefService.getWaLeaveTypeDefByData(dto);
            if (waLeaveTypeDefDto != null) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.STATISTIC_SETTING_DUPLICATED, null).getMsg(), waLeaveTypeDefDto.getLeaveTypeDefName()));
            }
            waLeaveTypeDefService.save(dto);

            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception ex) {
            log.error("StatisticalSettingsController.save executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, CommonConstant.FALSE);
        }
    }

    @ApiOperation(value = "同步假期类型", tags = "v1.2")
    @GetMapping("/sync")
    public Result syncWaLeaveTypeDefList() {
        try {
            waLeaveTypeDefService.syncWaLeaveTypeDefList();
            return Result.success();
        } catch (Exception ex) {
            log.error("syncWaLeaveTypeDefList exception, {}", ex.getMessage(), ex);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.SYNC_FAILED, CommonConstant.FALSE);
    }
}