package com.caidaocloud.attendance.service.interfaces.dto.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AnnualLeaveDto {

    @ApiModelProperty("年份")
    private Integer periodYear;
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("生效日期")
    private Long lastDate;
    @ApiModelProperty("本年额度")
    private Float quotaDay;
    @ApiModelProperty("当前额度")
    private Float nowQuotaDay;
    @ApiModelProperty("本年可用")
    private Float availabQuotaDay;
    @ApiModelProperty("本年已用")
    private Float usedDay;
    @ApiModelProperty("流程中")
    private Float inTransitQuota;
    @ApiModelProperty("调整额度")
    private Float adjustQuota;
    @ApiModelProperty("调整本年已用")
    private Float fixUsedDay;
    @ApiModelProperty("本年余额")
    private Float leftDay;
    @ApiModelProperty("预支年假")
    private Float advanceDay;
    @ApiModelProperty("单位 1:天 2:小时")
    private Integer unit;
    @ApiModelProperty("当前余额")
    private Float currentQuota;

    @ApiModelProperty("上年结转")
    private Float retainDay;
    @ApiModelProperty("上年留存流程中")
    private Float retainInTransitQuota;
    @ApiModelProperty("上年结转已用")
    private Float retainUsedDay;
    @ApiModelProperty("上年结转有效期至")
    private Long retainValidDate;


    public void reCalCurrentQuotaAndAdvance() {
        this.setCurrentQuota(this.nowQuotaDay + this.adjustQuota + this.retainDay - this.usedDay - this.inTransitQuota - this.fixUsedDay - this.retainInTransitQuota - this.retainUsedDay);
        if (this.getCurrentQuota() < 0) {
            this.setAdvanceDay(-this.getCurrentQuota());
            this.setCurrentQuota(0f);
        } else {
            this.setAdvanceDay(0f);
        }
    }
}
