package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.application.service.ICacheCommonService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@EnableFeignClients
public class WaDataScopeTest {
    @Autowired
    private ICacheCommonService cacheCommonService;
    @Autowired
    private WaEmpTravelMapper waEmpTravelMapper;

    @Test
    public void  data_scope_test() {
        try {
            SecurityUserInfo userInfo2 = new SecurityUserInfo();
            userInfo2.setTenantId("11");
            userInfo2.setUserId(1707559749105667L);
            userInfo2.setEmpId(1708445670930433L);
            SecurityUserUtil.setSecurityUserInfo(userInfo2);
            String dataScope = cacheCommonService.getOrgDataScope("11", 1681370780514308L, "", "", 1708445670930433L);
            System.out.println("dataScope:" + dataScope);
        } finally{
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
    @Test
    public void test2() {
        List<WaEmpTravel> travel = waEmpTravelMapper.getDurationOfTravel(Lists.newArrayList(1944833735546889L));
        System.out.println(travel);
    }
}
