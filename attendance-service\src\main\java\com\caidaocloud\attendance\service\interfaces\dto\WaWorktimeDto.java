package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/18
 */
@Data
public class WaWorktimeDto implements Serializable {
    private static final long serialVersionUID = 8987599009837406165L;
    @ApiModelProperty("工作日历ID")
    private Integer workCalendarId;
    @ApiModelProperty("特殊日期分组ID")
    private Integer calendarGroupId;
    @ApiModelProperty("排班ID")
    private Integer workRoundId;
    @ApiModelProperty("工作日历名称")
    private String workCalendarName;
    @ApiModelProperty("适用范围")
    private Boolean isDefault = false;
    @ApiModelProperty("开始日期")
    private Long startdate;
    @ApiModelProperty("结束日期")
    private Long enddate;
    private List<Long> empIds;
    @ApiModelProperty("员工ID")
    private String empIdArray;
    @ApiModelProperty("员工姓名")
    private String empNameArray;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    private String progress;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nWorkCalendarName;

    public void initWorkCalendarName() {
        if (StringUtils.isNotBlank(this.workCalendarName)) {
            return;
        }
        if (null == this.i18nWorkCalendarName || this.i18nWorkCalendarName.isEmpty() || null == this.i18nWorkCalendarName.get("default")) {
            return;
        }
        this.setWorkCalendarName(this.i18nWorkCalendarName.get("default"));
    }

    public void initI18nWorkCalendarName() {
        if (null != this.i18nWorkCalendarName && !this.i18nWorkCalendarName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.workCalendarName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.workCalendarName);
        this.setI18nWorkCalendarName(i18nName);
    }
}
