<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaBatchOvertimeMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchOvertime">
    <id column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="empid" jdbcType="BIGINT" property="empid" />
    <result column="wa_sob_id" jdbcType="INTEGER" property="waSobId" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="time_slot" jdbcType="VARCHAR" property="timeSlot" />
    <result column="time_duration" jdbcType="REAL" property="timeDuration" />
    <result column="valid_time_duration" jdbcType="REAL" property="validTimeDuration" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ext_custom_col" jdbcType="VARCHAR" property="extCustomCol" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="last_empid" jdbcType="BIGINT" property="lastEmpid" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="revoke_status" jdbcType="INTEGER" property="revokeStatus" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    batch_id, empid, wa_sob_id, start_time, end_time, file_path, file_name, time_slot, 
    time_duration, valid_time_duration, business_key, reason, ext_custom_col, status, 
    last_approval_time, last_empid, revoke_reason, revoke_status, tenant_id, deleted, 
    create_by, create_time, update_by, update_time,process_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_batch_overtime
    where batch_id = #{batchId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_batch_overtime
    where batch_id = #{batchId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchOvertime">
    insert into wa_batch_overtime (batch_id, empid, wa_sob_id, 
      start_time, end_time, file_path, 
      file_name, time_slot, time_duration, 
      valid_time_duration, business_key, reason, 
      ext_custom_col, status, last_approval_time, 
      last_empid, revoke_reason, revoke_status, 
      tenant_id, deleted, create_by, 
      create_time, update_by, update_time
      )
    values (#{batchId,jdbcType=BIGINT}, #{empid,jdbcType=BIGINT}, #{waSobId,jdbcType=INTEGER}, 
      #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, #{filePath,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{timeSlot,jdbcType=VARCHAR}, #{timeDuration,jdbcType=REAL}, 
      #{validTimeDuration,jdbcType=REAL}, #{businessKey,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{extCustomCol,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{lastApprovalTime,jdbcType=BIGINT}, 
      #{lastEmpid,jdbcType=BIGINT}, #{revokeReason,jdbcType=VARCHAR}, #{revokeStatus,jdbcType=INTEGER}, 
      #{tenantId,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchOvertime">
    insert into wa_batch_overtime
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="waSobId != null">
        wa_sob_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="timeSlot != null">
        time_slot,
      </if>
      <if test="timeDuration != null">
        time_duration,
      </if>
      <if test="validTimeDuration != null">
        valid_time_duration,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="extCustomCol != null">
        ext_custom_col,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="lastEmpid != null">
        last_empid,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="revokeStatus != null">
        revoke_status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="waSobId != null">
        #{waSobId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="timeSlot != null">
        #{timeSlot,jdbcType=VARCHAR},
      </if>
      <if test="timeDuration != null">
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="validTimeDuration != null">
        #{validTimeDuration,jdbcType=REAL},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchOvertime">
    update wa_batch_overtime
    <set>
      <if test="empid != null">
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="waSobId != null">
        wa_sob_id = #{waSobId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="timeSlot != null">
        time_slot = #{timeSlot,jdbcType=VARCHAR},
      </if>
      <if test="timeDuration != null">
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="validTimeDuration != null">
        valid_time_duration = #{validTimeDuration,jdbcType=REAL},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        last_empid = #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        revoke_status = #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where batch_id = #{batchId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchOvertime">
    update wa_batch_overtime
    set empid = #{empid,jdbcType=BIGINT},
      wa_sob_id = #{waSobId,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      time_slot = #{timeSlot,jdbcType=VARCHAR},
      time_duration = #{timeDuration,jdbcType=REAL},
      valid_time_duration = #{validTimeDuration,jdbcType=REAL},
      business_key = #{businessKey,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      revoke_status = #{revokeStatus,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where batch_id = #{batchId,jdbcType=BIGINT}
  </update>

  <select id="selectPageList" parameterType="hashmap" resultType="hashmap">
    SELECT * from(
    SELECT ot.batch_id,
    ot.time_duration,
    ot.valid_time_duration,
    ot.time_slot,
    ot.create_time as crttime,
    ot.last_approval_time,
    ot.status,
    ot.start_time,
    ot.end_time,
    ot.business_key,
    ot.empid,
    ei.workno,
    ei.emp_name,
    org.orgid,
    org.shortname  as "orgName",
    case
    when org.full_path is not null and org.full_path != ''
    then concat_ws('/', org.full_path, org.shortname)
    else org.shortname
    end        as "fullPath"
    FROM wa_batch_overtime ot
    JOIN sys_emp_info ei ON ot.empid = ei.empid AND ei.deleted = 0
    LEFT JOIN sys_corp_org org on ei.orgid = org.orgid AND org.deleted = 0
    <where>
      ot.tenant_id = #{tenantId}
      <if test="empid != null">
        AND ot.empid = #{empid}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
      </if>
      <if test="startTime != null and endTime != null">
        AND ot.start_time <![CDATA[<=]]> #{endTime} AND ot.end_time <![CDATA[>=]]> #{startTime}
      </if>
      <if test="datafilter != null and datafilter != ''">
        ${datafilter}
      </if>
    </where>
    ) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
  </select>

  <select id="selectListGroupByCompensateType" resultType="map">
    SELECT
    ot.status,
    ot.compensate_type as "compensateType",
    sum(ot.ot_duration) as "otDuration"
    FROM wa_emp_overtime ot
    JOIN sys_emp_info ei ON ot.empid = ei.empid
    WHERE ot.empid = #{empid}
    AND ei.belong_org_id = #{belongOrgId}
    AND ot.status IN <foreach collection="statusList" open="(" separator="," close=")" item="item">#{item}</foreach>
    AND (
      (ot.start_time  <![CDATA[>=]]> #{startTime} AND ot.start_time <![CDATA[<=]]> #{endTime})
      OR (ot.end_time <![CDATA[>=]]> #{startTime} AND ot.end_time <![CDATA[<=]]> #{endTime})
      OR (ot.start_time <![CDATA[<=]]> #{startTime} AND ot.end_time <![CDATA[>=]]> #{endTime})
    )
    GROUP BY ot.compensate_type, ot.status
  </select>
</mapper>