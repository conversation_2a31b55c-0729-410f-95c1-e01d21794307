<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.LeaveRecordMapper">

    <select id="getLeaveDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        SELECT
                el.leave_id            AS "leaveId",
                el.leave_type_id       AS "leaveTypeId",
                el.empid,
               ei.workno,
               ei.emp_name            AS "empName",
               lt.leave_name          AS "leaveName",
               lt.i18n_leave_name     AS "i18nLeaveName",
               welt.start_time        AS "startTime",
               welt.end_time          AS "endTime",
               welt.shift_start_time  AS "shiftStartTime",
               welt.shift_end_time    AS "shiftEndTime",
               welt.shalf_day         AS "shalfDay",
               welt.ehalf_day         AS "ehalfDay",
               welt.period_type       AS "periodType",
               el.time_unit           AS "timeUnit",
               el.total_time_duration AS "totalTimeDuration",
               el.time_slot           AS "timeSlot",
               el.status,
               CASE
                   WHEN el.status = 0
                       THEN '暂存'
                   WHEN el.status = 1
                       THEN '审批中'
                   WHEN el.status = 2
                       THEN '已通过'
                   WHEN el.status = 3
                       THEN '已拒绝'
                   WHEN el.status = 4
                       THEN '已作废'
                   WHEN el.status = 5
                       THEN '已退回'
                   WHEN el.status = 9
                       THEN '已撤销' END AS "statusName",
               el.reason,
               el.revoke_reason       as "revokeReason",
               wlf.file_name          as "fileNames",
               wlf.url                as "files", el.crttime,
               ei.hire_date           as "hireDate",
               co.shortname           as "orgName",
               lt.leave_type          as "leaveType",
               case
                   when co.full_path is not null and co.full_path != ''
                then concat_ws('/', co.full_path, co.shortname)
                   else co.shortname
                   end  as "fullPath",
               el.cancel_time_duration as "cancelTimeDuration",
               el.total_time_duration-el.cancel_time_duration as "actualTimeDuration",
               lt.leave_cancel_remark as "leaveCancelRemark",
               ei.workplace as "workCity",
               va.marriage,
               ei.employ_type as "employType",
               el.leave_status        as "leaveStatus",
               el.updtime             as "updtime",
               el.child_num           as "childNum",
               el.maternity_leave_type as "maternityLeaveType",
               el.manufacture_date as "manufactureDate",
               el.crtuser,
               el.process_code as "processCode",
               el.quota_detail as "quotaDetail"
        FROM wa_emp_leave el
                 JOIN wa_leave_type lt ON el.leave_type_id = lt.leave_type_id
                 JOIN wa_emp_leave_time welt ON welt.leave_id = el.leave_id
                 JOIN sys_emp_info ei ON el.empid = ei.empid and ei.deleted = 0
                 LEFT JOIN wa_leave_file wlf on wlf.leave_id = el.leave_id
                 LEFT JOIN sys_corp_org co ON co.orgid = ei.orgid
                 LEFT JOIN sys_emp_privacy va ON va.empid = ei.empid
        WHERE el.leave_id = #{leaveId}
          <if test="tenantId != null">
              AND ei.belong_org_id = #{tenantId,jdbcType=VARCHAR}
          </if>
    </select>

    <select id="getLeaveRecordList" parameterType="hashmap" resultType="hashmap">
        select * from(
        SELECT
        a.leave_id       as   waidtype,
        a.leave_id            as waid,
        b.workno,
        b.orgid,
        b.emp_name,
        b.eng_name,
        b.belong_org_id,
        a.crtuser,
        leave_name       as   wa_name,
        c.leave_type,
        a.time_slot as "timeSlot",
        a.leave_type_id,
        a.total_time_duration as duration,
        a.cancel_time_duration as "cancelTimeDuration",
        a.total_time_duration-a.cancel_time_duration as "actualTimeDuration",
        a.time_unit,
        a.crttime,
        a.status,
        1                     watype,
        a.last_approval_time,
        a.reason,
        case
        when sco.full_path is not null and sco.full_path != ''
        then concat_ws('/', sco.full_path, sco.shortname)
        else sco.shortname
        end  as   "fullPath",
        sco.shortname,
        a.revoke_status  AS   "revokeStatus",
        a.leave_form_no,
        a.revoke_reason  AS   "revokeReason",
        welt.start_time,
        welt.end_time,
        welt.shift_start_time,
        welt.shift_end_time,
        welt.period_type,
        d.leave_type_def_code as "leaveTypeCode",
        a.leave_status AS "leaveStatus",
        c.allowed_multiple_cancel AS "allowedMultipleCancel",
        c.allowed_cancel_type AS "allowedCancelType",
        b.employ_type,welt.shalf_day,welt.ehalf_day,
        a.process_code as "processCode",
        i18n_leave_name  "i18nLeaveName"
        FROM wa_emp_leave a
        JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted = 0
        JOIN wa_leave_type c ON a.leave_type_id = c.leave_type_id
        JOIN wa_leave_type_def d on d.leave_type_def_id = c.leave_type
        JOIN wa_emp_leave_time welt on welt.leave_id = A.leave_id
        LEFT JOIN sys_corp_org sco on b.orgid = sco.orgid AND sco.deleted = 0
        <where>
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
            and b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="filterStatus != null and filterStatus.length > 0">
                <foreach collection="filterStatus" open=" and a.status in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="empId != null">
                and a.empid = #{empId}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (b.workno like concat('%', #{keywords}, '%') or b.emp_name like concat('%', #{keywords}, '%') or a.process_code like concat(#{keywords}, '%'))
            </if>
        </where>
        ) as t
        <if test="filterCancel != null and filterCancel and startDateTime != null">
            JOIN LATERAL (
                SELECT count(1) as "dayCount" FROM wa_leave_daytime wld
                    WHERE wld.leave_id = t.waid
                AND wld.time_duration &gt; wld.cancel_time_duration
                AND wld.leave_date BETWEEN #{startDateTime} AND #{endDateTime}
            ) as t1 ON TRUE
        </if>
        <where>
            <if test="filter != null">
                ${filter}
            </if>
            <if test="startDateTime != null">
                AND (
                (t.shift_start_time &gt;= #{startDateTime} AND t.shift_start_time &lt;= #{endDateTime})
                OR (t.shift_end_time &gt;= #{startDateTime} AND t.shift_end_time &lt;= #{endDateTime})
                OR (t.shift_start_time &lt;= #{startDateTime} AND t.shift_end_time &gt;= #{endDateTime})
                )
            </if>
            <if test="filterCancel != null and filterCancel">
                AND t1."dayCount" &gt; 0
            </if>
        </where>
    </select>

    <select id="getUserLeaveList" resultType="hashmap">
        select wel.leave_id as "leaveId",
        wlt.leave_name         as "leaveName",
        wel.crttime            as "applyTime",
        wel.time_slot          as "timeSlot",
        coalesce(welt.shift_start_time, welt.start_time) AS "startTime",
        coalesce(welt.shift_end_time, welt.end_time) AS "endTime",
        wel.status,
        CASE
        WHEN wel.status = 0
        THEN '暂存'
        WHEN wel.status = 1
        THEN '审批中'
        WHEN wel.status = 2
        THEN '已通过'
        WHEN wel.status = 3
        THEN '已拒绝'
        WHEN wel.status = 4
        THEN '已作废'
        WHEN wel.status = 5
        THEN '已退回'
        WHEN wel.status = 8
        THEN '撤销中'
        WHEN wel.status = 9
        THEN '已撤销' END AS "statusName",
        wel.total_time_duration,
        wel.cancel_time_duration as "cancelTimeDuration",
        wel.total_time_duration-wel.cancel_time_duration as "actualTimeDuration",
        wel.time_unit,
        wel.reason,
        wel.revoke_reason as revokeReason,
        def.leave_type_def_code as "leaveTypeCode",
        wel.leave_status as "leaveStatus",
        wlt.allowed_multiple_cancel AS "allowedMultipleCancel",
        wlt.allowed_cancel_type AS "allowedCancelType",
        wel.last_approval_time AS "lastApprovalTime",
        wlt.i18n_leave_name         as "i18nLeaveName"
        from wa_emp_leave wel
        join wa_emp_leave_time welt ON wel.leave_id = welt.leave_id
        join wa_leave_type wlt on wel.leave_type_id = wlt.leave_type_id and is_emp_show = true
        JOIN wa_leave_type_def def on def.leave_type_def_id = wlt.leave_type
        where wel.empid = #{empid}
        <if test=" status != null">
            and wel.status = #{status}
        </if>
        <if test="leaveName != null and leaveName != ''">
            and wlt.leave_name like concat('%', #{leaveName}, '%')
        </if>
        <if test=" applyStartTime != null">
            and wel.crttime >= #{applyStartTime}
        </if>
        <if test=" applyEndTime != null">
            and  #{applyEndTime} >= wel.crttime
        </if>
        order by wel.crttime desc
    </select>

    <sql id="Base_Column_List" >
        leave_id, leave_form_no, leave_type_id, empid, reason, status, first_empid, approval_num,
        start_date, time_slot, total_time_duration, time_unit, crtuser, crttime, upduser,
        updtime, last_approval_time, last_empid, revoke_reason, cancel_form_no, revoke_status,
        province, city, county, forgid, emergency_contact, opt_flag, is_invalid, invalid_reason,
        wedding_date, expected_date, manufacture_date
    </sql>

    <resultMap id="BaseResultMap" type="com.caidao1.wa.mybatis.model.WaEmpLeave" >
        <id column="leave_id" property="leaveId" jdbcType="INTEGER" />
        <result column="leave_form_no" property="leaveFormNo" jdbcType="VARCHAR" />
        <result column="leave_type_id" property="leaveTypeId" jdbcType="INTEGER" />
        <result column="empid" property="empid" jdbcType="BIGINT" />
        <result column="reason" property="reason" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="SMALLINT" />
        <result column="first_empid" property="firstEmpid" jdbcType="BIGINT" />
        <result column="approval_num" property="approvalNum" jdbcType="SMALLINT" />
        <result column="start_date" property="startDate" jdbcType="VARCHAR" />
        <result column="time_slot" property="timeSlot" jdbcType="VARCHAR" />
        <result column="total_time_duration" property="totalTimeDuration" jdbcType="REAL" />
        <result column="time_unit" property="timeUnit" jdbcType="INTEGER" />
        <result column="crtuser" property="crtuser" jdbcType="BIGINT" />
        <result column="crttime" property="crttime" jdbcType="BIGINT" />
        <result column="upduser" property="upduser" jdbcType="BIGINT" />
        <result column="updtime" property="updtime" jdbcType="BIGINT" />
        <result column="last_approval_time" property="lastApprovalTime" jdbcType="BIGINT" />
        <result column="last_empid" property="lastEmpid" jdbcType="BIGINT" />
        <result column="revoke_reason" property="revokeReason" jdbcType="VARCHAR" />
        <result column="cancel_form_no" property="cancelFormNo" jdbcType="VARCHAR" />
        <result column="revoke_status" property="revokeStatus" jdbcType="INTEGER" />
        <result column="province" property="province" jdbcType="BIGINT" />
        <result column="city" property="city" jdbcType="BIGINT" />
        <result column="county" property="county" jdbcType="INTEGER" />
        <result column="forgid" property="forgid" jdbcType="INTEGER" />
        <result column="emergency_contact" property="emergencyContact" jdbcType="VARCHAR" />
        <result column="opt_flag" property="optFlag" jdbcType="INTEGER" />
        <result column="is_invalid" property="isInvalid" jdbcType="BIT" />
        <result column="invalid_reason" property="invalidReason" jdbcType="VARCHAR" />
        <result column="wedding_date" property="weddingDate" jdbcType="BIGINT" />
        <result column="expected_date" property="expectedDate" jdbcType="BIGINT" />
        <result column="manufacture_date" property="manufactureDate" jdbcType="BIGINT" />
    </resultMap>

    <select id="getWaEmpLeaveListForHaveQuota" resultMap="BaseResultMap">
        select
        wel.leave_id,
        wel.leave_form_no,
        wel.leave_type_id,
        wel.empid,
        wel.reason,
        wel.status,
        wel.first_empid,
        wel.approval_num,
        wel.start_date,
        wel.time_slot,
        wel.total_time_duration,
        wel.time_unit,
        wel.crtuser,
        wel.crttime,
        wel.upduser,
        wel.updtime,
        wel.last_approval_time,
        wel.last_empid,
        wel.revoke_reason,
        wel.cancel_form_no,
        wel.revoke_status,
        wel.province,
        wel.city,
        wel.county,
        wel.forgid,
        wel.emergency_contact,
        wel.opt_flag,
        wel.is_invalid,
        wel.invalid_reason,
        wel.wedding_date,
        wel.expected_date,
        wel.manufacture_date
        from wa_emp_leave wel
        join wa_leave_type wlt on wel.leave_type_id = wlt.leave_type_id
        where wlt.belong_orgid = #{belongOrgId,jdbcType=VARCHAR}
        and wlt.quota_restriction_type = 1
        <if test="quotaType != null">
            and wlt.quota_type = #{quotaType}
        </if>
        <if test="empId != null">
            and wel.empid = #{empId}
        </if>
        <if test="leaveId != null">
            and wel.leave_id = #{leaveId}
        </if>
        <if test="leaveTypeId != null">
            and wlt.leave_type_id = #{leaveTypeId}
        </if>
        order by wel.crttime
    </select>


    <select id="getLeaveRecords" parameterType="hashmap" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
      SELECT a.leave_id,
       a.empid,
       b.belong_org_id,
       a.crtuser,
       a.crttime,
       a.updtime,
       a.upduser,
       a.status,
       c.shift_start_time as start_time,
       c.shift_end_time as end_time
      FROM wa_emp_leave a
         JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted = 0
         JOIN wa_emp_leave_time c on c.leave_id = A.leave_id
      WHERE b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
    </select>

    <select id="getEmpLeaveList"  resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        select a.leave_id,b.empid,welt.start_time,welt.end_time,a.leave_type_id,a.last_approval_time
        from wa_emp_leave a
        join sys_emp_info b on a.empid = b.empid and b.deleted = 0
        join wa_leave_type c ON a.leave_type_id = c.leave_type_id
        join wa_emp_leave_time welt on welt.leave_id = a.leave_id
        and b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and status in <foreach collection="statusList" open="(" separator="," close=")" item="item">#{item}</foreach>
        and leave_status in <foreach collection="leaveStatusList" open="(" separator="," close=")" item="item">#{item}</foreach>
    </select>

    <select id="countLeave" resultType = "java.lang.Integer">
        SELECT count(m.*)
        FROM (
        SELECT
        el.start_date,
        el.time_slot,
        lt.shift_start_time,
        CASE
        WHEN(lt.period_type = 1 OR lt.period_type = 4)
        THEN lt.shift_end_time + 86399
        ELSE
        lt.shift_end_time
        END AS shift_end_time
        FROM
        wa_emp_leave el
        JOIN wa_emp_leave_time lt ON el.leave_id = lt.leave_id AND el.status != 3 AND el.status != 4 AND el.status != 9 AND el.status != 5
        JOIN wa_leave_type wlt on wlt.leave_type_id = el.leave_type_id
        WHERE el.empid = #{empId} and el.cancel_time_duration=0
        ) m
        WHERE (
        (#{start} <![CDATA[>=]]> m.shift_start_time AND #{start} <![CDATA[<]]> m.shift_end_time)
        OR (#{end} <![CDATA[>]]> m.shift_start_time AND #{end} <![CDATA[<=]]> m.shift_end_time)
        OR (#{start} <![CDATA[<=]]> m.shift_start_time AND #{end} <![CDATA[>=]]> m.shift_end_time)
        )
    </select>

    <select id="selectEmpNonBrjLtList" resultType="map">
        SELECT wd.leave_date, shalf_day, ehalf_day, start_time, end_time, period_type
        FROM wa_emp_leave el
                 JOIN wa_leave_daytime wd on wd.leave_id = el.leave_id
            AND el.status != 3 AND el.status != 4 AND el.status != 9 AND el.status != 5
         JOIN wa_leave_type wt ON wt.leave_type_id = el.leave_type_id
            JOIN wa_leave_type_def def ON def.leave_type_def_id = wt.leave_type
        WHERE el.empid = #{empId}
          and wd.cancel_time_duration = 0
          AND def.leave_type_def_code != 'BRJ'
          <if test="startDate != null and endDate != null">
            and wd.leave_date between #{startDate} and #{endDate}
          </if>
    </select>

    <select id="selectEmpLeaveByIds" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        select leave_id,
        shift_start_time,
        shift_end_time,
        period_type,
        shalf_day,
        ehalf_day,
        start_time,
        end_time
        from wa_emp_leave_time wel
        <where>
            AND leave_id in
            <foreach collection="leaveIds" open="(" close=")" item="leaveId" separator=",">
                #{leaveId}
            </foreach>
        </where>
    </select>

    <select id="selectEmpLeaveByEmpIds" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        select a.time_unit,
        coalesce(b.apply_time_duration, b.time_duration) - COALESCE (b.cancel_time_duration, 0) as total_time_duration,
        a.empid,
        b.real_date,wlt.leave_name,a.status
        from wa_emp_leave a
        left join wa_leave_daytime b on a.leave_id = b.leave_id
        left join wa_leave_type wlt on a.leave_type_id = wlt.leave_type_id
        where b.time_duration > 0 and coalesce(b.real_date,b.leave_date)=#{leaveDate}
        <if test="empIds != null and empIds.size() > 0">
            and a.empid in
            <foreach collection="empIds" open="(" close=")" item="empId" separator=",">
                #{empId}
            </foreach>
        </if>
    </select>

    <select id="queryEmpLeaveByEmpIds" resultType="com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo">
        SELECT
            el.leave_id            AS "leaveId",
            welt.start_time        AS "startTime",
            welt.end_time          AS "endTime",
            welt.shift_start_time  AS "shiftStartTime",
            welt.shift_end_time    AS "shiftEndTime",
            welt.shalf_day         AS "shalfDay",
            welt.ehalf_day         AS "ehalfDay",
            welt.period_type       AS "periodType",
            el.time_unit           AS "timeUnit"
        FROM wa_emp_leave el
        JOIN wa_emp_leave_time welt ON welt.leave_id = el.leave_id
        JOIN wa_leave_daytime wld on el.leave_id = wld.leave_id
        WHERE el.status in (1,2) and el.total_time_duration-el.cancel_time_duration>0
        <if test="leaveDaytimeIds != null and leaveDaytimeIds.size() > 0">
            AND wld.leave_daytime_id in
            <foreach collection="leaveDaytimeIds" item="leaveDaytimeId" open="(" separator="," close=")">
                #{leaveDaytimeId}
            </foreach>
        </if>
        order by welt.start_time
    </select>
</mapper>