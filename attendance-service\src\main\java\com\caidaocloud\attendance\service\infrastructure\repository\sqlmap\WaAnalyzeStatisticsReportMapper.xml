<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaAnalyzeStatisticsReportMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyzeStatisticsReport">
    <id column="statistics_report_id" jdbcType="BIGINT" property="statisticsReportId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="work_time" jdbcType="INTEGER" property="workTime" />
    <result column="actual_work_time" jdbcType="REAL" property="actualWorkTime" />
    <result column="register_time" jdbcType="INTEGER" property="registerTime" />
    <result column="late_time" jdbcType="REAL" property="lateTime" />
    <result column="early_time" jdbcType="REAL" property="earlyTime" />
    <result column="kg_work_time" jdbcType="INTEGER" property="kgWorkTime" />
    <result column="bdk_count" jdbcType="INTEGER" property="bdkCount" />
    <result column="ot_time" jdbcType="INTEGER" property="otTime" />
    <result column="leave_count" jdbcType="INTEGER" property="leaveCount" />
    <result column="travel_count" jdbcType="INTEGER" property="travelCount" />
    <result column="rpt_type" jdbcType="INTEGER" property="rptType" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />

    <result column="total_late_count" jdbcType="INTEGER" property="totalLateCount" />
    <result column="total_early_count" jdbcType="INTEGER" property="totalEarlyCount" />
    <result column="total_kg_count" jdbcType="INTEGER" property="totalKgCount" />
  </resultMap>
  <sql id="Base_Column_List">
    statistics_report_id, tenant_id, emp_id, work_time, actual_work_time, register_time, 
    late_time, early_time, kg_work_time, bdk_count, ot_time, leave_count, travel_count, rpt_type, start_date,
    deleted, create_by, create_time, update_by, update_time, total_late_count, total_early_count, total_kg_count
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_analyze_statistics_report
    where statistics_report_id = #{statisticsReportId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_analyze_statistics_report
    where statistics_report_id = #{statisticsReportId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyzeStatisticsReport">
    insert into wa_analyze_statistics_report (statistics_report_id, tenant_id, emp_id, 
      work_time, actual_work_time, register_time, 
      late_time, early_time, kg_work_time, 
      bdk_count, ot_time, leave_count, travel_count,
      rpt_type, start_date, deleted, 
      create_by, create_time, update_by, 
      update_time, total_late_count, total_early_count, total_kg_count)
    values (#{statisticsReportId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{empId,jdbcType=BIGINT},
      #{workTime,jdbcType=INTEGER}, #{actualWorkTime,jdbcType=REAL}, #{registerTime,jdbcType=INTEGER},
      #{lateTime,jdbcType=REAL}, #{earlyTime,jdbcType=REAL}, #{kgWorkTime,jdbcType=INTEGER},
      #{bdkCount,jdbcType=INTEGER}, #{otTime,jdbcType=INTEGER}, #{leaveCount,jdbcType=INTEGER}, #{travelCount,jdbcType=INTEGER}
      #{rptType,jdbcType=INTEGER}, #{startDate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{totalLateCount,jdbcType=INTEGER}, #{totalEarlyCount,jdbcType=INTEGER}, #{totalKgCount,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyzeStatisticsReport">
    insert into wa_analyze_statistics_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticsReportId != null">
        statistics_report_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="workTime != null">
        work_time,
      </if>
      <if test="actualWorkTime != null">
        actual_work_time,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="lateTime != null">
        late_time,
      </if>
      <if test="earlyTime != null">
        early_time,
      </if>
      <if test="kgWorkTime != null">
        kg_work_time,
      </if>
      <if test="bdkCount != null">
        bdk_count,
      </if>
      <if test="otTime != null">
        ot_time,
      </if>
      <if test="leaveCount != null">
        leave_count,
      </if>
      <if test="travelCount != null">
        travel_count,
      </if>
      <if test="rptType != null">
        rpt_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="totalLateCount != null">
        total_late_count,
      </if>
      <if test="totalEarlyCount != null">
        total_early_count,
      </if>
      <if test="totalKgCount != null">
        total_kg_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticsReportId != null">
        #{statisticsReportId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=BIGINT},
      </if>
      <if test="workTime != null">
        #{workTime,jdbcType=INTEGER},
      </if>
      <if test="actualWorkTime != null">
        #{actualWorkTime,jdbcType=REAL},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=INTEGER},
      </if>
      <if test="lateTime != null">
        #{lateTime,jdbcType=REAL},
      </if>
      <if test="earlyTime != null">
        #{earlyTime,jdbcType=REAL},
      </if>
      <if test="kgWorkTime != null">
        #{kgWorkTime,jdbcType=INTEGER},
      </if>
      <if test="bdkCount != null">
        #{bdkCount,jdbcType=INTEGER},
      </if>
      <if test="otTime != null">
        #{otTime,jdbcType=INTEGER},
      </if>
      <if test="leaveCount != null">
        #{leaveCount,jdbcType=INTEGER},
      </if>
      <if test="travelCount != null">
        #{travelCount,jdbcType=INTEGER},
      </if>
      <if test="rptType != null">
        #{rptType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="totalLateCount != null">
        #{totalLateCount,jdbcType=INTEGER},
      </if>
      <if test="totalEarlyCount != null">
        #{totalEarlyCount,jdbcType=INTEGER},
      </if>
      <if test="totalKgCount != null">
        #{totalKgCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyzeStatisticsReport">
    update wa_analyze_statistics_report
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=BIGINT},
      </if>
      <if test="workTime != null">
        work_time = #{workTime,jdbcType=INTEGER},
      </if>
      <if test="actualWorkTime != null">
        actual_work_time = #{actualWorkTime,jdbcType=REAL},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=INTEGER},
      </if>
      <if test="lateTime != null">
        late_time = #{lateTime,jdbcType=REAL},
      </if>
      <if test="earlyTime != null">
        early_time = #{earlyTime,jdbcType=REAL},
      </if>
      <if test="kgWorkTime != null">
        kg_work_time = #{kgWorkTime,jdbcType=INTEGER},
      </if>
      <if test="bdkCount != null">
        bdk_count = #{bdkCount,jdbcType=INTEGER},
      </if>
      <if test="otTime != null">
        ot_time = #{otTime,jdbcType=INTEGER},
      </if>
      <if test="leaveCount != null">
        leave_count = #{leaveCount,jdbcType=INTEGER},
      </if>
      <if test="travelCount != null">
        travel_count = #{travelCount,jdbcType=INTEGER},
      </if>
      <if test="rptType != null">
        rpt_type = #{rptType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="totalLateCount != null">
        total_late_count = #{totalLateCount,jdbcType=INTEGER},
      </if>
      <if test="totalEarlyCount != null">
        total_early_count = #{totalEarlyCount,jdbcType=INTEGER},
      </if>
      <if test="totalKgCount != null">
        total_kg_count = #{totalKgCount,jdbcType=INTEGER},
      </if>
    </set>
    where statistics_report_id = #{statisticsReportId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyzeStatisticsReport">
    update wa_analyze_statistics_report
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=BIGINT},
      work_time = #{workTime,jdbcType=INTEGER},
      actual_work_time = #{actualWorkTime,jdbcType=REAL},
      register_time = #{registerTime,jdbcType=INTEGER},
      late_time = #{lateTime,jdbcType=REAL},
      early_time = #{earlyTime,jdbcType=REAL},
      kg_work_time = #{kgWorkTime,jdbcType=INTEGER},
      bdk_count = #{bdkCount,jdbcType=INTEGER},
      ot_time = #{otTime,jdbcType=INTEGER},
      leave_count = #{leaveCount,jdbcType=INTEGER},
      travel_count = #{travelCount,jdbcType=INTEGER},
      rpt_type = #{rptType,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      total_late_count = #{totalLateCount,jdbcType=INTEGER},
      total_early_count = #{totalEarlyCount,jdbcType=INTEGER},
      total_kg_count = #{totalKgCount,jdbcType=INTEGER}
    where statistics_report_id = #{statisticsReportId,jdbcType=BIGINT}
  </update>
  <select id="getAnalyzeStatisticsReportPageList"
          resultType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaAnalyzeStatisticsReport">
    select
    <include refid="Base_Column_List" />
    from wa_analyze_statistics_report
    where tenant_id = #{tenantId,jdbcType=VARCHAR} and start_date = #{startDate} and rpt_type = #{rptType} and deleted = 0
    <if test="empId != null">
      and emp_id = #{empId}
    </if>
  </select>
  <insert id="insertBatch">
    insert into wa_analyze_statistics_report (statistics_report_id, tenant_id, emp_id,
    work_time, actual_work_time, register_time,
    late_time, early_time, kg_work_time,
    bdk_count, ot_time, leave_count,travel_count,
    rpt_type, start_date, deleted,
    create_by, create_time, update_by,
    update_time, total_late_count, total_early_count, total_kg_count)
    values
    <foreach collection="records" item="record" separator=",">
      (#{record.statisticsReportId,jdbcType=BIGINT}, #{record.tenantId,jdbcType=VARCHAR}, #{record.empId,jdbcType=BIGINT},
      #{record.workTime,jdbcType=INTEGER}, #{record.actualWorkTime,jdbcType=REAL}, #{record.registerTime,jdbcType=INTEGER},
      #{record.lateTime,jdbcType=REAL}, #{record.earlyTime,jdbcType=REAL}, #{record.kgWorkTime,jdbcType=INTEGER},
      #{record.bdkCount,jdbcType=INTEGER}, #{record.otTime,jdbcType=INTEGER}, #{record.leaveCount,jdbcType=INTEGER}, #{record.travelCount,jdbcType=INTEGER},
      #{record.rptType,jdbcType=INTEGER}, #{record.startDate,jdbcType=BIGINT}, #{record.deleted,jdbcType=INTEGER},
      #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT}, #{record.updateBy,jdbcType=BIGINT},
      #{record.updateTime,jdbcType=BIGINT}, #{record.totalLateCount,jdbcType=INTEGER}, #{record.totalEarlyCount,jdbcType=INTEGER}, #{record.totalKgCount,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>