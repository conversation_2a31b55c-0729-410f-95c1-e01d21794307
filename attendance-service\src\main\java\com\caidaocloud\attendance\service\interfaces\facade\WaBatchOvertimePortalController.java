package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.enums.AppTypeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchOvertimeService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeApplyDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.RevokeBatchOvertimeDto;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.BatchOvertimePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeApplyTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeDateVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.OvertimeStatisticsVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 门户-批量加班
 *
 * <AUTHOR>
 * @Date 2024/6/14
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/overtime/portal/v1")
@Api(value = "/api/attendance/batch/overtime/portal/v1", description = "门户-批量加班")
public class WaBatchOvertimePortalController {
    @Autowired
    private WaBatchOvertimeService waBatchOvertimeService;
    @Autowired
    private CacheService cacheService;

    @ApiOperation(value = "加班统计")
    @GetMapping(value = "/statistics")
    public Result<OvertimeStatisticsVo> statistics(@RequestParam("startDate") Long startDate,
                                                   @RequestParam("endDate") Long endDate) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        return Result.ok(waBatchOvertimeService.statistics(userInfo.getStaffId(), startDate, endDate));
    }

    @ApiOperation(value = "获取加班日期")
    @GetMapping(value = "/listDate")
    public Result<List<OvertimeDateVo>> lisDate(@RequestParam("startDate") Long startDate,
                                                @RequestParam("endDate") Long endDate) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        return Result.ok(waBatchOvertimeService.listOtDate(userInfo.getStaffId(), startDate, endDate));
    }

    @ApiOperation(value = "计算加班时长")
    @PostMapping(value = "/calTime")
    public Result<OvertimeApplyTimeVo> calTime(@RequestBody BatchOvertimeApplyDto applyDto) throws Exception {
        String lockKey = BatchOvertimeApplyDto.getIdempotentString(applyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            UserInfo userInfo = UserContext.getAndCheckUser();
            applyDto.setEmpid(userInfo.getStaffId());
            return Result.ok(waBatchOvertimeService.calTime(applyDto));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.GET_OVERTIME_DURATION_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "保存加班单据")
    @PostMapping(value = "/save")
    public Result<OvertimeApplyTimeVo> save(@RequestBody BatchOvertimeApplyDto applyDto) throws Exception {
        String lockKey = BatchOvertimeApplyDto.getIdempotentString(applyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            UserInfo userInfo = UserContext.getAndCheckUser();
            applyDto.setEmpid(userInfo.getStaffId());
            return Result.ok(waBatchOvertimeService.save(applyDto, AppTypeEnum.BATCH_PC));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.APPLY_OVERTIME_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "分页列表")
    @PostMapping(value = "/page")
    public Result<AttendancePageResult<BatchOvertimePageListVo>> getPageList(@RequestBody QueryPageBean queryPageBean) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        BatchOvertimeQueryDto dto = new BatchOvertimeQueryDto();
        dto.setEmpid(userInfo.getStaffId());
        PageUtil.doCopyFieldProperty(queryPageBean, dto);
        PageBean pageBean = PageUtil.getPageBean(dto);
        waBatchOvertimeService.preHandleFilterField(pageBean);
        PageList<BatchOvertimePageListVo> pageList = waBatchOvertimeService.getPageList(pageBean, dto, userInfo);
        AttendancePageResult<BatchOvertimePageListVo> pageResult = new AttendancePageResult<>(pageList, pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revoke(@RequestBody RevokeBatchOvertimeDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            waBatchOvertimeService.revoke(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_FAILED, Boolean.FALSE);
        }
    }
}
