package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import lombok.Data;

@TableName(value = "wa_parse_group")
@Data
public class WaParseGroupPo extends WaParseGroup {
    @TableField(exist = false)
    private Long empid;
    @TableField(exist = false)
    private Long startTime;
    @TableField(exist = false)
    private Long endTime;
    @TableField(exist = false)
    private Integer waGroupId;
    @TableField(exist = false)
    private Integer cyleStartdate;
    @TableField(exist = false)
    private Integer cyleMonth;
}
