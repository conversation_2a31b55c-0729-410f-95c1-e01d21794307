<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaWorkflowRevokeMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="entity_id" jdbcType="BIGINT" property="entityId" />
    <result column="module_name" jdbcType="VARCHAR" property="moduleName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, entity_id, module_name, status, reason, last_approval_time, revoke_reason, 
    deleted, create_by, create_time, update_by, update_time, process_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_workflow_revoke
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_workflow_revoke
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke">
    insert into wa_workflow_revoke (id, tenant_id, entity_id, 
      module_name, status, reason, 
      last_approval_time, revoke_reason, deleted, 
      create_by, create_time, update_by, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{entityId,jdbcType=BIGINT}, 
      #{moduleName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, 
      #{lastApprovalTime,jdbcType=BIGINT}, #{revokeReason,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke">
    insert into wa_workflow_revoke
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="moduleName != null">
        module_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="moduleName != null">
        #{moduleName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke">
    update wa_workflow_revoke
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="moduleName != null">
        module_name = #{moduleName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke">
    update wa_workflow_revoke
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      entity_id = #{entityId,jdbcType=BIGINT},
      module_name = #{moduleName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryOvertimeWorkflowRevokeList" parameterType="hashmap" resultType="hashmap">
    SELECT *
    FROM (
    SELECT a.ot_id AS waid,
    b.workno,
    b.emp_name,
    b.belong_org_id,
    a.crtuser,
    sco.shortname,
    case when sco.full_path is not null and sco.full_path != '' then concat_ws('/', sco.full_path, sco.shortname) else sco.shortname end  as   "fullPath",
    b.orgid,
    ot_duration duration,
    2 AS time_unit,
    wwr.create_time crttime,
    wwr.status,wwr.id,
    a.date_type,
    wwr.last_approval_time,
    a.compensate_type,
    a.reason,
    a.start_time,
    a.end_time,
    a.overtime_type_id,
    b.employ_type,
    a.transfer_duration,
    a.transfer_unit,
    ot.rule_id,wwr.reason revokeReason
    FROM wa_emp_overtime a
    JOIN wa_workflow_revoke wwr on wwr.entity_id=a.ot_id
    JOIN sys_emp_info b ON a.empid = b.empid AND b.deleted=0
    LEFT JOIN sys_corp_org sco ON sco.orgid = b.orgid AND sco.deleted = 0
    LEFT JOIN wa_overtime_type ot on ot.overtime_type_id=a.overtime_type_id
    <where>
      <if test="dataFilter != null and dataFilter != ''">
        ${dataFilter}
      </if>
      <if test="belongOrgId != null">
        AND b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
      </if>
      AND wwr.status <![CDATA[>]]> 0 AND (a.forgid IS NULL OR a.forgid != 0)
      <if test="keywords != null and keywords != ''">
        AND (b.workno LIKE concat('%', #{keywords}, '%') OR b.emp_name LIKE concat('%', #{keywords}, '%'))
      </if>
      <if test="moduleName!=null">
        and wwr.module_name=#{moduleName}
      </if>
    </where>
    ) AS t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
      <if test="startDateTime != null">
        AND (
        (t.start_time  <![CDATA[>=]]> #{startDateTime} AND t.start_time <![CDATA[<=]]> #{endDateTime})
        OR (t.end_time <![CDATA[>=]]> #{startDateTime} AND t.end_time <![CDATA[<=]]> #{endDateTime})
        OR (t.start_time <![CDATA[<=]]> #{startDateTime} AND t.end_time <![CDATA[>=]]> #{endDateTime})
        )
      </if>
    </where>
  </select>

  <select id="queryTravelWorkflowRevokeList" parameterType="hashmap" resultType="hashmap">
    select * from (
    select
    ei.workno,
    ei.emp_name,
    sco.shortname as "orgName",
    case
    when sco.full_path is not null and sco.full_path != ''
    then concat_ws('/', sco.full_path, sco.shortname)
    else sco.shortname
    end as "fullPath",
    wtt.travel_type_name as "travelType",
    wtt.i18n_travel_type_name as "i18nTravelTypeName",
    wtt.travel_type_id,
    wet.travel_id,
    wet.quota_id,
    wet.time_duration,
    wet.time_unit,
    wet.period_type,
    wet.start_time,
    wet.end_time,
    wet.shift_start_time,
    wet.shift_end_time,
    wet.shalf_day,
    wet.ehalf_day,
    wet.travel_mode,
    wet.province,
    wet.county,
    wet.city,
    wwr.status,
    wwr.last_approval_time,
    wet.reason,
    wwr.reason as revokeReason,
    wwr.create_time,
    wet.batch_travel_id,
    ei.employ_type,
    ei.orgid,
    wet.process_code as "processCode",
    wet.ext_custom_col,
    wwr.id,wet.update_time,wwr.module_name
    from wa_emp_travel wet
    JOIN wa_workflow_revoke wwr on wwr.entity_id=wet.travel_id
    join sys_emp_info ei on ei.empid = wet.emp_id and ei.deleted = 0
    left join sys_corp_org sco on sco.orgid = ei.orgid
    left join wa_travel_type wtt on wtt.travel_type_id = wet.travel_type_id
    <where>
      AND wet.deleted=0 AND ei.belong_org_id = '${belongOrgId}'
      <if test="dataFilter != null and dataFilter != ''">
        ${dataFilter}
      </if>
      <if test="moduleName!=null">
        and wwr.module_name=#{moduleName}
      </if>
      <if test="keywords != null and keywords != ''">
        and (ei.emp_name like concat('%', #{keywords}, '%') or ei.workno like concat('%', #{keywords}, '%') or wet.process_code like concat(#{keywords}, '%'))
      </if>
      <if test="travelMode != null">
        and string_to_array(travel_mode,',') <![CDATA[&&]]> string_to_array(#{travelMode},',')
      </if>
    </where>
    union all
    select
    ei.workno,
    ei.emp_name,
    sco.shortname as "orgName",
    case
    when sco.full_path is not null and sco.full_path != ''
    then concat_ws('/', sco.full_path, sco.shortname)
    else sco.shortname
    end as "fullPath",
    wtt.travel_type_name as "travelType",
    wtt.i18n_travel_type_name as "i18nTravelTypeName",
    wtt.travel_type_id,
    wet.travel_id,
    wet.quota_id,
    wet.time_duration,
    wet.time_unit,
    wet.period_type,
    wet.start_time,
    wet.end_time,
    wet.shift_start_time,
    wet.shift_end_time,
    wet.shalf_day,
    wet.ehalf_day,
    wet.travel_mode,
    wet.province,
    wet.county,
    wet.city,
    wwr.status,
    wwr.last_approval_time,
    wet.reason,
    wwr.reason as revokeReason,
    wwr.create_time,
    wet.batch_travel_id,
    ei.employ_type,
    ei.orgid,
    wwr.process_code as "processCode",
    wet.ext_custom_col,
    wwr.id,wet.update_time,wwr.module_name
    from wa_emp_travel wet
    join wa_batch_travel wbt on wbt.batch_travel_id=wet.batch_travel_id
    join wa_workflow_revoke wwr on wwr.entity_id=wbt.batch_travel_id
    join sys_emp_info ei on ei.empid = wet.emp_id and ei.deleted = 0
    left join sys_corp_org sco on sco.orgid = ei.orgid
    left join wa_travel_type wtt on wtt.travel_type_id = wet.travel_type_id
    <where>
      and wet.batch_travel_id is not null and wet.deleted=0 and ei.belong_org_id = '${belongOrgId}'
      <if test="datafilter != null and datafilter != ''">
        ${datafilter}
      </if>
      <if test="keywords != null and keywords != ''">
        and (ei.emp_name like concat('%', #{keywords}, '%') or ei.workno like concat('%', #{keywords}, '%') or wet.process_code like concat(#{keywords}, '%'))
      </if>
      <if test="batchModuleName!=null">
        and wwr.module_name=#{batchModuleName}
      </if>
      <if test="travelMode != null">
        and string_to_array(travel_mode,',') <![CDATA[&&]]> string_to_array(#{travelMode},',')
      </if>
    </where>
    ) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
      <if test="startDateTime != null">
        AND (
        (t.shift_start_time &gt;= #{startDateTime} AND t.shift_start_time &lt;= #{endDateTime})
        OR (t.shift_end_time &gt;= #{startDateTime} AND t.shift_end_time &lt;= #{endDateTime})
        OR (t.shift_start_time &lt;= #{startDateTime} AND t.shift_end_time &gt;= #{endDateTime})
        )
      </if>
    </where>
    order by update_time desc
  </select>

  <select id="queryWorkflowRevokeList" resultMap="BaseResultMap">
    select * from wa_workflow_revoke
    where deleted=0 and entity_id=#{entityId}
      <if test="tenantId!=null">
        and tenant_id=#{tenantId}
      </if>
      <if test="statusList!=null and statusList.size()>0">
        and status in
          <foreach collection="statusList" item="status" separator="," open="(" close=")">
            #{status}
          </foreach>
      </if>
    <if test="moduleNames!=null and moduleNames.size()>0">
      and module_name in
      <foreach collection="moduleNames" item="moduleName" separator="," open="(" close=")">
        #{moduleName}
      </foreach>
    </if>
  </select>

  <select id="queryLeaveWorkflowRevokeList" parameterType="hashmap" resultType="hashmap">
    select * from(
    SELECT
    a.leave_id,
    ei.workno,
    ei.orgid,
    ei.emp_name,
    ei.eng_name,
    ei.belong_org_id,
    a.crtuser,
    leave_name,
    c.leave_type,
    a.time_slot as "timeSlot",
    a.leave_type_id,
    a.total_time_duration as duration,
    a.cancel_time_duration as "cancelTimeDuration",
    a.total_time_duration-a.cancel_time_duration as "actualTimeDuration",
    a.time_unit,
    wwr.create_time as "crttime",
    wwr.status,
    wwr.last_approval_time,
    a.reason,
    wwr.reason as revokeReason,
    case
    when sco.full_path is not null and sco.full_path != ''
    then concat_ws('/', sco.full_path, sco.shortname)
    else sco.shortname
    end  as   "fullPath",
    sco.shortname,
    welt.start_time,
    welt.end_time,
    welt.shift_start_time,
    welt.shift_end_time,
    welt.period_type,
    d.leave_type_def_code as "leaveTypeCode",
    a.leave_status AS "leaveStatus",
    c.allowed_multiple_cancel AS "allowedMultipleCancel",
    c.allowed_cancel_type AS "allowedCancelType",
    ei.employ_type,welt.shalf_day,welt.ehalf_day,
    a.process_code as "processCode",
    i18n_leave_name  "i18nLeaveName",wwr.module_name,wwr.id
    FROM wa_emp_leave a
    JOIN wa_batch_leave wbl on wbl.batch_id=a.batch_id
    JOIN wa_workflow_revoke wwr on wwr.entity_id=wbl.batch_id
    JOIN sys_emp_info ei ON a.empid = ei.empid AND ei.deleted = 0
    JOIN wa_leave_type c ON a.leave_type_id = c.leave_type_id
    JOIN wa_leave_type_def d on d.leave_type_def_id = c.leave_type
    JOIN wa_emp_leave_time welt on welt.leave_id = A.leave_id
    LEFT JOIN sys_corp_org sco on ei.orgid = sco.orgid AND sco.deleted = 0
    <where>
      <if test="datafilter != null and datafilter != ''">
        ${datafilter}
      </if>
      and ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
      <if test="status != null">
        and a.status = #{status}
      </if>
      <if test="empId != null">
        and a.empid = #{empId}
      </if>
      <if test="moduleName!=null">
        and wwr.module_name=#{moduleName}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%') or a.process_code like concat(#{keywords}, '%'))
      </if>
    </where>
    ) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
      <if test="startDateTime != null">
        AND (
        (t.shift_start_time &gt;= #{startDateTime} AND t.shift_start_time &lt;= #{endDateTime})
        OR (t.shift_end_time &gt;= #{startDateTime} AND t.shift_end_time &lt;= #{endDateTime})
        OR (t.shift_start_time &lt;= #{startDateTime} AND t.shift_end_time &gt;= #{endDateTime})
        )
      </if>
    </where>
  </select>
</mapper>