package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.application.dto.FileBaseInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OverReApplyVo {
    @ApiModelProperty("加班补偿类型 1付现 2调休")
    private Integer compensateType;

    @ApiModelProperty("员工id")
    private Long empid;

    @ApiModelProperty("员工姓名")
    private String empName;

    @ApiModelProperty("结束日期 日")
    private Long endTime;

    @ApiModelProperty("结束时间")
    private Integer etime;

    @ApiModelProperty("主键id")
    private Integer otId;

    @ApiModelProperty("理由")
    private String reason;

    @ApiModelProperty("开始日期 日")
    private Long startTime;

    @ApiModelProperty("审批状态")
    private Integer status;

    @ApiModelProperty("开始时间")
    private Integer stime;

    @ApiModelProperty("开始时间")
    private List<FileBaseInfoDto> files;
}
