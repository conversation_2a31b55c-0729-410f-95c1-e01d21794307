package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DragSortDto {
    @ApiModelProperty("排序数据父ID，可选")
    private Object pid;
    @ApiModelProperty("排序数据")
    private List<DragSortItemDto> items;
}
