package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.dto.file.AttachmentInfoDto;
import com.caidaocloud.attendance.service.application.service.impl.FileService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;

@Slf4j
@RestController
@RequestMapping("/api/attendance/file/v1")
@Api(value = "/api/attendance/file/v1", description = "文件处理")
public class FileController {

    @Autowired
    private FileService fileService;

    @PostMapping("/upload")
    public Result<AttachmentInfoDto> uploadFile(@RequestParam("file") MultipartFile multipartFile) {
        return Result.ok(fileService.upload(multipartFile, null));
    }

    @PostMapping("/asyncUpload")
    public Result<AttachmentInfoDto> asyncUploadFile(@RequestParam("file") MultipartFile multipartFile) {
        return Result.ok(fileService.asyncUpload(multipartFile, UserContext.getAndCheckUser()));
    }

    @PostMapping("/download")
    public void downloadFile(@RequestParam("path") String path, @RequestParam("fileName") String fileName,
                             HttpServletResponse response) throws UnsupportedEncodingException {
        fileService.download(path, fileName, response);
    }

    @GetMapping("/preview")
    public void previewFile(@RequestParam("path") String path, @RequestParam("fileName") String fileName,
                            HttpServletResponse response) throws UnsupportedEncodingException {
        fileService.download(path, fileName, response);
    }
}
