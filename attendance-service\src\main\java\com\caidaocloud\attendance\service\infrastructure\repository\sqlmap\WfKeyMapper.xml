<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WfKeyMapper">
    <select id="getWfKey" resultType="com.caidaocloud.attendance.service.domain.entity.WfKeyDo">
        select proc_inst_id as "procInstId", business_key as "businessKey"
        from wf_func_process wfp
        JOIN wf_config wfc ON wfp.wf_config_id = wfc.wf_config_id
        where wfc.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        and business_key = #{id} || '_' || #{wfFuncId}
        order by start_time desc
        limit 1
    </select>
</mapper>