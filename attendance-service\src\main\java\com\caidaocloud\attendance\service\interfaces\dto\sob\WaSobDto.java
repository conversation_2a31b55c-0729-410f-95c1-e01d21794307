package com.caidaocloud.attendance.service.interfaces.dto.sob;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Data
public class WaSobDto {
    @ApiModelProperty("主键")
    private Integer waSobId;
    @ApiModelProperty("周期名称")
    private String waSobName;
    @ApiModelProperty("所属周期")
    private Integer sysPeriodMonth;
    @ApiModelProperty("考勤方案")
    private Integer waGroupId;
    @ApiModelProperty("周期开始日期")
    private Long startDate;
    @ApiModelProperty("周期结束日期")
    private Long endDate;
    @ApiModelProperty("考勤截止日")
    private Long sobEndDate;
    @ApiModelProperty("账套关闭日期")
    private Long sobCloseDate;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("是否锁定：true/false")
    private Boolean isLock;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nWaSobName;

    public void initWaSobName() {
        if (StringUtils.isNotBlank(this.waSobName)) {
            return;
        }
        if (null == this.i18nWaSobName || this.i18nWaSobName.isEmpty() || null == this.i18nWaSobName.get("default")) {
            return;
        }
        this.setWaSobName(this.i18nWaSobName.get("default"));
    }

    public void initI18nWaSobName() {
        if (null != this.i18nWaSobName && !this.i18nWaSobName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.waSobName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.waSobName);
        this.setI18nWaSobName(i18nName);
    }
}
