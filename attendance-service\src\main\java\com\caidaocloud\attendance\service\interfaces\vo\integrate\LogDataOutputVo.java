package com.caidaocloud.attendance.service.interfaces.vo.integrate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LogDataOutputVo implements Serializable {
    private static final long serialVersionUID = 1964479240800583132L;
    @ApiModelProperty("接出日志主键")
    private Integer logDataOutputId;
    @ApiModelProperty("用人单位")
    private Integer belongOrgId;
    @ApiModelProperty("接出接口主键")
    private Integer sysDataOutputId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("日志")
    private String logList;
    @ApiModelProperty("接出数据源")
    private Object sourceResult;
}
