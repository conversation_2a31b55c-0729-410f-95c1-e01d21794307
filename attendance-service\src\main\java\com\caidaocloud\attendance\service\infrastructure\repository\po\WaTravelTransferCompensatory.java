package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "wa_travel_transfer_compensatory")
public class WaTravelTransferCompensatory {
    private Long id;
    private String tenantId;
    private Long empId;
    private Long quotaId;
    private Integer sobId;
    private Integer quotaUnit;
    private Float quotaDay;
    private Integer status;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
}