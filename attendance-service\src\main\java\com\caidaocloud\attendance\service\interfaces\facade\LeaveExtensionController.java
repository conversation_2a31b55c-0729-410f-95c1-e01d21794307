package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.service.ILeaveExtensionService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.*;
import com.caidaocloud.attendance.service.interfaces.vo.leaveExtension.LeaveExtensionVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/attendance/leaveExtension/v1")
public class LeaveExtensionController {

    @Autowired
    private ILeaveExtensionService leaveExtensionService;
    @Autowired
    private ISessionService sessionService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("假期延期申请列表")
    @PostMapping(value = "/list")
    @Security(code = "LeaveExtensionRecordList")
    public Result<AttendancePageResult<LeaveExtensionVo>> getEmpLeaveExtensionList(@RequestBody LeaveExtensionReqDto dto, HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_EXTENSION_APPLY_LIST, "b");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("假期延期申请列表 DataScope = {}", dataScope);
            UserInfo userInfo = getUserInfo();
            PageList<LeaveExtensionDto> pageList = leaveExtensionService.getEmpLeaveExtensionList(dto, userInfo);
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<LeaveExtensionVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(LeaveExtensionVo.class);
            AttendancePageResult<LeaveExtensionVo> pageResult = new AttendancePageResult<LeaveExtensionVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("LeaveExtensionController.getEmpLeaveExtensionList has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("假期延期申请列表门户")
    @PostMapping(value = "/listOfPortal")
    public Result<AttendancePageResult<LeaveExtensionVo>> getEmpLeaveExtensionList(@RequestBody LeaveExtensionReqDto dto) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            UserInfo userInfo = getUserInfo();
            dto.setEmpId(userInfo.getStaffId());
            PageList<LeaveExtensionDto> pageList = leaveExtensionService.getEmpLeaveExtensionList(dto, userInfo);
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<LeaveExtensionVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(LeaveExtensionVo.class);
            AttendancePageResult<LeaveExtensionVo> pageResult = new AttendancePageResult<LeaveExtensionVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("LeaveExtensionController.getEmpLeaveExtensionList has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation(value = "假期延期申请")
    @PostMapping("/apply")
    public Result<String> applyLeaveExtension(@RequestBody LeaveExtensionApplyDto dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return ResponseWrap.wrapResult(AttendanceCodes.EXTENSION_QUOTA_EMPTY, null);
        }
        try {
            return leaveExtensionService.applyLeaveExtension(dto, getUserInfo());
        } catch (Exception e) {
            log.error("LeaveExtensionController.applyLeaveExtension exception:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_LEAVE_EXTENSION_FAILED, null);
        }
    }

    @ApiOperation(value = "假期延期申请撤销")
    @PostMapping(value = "/revoke")
    public Result<Boolean> revokeLeaveExtension(@RequestBody LeaveExtensionRevokeDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.EXIST_NUMBER_VALUE_EXCEEDS_UPPERLIMIT, Boolean.FALSE);
        }
        try {
            return leaveExtensionService.revokeLeaveExtension(dto, getUserInfo());
        } catch (Exception e) {
            log.error("LeaveExtensionController.revokeLeaveExtension exception:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "假期延期假期类型")
    @GetMapping(value = "/quotaType")
    public Result<ItemsResult<KeyValue>> getLeaveExtensionQuotaType() {
        return ResponseWrap.wrapResult(new ItemsResult<>(leaveExtensionService.getLeaveExtensionQuotaType()));
    }

    @ApiOperation(value = "假期延期配额")
    @PostMapping(value = "/quota")
    public Result<List<LeaveExtensionQuotaDto>> getLeaveExtensionQuotaList(@RequestBody LeaveExtensionQuota dto) {
        UserInfo userInfo = getUserInfo();
        if (null == dto.getEmpId()) {
            dto.setEmpId(userInfo.getStaffId());
        }
        try {
            return Result.ok(leaveExtensionService.getLeaveExtensionQuotaList(dto, userInfo));
        } catch (Exception e) {
            log.error("LeaveExtensionController.getLeaveExtensionQuotaList has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_EXTENSION_QUOTA_FAILED, new ArrayList<>());
        }
    }

    @ApiOperation(value = "假期延期假期类型下拉")
    @GetMapping(value = "/quotaTypeWorkflow")
    public Result<List<KeyValue>> getLeaveExtensionQuotaTypeWorkflow() {
        return ResponseWrap.wrapResult(leaveExtensionService.getLeaveExtensionQuotaType());
    }
}
