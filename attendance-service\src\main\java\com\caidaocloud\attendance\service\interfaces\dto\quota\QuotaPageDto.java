package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.FilterBean;
import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import lombok.Data;

import java.util.List;

@Data
public class QuotaPageDto extends ExportBasePage {

    private Integer year;

    private List<Integer> years;

    private List<FilterBean> filterList;

    private Integer[] quotaSettingId;

    private String lang;

    public String getLang() {
        return lang != null ? lang : SessionHolder.getLang();
    }

    public void setLang(String lang) {
        this.lang = lang;
    }
}
