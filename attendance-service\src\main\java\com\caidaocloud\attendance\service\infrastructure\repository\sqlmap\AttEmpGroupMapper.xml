<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.AttEmpGroupMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpGroupPo">
        <id column="emp_group_id" jdbcType="INTEGER" property="empGroupId"/>
        <result column="wa_group_id" jdbcType="INTEGER" property="waGroupId"/>
        <result column="empid" jdbcType="BIGINT" property="empId"/>
        <result column="start_time" jdbcType="INTEGER" property="startTime"/>
        <result column="end_time" jdbcType="INTEGER" property="endTime"/>
        <result column="crttime" jdbcType="BIGINT" property="createTime"/>
        <result column="crtuser" jdbcType="BIGINT" property="createUser"/>
        <result column="updtime" jdbcType="INTEGER" property="updateTime"/>
        <result column="upduser" jdbcType="INTEGER" property="updateUser"/>
        <result column="workno" jdbcType="VARCHAR" property="workNo"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="full_path" jdbcType="VARCHAR" property="fullPath"/>
        <result column="shortname" jdbcType="VARCHAR" property="orgName"/>
        <result column="wa_group_name" jdbcType="VARCHAR" property="waGroupName"/>

        <result column="stats" jdbcType="INTEGER" property="empStatus"/>
        <result column="employ_type" jdbcType="BIGINT" property="empStyle"/>
        <result column="hire_date" jdbcType="BIGINT" property="hireDate"/>
        <result column="termination_date" jdbcType="BIGINT" property="terminationDate"/>
        <result column="i18n_wa_group_name" jdbcType="VARCHAR" property="i18nWaGroupName"/>
    </resultMap>

    <sql id="Base_Column_List">
       weg.emp_group_id,weg.wa_group_id,weg.empid,weg.start_time,weg.end_time,
       weg.crttime,weg.crtuser,weg.updtime,weg.upduser, ei.workno,ei.emp_name,
       sco.full_path,sco.shortname,wg.wa_group_name,ei.stats,ei.employ_type,ei.hire_date,ei.termination_date
    </sql>

    <select id="getWaEmpGroupList" resultMap="BaseResultMap">
        SELECT * FROM(
        SELECT
        weg.emp_group_id,weg.wa_group_id,weg.empid,weg.start_time,weg.end_time,
        weg.crttime,weg.crtuser,weg.updtime,weg.upduser, ei.workno,ei.emp_name,
        sco.shortname,wg.wa_group_name,wg.i18n_wa_group_name,ei.stats,ei.employ_type,ei.hire_date,ei.termination_date,
        sco.orgid AS "orgid",
        case
            when sei.workno is not null and sei.workno!=''
                then concat(sei.workno, '(', sei.emp_name, ')')
            when sui.empname is not null and sui.empname != ''
                then concat(sui.account, '(', sui.empname, ')')
            else ''
            end AS "operator",
          case
            when sco.full_path is not null and sco.full_path != ''
                then concat_ws('/', sco.full_path, sco.shortname)
            else sco.shortname
            end as "full_path"
        FROM
        wa_emp_group weg
        JOIN wa_group wg ON weg.wa_group_id = wg.wa_group_id
        JOIN sys_emp_info ei ON weg.empid = ei.empid AND ei.deleted = 0
        LEFT JOIN sys_corp_org sco ON sco.orgid = ei.orgid
        LEFT JOIN sys_user_info sui ON sui.userid = weg.upduser
        LEFT JOIN sys_emp_info sei ON sei.empid=sui.empid
        <where>
            AND ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
            <if test="keywords != null and keywords != ''">
                and (ei.emp_name like concat('%', #{keywords}, '%') or ei.workno like concat('%', #{keywords}, '%'))
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective'">
                and weg.start_time <![CDATA[>]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'InEffect'">
                and #{nowTime} <![CDATA[>=]]> weg.start_time and #{nowTime} <![CDATA[<=]]> weg.end_time
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'Expired'">
                and weg.end_time <![CDATA[<]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective+InEffect'">
                and weg.end_time <![CDATA[>=]]> #{nowTime}
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'NotEffective+Expired'">
                and (weg.start_time <![CDATA[>]]> #{nowTime} or weg.end_time <![CDATA[<]]> #{nowTime})
            </if>
            <if test="effectiveStatus != null and effectiveStatus == 'InEffect+Expired'">
                and weg.start_time <![CDATA[<=]]> #{nowTime}
            </if>
        </where>
        order by updtime desc
        ) as t
        <where>
            ${filter}
        </where>
    </select>

    <select id="queryWaEmpGroupByPeriod" resultMap="BaseResultMap">
        select * from wa_emp_group weg JOIN sys_emp_info ei on weg.empid = ei.empid and ei.deleted = 0
        <where>
            AND ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            <if test="empId!=null">
                AND weg.empid = #{empId}
            </if>
            <if test="startTime!=null and endTime!=null">
                AND ((#{startTime} between start_time and end_time) or (#{endTime} between start_time and end_time)
                or (start_time <![CDATA[>=]]> #{startTime} AND end_time <![CDATA[<=]]> #{endTime}))
            </if>
            <if test="empGroupId != null">
                AND emp_group_id <![CDATA[<>]]> #{empGroupId}
            </if>
            <if test="waGroupId != null">
                AND weg.wa_group_id=#{waGroupId}
            </if>
        </where>
    </select>

    <select id="queryEmpGroupByPeriod" resultMap="BaseResultMap">
        select * from wa_emp_group weg
        join sys_emp_info ei on weg.empid = ei.empid and ei.deleted = 0
        <where>
            and ei.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            <if test="empId != null">
                and weg.empid = #{empId}
            </if>
            <if test="empIds!=null and empIds.size>0">
                AND ei.empid in <foreach collection="empIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="currentTime != null">
                and #{currentTime} between weg.start_time and weg.end_time
            </if>
        </where>
    </select>

    <select id="queryEmpGroupByPeriodAndEmpIds" resultMap="BaseResultMap">
        select wg.wa_group_name,
               wg.i18n_wa_group_name,
               weg.empid,
               weg.start_time,
               weg.end_time
        from wa_group wg
        join wa_emp_group weg on wg.wa_group_id=weg.wa_group_id
        where empid=any(${empIds}) AND start_time <![CDATA[<=]]> #{endDate} AND end_time <![CDATA[>=]]> #{startDate}
          and belong_orgid=#{tenantId}
        order by start_time
    </select>

    <update id="updateGroupExpCondition">
        update wa_group set group_exp_condition = #{groupExpCondition}
        where wa_group_id = #{waGroupId}
    </update>
</mapper>