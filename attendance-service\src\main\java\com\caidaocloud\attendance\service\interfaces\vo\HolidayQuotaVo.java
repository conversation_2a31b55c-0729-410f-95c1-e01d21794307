package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class HolidayQuotaVo {
    @ApiModelProperty("本年调整")
    private BigDecimal adjustDay;

    @ApiModelProperty("调整已失效")
    private BigDecimal adjustInvalid;

    @ApiModelProperty("本年调整已使用")
    private BigDecimal adjustUsed;

    @ApiModelProperty("本年扣减")
    private BigDecimal deductionDay;

    @ApiModelProperty("余额类型名称")
    private String holidayName;

    @ApiModelProperty("当前剩余")
    private BigDecimal leftQuotaDay;

    @ApiModelProperty("当前额度")
    private BigDecimal nowQuotaDay;

    @ApiModelProperty("本年额度")
    private BigDecimal quotaDay;

    @ApiModelProperty("当前冻结配额")
    private BigDecimal quotaFreeze;

    @ApiModelProperty("当前配额已失效")
    private BigDecimal quotaInvalid;

    @ApiModelProperty("当前配额已使用")
    private BigDecimal quotaUsed;

    @ApiModelProperty("上年结转")
    private BigDecimal remainDay;

    @ApiModelProperty("上年留存失效")
    private BigDecimal remainInvalid;

    @ApiModelProperty("上年留存已使用")
    private BigDecimal remainUsed;

    @ApiModelProperty("时间单位")
    private String timeUnit;

    @ApiModelProperty("当前剩余")
    private BigDecimal nowRemain;

    @ApiModelProperty("本年剩余")
    private BigDecimal curRemain;
}