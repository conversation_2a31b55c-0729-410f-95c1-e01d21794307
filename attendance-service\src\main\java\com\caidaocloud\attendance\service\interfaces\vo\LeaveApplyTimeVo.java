package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 休假申请VO
 *
 * <AUTHOR>
 */
@Data
public class LeaveApplyTimeVo {
    @ApiModelProperty("申请时长单位")
    private Integer timeUnit;
    @ApiModelProperty("申请时长单位")
    private String timeUnitTxt;
    @ApiModelProperty("原始申请时长, 单位为天时，返回天数，为小时时，返回分钟数")
    private Double originalDuration;
    @ApiModelProperty("申请时长 单位为天时，返回天数，为小时时，返回小时数")
    private Double durationValue;
    @ApiModelProperty("申请时长文本")
    private String duration;
    @ApiModelProperty("幂等校验key")
    private String secretKey;
}
