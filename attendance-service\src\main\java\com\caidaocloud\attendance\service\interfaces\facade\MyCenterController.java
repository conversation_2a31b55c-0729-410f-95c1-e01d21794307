package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.exception.MobileException;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IEmpCompensatoryCaseApplyService;
import com.caidaocloud.attendance.service.application.service.IRegisterRecordService;
import com.caidaocloud.attendance.service.application.service.IWorkCalendarService;
import com.caidaocloud.attendance.service.application.service.user.MyCenterService;
import com.caidaocloud.attendance.service.domain.entity.EmpTransitAppLyDo;
import com.caidaocloud.attendance.service.domain.entity.WaAnalyzeStatisticsReportDo;
import com.caidaocloud.attendance.service.domain.entity.WaTravelTypeDo;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseItemDto;
import com.caidaocloud.attendance.service.interfaces.dto.compensatory.CompensatoryCaseReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensotaryDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ChangeShiftReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.*;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.attendance.service.interfaces.vo.*;
import com.caidaocloud.attendance.service.interfaces.vo.leave.EmpLeaveRevokeVo;
import com.caidaocloud.attendance.service.interfaces.vo.register.WaRegisterRecordOfPortalVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.ChangeShiftVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.ShiftInfoDto;
import com.caidaocloud.attendance.service.interfaces.vo.user.*;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/user/v1")
@Api(value = "/api/attendance/user/v1", description = "用户个人门口接口", tags = "v1.1")
public class MyCenterController {
    @Resource
    private MyCenterService myCenterService;
    @Resource
    private IRegisterRecordService registerRecordService;
    @Autowired
    private IEmpCompensatoryCaseApplyService empCompensatoryCaseService;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private IWorkCalendarService workCalendarService;

    @ApiOperation("获取工作日历申请方式")
    @GetMapping("/getApplyList")
    public Result<MyApplyTypeVo> getApplyList() {
        WaClockPlanDto planDto = myCenterService.getWaClockPlanDto();
        List<WaTravelTypeDo> list = myCenterService.getTravelTypeList();
        MyApplyTypeVo typeVo = MyApplyTypeVo.builder().build();
        if (planDto != null && BooleanUtils.isTrue(planDto.getIsSupplement())) {
            typeVo.setApplySupplement(1);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            typeVo.setApplyTravel(1);
        }
        return Result.ok(typeVo);
    }

    @ApiOperation("我的工作日历")
    @GetMapping(value = "/myShopCalendar")
    public Result<List<MyCalendarDateVo>> getMyShopCalendar(@RequestParam("searchMonth") Integer searchMonth) {
        List<MyCalendarDateDto> list = myCenterService.getMyShopCalendar(searchMonth);
        List<MyCalendarDateVo> myCalendars = ObjectConverter.convertList(list, MyCalendarDateVo.class);
        return Result.ok(myCalendars);
    }

    @ApiOperation("我的考勤信息")
    @GetMapping(value = "/myAttendance")
    public Result<MyWorkEventVo> getMyAttendance(@RequestParam("day") Integer day) {
        MyWorkEventVo vo = new MyWorkEventVo();
        Long daytime = DateUtil.convertStringToDateTime(day.toString(), "yyyyMMdd", true);
        WaParseGroup parseGroup = myCenterService.calcClockType(vo, daytime, null, null);
        MyWorkEventDto myWorkEventDto = myCenterService.getMyAttendance(day, vo.getClockType(), parseGroup, null, null);
        vo.setRecords(ObjectConverter.convertList(myWorkEventDto.getRecords(), MyClockInfoVo.class));
        vo.setLts(ObjectConverter.convertList(myWorkEventDto.getLts(), MyLeaveTimeVo.class));
        vo.setOts(ObjectConverter.convertList(myWorkEventDto.getOts(), MyOverTimeVo.class));
        vo.setShifts(ObjectConverter.convertList(myWorkEventDto.getShifts(), MyWorkDateShiftVo.class));
        vo.setBdkRecords(ObjectConverter.convertList(myWorkEventDto.getBdkRecords(), MyBdkClockInfoVo.class));
        vo.setTts(ObjectConverter.convertList(myWorkEventDto.getTts(), MyTravelInfoVo.class));
        vo.setLateTime(myWorkEventDto.getLateTime());
        vo.setEarlyTime(myWorkEventDto.getEarlyTime());
        vo.setKgWorkTime(myWorkEventDto.getKgWorkTime());
        return Result.ok(vo);
    }

    @ApiOperation("我申请补卡")
    @PostMapping(value = "/punchIn")
    public Result saveBdkRegister(@RequestBody SaveBdkDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getRegDateTimes()) && dto.getRegDateTimes().stream().anyMatch(r -> r > DateUtil.getCurrentTime(true))) {
            return ResponseWrap.wrapResult(AttendanceCodes.PUNCH_IN_DATE_GE_CURRENT, null);
        }
        WaClockPlanDto planDto = myCenterService.getWaClockPlanDto();
        if (null == planDto) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_CHECK_IN_PLAN_SET_UP, null);
        }
        if (null != planDto.getEnclosureRequired() && planDto.getEnclosureRequired() && StringUtil.isEmpty(dto.getFile())) {
            return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_FILE, null);
        }
        if (BooleanUtils.isFalse(planDto.getIsSupplement())) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_PERMISSION_CARD_REPLACEMENT, null);
        }
        if (planDto.getReasonMust() != null && planDto.getReasonMust() && StringUtil.isBlank(dto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.PUNCH_IN_REASON_MUST, null);
        }
        if (null != planDto.getReasonWordNum() && dto.getReason().length() < planDto.getReasonWordNum()) {
            return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.BDK_REASON_LESS_LIMIT, null).getMsg(), planDto.getReasonWordNum()));
        }
        if (CollectionUtils.isNotEmpty(dto.getRegDateTimes()) && planDto.getSupplementNumber() != null && dto.getRegDateTimes().size() > planDto.getSupplementNumber()) {
            return ResponseWrap.wrapResult(AttendanceCodes.NUMBER_EXCEEDED_UPPER_LIMIT, null);
        }
        try {
            return ResponseWrap.wrapResult(myCenterService.saveBdkRegister(dto));
        } catch (Exception e) {
            log.error("saveBdkRegister exception：{}", e.getMessage(), e);
            if (e instanceof MobileException  || e instanceof CDException) {
                return Result.fail(e.getMessage());
            } else {
                throw new RuntimeException(e);
            }
        }
    }

    @ApiOperation("我的加班记录列表")
    @PostMapping("/myWorkOvertimeList")
    public Result<PageResult<List<MyWorkOvertimeVo>>> myWorkOvertimeList(@RequestBody MyWorkOvertimeDto myWorkOvertimeDto) {
        PageResult pageResult = myCenterService.myWorkOvertimeList(myWorkOvertimeDto);
        if (null != pageResult && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<MyWorkOvertimeVo> voList = ObjectConverter.convertList(pageResult.getItems(), MyWorkOvertimeVo.class);
            pageResult.setItems(voList);
        }
        return Result.ok(pageResult);
    }

    @ApiOperation("门户我的加班记录列表")
    @PostMapping("/myWorkOvertimeListOfPortal")
    public Result<PageResult<MyWorkOvertimeVo>> myWorkOvertimeListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.myWorkOvertimeListOfPortal(queryPageBean));
    }

    @ApiOperation("根据加班ID获取wf信息对应key")
    @GetMapping("/ot/wfKey")
    public Result<WfKeyVo> getWfKeyByOtId(@RequestParam("id") String id) {
        WfKeyVo wfKeyVo = new WfKeyVo();
        wfKeyVo.setBusinessKey(String.format("%s_%s", id, BusinessCodeEnum.OVERTIME.getCode()));
        return Result.ok(wfKeyVo);
    }

    @ApiOperation("获取休假ID获取wf信息对应key")
    @GetMapping("/leave/wfKey")
    public Result<WfKeyVo> getWfKeyByLeaveId(@RequestParam("id") String id) {
        WfKeyVo wfKeyVo = new WfKeyVo();
        wfKeyVo.setBusinessKey(String.format("%s_%s", id, BusinessCodeEnum.LEAVE.getCode()));
        return Result.ok(wfKeyVo);
    }

    @ApiOperation("查询打卡记录")
    @PostMapping(value = "/registerList")
    public Result getRegisterRecordList(@RequestBody RegisterRecordRequestDto requestDto) {
        UserInfo userInfo = sessionService.getUserInfo();
        return Result.ok(registerRecordService.getRegisterRecordListByEmpId(requestDto, userInfo));
    }

    @ApiOperation(value = "获取出差记录分页列表", tags = "v1.1")
    @PostMapping("/myTravelList")
    public Result<PageResult<EmpTravelVo>> getTravelPageList(@RequestBody EmpTravelReqDto dto) {
        PageResult<EmpTravelDto> pageResult = myCenterService.getTravelList(dto);
        List<EmpTravelVo> voList = ObjectConverter.convertList(pageResult.getItems(), EmpTravelVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation(value = "门户获取出差记录分页列表", tags = "v1.1")
    @PostMapping("/myTravelListOfPortal")
    public Result<PageResult<EmpTravelVo>> getTravelPageListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getTravelListOfPortal(queryPageBean));
    }

    @ApiImplicitParam(name = "startDate", value = "头部筛选值", required = true, dataType = "Long")
    @ApiOperation("月度考勤汇总")
    @GetMapping(value = "/MyStatisticsMonthReport")
    public Result<MyMonthReportVo> getStatisticsMonthReport(@RequestParam("startDate") Long startDate) {
        WaAnalyzeStatisticsReportDo reportDo = myCenterService.getStatisticsReportByEmpId(2, startDate);
        return Result.ok(ObjectConverter.convert(reportDo, MyMonthReportVo.class));
    }

    @ApiOperation("假期余额")
    @GetMapping(value = "/MyQuotaLeaveList")
    public Result<List<MyLeaveQuotaVo>> MyQuotaLeaveList() {
        UserInfo userInfo = null;
        try {
            userInfo = workCalendarService.checkSession();
        } catch (ServerException e) {
            log.error("getLeaveQuotaList occur error", e);
            return  Result.ok(Lists.newArrayList());
        }
        List<LeaveQuotaDto> list = myCenterService.getLeaveQuotaList(userInfo.getTenantId(), userInfo.getStaffId(), null);
        return Result.ok(ObjectConverter.convertList(list, MyLeaveQuotaVo.class));

    }

    @ApiImplicitParam(name = "leaveTypeId", value = "假期类型,当quotaType = 2 时不传,其他必传", required = false)
    @ApiOperation("假期明细")
    @GetMapping(value = "/MyLeaveDetail")
    public Result<MyLeaveDetailVo> MyLeaveDetail(@RequestParam(value = "leaveTypeId", required = false) Integer leaveTypeId,
                                                 @RequestParam(value = "quotaType") Integer quotaType) {
        LeaveDetailDto leaveDetailDto = myCenterService.getLeaveDetail(leaveTypeId, quotaType);
        return Result.ok(ObjectConverter.convert(leaveDetailDto, MyLeaveDetailVo.class));
    }

    @ApiOperation("获取失效调休明细")
    @PostMapping(value = "/invalid/compensatoryDetail")
    public Result<AttendancePageResult<CompensotaryDto>> getInvalidCompensatoryDetail(@RequestBody AttendanceBasePage page) {
        return Result.ok(myCenterService.getInvalidCompensatoryDetail(page));
    }

    @ApiOperation("在途申请")
    @PostMapping(value = "/MyTransitAppLyList")
    public Result<PageResult<MyTransitAppLyVo>> MyTransitAppLyList(@RequestBody TransitAppLyReqDto dto) {
        PageResult<EmpTransitAppLyDo> pageResult = myCenterService.getEmpTransitAppLyList(dto);
        List<MyTransitAppLyVo> voList = ObjectConverter.convertList(pageResult.getItems(), MyTransitAppLyVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation(value = "我的调班记录分页列表")
    @PostMapping("/myChangeShiftList")
    public Result<PageResult<ChangeShiftVo>> myChangeShiftList(@RequestBody ChangeShiftReqDto reqDto) {
        PageResult<ApplyShiftRecordDto> pageResult = myCenterService.getShiftPageList(reqDto);
        List<ChangeShiftVo> voList = ObjectConverter.convertList(pageResult.getItems(), ChangeShiftVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation(value = "门户我的调班记录分页列表")
    @PostMapping("/myChangeShiftListOfportal")
    public Result<PageResult<ChangeShiftVo>> myChangeShiftListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getShiftPageListOfPortal(queryPageBean));
    }

    @ApiOperation("根据日期获取班次信息")
    @GetMapping(value = "/getShiftInfoByYmd")
    public Result<ShiftInfoDto> getShiftInfoByYmd(@RequestParam("ymd") Long date) {
        ShiftInfoDto shiftInfo = myCenterService.getShiftInfo(date);
        return Result.ok(shiftInfo);
    }

    @ApiOperation("申请调班")
    @PostMapping(value = "/applyShift")
    public Result applyShift(@RequestBody ApplyShiftDto dto) {
        return myCenterService.applyShift(dto);
    }

    @ApiOperation("我的销假记录分页列表")
    @PostMapping("/myLeaveCancelList")
    public Result<PageResult<LeaveCancelListVo>> myLeaveCancelList(@RequestBody LeaveCancelReqDto reqDto) {
        PageResult<LeaveCancelListDto> pageResult = myCenterService.getLeaveCancelList(reqDto);
        List<LeaveCancelListVo> voList = ObjectConverter.convertList(pageResult.getItems(), LeaveCancelListVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("获取全部类型分页")
    @PostMapping("/allType")
    public Result<PageResult<WaEmpApplyRecordVo>> getAllType(@RequestBody QueryPageBean allTypeQueryPageDto) {
        return Result.ok(myCenterService.getAllType(allTypeQueryPageDto));
    }

    @ApiOperation("获取补打卡类型分页")
    @PostMapping("/getPageOfRegister")
    public Result<PageResult<WaRegisterRecordOfPortalVo>> getMakeupPuneCard(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getMakeupPuneCard(queryPageBean));
    }

    @ApiOperation("调休转付现申请列表")
    @PostMapping(value = "/getCompensatoryApplyRecordList")
    public Result<AttendancePageResult<CompensatoryCaseVo>> getEmpCompensatoryCaseList(@RequestBody CompensatoryCaseReqDto dto) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            UserInfo userInfo = getUserInfo();
            dto.setEmpId(userInfo.getStaffId());
            PageList<CompensatoryCaseItemDto> pageList = empCompensatoryCaseService.getEmpCompensatoryCaseList(dto, pageBean, userInfo.getTenantId());
            if (CollectionUtils.isEmpty(pageList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<CompensatoryCaseVo> listVos = JSON.parseArray(JSON.toJSONString(pageList)).toJavaList(CompensatoryCaseVo.class);
            AttendancePageResult<CompensatoryCaseVo> pageResult = new AttendancePageResult<CompensatoryCaseVo>(listVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("EmpCompensatoryCaseController.getEmpCompensatoryCaseList has exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("工作日历数据范围")
    @GetMapping("/myCalendarDataRange")
    public Result<Long> myCalendarDataRange() {
        return Result.ok(myCenterService.myCalendarDataRange());
    }

    @ApiOperation("门户我的加班撤销记录列表")
    @PostMapping("/myWorkOvertimeRevokeListOfPortal")
    public Result<PageResult<MyWorkOvertimeVo>> myWorkOvertimeRevokeListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.myWorkOvertimeRevokeListOfPortal(queryPageBean, BusinessCodeEnum.OVERTIME_REVOKE));
    }

    @ApiOperation("门户我的加班废止记录列表")
    @PostMapping("/myWorkOvertimeAbolishListOfPortal")
    public Result<PageResult<MyWorkOvertimeVo>> myWorkOvertimeAbolishListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.myWorkOvertimeRevokeListOfPortal(queryPageBean, BusinessCodeEnum.OVERTIME_ABOLISH));
    }

    @ApiOperation(value = "门户获取出差撤销记录分页列表", tags = "v1.1")
    @PostMapping("/myTravelRevokeListOfPortal")
    public Result<PageResult<EmpTravelVo>> myTravelRevokeListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getTravelRevokeListOfPortal(queryPageBean, BusinessCodeEnum.TRAVEL_REVOKE));
    }

    @ApiOperation(value = "门户获取出差废止记录分页列表", tags = "v1.1")
    @PostMapping("/myTravelAbolishListOfPortal")
    public Result<PageResult<EmpTravelVo>> myTravelAbolishListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getTravelRevokeListOfPortal(queryPageBean, BusinessCodeEnum.TRAVEL_ABOLISH));
    }

    @ApiOperation(value = "门户获取我的调休配额", tags = "v1.1")
    @GetMapping("/myCompensatoryQuota")
    public Result<CompensatoryDataVo> myCompensatoryQuota() {
        try {
            return Result.ok(ObjectConverter.convert(myCenterService.myCompensatoryQuota(), CompensatoryDataVo.class));
        } catch (Exception e) {
            log.error("myCompensatoryQuota error{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
        }
        return ResponseWrap.wrapResult(AttendanceCodes.QUERY_APPLY_COMPENSATORY_TO_CASH_FAILED, null);
    }

    @ApiOperation(value = "门户获取批量出差撤销记录分页列表", tags = "v1.1")
    @PostMapping("/myBatchTravelRevokeListOfPortal")
    public Result<PageResult<EmpTravelVo>> myBatchTravelRevokeListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getBatchTravelRevokeListOfPortal(queryPageBean, BusinessCodeEnum.BATCH_TRAVEL_REVOKE));
    }

    @ApiOperation(value = "门户获取批量出差废止记录分页列表", tags = "v1.1")
    @PostMapping("/myBatchTravelAbolishListOfPortal")
    public Result<PageResult<EmpTravelVo>> myBatchTravelAbolishListOfPortal(@RequestBody QueryPageBean queryPageBean) {
        return Result.ok(myCenterService.getBatchTravelRevokeListOfPortal(queryPageBean, BusinessCodeEnum.BATCH_TRAVEL_ABOLISH));
    }

    @ApiOperation(value = "门户获取批量休假撤销记录分页列表", tags = "v1.1")
    @PostMapping("/myBatchLeaveRevokeListOfPortal")
    public Result<PageResult<EmpLeaveRevokeVo>> myBatchLeaveRevokeListOfPortal(@RequestBody WorkflowRevokeReqDto dto) {
        return Result.ok(myCenterService.getBatchLeaveRevokeListOfPortal(dto, BusinessCodeEnum.BATCH_LEAVE_REVOKE));
    }

    @ApiOperation(value = "门户获取批量休假废止记录分页列表", tags = "v1.1")
    @PostMapping("/myBatchLeaveAbolishListOfPortal")
    public Result<PageResult<EmpLeaveRevokeVo>> myBatchLeaveAbolishListOfPortal(@RequestBody WorkflowRevokeReqDto dto) {
        return Result.ok(myCenterService.getBatchLeaveRevokeListOfPortal(dto, BusinessCodeEnum.BATCH_LEAVE_ABOLISH));
    }
}