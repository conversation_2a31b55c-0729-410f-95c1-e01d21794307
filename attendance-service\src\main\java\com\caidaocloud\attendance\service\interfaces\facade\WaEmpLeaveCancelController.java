package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.LeaveInfoDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.service.application.service.impl.WaEmpLeaveCancelService;
import com.caidaocloud.attendance.service.application.service.impl.WfService;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.vo.WfDetailVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/attendance/leaveCancel/v1")
@Api(value = "/api/attendance/leaveCancel/v1", description = "销假", tags = "v1.0")
public class WaEmpLeaveCancelController {
    @Autowired
    private WaEmpLeaveCancelService waEmpLeaveCancelService;
    @Autowired
    private WfService wfService;

    @ApiOperation("获取销假时长")
    @PostMapping("/getTime")
    public Result getTime(@RequestBody LeaveCancelDto dto) {
        try {
            return waEmpLeaveCancelService.getTime(dto);
        } catch (Exception e) {
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.CAL_LEAVE_CANCEL_TIME_FAILED, null);
        }
    }

    @ApiOperation("销假申请-获取假期明细")
    @GetMapping("/getLeaveInfo")
    public Result<LeaveInfoDto> getLeaveInfo(@RequestParam Long leaveId) {
        return Result.ok(waEmpLeaveCancelService.getLeaveInfo(leaveId));
    }

    @ApiOperation("销假申请")
    @PostMapping("/apply")
    @LogRecordAnnotation(success = "销假了{empName{#empId}}的{{#leaveName}}", category = "销假", menu = "休假管理-休假记录-休假记录")
    public Result apply(@RequestBody LeaveCancelDto dto) throws Exception {
        if (StringUtils.isEmpty(dto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.PLEASE_WRITE_REASON, Boolean.FALSE);
        }
        try {
            return waEmpLeaveCancelService.saveCancelLeave(dto);
        } catch (Exception e) {
            log.error("apply exception {}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation("销假记录分页列表")
    @PostMapping("/list")
    public Result<PageResult<LeaveCancelListVo>> list(@RequestBody LeaveCancelReqDto reqDto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "c.orgid")
                    .replaceAll("empid", "c.empid");
            reqDto.setDataScope(orgDataScope);
        }
        PageResult<LeaveCancelListDto> pageResult = waEmpLeaveCancelService.getPageList(reqDto, null);
        List<LeaveCancelListVo> voList = ObjectConverter.convertList(pageResult.getItems(), LeaveCancelListVo.class);
        return ResponseWrap.wrapResult(new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销员工销假申请")
    public Result<Boolean> revoke(@RequestBody RevokeLeaveCancelDto dto) {
        try {
            return waEmpLeaveCancelService.revokeCancelLeave(dto);
        } catch (Exception e) {
            log.error("revoke failed {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("查看明细")
    @GetMapping(value = "/getWfFuncDetail")
    public Result getWfFuncDetail(@RequestParam("businessKey") String businessKey,
                                  @RequestParam(value = "nodeId", required = false) String nodeId,
                                  @RequestParam(value = "funcType", required = false) Integer funcType) throws Exception {
        // 销假单明细
        WfDetailDto detailDto = wfService.getWfFuncDetail(businessKey, nodeId, funcType);
        WfDetailVo vo = ObjectConverter.convert(detailDto, WfDetailVo.class);
        vo.setData(detailDto.getItems());
       /* // 休假单明细
        WfDetailDto leaveApplyInfo = detailDto.getLeaveApplyInfo();
        WfDetailVo leaveApplyInfoVo = ObjectConverter.convert(leaveApplyInfo, WfDetailVo.class);
        leaveApplyInfoVo.setData(leaveApplyInfo.getItems());
        vo.setLeaveApplyInfo(leaveApplyInfoVo);*/
        return ResponseWrap.wrapResult(vo);
    }

    @ApiOperation("获取销假类型")
    @GetMapping(value = "/getLeaveCancelType")
    public Result<List<KeyValue>> getLeaveCancelType(@RequestParam(value = "leaveId", required = false) Integer leaveId) {
        return waEmpLeaveCancelService.getLeaveCancelType(leaveId);
    }

    @ApiOperation("校验销假类型附件是否不为空")
    @GetMapping(value = "/checkAttachmentRequired")
    public Result<Boolean> checkAttachmentRequired(@RequestParam("leaveId") Integer leaveId, @RequestParam("cancelType") Integer cancelType) {
        return Result.ok(waEmpLeaveCancelService.checkAttachmentRequired(leaveId, cancelType));
    }
}
