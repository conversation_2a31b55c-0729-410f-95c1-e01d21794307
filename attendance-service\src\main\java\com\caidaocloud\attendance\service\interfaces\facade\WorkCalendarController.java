package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaWorktimeGroup;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IWorkCalendarService;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.util.KeyToFilterUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.EmpShiftInfoDto.EmpShiftInfoDto;
import com.caidaocloud.attendance.service.interfaces.vo.EmpShiftVerifyVo;
import com.caidaocloud.attendance.service.interfaces.vo.WaWorktimeGridVo;
import com.caidaocloud.attendance.service.interfaces.vo.WaWorktimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.EmpShiftVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/workcalendar/v1")
public class WorkCalendarController {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private IWorkCalendarService workCalendarService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private AsyncExecService asyncService;
    @Autowired
    private CacheService cacheService;

    private static final String GEN_WORKCALENDAR_PROCESS = "GEN_WORKCALENDAR_PROCESS_";

    private static final String GEN_WORKCALENDAR_MSG_PROCESS = "GEN_WORKCALENDAR_MSG_PROCESS_";

    private static final String SYN_EMPSHIFT_PROCESS = "SYN_EMPSHIFT_PROCESS_";

    private static final String SYN_EMPSHIFT_MSG_PROCESS = "SYN_EMPSHIFT_MSG_PROCESS_";

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "查询工作日历分页列表")
    @PostMapping(value = "/list")
    public Result<AttendancePageResult<WaWorktimeGridVo>> getWorkCalendarList(@RequestBody WorkCalendarReqDto requestDto) {
        PageBean pageBean = PageUtil.getPageBean(requestDto);
        List<FilterBean> list = KeyToFilterUtil.KeyToFilterList(requestDto.getKeywords(), "work_calendar_name", requestDto.getFilterList());
        pageBean.setFilterList(list);
        PageList<Map> pageList = (PageList<Map>) waConfigService.getWorkCalendarList(pageBean);
        pageList.forEach(item -> {
            if (null != item.get("i18n_group_name")) {
                String i18n = LangParseUtil.getI18nLanguage(item.get("i18n_group_name").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    item.put("group_name", i18n);
                }
            }
            if (null != item.get("i18n_round_name")) {
                String i18n = LangParseUtil.getI18nLanguage(item.get("i18n_round_name").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    item.put("round_name", i18n);
                }
            }
            if (null != item.get("i18n_work_calendar_name")) {
                String i18n = LangParseUtil.getI18nLanguage(item.get("i18n_work_calendar_name").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    item.put("work_calendar_name", i18n);
                }
            }
        });
        List<WaWorktimeGridVo> items = JSON.parseArray(JSON.toJSONString(pageList), WaWorktimeGridVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
    }

    @ApiOperation(value = "取得工作日历具体信息")
    @RequestMapping(value = "/detail")
    public Result<WaWorktimeVo> getWorkCalendar(@RequestParam("id") Integer id, @RequestParam(value = "queryGroupExp", required = false, defaultValue = "false") Boolean queryGroupExp) throws Exception {
        WaWorktimeDto workTimeDto = workCalendarService.getWorkCalendarById(id, queryGroupExp);
        WaWorktimeVo workTimeVo = ObjectConverter.convert(workTimeDto, WaWorktimeVo.class);
        return ResponseWrap.wrapResult(workTimeVo);
    }

    @ApiOperation(value = "新增工作日历")
    @PostMapping(value = "/save")
    public Result<Boolean> saveWorkCalendar(@RequestBody WaWorktimeDto waWorktimeDto) throws Exception {
        waWorktimeDto.initWorkCalendarName();
        waWorktimeDto.initI18nWorkCalendarName();
        if (StringUtils.isNotBlank(waWorktimeDto.getWorkCalendarName()) && waWorktimeDto.getWorkCalendarName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        UserInfo userInfo = getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_WORKCALENDAR_LOCK_{0}_{1}_{2}_{3}", userInfo.getTenantId(), waWorktimeDto.getWorkCalendarName(), waWorktimeDto.getStartdate(), waWorktimeDto.getEnddate());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            String msg = workCalendarService.saveOrUpdateWorkCalendar(waWorktimeDto, userInfo);
            if (!"".equals(msg)) {
                return Result.fail(msg);
            }
            return Result.ok(true);
        } finally {
            //执行结束，释放key值
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "修改工作日历")
    @PostMapping(value = "/update")
    public Result<Boolean> updateWorkCalendar(@RequestBody WaWorktimeDto waWorktimeDto) throws Exception {
        waWorktimeDto.initWorkCalendarName();
        waWorktimeDto.initI18nWorkCalendarName();
        if (StringUtils.isNotBlank(waWorktimeDto.getWorkCalendarName()) && waWorktimeDto.getWorkCalendarName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        UserInfo userInfo = getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("UPDATE_WORKCALENDAR_LOCK_{0}_{1}", userInfo.getTenantId(), waWorktimeDto.getWorkCalendarId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            String msg = workCalendarService.saveOrUpdateWorkCalendar(waWorktimeDto, userInfo);
            if (!"".equals(msg)) {
                return Result.fail(msg);
            }
            return Result.ok(true);
        } finally {
            //执行结束，释放key值
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "异步生成工作日历")
    @PostMapping(value = "/asynGenWorkCalendar")
    public Result<Boolean> asynGenWorkCalendar(@RequestBody WaWorktimeDto waWorktimeDto) throws Exception {
        UserInfo userInfo = getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_WORKCALENDAR_LOCK_{0}_{1}_{2}_{3}", userInfo.getTenantId(), waWorktimeDto.getWorkCalendarName(), waWorktimeDto.getStartdate(), waWorktimeDto.getEnddate());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            cacheService.cacheValue(GEN_WORKCALENDAR_PROCESS + waWorktimeDto.getProgress(), "0.5");
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        String msg = workCalendarService.saveOrUpdateWorkCalendar(waWorktimeDto, userInfo);
                        if (!"".equals(msg)) {
                            engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                            engineMessage.setMessage(msg);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.WORK_CALENDAR_GEN_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }
                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(GEN_WORKCALENDAR_PROCESS + waWorktimeDto.getProgress(), "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(GEN_WORKCALENDAR_MSG_PROCESS + waWorktimeDto.getProgress(), JsonSerializeHelper.serialize(engineMessage), 300);
                    }
                }
            });
            return Result.ok(true);
        } finally {
            //执行结束，释放key值
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("获取日历生成进度")
    @RequestMapping(value = "/getProgress", method = RequestMethod.GET)
    public Result getProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(GEN_WORKCALENDAR_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        Double rate = Double.valueOf(cacheService.getValue(GEN_WORKCALENDAR_PROCESS + progress));
        if (rate == null) {
            rate = 0.5d;
        }
        if (rate >= 1) {
            //执行结果
            String analyzeMsg = cacheService.getValue(GEN_WORKCALENDAR_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(analyzeMsg)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(analyzeMsg, AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            //删除缓存
            cacheService.remove(GEN_WORKCALENDAR_PROCESS + progress);
            cacheService.remove(GEN_WORKCALENDAR_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation(value = "删除工作日历")
    @DeleteMapping(value = "/delete")
    public Result<Boolean> deleteWorkCalendar(@RequestParam("id") Integer id) {
        try {
            if (workCalendarService.checkWorkCalendarIfAssigned(id)) {
                return ResponseWrap.wrapResult(AttendanceCodes.WORK_CALENDAR_ASSIGNED, CommonConstant.FALSE);
            }
            workCalendarService.deleteWorkCalendarById(id);
            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception e) {
            log.error("删除工作日历异常:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, CommonConstant.TRUE);
        }
    }

    @ApiOperation(value = "生成工作日历")
    @PostMapping(value = "/gen")
    public Result<Boolean> genWorkCalendar(@RequestBody WaWorktimeGroupDto worktimeGroupDto) throws Exception {
        UserInfo userInfo = getUserInfo();
        WaWorktimeGroup waWorktimeGroup = ObjectConverter.convert(worktimeGroupDto, WaWorktimeGroup.class);
        waConfigService.genWorkCalendar(waWorktimeGroup, userInfo.getTenantId(), userInfo.getUserId());
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @ApiOperation("校验已选员工是否已适用某日历")
    @PostMapping("/verifySelectedEmployees")
    public Result<ItemsResult<EmpShiftVerifyVo>> verifySelectedEmployees(@RequestBody EmpShiftVerifyReqDto reqDto) {
        List<EmpShiftVerifyDto> list = workCalendarService.verifySelectedEmployees(reqDto);
        return ResponseWrap.wrapResult(ItemsResult.of(ObjectConverter.convertList(list, EmpShiftVerifyVo.class)));
    }

    @ApiOperation(value = "查询日历归属员工列表")
    @PostMapping(value = "/getCalendarEmpList")
    public Result<AttendancePageResult> getEmpShiftListByCalendarId(@RequestBody EmpShiftReqDto reqDto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            reqDto.setDataScope(orgDataScope);
        }
        return ResponseWrap.wrapResult(workCalendarService.getEmpShiftListByCalendarId(reqDto, getUserInfo()));
    }

    @ApiOperation("查看工作日历")
    @GetMapping(value = "/getDetailList")
    public Result getWorkCalendarDetailList(@RequestParam("id") Integer id, @RequestParam("start") String start,
                                            @RequestParam("end") String end) throws Exception {
        return ResponseWrap.wrapResult(workCalendarService.getWorkCalendarDetailList(id, start, end));
    }

    @ApiOperation("查看员工个人工作日历")
    @GetMapping(value = "/getEmpCalendarDetailListByYm")
    public Result getEmpCalendarDetailListByYm(@RequestParam("empId") Long empId, @RequestParam("start") String start,
                                               @RequestParam("end") String end) throws Exception {

        return ResponseWrap.wrapResult(workCalendarService.getEmpCalendarDetailListByYm(getUserInfo().getTenantId(), empId, start, end));
    }

    @ApiOperation(value = "查询非门店员工排班")
    @PostMapping(value = "/getWaShiftList")
    public Result getWaShiftList(@RequestBody WorkCalendarReqDto requestDto) {
        return Result.ok(workCalendarService.getWaShiftList(requestDto));
    }

    /***************************************** 员工日历 *****************************************/

    @ApiOperation("保存员工日历（排班）")
    @PostMapping("/saveEmpShift")
    public Result<Boolean> saveEmpShift(@RequestBody EmpShiftInfoDto dto) {
        try {
            if (dto.getEmpInfo() == null || dto.getEmpInfo().getEmpId() == null) {
                log.error("员工为空");
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_EMPTY, Boolean.FALSE);
            }
            if (dto.getWorkCalendarId() == null) {
                log.error("工作日历为空");
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_CALENDAR_EMPTY, Boolean.FALSE);
            }
            if (dto.getStartTime() == null) {
                log.error("有效期开始时间为空");
                return ResponseWrap.wrapResult(AttendanceCodes.CALENDAR_START_DATE, Boolean.FALSE);
            }
            if (dto.getEndTime() == null) {
                log.error("有效期结束时间为空");
                return ResponseWrap.wrapResult(AttendanceCodes.CALENDAR_END_DATE, Boolean.FALSE);
            }
            return workCalendarService.saveEmpShift(dto);
        } catch (Exception e) {
            log.error("WorkCalendarController.saveEmpShift has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "删除员工日历（排班）")
    @DeleteMapping(value = "/deleteEmpShift")
    public Result<Boolean> deleteEmpShift(@RequestParam("id") Integer id) {
        try {
            workCalendarService.deleteEmpShift(id);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("WorkCalendarController.deleteEmpShift has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "查询员工日历（排班）")
    @GetMapping(value = "/getEmpShift")
    public Result<EmpShiftVo> getEmpShift(@RequestParam("id") Integer id) {
        try {
            EmpShiftDto empShiftDto = workCalendarService.getEmpShift(id);
            if (empShiftDto == null) {
                return ResponseWrap.wrapResult(AttendanceCodes.NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(empShiftDto, EmpShiftVo.class));
        } catch (Exception e) {
            log.error("WorkCalendarController.getEmpShift has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @ApiOperation(value = "工作日历下拉列表")
    @GetMapping(value = "/getCalendarOptions")
    public Result<ItemsResult<KeyValue>> getCalendarOptions() {
        try {
            List<KeyValue> list = workCalendarService.getCalendarOptions();
            if (CollectionUtils.isNotEmpty(list)) {
                return ResponseWrap.wrapResult(ItemsResult.of(list));
            }
            return ResponseWrap.wrapResult(ItemsResult.of(new ArrayList<>()));
        } catch (Exception e) {
            log.error("WorkCalendarController.getCalendarOptions has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SELECT_OPTION_FAILED, null);
        }
    }

    @ApiOperation("员工日历同步更新接口")
    @GetMapping("/synchronize")
    public Result<Boolean> synchronizeEmpShift(@RequestParam("progress") String progress) {
        UserInfo userInfo = getUserInfo();
        //重复生成校验
        String lockKey = MessageFormat.format("SYNEMPSHIFT_LOCK_{0}", userInfo.getBelongOrgId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.ANOTHER_SYNCHRONIZATION_PROCESS_RUNNING, null);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            cacheService.cacheValue(SYN_EMPSHIFT_PROCESS + progress, "0.5");
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        log.info("synchronizeEmpShift params ... " + progress + ",user info = " + JSONUtils.ObjectToJson(userInfo));
                        workCalendarService.synchronizeEmpShift(userInfo.getTenantId(), userInfo.getUserId(), ConvertHelper.longConvert(userInfo.getTenantId()));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.SYNC_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }
                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(SYN_EMPSHIFT_PROCESS + progress, "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(SYN_EMPSHIFT_MSG_PROCESS + progress, JsonSerializeHelper.serialize(engineMessage), 5 * 60);
                    }
                }
            });
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("WorkCalendarController.synchronizationEmpShift has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SYNC_FAILED, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("获取同步日历生成进度")
    @RequestMapping(value = "/getSynEmpShiftProgress", method = RequestMethod.GET)
    public Result getSynEmpShiftProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(SYN_EMPSHIFT_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        Double rate = Double.valueOf(cacheService.getValue(SYN_EMPSHIFT_PROCESS + progress));
        if (rate == null) {
            rate = 0.5d;
        }
        if (rate >= 1) {
            //执行结果
            String analyzeMsg = cacheService.getValue(SYN_EMPSHIFT_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(analyzeMsg)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(analyzeMsg, AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            //删除缓存
            cacheService.remove(SYN_EMPSHIFT_PROCESS + progress);
            cacheService.remove(SYN_EMPSHIFT_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation(value = "批量删除员工日历（排班）")
    @PostMapping(value = "/deleteEmpShifts")
    public Result<Boolean> deleteEmpShifts(@RequestBody ItemsResult<Integer> dto) {
        try {
            workCalendarService.deleteEmpShifts(dto.getItems());
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("WorkCalendarController.deleteEmpShifts has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }
}
