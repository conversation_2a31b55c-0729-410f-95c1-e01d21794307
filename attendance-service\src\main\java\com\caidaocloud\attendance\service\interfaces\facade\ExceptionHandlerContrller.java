package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * requestbody配合valid的校验信息返回
 */
@Slf4j
@ControllerAdvice
public class ExceptionHandlerContrller {
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Result exception(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        List<ObjectError> allErrors = bindingResult.getAllErrors();

        String message = allErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(";"));
        return Result.fail(message);
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result serverException(Exception exception) {
        log.error(String.format("error message=%s", exception.getMessage()), exception);
        if (!(exception instanceof ServerException)) {
            String errorMsg = MessageHandler.getMessage("caidao.exception.error_90014", WebUtil.getRequest());
            return Result.fail(errorMsg);
        }
        return Result.fail(exception.getMessage());
    }
}
