package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.interfaces.dto.QuotaEmpGroupDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeaveQuotaConfigVo {
    @ApiModelProperty("余额规则配置主键")
    private Long configId;
    @ApiModelProperty("发放周期：1 自然年、2 入职年、3 自定义周期")
    private Integer distributionCycle;
    @ApiModelProperty("发放周期开始日")
    private Long disCycleStart;
    @ApiModelProperty("发放周期结束日")
    private Long disCycleEnd;
    @ApiModelProperty("有效期类型：1 限制有效期 、2 不限制有效期")
    private Integer validityPeriodType;
    @ApiModelProperty("有效期")
    private Float validityDuration;
    @ApiModelProperty("有效期单位：1 天、2 月、3 ")
    private Integer validityUnit;
    @ApiModelProperty("调休有效期对应的开始日期计算规则：1 当年1月1号、2 加班开始日期、3 加班开始月")
    private Integer validityStartType;
    @ApiModelProperty("本年额度舍位规则，当发放规则为2时才有效")
    private Integer quotaDistributeRule;
    @ApiModelProperty("本年额度舍位规则，当发放规则为2时才有效，值为：1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5")
    private Integer quotaRoundingRule;
    @ApiModelProperty("当前额度发放规则：1 按天")
    private Integer nowDistributeRule;
    @ApiModelProperty("当前额度舍位规则，值为：1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5")
    private Integer nowRoundingRule;
    @ApiModelProperty("可否预支：0 不可预支 、 1 可预支")
    private Integer ifAdvance;
    @ApiModelProperty("过期规则： 1 付现、2 作废")
    private Integer expirationRule;
    @ApiModelProperty("结转至，要结转的假期类型ID")
    private Long carryOverTo;
    @ApiModelProperty("结转有效期开始日期类型：1 原配额生效日期 、2 原配额失效日期后一天、3 原配额失效日期")
    private Integer carryOverStartType;
    @ApiModelProperty("结转有效期")
    private Float carryOverValidityDuration;
    @ApiModelProperty("结转有效期单位：1 天、2 月、3 ")
    private Integer carryOverValidityUnit;

    @ApiModelProperty("配额规则")
    private List<QuotaEmpGroupDto> quotaGroups;

    @ApiModelProperty("配额折算规则：1 生成时折算、2 到期时折算")
    private Integer convertRule;
    @ApiModelProperty("育儿假生成规则 1 按子女个数生成  2 按子女个数累加生成  3 仅生成一条")
    private Integer childRule;
    @ApiModelProperty("失效日期类型：1 固定有效期 2 当年失效 3 次年失效")
    private Integer invalidType;
    @ApiModelProperty("失效日期")
    private String invalidDate;
    @ApiModelProperty("失效日期是否延长至当月月底")
    private Boolean validityExtension;

    @ApiModelProperty("额度说明")
    private String description;

    @ApiModelProperty("是否包含产前假：0不包含1包含，仅产假有效")
    private Integer containPrenatalLeave;
}