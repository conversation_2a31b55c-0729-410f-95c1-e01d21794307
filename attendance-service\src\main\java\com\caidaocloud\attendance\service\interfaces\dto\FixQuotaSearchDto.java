package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FixQuotaSearchDto extends ExportBasePage {
    @ApiModelProperty("假期类型def id")
    private List<Long> leaveTypeDefId;
    @ApiModelProperty("婚姻状态ID")
    private List<Long> marriage;
    @ApiModelProperty("工作地ID")
    private List<Long> workplace;
}
