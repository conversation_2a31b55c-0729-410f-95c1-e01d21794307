package com.caidaocloud.attendance.service.interfaces.dto.compensatory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpCompensatoryCaseDto {
    @ApiModelProperty("员工")
    private Long empId;
    @ApiModelProperty("付现失效额度")
    private Float applyDuration;
    @ApiModelProperty("付现生效中有效额度")
    private Float validDuration;
    @ApiModelProperty("备注")
    private String note;
    @ApiModelProperty("单位：1天，2小时")
    private Integer timeUnit;
}
