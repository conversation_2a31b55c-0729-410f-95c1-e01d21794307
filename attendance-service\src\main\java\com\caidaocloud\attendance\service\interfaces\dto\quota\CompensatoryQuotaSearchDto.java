package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CompensatoryQuotaSearchDto extends ExportBasePage {
    @ApiModelProperty("加班日期")
    private Long overtimeDate;
    @ApiModelProperty("日期类型 1 工作日加班、2 休息日加班、3 法定假日加班")
    private List<Integer> overtimeType;
    @ApiModelProperty("状态 0失效 、1 生效 、2 撤销")
    private Integer status;
    @ApiModelProperty("数据来源：AUTO系统生成，MANUAL新增/导入")
    private List<String> dataSource;
}
