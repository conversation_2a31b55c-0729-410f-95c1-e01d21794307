package com.caidaocloud.attendance.service.interfaces.vo.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpGroupVo {

    @ApiModelProperty("id")
    private Integer empGroupId;

    @ApiModelProperty("方案名称")
    private String waGroupName;

    @ApiModelProperty("所属公司")
    private String corpName;

    @ApiModelProperty("所属部门")
    private String orgName;

    @ApiModelProperty("组织全路径")
    private String fullPath;

    @ApiModelProperty("中文名")
    private String empName;

    @ApiModelProperty("英文名")
    private String engName;

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("生效时间")
    private Long startTime;

    @ApiModelProperty("失效时间")
    private Long endTime;

    @ApiModelProperty("任职岗位")
    private String postName;

    @ApiModelProperty("员工类型")
    private String empType;

    @ApiModelProperty("员工状态")
    private String empStatus;

    @ApiModelProperty("工作地")
    private String workCity;

    @ApiModelProperty("入职日期")
    private String hireDate;
}
