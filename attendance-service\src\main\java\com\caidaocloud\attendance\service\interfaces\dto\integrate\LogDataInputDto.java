package com.caidaocloud.attendance.service.interfaces.dto.integrate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LogDataInputDto implements Serializable {
    private static final long serialVersionUID = -6815987489850952550L;
    @ApiModelProperty("接入日志主键")
    private Integer logDataInputId;
    @ApiModelProperty("用人单位")
    private Integer belongOrgId;
    @ApiModelProperty("接入接口主键")
    private Integer sysDataInputId;
    @ApiModelProperty("接口名称")
    private String name;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("日志")
    private String logList;
    @ApiModelProperty("取得接入源数据")
    private Object sourceResult;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("校验结果")
    private Object checkResult;
}
