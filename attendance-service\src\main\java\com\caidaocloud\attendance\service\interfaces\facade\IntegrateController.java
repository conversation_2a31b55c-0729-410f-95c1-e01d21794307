package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.BaseController;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.integrate.mybatis.mapper.SysDataInputMapper;
import com.caidao1.integrate.mybatis.model.LogDataInput;
import com.caidao1.integrate.mybatis.model.LogDataOutput;
import com.caidao1.integrate.mybatis.model.SysDataInput;
import com.caidao1.integrate.mybatis.model.SysDataOutput;
import com.caidao1.integrate.service.IntegratedService;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.cron.XxlJobService;
import com.caidaocloud.attendance.service.infrastructure.util.KeyToFilterUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.integrate.*;
import com.caidaocloud.attendance.service.interfaces.vo.integrate.LogDataInputVo;
import com.caidaocloud.attendance.service.interfaces.vo.integrate.LogDataOutputVo;
import com.caidaocloud.attendance.service.interfaces.vo.integrate.SysDataInputVo;
import com.caidaocloud.attendance.service.interfaces.vo.integrate.SysDataOutputVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
@RequestMapping("/api/attendance/integrate/v1")
@Api(value = "/api/attendance/integrate/v1", description = "接入接出")
public class IntegrateController extends BaseController {

    @Resource
    private IntegratedService integratedService;
    @Resource
    private SysDataInputMapper dataInputMapper;
    @Autowired
    private ISessionService sessionService;
    @Resource
    private XxlJobService xxlJobService;

    @ApiOperation(value = "取得输入接口列表")
    @PostMapping(value = "/getDataInputList")
    @ResponseBody
    public Result<PageResult<Map>> getDataInputList(@RequestBody AttendanceBasePage basePage) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<FilterBean> beans = KeyToFilterUtil.KeyToFilterList(basePage.getKeywords(), "note", basePage.getFilterList());
        pageBean.setFilterList(beans);
        PageList<Map> pageList = (PageList<Map>) integratedService.getDataInputList(pageBean);
        pageList.forEach(item -> {
            if (null != item.get("i18n_note")) {
                String i18n = LangParseUtil.getI18nLanguage(item.get("i18n_note").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    item.put("note", i18n);
                }
            }
        });
        return ResponseWrap.wrapResult(new PageResult<>(pageList, basePage.getPageNo(), basePage.getPageSize(), basePage.getTotal()));
    }

    @ApiOperation(value = "取得输入接口具体信息")
    @GetMapping(value = "/getDataInput")
    @ResponseBody
    public Result<SysDataInputVo> getDataInput(@RequestParam("id") Integer id) {
        SysDataInput input = integratedService.getDataInput(id);
        SysDataInputVo sysDataInputVo = ObjectConverter.convert(input, SysDataInputVo.class);
        if (StringUtils.isNotBlank(input.getI18nNote())) {
            sysDataInputVo.setI18nNote(FastjsonUtil.toObject(input.getI18nNote(), Map.class));
        } else if (StringUtils.isNotBlank(input.getNote())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", input.getNote());
            sysDataInputVo.setI18nNote(i18nName);
        }
        PGobject sourceConfig = (PGobject) sysDataInputVo.getSourceConfig();
        if (sourceConfig != null) {
            sysDataInputVo.setSourceConfig(sourceConfig.getValue());
        }
        return ResponseWrap.wrapResult(sysDataInputVo);
    }

    @ApiOperation(value = "保存输入接口")
    @PostMapping(value = "/saveDataInput")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-打卡接口")
    @ResponseBody
    public Result<Boolean> saveDataInput(@RequestBody SysDataInputDto dto) {
        dto.initNote();
        dto.initI18nNote();
        try {
            if (dto.getSourceConfig() != null && StringUtils.isNotBlank(dto.getSourceConfig().toString())) {
                try {
                    PGobject pgobject = new PGobject();
                    pgobject.setType("jsonb");
                    pgobject.setValue(new String(Base64.decodeBase64(dto.getSourceConfig().toString())));
                    dto.setSourceConfig(pgobject);
                    dto.setSourceExp(new String(Base64.decodeBase64(dto.getSourceExp())));
                    dto.setTargetCol(new String(Base64.decodeBase64(dto.getTargetCol())));
                    dto.setTargetExp(new String(Base64.decodeBase64(dto.getTargetExp())));
                    if (StringUtils.isNotBlank(dto.getBeforeTrigger())) {
                        dto.setBeforeTrigger(new String(Base64.decodeBase64(dto.getBeforeTrigger())));
                    }
                    if (StringUtils.isNotBlank(dto.getAfterTrigger())) {
                        dto.setAfterTrigger(new String(Base64.decodeBase64(dto.getAfterTrigger())));
                    }
                    if (StringUtils.isNotBlank(dto.getCallbackType())) {
                        dto.setCallbackType(new String(Base64.decodeBase64(dto.getCallbackType())));
                    }
                    if (StringUtils.isNotBlank(dto.getCallbackExp())) {
                        dto.setCallbackExp(new String(Base64.decodeBase64(dto.getCallbackExp())));
                    }
                    log.info("saveDataInput base64 decode after data {}", dto);
                } catch (Exception e) {
                    log.error("Json conversion exception err,{}", e.getMessage(), e);
                }
            }
            SysDataInput sysDataInput = ObjectConverter.convert(dto, SysDataInput.class);
            if (null != dto.getI18nNote()) {
                sysDataInput.setI18nNote(FastjsonUtil.toJson(dto.getI18nNote()));
            }
            Boolean add = sysDataInput.getSysDataInputId() == null;
            LogRecordContext.putVariable("operate", add ? "新增" : "编辑");
            LogRecordContext.putVariable("name", sysDataInput.getNote());
            sysDataInput.setSysDataInputId(integratedService.saveDataInput(sysDataInput));
            xxlJobService.addOrUpdJobInfoBySysDataInput(sysDataInput, add);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("saveDataInput error,{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "取得输出接口列表")
    @PostMapping(value = "/getDataOutputList")
    @ResponseBody
    public Result<PageResult<Map>> getDataOutputList(@RequestBody AttendanceBasePage basePage) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<Map> dataInputList = integratedService.getDataOutputList(pageBean);
        return ResponseWrap.wrapResult(new PageResult<>(dataInputList, pageBean.getPageNo(), pageBean.getPageSize(), pageBean.getCount()));
    }

    @ApiOperation(value = "取得输出接口具体信息")
    @GetMapping(value = "/getDataOutput")
    @ResponseBody
    public Result<SysDataOutputVo> getDataOutput(@RequestParam("id") Integer id) {
        return ResponseWrap.wrapResult(ObjectConverter.convert(integratedService.getDataOutput(id), SysDataOutputVo.class));
    }

    @ApiOperation(value = "保存输出接口")
    @PostMapping(value = "/saveDataOutput")
    @ResponseBody
    public Result<Boolean> saveDataOutput(@RequestBody SysDataOutputDto record) {
        try {
            integratedService.saveDataOutput(ObjectConverter.convert(record, SysDataOutput.class));
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("saveDataOutput error,{},{}", e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "执行接入接口")
    @GetMapping(value = "/syncDataInput")
    @LogRecordAnnotation(success = "执行了{{#name}}", category = "执行", menu = "考勤设置-打卡接口")
    @ResponseBody
    public Result<Boolean> syncDataInput(@RequestParam("id") Integer id) {
        UserInfo userInfo = sessionService.getUserInfo();
        try {
            integratedService.syncDataInput(id, ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), true, null);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("syncDataInput id[{}] error,{},{}", id, e.getMessage(), e.getStackTrace());
            return ResponseWrap.wrapResult(AttendanceCodes.DATA_INPUT_INTERFACE_EXECUTE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "启动接入接口")
    @GetMapping(value = "/startDataInputJob")
    @LogRecordAnnotation(success = "启用了{{#name}}", category = "启用", menu = "考勤设置-打卡接口")
    @ResponseBody
    public Result<Boolean> startDataInputJob(@RequestParam("id") Integer id) {
        try {
            SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
            LogRecordContext.putVariable("name", dataInput.getNote());
            if (dataInput.getTrigger() == null || dataInput.getTrigger().isEmpty()) {
                return ResponseWrap.wrapResult(AttendanceCodes.ENABLE_NEED_TRIGGER, Boolean.FALSE);
            }
            dataInput.setStatus(1);
            dataInputMapper.updateByPrimaryKey(dataInput);
            xxlJobService.startJobBySysDataInputId(id);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("startDataInputJob id[{}] error,{},{}", id, e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.ENABLE_DATA_INPUT_INTERFACE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "停止接入接口")
    @GetMapping(value = "/stopDataInputJob")
    @LogRecordAnnotation(success = "停用了{{#name}}", category = "停用", menu = "考勤设置-打卡接口")
    @ResponseBody
    public Result<Boolean> stopDataInputJob(@RequestParam("id") Integer id) {
        try {
            SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
            LogRecordContext.putVariable("name", dataInput.getNote());
            dataInput.setStatus(2);
            dataInputMapper.updateByPrimaryKey(dataInput);
            xxlJobService.stopJobBySysDataInputId(id);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("stopDataInputJob id[{}] error,{},{}", id, e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.STOP_DATA_INPUT_INTERFACE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "删除接入接口")
    @DeleteMapping(value = "/deleteDataInput")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-打卡接口")
    @ResponseBody
    public Result<Boolean> deleteDataInput(@RequestParam("id") Integer id) {
        try {
            integratedService.clearInputLog(id);
            SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
            LogRecordContext.putVariable("name", dataInput.getNote());
            dataInputMapper.deleteByPrimaryKey(id);
            xxlJobService.deleteJobBySysDataInputId(id);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("stopDataInputJob id[{}] error,{},{}", id, e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_DATA_INPUT_INTERFACE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "执行输出接口")
    @PostMapping(value = "/syncDataOutput")
    @ResponseBody
    public Result<Boolean> syncDataOutput(@RequestBody SyncDataOutPutDto dto) {
        UserInfo userInfo = sessionService.getUserInfo();
        try {
            integratedService.syncDataOutput(dto.getSysDataOutputId(), ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), dto.getParams());
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("syncDataOutput id[{}] error,{},{}", dto.getSysDataOutputId(), e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.DATA_OUTPUT_INTERFACE_EXECUTE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "启动输出接口")
    @GetMapping(value = "/startDataOutputJob")
    @ResponseBody
    public Result<Boolean> startDataOutputJob(@RequestParam("id") Integer id) {
        try {
            integratedService.startDataOutputJob(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("syncDataOutput id[{}] error,{},{}", id, e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.ENABLE_DATA_OUTPUT_INTERFACE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "停止输出接口")
    @GetMapping(value = "/stopDataOutputJob")
    @ResponseBody
    public Result<Boolean> stopDataOutputJob(@RequestParam("id") Integer id) {
        try {
            integratedService.stopDataOutputJob(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("stopDataOutputJob id[{}] error,{},{}", id, e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.STOP_DATA_OUTPUT_INTERFACE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "删除输出接口")
    @DeleteMapping(value = "/deleteDataOutput")
    @ResponseBody
    public Result<Boolean> deleteDataOutput(@RequestParam("id") Integer id) {
        try {
            integratedService.deleteDataOutput(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("deleteDataOutput id[{}] error,{},{}", id, e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_DATA_OUTPUT_INTERFACE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "清除接入接口日志")
    @GetMapping(value = "/clearInputLog")
    @ResponseBody
    public Result<Boolean> clearInputLog(@RequestParam("id") Integer id) {
        try {
            integratedService.clearInputLog(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("clearInputLog id[{}] error,{},{}", id, e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.CLEAR_DATA_INPUT_LOG_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "清除接出接口日志")
    @GetMapping(value = "/clearOutputLog")
    @ResponseBody
    public Result<Boolean> clearOutputLog(@RequestParam("id") Integer id) {
        try {
            integratedService.clearOutputLog(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("clearOutputLog id[{}] error,{},{}", id, e, e.getMessage());
            return ResponseWrap.wrapResult(AttendanceCodes.CLEAR_DATA_OUTPUT_LOG_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "获取接入日志详情")
    @GetMapping(value = "/getLogDataInput")
    @ResponseBody
    public Result<LogDataInputVo> getLogDataInput(@RequestParam("id") Integer id) {
        return ResponseWrap.wrapResult(ObjectConverter.convert(integratedService.getLogDataInput(id), LogDataInputVo.class));
    }

    @ApiOperation(value = "获取接出日志详情")
    @GetMapping(value = "/getLogDataOutput")
    @ResponseBody
    public Result<LogDataOutputVo> getLogDataOutput(@RequestParam("id") Integer id) {
        return ResponseWrap.wrapResult(ObjectConverter.convert(integratedService.getLogDataOutput(id), LogDataOutputVo.class));

    }

    @ApiOperation(value = "根据数据接入ID获取接入日志列表")
    @PostMapping(value = "/getDataInputLogList")
    @ResponseBody
    public Result<PageResult<LogDataInputVo>> getDataInputLogList(@RequestBody AttendanceBasePage basePage, @RequestBody LogDataInputDto dto) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<LogDataInput> dataInputList = integratedService.getLogDataInputList(pageBean, dto.getSysDataInputId());
        return ResponseWrap.wrapResult(new PageResult<>(ObjectConverter.convertList(dataInputList, LogDataInputVo.class), pageBean.getPageNo(), pageBean.getPageSize(), pageBean.getCount()));
    }

    @ApiOperation(value = "根据数据接入ID获取接出日志列表")
    @PostMapping(value = "/getDataOutputLogList")
    @ResponseBody
    public Result<PageResult<LogDataOutputVo>> getDataOutputLogList(@RequestBody AttendanceBasePage basePage, @RequestBody LogDataOutputDto dto) {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        List<LogDataOutput> dataOutputList = integratedService.getLogDataOutputList(pageBean, dto.getSysDataOutputId());
        return ResponseWrap.wrapResult(new PageResult<>(ObjectConverter.convertList(dataOutputList, LogDataOutputVo.class), pageBean.getPageNo(), pageBean.getPageSize(), pageBean.getCount()));
    }
}
