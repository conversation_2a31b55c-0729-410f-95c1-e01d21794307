package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyClockInfoVo {
    @ApiModelProperty("打卡记录ID")
    private Integer recordId;
    @ApiModelProperty("打卡方式： 1 GPS 2 扫码 3 外勤 4 蓝牙 5 WIFI 6 补卡")
    private Integer type;
    @ApiModelProperty("打卡类型: 1 签到 2 签退")
    private Integer registerType;
    @ApiModelProperty("打卡时间")
    private Long regDateTime;
    @ApiModelProperty("打卡结果：1 正常 2 异常")
    private Integer resultType;
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;
    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;
    @ApiModelProperty("审批流程key")
    private String businessKey;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    private Integer funcType;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态文本")
    private String statusName;
    @ApiModelProperty("申请类型")
    private String applyName;
    @ApiModelProperty("打卡地点")
    private String regAddr;
}
