package com.caidaocloud.attendance.service.interfaces.dto.travel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TravelTransferRulePeriod {
    @ApiModelProperty("出差时长>=")
    private Double start;
    @ApiModelProperty("出差时长>=")
    private Double end;
    @ApiModelProperty("转换时长")
    private Double duration;
    @ApiModelProperty("假期类型")
    private Integer leaveTypeId;
}
