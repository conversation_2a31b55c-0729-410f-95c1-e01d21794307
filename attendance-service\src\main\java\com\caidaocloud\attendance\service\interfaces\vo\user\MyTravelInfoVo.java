package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyTravelInfoVo {
    @ApiModelProperty("单据ID")
    private Long travelId;
    @ApiModelProperty("出差类型")
    private String travelType;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态")
    private String statusName;
    @ApiModelProperty("休假时间文本")
    private String travelTimeTxt;
    @ApiModelProperty("审批流程key")
    private String businessKey;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long createTime;
    private Integer funcType;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    @ApiModelProperty("申请类型")
    private String applyName;
}
