package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MyWorkEventVo {
    @ApiModelProperty("打卡记录")
    private List<MyClockInfoVo> records;
    @ApiModelProperty("休假记录")
    private List<MyLeaveTimeVo> lts;
    @ApiModelProperty("加班记录")
    private List<MyOverTimeVo> ots;
    @ApiModelProperty("排班记录")
    private List<MyWorkDateShiftVo> shifts;
    @ApiModelProperty("补打卡记录")
    private List<MyBdkClockInfoVo> bdkRecords;
    @ApiModelProperty("出差记录")
    private List<MyTravelInfoVo> tts;
    @ApiModelProperty("打卡类型")
    private Integer clockType;
    @ApiModelProperty("早退时长")
    private Float earlyTime;
    @ApiModelProperty("迟到时长")
    private Float lateTime;
    @ApiModelProperty("旷工时长")
    private Integer kgWorkTime;
    @ApiModelProperty("员工姓名")
    private String empName;
}
