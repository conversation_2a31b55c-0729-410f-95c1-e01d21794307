<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.AnalysisRuleMapper">
    <select id="getAnalysisList" resultType="map">
        SELECT * FROM (
            SELECT
                parse_group_id,
                parse_group_name,
                ot_pase_jsonb,
                updtime
            FROM
                wa_parse_group
            WHERE
                belong_orgid = #{belongId}
        )AS t
    </select>

    <select id="getAllOtAmount" resultType="map">
        SELECT empid,COALESCE(SUM((duration)::int / 60 :: FLOAT4), 0 ) AS "timeTotal"
        FROM (
            SELECT
                al.empid,
                (jsonb_each(al.ot_column_jsob)).key AS "ot",
                (jsonb_each(al.ot_column_jsob)).value ->> 0 AS "duration"
            FROM wa_analyze al
            WHERE al.belong_date BETWEEN #{tmStartDay} AND #{tmEndDay}
            AND al.empid IN <foreach collection="empids" item="item" open="(" separator="," close=")">#{item}</foreach>
            AND al.ot_column_jsob IS NOT NULL
        ) tmp
        <if test="compensateType != null">
            WHERE ot = 'ot_${overtimeType}_${compensateType}_key'
        </if>
        <if test="compensateType == null">
            WHERE ot ~ 'ot_${overtimeType}_.*key'
        </if>
        GROUP BY empid
    </select>

    <select id="getLeaveTypeAmountByEmpids" resultType="map">
        SELECT
            al.empid,
            COALESCE (SUM((al.level_column_jsonb ->> 'lt_${leaveTypeId}_key')::numeric), 0) AS "timeTotal"
        FROM wa_analyze al
        WHERE al.belong_date BETWEEN #{tmStartDay} AND #{tmEndDay}
        AND al.empid IN <foreach collection="empids" item="item" open="(" separator="," close=")">#{item}</foreach>
        GROUP BY
          al.empid
    </select>

    <select id="getAllEmpQuotaAmount" resultType="map">
        SELECT
            eq.empid,
            eq.leave_type_id AS "leaveTypeId",
            SUM(COALESCE(quota_day,0)) AS "quotaDay",
            SUM(COALESCE((CASE WHEN eq.if_advance == 1 THEN eq.quota_day ELSE eq.now_quota END), 0) - COALESCE(eq.used_day, 0) + COALESCE(eq.adjust_quota, 0) - COALESCE(eq.fix_used_day, 0)) AS "curRemain"
        FROM wa_emp_quota eq
        WHERE #{tmEndDay} BETWEEN eq.start_date AND eq.last_date
        AND eq.empid IN <foreach collection="empids" item="item" open="(" separator="," close=")">#{item}</foreach>
        AND eq.leave_type_id = #{leaveTypeId}
        GROUP BY eq.empid,eq.leave_type_id
    </select>

    <select id="selectDailyInfo" resultType="map">
          WITH temp AS (
            SELECT MIN(reg_date_time) AS mindatetime, MAX(reg_date_time) AS maxdatetime, empid
                 FROM wa_register_record
                 WHERE belong_date = #{belongDate}
                   AND belong_org_id = #{tenantId,jdbcType=VARCHAR}
                   AND (type <![CDATA[<>]]> 6 OR (type=6 AND approval_status=2))
                 GROUP BY empid
            )
          SELECT
           c.mindatetime,
           c.maxdatetime,
           a.empid,
           b.shift_def_name,
           b.start_time,
           b.end_time,
           a.actual_work_time,
           a.late_time    AS late_time,
           a.early_time   AS early_time,
           a.kg_work_time AS kg_work_time,
           a.belong_date
        FROM wa_analyze a
             LEFT JOIN temp c ON a.empid = c.empid
             LEFT JOIN wa_shift_def b ON a.shift_def_id = b.shift_def_id
        WHERE a.belong_org_id = #{tenantId,jdbcType=VARCHAR}
          AND a.belong_date = #{belongDate}
          AND b.date_type = 1
        ORDER BY a.empid, a.belong_date
        LIMIT 2000 OFFSET ${pageNo}
    </select>
</mapper>