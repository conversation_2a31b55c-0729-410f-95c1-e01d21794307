package com.caidaocloud.attendance.service.interfaces.dto.group;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AttEmpGroupDto {
    @ApiModelProperty("员工id")
    @Deprecated
    private Long empId;
    @ApiModelProperty("员工信息dto")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("员工分组主键")
    private Integer empGroupId;
    @ApiModelProperty("考勤方案主键")
    private Integer waGroupId;
    @ApiModelProperty("员工号")
    private String workNo;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("组织路径")
    private String fullPath;
    @ApiModelProperty("组织名称")
    private String orgName;
    @ApiModelProperty("考勤方案")
    private String waGroupName;
    @ApiModelProperty("生效日期")
    private Long startTime;
    @ApiModelProperty("失效日期")
    private Long endTime;
    @ApiModelProperty("操作人员工号")
    private String updWorkNo;
    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("操作时间")
    private Long updateTime;
    private String belongOrgId;
    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("离职日期")
    private Long terminationDate;
    private String i18nWaGroupName;
    @ApiModelProperty("方案状态")
    private String effectiveStatus;

    public void doSetEffectiveStatus(Long nowTime) {
        if (this.startTime == null || this.endTime == null || nowTime == null) {
            return;
        }
        if (nowTime < this.startTime) {
            // 未生效
            this.setEffectiveStatus(MessageHandler.getMessage("caidao.exception.error_202018", WebUtil.getRequest()));
        } else if (nowTime >= this.startTime && nowTime <= this.endTime) {
            // 生效中
            this.setEffectiveStatus(MessageHandler.getMessage("caidao.exception.error_202019", WebUtil.getRequest()));
        } else if (nowTime > this.endTime) {
            // 已失效
            this.setEffectiveStatus(MessageHandler.getMessage("caidao.exception.error_202020", WebUtil.getRequest()));
        }
    }
}
