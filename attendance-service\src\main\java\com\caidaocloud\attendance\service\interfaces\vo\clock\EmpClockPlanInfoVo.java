package com.caidaocloud.attendance.service.interfaces.vo.clock;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/9/9
 */
@Data
public class EmpClockPlanInfoVo {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("员工信息dto")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("打卡方案ID")
    private Long planId;
    @ApiModelProperty("打卡方案名称")
    private String planName;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
}
