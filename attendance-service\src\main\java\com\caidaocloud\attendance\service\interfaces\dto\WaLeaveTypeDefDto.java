package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 假期定义类型
 *
 * <AUTHOR>
 */
@Data
public class WaLeaveTypeDefDto {
    @ApiModelProperty("id")
    private Integer leaveTypeDefId;
    @ApiModelProperty("编码")
    private String leaveTypeDefCode;
    @ApiModelProperty("名称")
    private String leaveTypeDefName;
    @ApiModelProperty("多语言")
    private Object leaveTypeDefLang;
    @ApiModelProperty("是否删除 0:未删除 1:删除")
    private Short deleted;
    @ApiModelProperty("状态 0:有效 -1:无效")
    private Integer status;
    @ApiModelProperty("所属公司")
    private String belongOrgid;
    @ApiModelProperty("创建用户")
    private Long crtuser;
    @ApiModelProperty("创建时间")
    private Long crttime;
    @ApiModelProperty("更新用户")
    private Long upduser;
    @ApiModelProperty("更新时间")
    private Long updtime;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nLeaveTypeDefName;

    public void initLeaveTypeDefName() {
        if (StringUtils.isNotBlank(this.leaveTypeDefName)) {
            return;
        }
        if (null == this.i18nLeaveTypeDefName || this.i18nLeaveTypeDefName.isEmpty() || null == this.i18nLeaveTypeDefName.get("default")) {
            return;
        }
        this.setLeaveTypeDefName(this.i18nLeaveTypeDefName.get("default"));
    }

    public void initI18nLeaveTypeDefName() {
        if (null != this.i18nLeaveTypeDefName && !this.i18nLeaveTypeDefName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.leaveTypeDefName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.leaveTypeDefName);
        this.setI18nLeaveTypeDefName(i18nName);
    }
}
