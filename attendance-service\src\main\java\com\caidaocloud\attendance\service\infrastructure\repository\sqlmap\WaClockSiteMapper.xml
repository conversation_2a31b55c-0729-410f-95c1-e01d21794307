<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaClockSiteMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockSitePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="corp_id" jdbcType="BIGINT" property="corpId" />
    <result column="belong_org_id" jdbcType="VARCHAR" property="belongOrgId" />
    <result column="site_name" jdbcType="VARCHAR" property="siteName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="range" jdbcType="NUMERIC" property="range" />
    <result column="lng" jdbcType="NUMERIC" property="lng" />
    <result column="lat" jdbcType="NUMERIC" property="lat" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, corp_id, belong_org_id, site_name, address, range, lng, lat, creator, create_time,
    updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_clock_site
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_clock_site
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockSitePo">
    insert into wa_clock_site (id, corp_id, belong_org_id, 
      site_name, address, range, 
      lng, lat, clock_in_allowed, creator,
      create_time, updater, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{corpId,jdbcType=BIGINT}, #{belongOrgId,jdbcType=VARCHAR},
      #{siteName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{range,jdbcType=NUMERIC}, 
      #{lng,jdbcType=NUMERIC}, #{lat,jdbcType=NUMERIC}, #{creator,jdbcType=BIGINT},
      #{createTime,jdbcType=BIGINT}, #{updater,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockSitePo">
    insert into wa_clock_site
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="belongOrgId != null">
        belong_org_id,
      </if>
      <if test="siteName != null">
        site_name,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="range != null">
        range,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=BIGINT},
      </if>
      <if test="belongOrgId != null">
        #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="siteName != null">
        #{siteName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="range != null">
        #{range,jdbcType=NUMERIC},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=NUMERIC},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=NUMERIC},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockSitePo">
    update wa_clock_site
    <set>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=BIGINT},
      </if>
      <if test="belongOrgId != null">
        belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      </if>
      <if test="siteName != null">
        site_name = #{siteName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="range != null">
        range = #{range,jdbcType=NUMERIC},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=NUMERIC},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=NUMERIC},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaClockSitePo">
    update wa_clock_site
    set corp_id = #{corpId,jdbcType=BIGINT},
      belong_org_id = #{belongOrgId,jdbcType=VARCHAR},
      site_name = #{siteName,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      range = #{range,jdbcType=NUMERIC},
      lng = #{lng,jdbcType=NUMERIC},
      lat = #{lat,jdbcType=NUMERIC},
      creator = #{creator,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      updater = #{updater,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getClockSitePageList" resultMap="BaseResultMap">
      select id, site_name, address, range, lng, lat
      from wa_clock_site
      where 1=1
        <if test="corpId != null">
          and corp_id = #{corpId}
        </if>
        <if test="belongOrgId != null">
          and belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
        </if>
      order by site_name
  </select>

  <update id="updateClockSites">
    <if test="records != null and records.size() > 0">
      <foreach collection="records" item="record">
        update wa_clock_site set lng=#{record.lng}, lat=#{record.lat} where id=#{record.id};
      </foreach>
    </if>
  </update>
</mapper>