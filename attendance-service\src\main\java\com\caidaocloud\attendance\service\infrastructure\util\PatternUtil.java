package com.caidaocloud.attendance.service.infrastructure.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PatternUtil {

    public static String replaceTotalTimeDurationFromRegex(String source, String regField) {
        String sourceStr = source;
        Matcher matcher = Pattern.compile(regField + "\\s*(>=|>|<=|<|<>|=)\\s*'(\\d+(?:\\.\\d*)?|\\.\\d+)'").matcher(sourceStr);
        while (matcher.find()) {
            String matcherGroup = matcher.group();
            String matcherGroupRegex = "(\\d+(?:\\.\\d*)?|\\.\\d+)";
            Matcher groupMatcher = Pattern.compile(matcherGroupRegex).matcher(matcherGroup);
            while (groupMatcher.find()) {
                String replaceResult = matcherGroup.replaceAll(matcherGroupRegex, String.valueOf(Integer.parseInt(groupMatcher.group()) * 60));
                sourceStr = sourceStr.replaceAll(matcherGroup, replaceResult);
            }
        }
        return sourceStr;
    }

    public static String getStrFromRegex(String source, String regField, String regex) {
        if (null == regex) {
            regField = "";
        }
        if (null == source || "".equals(source) || null == regex || "".equals(regex)) {
            return "";
        }
        Matcher matcher = Pattern.compile(String.format("%s%s", regField, regex)).matcher(source);
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }

    public static String strReplaceFromRegex(String source, String regField, String regex, String replacement) {
        String sourceStr = source;
        Matcher matcher = Pattern.compile(String.format("%s%s", regField, regex)).matcher(sourceStr);
        while (matcher.find()) {
            String matcherGroup = matcher.group();
            sourceStr = sourceStr.replaceAll(matcherGroup, replacement);
        }
        return sourceStr;
    }
}
