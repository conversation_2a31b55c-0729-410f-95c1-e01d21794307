package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchLeaveService;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveApplyDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveApplyMultiTypeDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.RevokeBatchLeaveDto;
import com.caidaocloud.attendance.service.interfaces.vo.BatchLeaveApplyTimeMultiTypeVo;
import com.caidaocloud.attendance.service.interfaces.vo.LeaveApplyTimeVo;
import com.caidaocloud.attendance.service.interfaces.vo.leave.BatchLeavePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.leave.LeaveDateInfoVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.EmpQuotaSummaryVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门户-批量休假
 *
 * <AUTHOR>
 * @Date 2024/6/14
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/leave/portal/v1")
@Api(value = "/api/attendance/batch/leave/portal/v1", description = "门户-批量休假")
public class WaBatchLeavePortalController {
    @Autowired
    private WaBatchLeaveService waBatchLeaveService;
    @Autowired
    private CacheService cacheService;

    @ApiOperation(value = "获取休假日期列表")
    @GetMapping(value = "/getLeaveDateList")
    public Result<List<LeaveDateInfoVo>> getLeaveDateList(@RequestParam("startDate") Long startDate,
                                                          @RequestParam("endDate") Long endDate,
                                                          @RequestParam("leaveTypeId") Integer leaveTypeId) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        try {
            return Result.ok(waBatchLeaveService.getLeaveDateList(userInfo.getStaffId(), startDate, endDate, leaveTypeId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_DATE_LIST_FAILED, new ArrayList<>());
        }
    }

    @Deprecated
    @ApiOperation(value = "计算休假时长（仅支持选择一种假期类型申请）已过期，请使用支持假期类型支持多选的接口")
    @PostMapping(value = "/calLeaveTime")
    public Result<LeaveApplyTimeVo> calLeaveTime(@RequestBody BatchLeaveApplyDto leaveApplyDto) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        leaveApplyDto.setEmpid(userInfo.getStaffId());
        String lockKey = BatchLeaveApplyDto.getIdempotentString(leaveApplyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            return Result.ok(waBatchLeaveService.calLeaveTime(leaveApplyDto, lockKey));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "计算休假时长（支持选择多种假期类型申请）")
    @PostMapping(value = "/calLeaveTimeForMultiType")
    public Result<BatchLeaveApplyTimeMultiTypeVo> calLeaveTimeForMultiType(@RequestBody BatchLeaveApplyMultiTypeDto leaveApplyDto) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        leaveApplyDto.setEmpid(userInfo.getStaffId());
        String lockKey = BatchLeaveApplyMultiTypeDto.getIdempotentString(leaveApplyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            return Result.ok(waBatchLeaveService.calLeaveTimeForMultiType(leaveApplyDto, lockKey));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_DURATION_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @Deprecated
    @ApiOperation(value = "申请休假（仅支持选择一种假期类型申请）已过期，请使用支持假期类型支持多选的接口")
    @PostMapping(value = "/saveLeave")
    public Result saveLeave(@RequestBody BatchLeaveApplyDto leaveApplyDto) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        leaveApplyDto.setEmpid(userInfo.getStaffId());
        String lockKey = BatchLeaveApplyDto.getIdempotentString(leaveApplyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            return waBatchLeaveService.saveLeave(leaveApplyDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "申请休假（支持选择多种假期类型申请）")
    @PostMapping(value = "/saveLeaveForMultiType")
    public Result saveLeaveForMultiType(@RequestBody BatchLeaveApplyMultiTypeDto leaveApplyDto) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();
        leaveApplyDto.setEmpid(userInfo.getStaffId());
        String lockKey = BatchLeaveApplyMultiTypeDto.getIdempotentString(leaveApplyDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 120);
        }
        try {
            return waBatchLeaveService.saveLeaveForMultiType(leaveApplyDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_APPLY_FAILED, null);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "分页列表")
    @PostMapping(value = "/page")
    public Result<AttendancePageResult<BatchLeavePageListVo>> getPageList(@RequestBody QueryPageBean queryPageBean) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        BatchLeaveQueryDto dto = new BatchLeaveQueryDto();
        dto.setEmpid(userInfo.getStaffId());
        PageUtil.doCopyFieldProperty(queryPageBean, dto);
        PageBean pageBean = PageUtil.getPageBean(dto);
        waBatchLeaveService.preHandleFilterField(pageBean);
        PageList<BatchLeavePageListVo> pageList = waBatchLeaveService.getPageList(pageBean, dto, UserContext.getAndCheckUser());
        AttendancePageResult<BatchLeavePageListVo> pageResult = new AttendancePageResult<>(pageList, pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revoke(@RequestBody RevokeBatchLeaveDto dto) {
        if (StringUtils.isEmpty(dto.getRevokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRevokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            return waBatchLeaveService.revoke(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException || e instanceof ServerException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_FAILED, Boolean.FALSE);
        }
    }

    @Deprecated
    @ApiOperation(value = "查询员工单个假期配额信息 已过期，请使用支持假期类型支持多选的接口")
    @GetMapping(value = "/getEmpQuota")
    public Result<EmpQuotaSummaryVo> getEmpQuota(@RequestParam("leaveTypeId") Integer leaveTypeId) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        return Result.ok(waBatchLeaveService.getEmpQuota(userInfo.getStaffId(), leaveTypeId));
    }

    @ApiOperation(value = "查询员工多个假期配额信息")
    @GetMapping(value = "/listEmpQuota")
    public Result<List<EmpQuotaSummaryVo>> getEmpQuotaList(@RequestParam("empid") Long empid,
                                                           @RequestParam("leaveTypeIds") String leaveTypeIds) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        List<Integer> leaveTypeIdList = Arrays.stream(leaveTypeIds.split(","))
                .map(Integer::valueOf).collect(Collectors.toList());
        return Result.ok(waBatchLeaveService.getEmpQuotaList(userInfo.getStaffId(), leaveTypeIdList));
    }
}
