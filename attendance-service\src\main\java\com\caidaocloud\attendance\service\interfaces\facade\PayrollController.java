package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidaocloud.attendance.service.application.service.impl.PayrollService;
import com.caidaocloud.attendance.service.interfaces.dto.payroll.OtAmountSearchDto;
import com.caidaocloud.attendance.service.interfaces.vo.payroll.EmpQuotaAmountVo;
import com.caidaocloud.attendance.service.interfaces.vo.payroll.OtAmountVo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 薪资考勤计算项接口
 * <AUTHOR>
 * @date 2021-08-06
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/attendance/payroll/v1")
@Api(value = "/api/attendance/payroll/v1", description = "薪资考勤计算项接口", tags = "v1.2")
public class PayrollController {
    @Resource
    private PayrollService payrollService;

    @PostMapping("/otAmount")
    @ApiOperation(value = "加班总时长", tags = "v1.2")
    public Result<List<OtAmountVo>> getAllOtAmount(OtAmountSearchDto searchDto){
        checkArgs(searchDto);

        List<Map> data = payrollService.getAllOtAmount(searchDto);
        List<OtAmountVo> resultData = ObjectConverter.convertList(data, OtAmountVo.class);
        return Result.ok(resultData);
    }

    @PostMapping("/allLeaveTypeAmount")
    @ApiOperation(value = "休假总时长", tags = "v1.2")
    public Result<List<OtAmountVo>> getAllLeaveTypeAmount(OtAmountSearchDto searchDto){
        checkArgs(searchDto);

        List<Map> data = payrollService.getAllLeaveTypeAmount(searchDto);
        List<OtAmountVo> resultData = ObjectConverter.convertList(data, OtAmountVo.class);
        return Result.ok(resultData);
    }

    @PostMapping("/empQuotaAmount")
    @ApiOperation(value = "调整剩余和当前剩余总时长", tags = "v1.2")
    public Result<List<EmpQuotaAmountVo>> getEmpQuotaAmount(OtAmountSearchDto searchDto){
        checkArgs(searchDto);

        List<Map> data = payrollService.getAllEmpQuotaAmount(searchDto);
        List<EmpQuotaAmountVo> resultData = ObjectConverter.convertList(data, EmpQuotaAmountVo.class);
        return Result.ok(resultData);
    }

    private void checkArgs(OtAmountSearchDto searchDto){
        boolean checkArg = null == searchDto || null == searchDto.getTmEndDay() || null == searchDto.getTmStartDay();
        checkArg = (checkArg || null == searchDto.getEmpids() || searchDto.getEmpids().isEmpty());

        PreCheck.preCheckArgument(checkArg, "查询参数无效");
    }

}
