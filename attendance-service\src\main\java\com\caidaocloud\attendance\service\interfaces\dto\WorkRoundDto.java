package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/9
 */
@Data
public class WorkRoundDto implements Serializable {
    private static final long serialVersionUID = 7026287618315435192L;

    @ApiModelProperty("排班计划ID")
    private Integer workRoundId;

    @ApiModelProperty("排班名称")
    private String roundName;

    @ApiModelProperty("班次轮次信息")
    private List<RoundShiftDto> roundShiftList;

    @ApiModelProperty("所属公司ID")
    private String belongOrgid;

    @ApiModelProperty("用户ID")
    private Long crtuser;

    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nRoundName;

    public void initRoundName() {
        if (StringUtils.isNotBlank(this.roundName)) {
            return;
        }
        if (null == this.i18nRoundName || this.i18nRoundName.isEmpty() || null == this.i18nRoundName.get("default")) {
            return;
        }
        this.setRoundName(this.i18nRoundName.get("default"));
    }

    public void initI18nRoundName() {
        if (null != this.i18nRoundName && !this.i18nRoundName.isEmpty()) {
            return;
        }
        if (StringUtils.isBlank(this.roundName)) {
            return;
        }
        Map<String, String> i18nName = new HashMap<>();
        i18nName.put("default", this.roundName);
        this.setI18nRoundName(i18nName);
    }
}
