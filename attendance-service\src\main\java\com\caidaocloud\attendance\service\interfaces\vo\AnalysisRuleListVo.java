package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AnalysisRuleListVo implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Integer parseGroupId;

    @ApiModelProperty(value = "考勤分析分组名称")
    private String parseGroupName;

    @ApiModelProperty(value = "加班类型数据")
    private Object otPaseJsonb;

}
