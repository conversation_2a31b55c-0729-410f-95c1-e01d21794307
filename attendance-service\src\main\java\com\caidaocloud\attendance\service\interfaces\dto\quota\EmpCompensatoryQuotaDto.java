package com.caidaocloud.attendance.service.interfaces.dto.quota;

import com.caidaocloud.attendance.service.application.enums.PreTimeUnitEnum;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EmpCompensatoryQuotaDto {

    @ApiModelProperty("配额id")
    private Long quotaId;
    @ApiModelProperty("员工姓名")
    private String empName;
    @ApiModelProperty("加班日期")
    private Long overtimeDate;
    @ApiModelProperty("调休单位")
    private Integer quotaUnit;
    @ApiModelProperty("调休额度")
    private Float quotaDay;
    @ApiModelProperty("额度调整")
    private Float adjustQuotaDay;
    @ApiModelProperty("已用")
    private Float usedDay;
    @ApiModelProperty("流程中")
    private Float inTransitQuota;
    @ApiModelProperty("余额")
    private Float leftQuotaDay;
    @ApiModelProperty("生效日期")
    private Long startDate;
    @ApiModelProperty("失效日期")
    private Long lastDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("时间单位名称")
    private String unitTimeName;
    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;

    /**
     * 计算剩余额度
     */
    public void calculateLeftDay() {
        //单位为小时
        Float left = this.quotaDay + this.adjustQuotaDay - this.usedDay - this.inTransitQuota;
        if (quotaUnit == 2) {
            left = new BigDecimal(left).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
            setLeftQuotaDay(left);
            //额度
            this.quotaDay = new BigDecimal(quotaDay).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
            setQuotaDay(this.quotaDay);
            //调整额度
            this.adjustQuotaDay = new BigDecimal(adjustQuotaDay).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
            setAdjustQuotaDay(adjustQuotaDay);
            //已用
            this.usedDay = new BigDecimal(usedDay).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
            setUsedDay(this.usedDay);
            this.inTransitQuota = new BigDecimal(inTransitQuota).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).floatValue();
            setInTransitQuota(this.inTransitQuota);
        } else {
            setLeftQuotaDay(left);
        }
        setUnitTimeName(PreTimeUnitEnum.getName(quotaUnit));
    }
}
