package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidao1.report.dto.FilterBean;
import com.caidaocloud.attendance.service.application.enums.TimePeriodEnum;
import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MonthAnalysePageDto extends ExportBasePage implements Serializable {

    @ApiModelProperty("时间类型枚举")
    private TimePeriodEnum timePeriod;

    @ApiModelProperty("开始日期")
    private Long startDate;

    @ApiModelProperty("结束日期")
    private Long endDate;

    @ApiModelProperty("考勤分组id")
    private Integer groupId;

    @ApiModelProperty("考勤分组月份")
    private Integer ym;

    @ApiModelProperty("是否原始：false/true")
    private boolean isOrigin;

    @ApiModelProperty("员工Id")
    private List<Long> empIds;

    @ApiModelProperty("筛选条件")
    private List<FilterBean> filterList;

    @ApiModelProperty("分析类型 默认为0:休假 1:出差 2:加班 3:迟到 4:早退")
    private Integer statisticsType;
}
