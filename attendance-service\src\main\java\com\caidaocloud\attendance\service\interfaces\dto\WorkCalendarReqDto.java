package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/3/7
 */
@Data
public class WorkCalendarReqDto extends AttendanceBasePage {
    @ApiModelProperty("搜索关键字")
    private String keywords;
    @ApiModelProperty("公司ID")
    private String belongOrgId;
    @ApiModelProperty("开始日期")
    private Long startDate;
    @ApiModelProperty("结束日期")
    private Long endDate;
    private Long corpId;
}
