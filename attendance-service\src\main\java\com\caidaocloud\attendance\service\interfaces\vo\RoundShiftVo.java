package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2021/2/9
 */
@Data
public class RoundShiftVo implements Serializable{
    private static final long serialVersionUID = 5855602996053456531L;

    @ApiModelProperty("班次ID")
    private Integer shiftDefId;

    @ApiModelProperty("顺序")
    private Integer roundNo;

    @ApiModelProperty("班次名称")
    private String shiftDefName;
}
