package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SobDto extends AttendanceBasePage implements Serializable {

    @ApiModelProperty(value = "帐套名称")
    private String sobName;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "员工ID")
    private Long empId;
}
