package com.caidaocloud.attendance.core.wa.dto;

import lombok.Data;

@Data
public class MobileLeaveParamBean {
    Integer leaveTypeId;//请假类型
    String reason;//原因
    String myFiles;//附件
    String leavetime;//时间
    Integer province;//省
    Integer city;//市
    Integer county;//区
    Integer leaveId;//请假ID
    Long weddingDate;//结婚日期
    Long expectedDate;//预产期
    Long manufactureDate;//生产日期


    //req params
    private Long startTime;
    private Long endTime;
    private String shalfDay;
    private String ehalfDay;
    private Integer stime;
    private Integer etime;
    private Integer showDay;
    private Integer showMin;
    private String fileName;
    private String period;

    private Integer funcType;

    private String file;//附件

    private Boolean enableWorkflow;

    private Integer childNum;
    private Integer maternityLeaveType;

    /**
     * 是否为批量申请（可选，批量申请时使用）
     */
    private boolean ifBtch;
    /**
     * 批量单据ID（可选，批量申请时使用）
     */
    private Long batchId;
    /**
     * 批量申请时是否更新配额（可选，批量申请时使用）
     */
    private boolean updateQuotaWhenBatch;
}
