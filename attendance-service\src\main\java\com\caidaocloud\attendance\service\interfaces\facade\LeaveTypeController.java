package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.wa.mybatis.model.WaLeaveType;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.TimeControlPeriod;
import com.caidaocloud.attendance.service.application.enums.QuotaRestrictionTypeEnum;
import com.caidaocloud.attendance.service.application.enums.QuotaTypeEnum;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.IWaLeaveTypeDefService;
import com.caidaocloud.attendance.service.application.service.impl.OverTimeService;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.KeyValueExt;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveEnabelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveTypeInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaRuleConfigDto;
import com.caidaocloud.attendance.service.interfaces.vo.*;
import com.caidaocloud.attendance.service.interfaces.vo.quota.LeaveQuotaRuleConfigVo;
import com.caidaocloud.attendance.service.interfaces.vo.quota.LeaveQuotaRuleVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.DragSort;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(value = "/api/attendance/leavetype/v1", description = "假期类型接口")
@RequestMapping("/api/attendance/leavetype/v1")
public class LeaveTypeController {
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private ILeaveTypeService leaveTypeService;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private OverTimeService overTimeCheckService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private IWaLeaveTypeDefService leaveTypeDefService;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("获取假期类型分页列表")
    @PostMapping(value = "/list")
    public Result<AttendancePageResult<LeaveTypeGridVo>> getLeaveTypeList(@RequestBody LeaveTypeReqDto reqDto) throws Exception {
        AttendancePageResult<LeaveTypeDto> pageResult = leaveTypeService.getLeaveTypeList(reqDto);
        List<LeaveTypeGridVo> items = JSON.parseArray(JSON.toJSONString(pageResult.getItems()), LeaveTypeGridVo.class);
        items.forEach(row -> {
            row.setAcctTimeType(row.getAcctTimeTypeTxt());
            row.setIsRestDay(row.getIsRestDaytxt());
            row.setIsLegalHoliday(row.getIsLegalHolidayTxt());
            row.setGenderType(row.getGenderTypeTxt());
            if (row.getQuotaRestrictionType() != null) {
                row.setQuotaRestrictionTypeName(QuotaRestrictionTypeEnum.getName(row.getQuotaRestrictionType()));
            }
        });
        return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation(value = "查询休假类型详情")
    @GetMapping(value = "/detail")
    public Result<LeaveTypeVo> getLeaveTypeInfoById(@RequestParam("id") Integer id) {
        try {
            LeaveTypeDto leaveTypeDto = leaveTypeService.getLeaveTypeById(id);
            if (null != leaveTypeDto.getTimelinessControlJsonb()) {
                PGobject pGobject = (PGobject) leaveTypeDto.getTimelinessControlJsonb();
                List<TimeControlPeriod> periods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<TimeControlPeriod>>() {
                });
                if (CollectionUtils.isNotEmpty(periods)) {
                    leaveTypeDto.setTimelinessControlJsonb(periods);
                }
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(leaveTypeDto, LeaveTypeVo.class));
        } catch (Exception e) {
            log.error("LeaveTypeController.getLeaveTypeInfoById exception:{}", e.getMessage(), e);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
    }

    private Result<Boolean> checkLeaveParams(LeaveTypeDto dto) {
        if (dto.getLeaveCode() != null && dto.getLeaveCode().length() > 20) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CODE_TOO_LONG, Boolean.FALSE);
        }
        if (dto.getLeaveName() != null && dto.getLeaveName().length() > 20) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_NAME_TOO_LONG, Boolean.FALSE);
        }
        if (dto.getLeaveTypeId() != null && dto.getUpperLevel() != null && dto.getLeaveTypeId().equals(dto.getUpperLevel())) {
            return ResponseWrap.wrapResult(AttendanceCodes.UPPER_LEVEL_ERROR, Boolean.FALSE);
        }
        if (BooleanUtils.isTrue(dto.getIfCheckTime()) && dto.getMinLeaveTime() != null && dto.getMaxLeaveTime() != null && dto.getMaxLeaveTime() < dto.getMinLeaveTime()) {
            return ResponseWrap.wrapResult(AttendanceCodes.LOWER_EXCEED_UPPER, Boolean.FALSE);
        }
        /*List<LeaveTypeInfoDto> leaveTypes = leaveTypeService.getLeaveTypes(dto.getWaGroupId(), null, null);
        if (dto.getLeaveTypeId() == null) {
            leaveTypes = leaveTypes.stream().filter(l -> l.getLeaveType().equals(dto.getLeaveType())).collect(Collectors.toList());
        } else {
            leaveTypes = leaveTypes.stream().filter(l -> !l.getLeaveTypeId().equals(dto.getLeaveTypeId()) && l.getLeaveType().equals(dto.getLeaveType())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(leaveTypes)) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_EXIST, Boolean.FALSE);
        }*/
        return Result.ok(true);
    }

    @ApiOperation(value = "新增休假类型")
    @PostMapping(value = "/save")
    @LogRecordAnnotation(success = "新增了{{#name}}", category = "新增", menu = "考勤设置-假期设置-假期规则")
    public Result<Boolean> saveLeaveType(@RequestBody LeaveTypeDto leaveTypeSetInfoDto) {
        leaveTypeSetInfoDto.initLeaveName();
        leaveTypeSetInfoDto.initI18nLeaveName();
        Result<Boolean> checkResult = checkLeaveParams(leaveTypeSetInfoDto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_LEAVETYPE_LOCK_{0}_{1}", getUserInfo().getTenantId(), leaveTypeSetInfoDto.getLeaveCode());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, null);
        }
        cacheService.cacheSet(lockKey, "1", 60);
        try {
            return leaveTypeService.saveOrUpdateLeaveType(leaveTypeSetInfoDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        } finally {
            //执行结束，释放key值
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "修改休假类型")
    @PostMapping(value = "/update")
    @LogRecordAnnotation(success = "修改了{{#name}}", category = "修改", menu = "考勤设置-假期设置-假期规则")
    public Result<Boolean> updateLeaveType(@RequestBody LeaveTypeDto leaveTypeSetInfoDto) {
        leaveTypeSetInfoDto.initLeaveName();
        leaveTypeSetInfoDto.initI18nLeaveName();
        Result<Boolean> checkResult = checkLeaveParams(leaveTypeSetInfoDto);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        //重复提交校验
        String lockKey = MessageFormat.format("UPDATE_LEAVETYPE_LOCK_{0}_{1}", getUserInfo().getTenantId(), leaveTypeSetInfoDto.getLeaveTypeId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, null);
        }
        cacheService.cacheSet(lockKey, "1", 60);
        try {
            return leaveTypeService.saveOrUpdateLeaveType(leaveTypeSetInfoDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        } finally {
            //执行结束，释放key值
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "删除休假类型")
    @DeleteMapping(value = "/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-假期设置-假期规则")
    public Result<Boolean> deleteLeaveType(@RequestParam(value = "waGroupId", required = false) Integer waGroupId,
                                           @RequestParam("id") Integer id) {
        if (overTimeCheckService.getGroupLeaveRelCount(SessionHolder.getBelongOrgId(), id) > 0) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_CAN_NOT_DELETE, Boolean.FALSE);
        }
        try {
            int flag = leaveTypeService.checkBeforeDeleteLeaveType(id);
            if (flag == 1) {
                return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_DETLETE_FOR_QUOTA_REL, Boolean.TRUE);
            }
            leaveTypeService.deleteLeaveTypeById(id);
        } catch (Exception e) {
            log.error("LeaveTypeController.deleteLeaveType exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_CAN_NOT_DELETE, Boolean.FALSE);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @Deprecated
    @ApiOperation(value = "删除休假类型，更新考勤方案")
    @DeleteMapping(value = "/deleteLeaveType")
    public Result<Boolean> deleteGroupLeaveType(@RequestParam(value = "waGroupId", required = false) Integer waGroupId, @RequestParam("id") Integer id) {
        try {
            int flag = leaveTypeService.checkBeforeDeleteLeaveType(id);
            if (flag == 1) {
                return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_DETLETE_FOR_QUOTA_REL, Boolean.TRUE);
            }
            leaveTypeService.deleteLeaveTypeById(id);
            groupService.deleteGroupLeaveType(waGroupId, id);
            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception e) {
            log.error("LeaveTypeController.deleteGroupLeaveType exception {}", e.getMessage(), e);
        }
        return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_CAN_NOT_DELETE, Boolean.FALSE);
    }

    @ApiOperation(value = "查询假期类型定义数据")
    @GetMapping(value = "/getLeaveTypeDefList")
    public Result<?> getLeaveTypeDefList(@RequestParam(value = "dataType", required = false) String dataType) {
        List<Map> mapList = waConfigService.listLeaveTypeDef(getUserInfo().getTenantId(), SessionHolder.getLang());
        mapList.sort(Comparator.comparing(it -> {
            int index = Lists.list("年假", "调休", "产假", "哺乳假", "育儿假", "探亲假", "丧假").indexOf((String) it.get("text"));
            if (index < 0) {
                index = 100;
            }
            return index;
        }));
        if (!"data".equals(dataType)) {
            Map<String, Object> map = new HashMap<>();
            map.put("items", mapList);
            return ResponseWrap.wrapResult(map);
        } else {
            return ResponseWrap.wrapResult(mapList);
        }
    }

    @ApiOperation("获取假期规则")
    @GetMapping("/getLeaveTypes")
    public Result<ItemsResult<LeaveTypeGridVo>> getLeaveTypes(@RequestParam(value = "groupId", required = false) Integer groupId) {
        List<LeaveTypeInfoDto> list = leaveTypeService.getLeaveTypes(groupId, null, null);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseWrap.wrapResult(new ItemsResult<>());
        }
        return ResponseWrap.wrapResult(ItemsResult.of(ObjectConverter.convertList(list, LeaveTypeGridVo.class)));
    }

    @ApiOperation(value = "新增余额规则配置-按年发放")
    @PostMapping(value = "/saveQuotaConfigByYear")
    @LogRecordAnnotation(success = "编辑了{{#name}}的额度规则", category = "编辑", menu = "考勤设置-假期设置-假期规则-额度规则")
    public Result<Boolean> saveQuotaConfigByYear(@RequestBody LeaveQuotaConfigDto dto) {
        dto.initRuleName();
        dto.initI18nRuleName();
        var verify = dto.verify(Boolean.FALSE);
        if (verify != null) {
            return verify;
        }
        if (leaveTypeService.checkLeaveQuotaRuleName(dto.getLeaveTypeId(), dto.getConfigId(), dto.getRuleName())) {
            return ResponseWrap.wrapResult(AttendanceCodes.QUOTA_RULE_NAME_REPEAT, Boolean.FALSE);
        }
        try {
            leaveTypeService.saveOrUpdateLeaveQuotaConfig(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error("Save annual leave quota rule exception:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException("Save annual leave quota rule exception", e);
        }
    }

    @ApiOperation(value = "新增额度规则配置-调休")
    @PostMapping(value = "/saveQuotaConfigByTx")
    public Result<Boolean> saveQuotaConfigByTx(@RequestBody LeaveQuotaConfigDto dto) {
        dto.initRuleName();
        dto.initI18nRuleName();
        var verify = dto.verify(Boolean.TRUE);
        if (verify != null) {
            return verify;
        }
        try {
            leaveTypeService.saveOrUpdateTxLeaveQuotaConfig(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error("Save Tx quota rule exception:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail("Save Tx quota rule exception");
            }
            throw new RuntimeException("Save Tx quota rule exception", e);
        }
    }

    @ApiOperation(value = "删除额度规则")
    @DeleteMapping(value = "/deleteLeaveQuotaConfig")
    public Result<Boolean> deleteLeaveQuotaConfig(@RequestParam("configId") Long configId) {
        try {
            leaveTypeService.deleteLeaveQuotaConfig(configId);
            return Result.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("delete leave quota config exception:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "查询假期额度规则配置列表")
    @GetMapping(value = "/getQuotaConfigs")
    public Result<ItemsResult<LeaveQuotaRuleConfigVo>> getQuotaConfigs(@RequestParam("leaveTypeId") Integer leaveTypeId) {
        try {
            List<LeaveQuotaRuleConfigDto> list = leaveTypeService.getQuotaConfigs(leaveTypeId);
            return ResponseWrap.wrapResult(new ItemsResult<>(ObjectConverter.convertList(list, LeaveQuotaRuleConfigVo.class)));
        } catch (Exception e) {
            log.error("Select tx quota rule list exception：{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, new ItemsResult<>());
        }
    }

    @ApiOperation(value = "查询假期额度规则排序下拉列表")
    @GetMapping(value = "/getQuotaTypeSortList")
    public Result<ItemsResult<QuotaTypeSortListVo>> getQuotaTypeSortList(@RequestParam("leaveTypeId") Integer leaveTypeId) {
        return Result.ok(new ItemsResult<>(leaveTypeService.getQuotaTypeSortList(leaveTypeId)));
    }

    @ApiOperation(value = "检查是否自定义额度扣减规则")
    @GetMapping(value = "/checkForCustSort")
    public Result<Boolean> checkForCustSort(@RequestParam("leaveTypeId") Integer leaveTypeId) {
        return Result.ok(leaveTypeService.checkForCustSort(leaveTypeId));
    }

    @ApiOperation(value = "假期规则配置启用")
    @PostMapping(value = "/quota/config/enable")
    public Result<Boolean> enableQuotaConfig(@RequestParam("quotaConfigId") Long quotaConfigId) {
        leaveTypeService.enableQuotaConfig(quotaConfigId);
        return Result.ok();
    }

    @ApiOperation(value = "假期规则配置状态修改")
    @PostMapping(value = "/quota/config/status/change")
    public Result<Boolean> changeQuotaConfigStatus(@RequestBody QuotaConfigStatusChangeDto dto) {
        if ("DISABLED".equals(dto.getStatus())) {
            leaveTypeService.disableQuotaConfig(dto.getQuotaConfigId());
        } else {
            leaveTypeService.enableQuotaConfig(dto.getQuotaConfigId());
        }
        return Result.ok();
    }

    @ApiOperation(value = "假期规则配置停用")
    @PostMapping(value = "/quota/config/disable")
    public Result<Boolean> disableQuotaConfig(@RequestParam("quotaConfigId") Long quotaConfigId) {
        leaveTypeService.disableQuotaConfig(quotaConfigId);
        return Result.ok();
    }

    @ApiOperation(value = "新增余额规则配置-固定额度")
    @PostMapping(value = "/saveQuotaConfigByFix")
    @LogRecordAnnotation(success = "编辑了{{#name}}的额度规则", category = "编辑", menu = "考勤设置-假期设置-假期规则-额度规则")
    public Result<Boolean> saveQuotaConfigByFix(@RequestBody LeaveQuotaConfigDto dto) {
        Result result = dto.verify3();
        if (result != null) {
            return result;
        }
        try {
            leaveTypeService.saveOrUpdateFixedLeaveQuotaConfig(dto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error("Save fixed quota rule exception:{}", e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail("Save quota rule exception");
            }
            throw new RuntimeException("Save quota rule exception", e);
        }
    }

    @ApiOperation(value = "查询余额规则配置明细")
    @GetMapping(value = "/getQuotaConfigDetail")
    public Result<LeaveQuotaConfigVo> getQuotaConfigDetail(@RequestParam("leaveTypeId") Integer leaveTypeId) {
        LeaveQuotaConfigDto dto = leaveTypeService.getQuotaConfigDetail(leaveTypeId);
        LeaveQuotaConfigVo vo = ObjectConverter.convert(dto, LeaveQuotaConfigVo.class);
        if (CollectionUtils.isNotEmpty(dto.getQuotaGroups())) {
            vo.setQuotaGroups(dto.getQuotaGroups());
        }
        return ResponseWrap.wrapResult(vo);
    }

    @ApiOperation(value = "查询调休余额规则配置明细")
    @GetMapping(value = "/getLeaveQuotaConfigDetail")
    public Result<TxLeaveQuotaConfigVo> getTxLeaveQuotaConfigDetail(@RequestParam("configId") Long configId) {
        var optional = leaveTypeService.getTxLeaveQuotaConfigDetail(configId);
        if (!optional.isPresent()) {
            return Result.ok(new TxLeaveQuotaConfigVo());
        }
        TxLeaveQuotaConfigVo vo = FastjsonUtil.convertObject(optional.get(), TxLeaveQuotaConfigVo.class);
        if (CollectionUtils.isNotEmpty(optional.get().getQuotaGroups())) {
            vo.setQuotaGroups(optional.get().getQuotaGroups());
        }
        return ResponseWrap.wrapResult(vo);
    }

    @ApiOperation("获取员工固定配额假期规则")
    @GetMapping("/getEmpFixLeaveTypes")
    public Result<List<EmpFixLeaveTypeVo>> getEmpFixLeaveTypes(@RequestParam("empid") Long empid) {
        List<EmpFixLeaveTypeDto> list = leaveTypeService.getEmpFixLeaveTypes(empid);
        List<EmpFixLeaveTypeVo> voList = list.stream().map(dto -> {
            val vo = new EmpFixLeaveTypeVo();
            BeanUtils.copyProperties(dto, vo);
            return vo;
        }).collect(Collectors.toList());
        return ResponseWrap.wrapResult(voList);
    }

    @ApiOperation("获取按年发放假期规则下拉列表")
    @GetMapping("/getIssuedAnnuallyLeaveTypeSelectList")
    public Result<ItemsResult<LeaveQuotaRuleVo>> getIssuedAnnuallyLeaveTypeSelectList(@RequestParam(value = "groupId", required = false) Integer groupId,
                                                                                      @RequestParam(value = "type", required = false) Integer type) {
        List<LeaveTypeInfoDto> list = leaveTypeService.getLeaveTypes(groupId, QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex(), QuotaTypeEnum.ISSUED_ANNUALLY.getIndex());
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(new ItemsResult<>());
        }
        List<Integer> leaveTypeIds = list.stream().map(LeaveTypeInfoDto::getLeaveTypeId).distinct().collect(Collectors.toList());
        List<LeaveQuotaRuleConfigDto> quotaRules = leaveTypeService.getConfigListByIds(leaveTypeIds);
        if (CollectionUtils.isEmpty(quotaRules)) {
            return Result.ok(new ItemsResult<>());
        }
        Map<Integer, String> leaveTypeMap = list.stream().collect(Collectors.toMap(LeaveTypeInfoDto::getLeaveTypeId, LeaveTypeInfoDto::getLeaveName));
        quotaRules = quotaRules.stream().filter(r -> leaveTypeMap.containsKey(r.getLeaveTypeId())).sorted(Comparator.comparing(LeaveQuotaRuleConfigDto::getSort)).collect(Collectors.toList());
        quotaRules.forEach(q -> q.setLeaveName(leaveTypeMap.get(q.getLeaveTypeId())));
        if (null != type && type == 1) {
            List<LeaveQuotaRuleConfigDto> rules = new ArrayList<>();
            LeaveQuotaRuleConfigDto row = new LeaveQuotaRuleConfigDto();
            row.setConfigId(-1L);
            row.setRuleName(ResponseWrap.wrapResult(AttendanceCodes.CURRENT_LEAVE_QUOTA, null).getMsg());
            row.setLeaveName(ResponseWrap.wrapResult(AttendanceCodes.CURRENT_LEAVE_QUOTA, null).getMsg());
            rules.add(row);
            rules.addAll(quotaRules);
            return Result.ok(new ItemsResult<>(ObjectConverter.convertList(rules, LeaveQuotaRuleVo.class)));
        }
        return Result.ok(new ItemsResult<>(ObjectConverter.convertList(quotaRules, LeaveQuotaRuleVo.class)));
    }

    @ApiOperation("获取假期类型下拉列表")
    @GetMapping("/getLeaveTypeOptions")
    public Result<ItemsResult<KeyValueExt>> getLeaveTypeOptions(@RequestParam(value = "compensateType", required = false) Integer compensateType,
                                                                @RequestParam(value = "quotaType", required = false) Integer quotaType,
                                                                @RequestParam(value = "restrictionType", required = false) Integer restrictionType) {
        try {
            return ResponseWrap.wrapResult(ItemsResult.of(leaveTypeService.getLeaveTypeOptions(compensateType, quotaType, restrictionType)));
        } catch (Exception e) {
            log.error("getLeaveTypeOptions exception: {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new ItemsResult<>());
        }
    }

    @ApiOperation("启用")
    @PostMapping("/enable")
    public Result<Boolean> enable(@RequestBody LeaveEnabelReqDto dto) {
        try {
            return leaveTypeService.enable(dto);
        } catch (Exception ex) {
            log.error("LeaveTypeController.enable executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_ENABLE_FAIL, Boolean.FALSE);
        }
    }

    @ApiOperation("停用")
    @PostMapping("/unenable")
    public Result<Boolean> unenable(@RequestBody LeaveEnabelReqDto dto) {
        try {
            leaveTypeService.unenable(dto);
            return ResponseWrap.wrapResult(CommonConstant.TRUE);
        } catch (Exception ex) {
            log.error("LeaveTypeController.unenable executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_UNENABLE_FAIL, Boolean.FALSE);
        }
    }

    @ApiOperation("拖动排序")
    @PostMapping("/dragSort")
    public Result<Boolean> dragSort(@RequestBody DragSort dragSort) {
        return leaveTypeService.dragSort(dragSort);
    }

    @ApiOperation("员工假期额度类型扣减顺序拖动排序")
    @PostMapping("/configDragSort")
    public Result<Boolean> configDragSort(@RequestBody DragSortDto dragSort) {
        return leaveTypeService.dragSortQuotaRule(dragSort);
    }

    @ApiOperation("员工假期额度类型扣减顺序拖动排序（含留存本年扣减顺序）")
    @PostMapping("/sortForQuotaDeduct")
    public Result<Boolean> sortForQuotaDeduct(@RequestBody DragSortDto dragSort) {
        return leaveTypeService.sortForQuotaDeduct(dragSort);
    }

    @ApiOperation("清除员工假期额度类型扣减顺序排序（含留存本年扣减顺序）")
    @PostMapping("/clearSortForQuotaDeduct")
    public Result<Boolean> clearSortForQuotaDeduct(@RequestBody DragSortDto dragSort) {
        return leaveTypeService.clearSortForQuotaDeduct(dragSort);
    }


    @ApiOperation("保存假期额度说明")
    @PostMapping("/saveLeaveTypeRmk")
    public Result<Boolean> saveLeaveTypeRmk(@RequestBody WaLeaveType waLeaveType) {
        try {
            leaveTypeService.saveLeaveTypeRmk(waLeaveType);
            return Result.ok(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("LeaveTypeController.saveLeaveTypeRmk executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取假期额度说明")
    @GetMapping("/getLeaveTypeRmk")
    public Result<String> getLeaveTypeRmk(@RequestParam("leaveTypeId") Integer leaveTypeId) {
        try {
            return Result.ok(leaveTypeService.getLeaveTypeRmk(leaveTypeId));
        } catch (Exception ex) {
            log.error("LeaveTypeController.getLeaveTypeRmk executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @ApiOperation("获取假期类型code")
    @GetMapping("/getCodeByLeaveTypeDefId")
    public Result<String> getCodeByLeaveTypeDefId(@RequestParam("leaveTypeDefId") Integer leaveTypeDefId) {
        try {
            val code = leaveTypeDefService.getWaLeaveTypeDefList().stream()
                    .filter(it -> it.getLeaveTypeDefId().equals(leaveTypeDefId)).findFirst().get().getLeaveTypeDefCode();
            return Result.ok(code);
        } catch (Exception ex) {
            log.error("获取假期类型code executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_CODE_FAILED, null);
        }
    }
}
