package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.LangUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.bean.LeaveParamBean;
import com.caidao1.wa.service.WaLeaveService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaLeaveCoreService;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.leave.LeaveApplyResultDto;
import com.caidaocloud.attendance.service.application.service.ILeaveApplyService;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.impl.WfService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.SelectListMapper;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaRuleConfigDto;
import com.caidaocloud.attendance.service.interfaces.vo.*;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.qcloud.cos.utils.Md5Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/attendance/leaveapply/v1")
@Api(value = "/api/attendance/leaveapply/v1", description = "休假申请", tags = "v2.0")
public class LeaveApplyController {
    @Autowired
    private WaLeaveCoreService waLeaveCoreService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;
    @Resource
    private WaLeaveService waLeaveService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private WfService wfService;
    @Resource
    private SelectListMapper selectListMapper;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private ILeaveTypeService leaveTypeService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation(value = "休假申请列表")
    @PostMapping(value = "/getLeaveList")
    @Security(code = "LeaveApplyRecordList")
    public Result<AttendancePageResult<LeaveApplyListVo>> getLeaveList(@RequestBody LeaveApplyDto dto, HttpServletRequest request) {
        PageBean pageBean = PageUtil.getPageBean(dto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_APPLY_RECORD_LIST, "b");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("休假申请列表 DataScope = {}", dataScope);
        PageList<Map> leaveList = (PageList<Map>) leaveApplyService.getLeaveApplyList(dto, pageBean, sessionService.getUserInfo());
        if (CollectionUtils.isEmpty(leaveList)) {
            return ResponseWrap.wrapResult(new AttendancePageResult<>());
        }
        List<LeaveApplyListVo> listVos = JSON.parseArray(JSON.toJSONString(leaveList)).toJavaList(LeaveApplyListVo.class);
        AttendancePageResult<LeaveApplyListVo> pageResult = new AttendancePageResult<>(listVos, leaveList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return ResponseWrap.wrapResult(pageResult);
    }

    @ApiOperation(value = "用户本人休假申请列表", tags = "v1.1")
    @PostMapping(value = "/getUserLeaveList")
    @Security(code = "UserLeaveApplyRecordList")
    public Result<AttendancePageResult<UserLeaveApplyListVo>> getUserLeaveList(@RequestBody UserLeaveApplySearchDto dto) {
        return ResponseWrap.wrapResult(leaveApplyService.getUserLeaveApplyList(dto));
    }

    @ApiOperation(value = "门户用户本人休假申请列表", tags = "v1.2")
    @PostMapping(value = "/getUserLeaveListOfPortal")
    @Security(code = "UserLeaveApplyRecordList")
    public Result<AttendancePageResult<UserLeaveApplyListVo>> getUserLeaveListOfPortal(@RequestBody QueryPageBean dto) {
        return Result.ok(leaveApplyService.getUserLeaveApplyListOfPortal(dto));
    }

    @ApiOperation(value = "获取员工列表")
    @PostMapping(value = "/getEmpList")
    @Security(code = "LeaveApplyEmpList")
    public Result<AttendancePageResult> getEmpList(@RequestBody EmpListDto dto) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.LEAVE_APPLY_EMP_LIST, "emp");
        dto.setDataScope(dataScope);
        log.info("LeaveApplyService.getEmpList dataScope = {}", dataScope);
        return ResponseWrap.wrapResult(leaveApplyService.getEmpList(dto));
    }

    /**
     * PC后台 获取假期类型
     *
     * @param empid
     * @return
     */
    @ApiOperation(value = "根据empid获取假期类型")
    @GetMapping(value = "/getLeaveTypes")
    public Result<List<Map>> getLeaveTypes(@RequestParam(value = "empid") Long empid,
                                           @RequestParam(value = "quotaType", required = false) Integer quotaType) {
        try {
            UserInfo userInfo = this.getUserInfo();
            List<Map> originData = leaveApplyService.getLeaveTypeList(empid, quotaType);
            //增加自定义校验
            List<Map> list = LangUtil.langlist(waLeaveCoreService.filterLeaveType(Long.valueOf(userInfo.getTenantId()), userInfo.getTenantId(), empid, originData), SessionHolder.getLang(),
                    new String[]{"leaveName", "warnMsg"}, new String[]{"leave_name_lang", "warnMsgLang"});
            return ResponseWrap.wrapResult(list);
        } catch (Exception e) {
            log.error("waLeaveController.getLeaveTypes error，{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询假期额度规则下拉列表")
    @GetMapping(value = "/listLeaveQuotaRule")
    public Result<ItemsResult<KeyValue>> listLeaveQuotaRule() {
        List<LeaveQuotaRuleConfigDto> quotaRules = leaveTypeService.listByTenantId();
        if (CollectionUtils.isEmpty(quotaRules)) {
            return Result.ok(new ItemsResult<>());
        }
        List<KeyValue> options = new ArrayList<>();
        quotaRules.forEach(q -> options.add(new KeyValue(q.getRuleName(), q.getConfigId())));
        return Result.ok(new ItemsResult<>(options));
    }

    /**
     * PC后台 获取假期额度规则
     *
     * @param empid
     * @return
     */
    @ApiOperation(value = "根据empId获取假期额度规则")
    @GetMapping(value = "/getLeaveQuotaRules")
    public Result<ItemsResult<KeyValue>> getLeaveQuotaRules(@RequestParam(value = "empid") Long empid,
                                                            @RequestParam(value = "quotaType", required = false) Integer quotaType) {
        try {
            UserInfo userInfo = this.getUserInfo();
            List<Map> originData = leaveApplyService.getLeaveTypeList(empid, quotaType);
            //增加自定义校验
            List<Map> list = LangUtil.langlist(waLeaveCoreService.filterLeaveType(Long.valueOf(userInfo.getTenantId()), userInfo.getTenantId(), empid, originData), SessionHolder.getLang(), new String[]{"leaveName", "warnMsg"}, new String[]{"leave_name_lang", "warnMsgLang"});
            List<Integer> leaveTypeIds = list.stream().map(l -> (Integer) l.get("leaveType")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(leaveTypeIds)) {
                List<LeaveQuotaRuleConfigDto> quotaRules = leaveTypeService.getConfigListByIds(leaveTypeIds);
                if (CollectionUtils.isEmpty(quotaRules)) {
                    return Result.ok(new ItemsResult<>());
                }
                List<KeyValue> options = new ArrayList<>();
                quotaRules.forEach(q -> {
                    options.add(new KeyValue(q.getRuleName(), q.getConfigId()));
                });
                return Result.ok(new ItemsResult<>(options));
            }
        } catch (Exception e) {
            log.error("waLeaveController.getLeaveQuotaRules error，{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(new ItemsResult<>());
    }

    @ApiOperation(value = "获取用户假期类型", tags = "v1.1")
    @GetMapping(value = "/getUserLeaveTypes")
    public Result<List<Map>> getUserLeaveTypes() {
        try {
            val list = leaveApplyService.getUserLeaveTypes();
            return ResponseWrap.wrapResult(list);
        } catch (Exception e) {
            log.error("waLeaveController.getLeaveTypes error，{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取用户假期类型配额", tags = "v1.1")
    @GetMapping(value = "/getUserLeaveTypesQuota")
    public Result<List<HolidayQuotaVo>> getUserLeaveTypesQuota() {
        try {
            val list = leaveApplyService.getUserLeaveTypeQuotas();
            return ResponseWrap.wrapResult(list);
        } catch (Exception e) {
            log.error("getUserLeaveTypesQuota error，{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取计算的申请时间")
    @PostMapping(value = "/getLeaveTotalTime")
    public Result<LeaveApplyTimeVo> getLeaveTotalTime(@RequestBody LeaveApplySaveDto leaveApplySaveDto) {
        String lockKey = getIdempotentString(leaveApplySaveDto);
        if (!cacheService.containsKey(lockKey)) {
            cacheService.cacheValue(lockKey, "1", 300);
        }
        try {
            return waLeaveService.calLeaveTime(leaveApplySaveDto, lockKey);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("休假申请失败");
        }
    }

    private String getIdempotentString(LeaveApplySaveDto dto) {
        return Md5Utils.md5Hex(String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s",
                dto.getEmpid(), dto.getLeaveTypeId(), dto.getStartTime(), dto.getEndTime(),
                dto.getShalfDay(), dto.getEhalfDay(), dto.getShowMin(), dto.getStime(), dto.getEtime()));
    }

    @ApiOperation(value = "获取计算的申请时间", tags = "v1.1")
    @PostMapping(value = "/getUserLeaveTotalTime")
    public Result<LeaveApplyTimeVo> getUserLeaveTotalTime(@RequestBody UserLeaveApplySaveDto leaveApplySaveDto) {
        try {
            UserInfo userInfo = this.getUserInfo();
            String lockKey = getIdempotentString(leaveApplySaveDto, userInfo.getStaffId());
            if (!cacheService.containsKey(lockKey)) {
                cacheService.cacheValue(lockKey, "1", 300);
            }
            LeaveApplySaveDto dto = new LeaveApplySaveDto();
            BeanUtils.copyProperties(leaveApplySaveDto, dto);
            dto.setEmpid(userInfo.getStaffId());
            Result checkResult = waLeaveService.checkLeaveApplyParams(dto, false, false);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            if (leaveApplySaveDto.getDailyDuration() == null) {
                if (leaveApplySaveDto.getShowMin() == 1 && leaveApplySaveDto.getStartTime().equals(leaveApplySaveDto.getEndTime()) && leaveApplySaveDto.getStime().equals(leaveApplySaveDto.getEtime())) {
                    return ResponseWrap.wrapResult(AttendanceCodes.START_TIME_EQUAL_END, null);
                }
            }
            Map map = waLeaveService.getLeaveTotalTime(Long.valueOf(userInfo.getTenantId()), userInfo.getTenantId(), userInfo.getUserId(), true, ObjectConverter.convert(dto, LeaveParamBean.class));
            if ("-1".equals(map.get("status").toString())) {
                return Result.fail(map.get("message").toString());
            }
            map.put("duration", map.remove("data"));
            map.put("secretKey", lockKey);
            return ResponseWrap.wrapResult(JSON.parseObject(JSON.toJSONString(map), LeaveApplyTimeVo.class));
        } catch (Exception e) {
            log.error("获取计算的休假申请时间异常：{}", e.getMessage(), e);
            return Result.fail("获取计算的休假申请时间失败");
        }
    }

    private String getIdempotentString(UserLeaveApplySaveDto dto, Long empId) {
        return Md5Utils.md5Hex(String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s",
                empId, dto.getLeaveTypeId(), dto.getStartTime(), dto.getEndTime(),
                dto.getShalfDay(), dto.getEhalfDay(), dto.getShowMin(), dto.getStime(), dto.getEtime()));
    }

    @PostMapping(value = "/saveUserLeaveApply")
    @ApiOperation(value = "保存用户假期申请", tags = "v1.1")
    public Result saveUserLeaveApply(@RequestBody UserLeaveApplySaveDto userleaveApplySaveDto) {
        UserInfo userInfo = sessionService.getUserInfo();
        String idempotentLockKey = userleaveApplySaveDto.getSecretKey();
        if (StringUtil.isNotBlank(idempotentLockKey) && !cacheService.containsKey(idempotentLockKey)) {
            return Result.fail("请勿频繁操作！");
        }
        cacheService.remove(idempotentLockKey);
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_LEAVE_APPLY_LOCK_{0}_{1}", userInfo.getBelongOrgId(), userInfo.getEmpid());
        if (cacheService.containsKey(lockKey)) {
            return Result.fail("请勿频繁操作！");
        }
        cacheService.cacheValue(lockKey, "1", 15);
        LeaveApplySaveDto leaveApplySaveDto = new LeaveApplySaveDto();
        BeanUtils.copyProperties(userleaveApplySaveDto, leaveApplySaveDto);
        leaveApplySaveDto.setEmpid(userInfo.getStaffId());
        LeaveTypeDto leaveType = leaveTypeService.getLeaveTypeById(userleaveApplySaveDto.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        if (leaveType.getReasonMust() != null && leaveType.getReasonMust() && StringUtil.isBlank(userleaveApplySaveDto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_MUST, Boolean.FALSE);
        }
        try {
            Result checkResult = waLeaveService.checkLeaveApplyParams(leaveApplySaveDto, true, leaveType.getLeaveType() == 4);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            Result leaveApplyResult = leaveApplyService.saveLeaveApply(leaveApplySaveDto, true);
            if (!leaveApplyResult.isSuccess()) {
                return leaveApplyResult;
            }
            LeaveApplyResultDto applyResultDto = (LeaveApplyResultDto) leaveApplyResult.getData();
            return Result.ok(applyResultDto.getBusinessKey());
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "请假申请", tags = "v1.1")
    @PostMapping(value = "/saveLeaveApply")
    @LogRecordAnnotation(success = "申请了{empName{#empId}}的{{#leaveName}}", category = "申请休假", menu = "休假管理-休假记录-休假记录")
    public Result saveLeaveApply(@RequestBody LeaveApplySaveDto leaveApplySaveDto) {
        UserInfo userInfo = this.getUserInfo();
        String idempotentLockKey = leaveApplySaveDto.getSecretKey();
        if (StringUtil.isNotBlank(idempotentLockKey) && !cacheService.containsKey(idempotentLockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, null);
        }
        cacheService.remove(idempotentLockKey);
        String lockKey = MessageFormat.format("SAVE_LEAVE_APPLY_LOCK_{0}_{1}", userInfo.getBelongOrgId(), leaveApplySaveDto.getEmpid());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, null);
        }
        cacheService.cacheValue(lockKey, "1", 15);
        LeaveTypeDto leaveType = leaveTypeService.getLeaveTypeById(leaveApplySaveDto.getLeaveTypeId());
        if (null == leaveType) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        LogRecordContext.putVariable("leaveName", leaveType.getLeaveName());
        if (leaveType.getReasonMust() != null && leaveType.getReasonMust() && StringUtil.isBlank(leaveApplySaveDto.getReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_MUST, Boolean.FALSE);
        }
        try {
            Result<?> checkResult = waLeaveService.checkLeaveApplyParams(leaveApplySaveDto, true, leaveType.getLeaveType() == 4);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            leaveApplySaveDto.setEmpid(leaveApplySaveDto.getEmpid());
            Result leaveApplyResult = leaveApplyService.saveLeaveApply(leaveApplySaveDto, true);
            if (!leaveApplyResult.isSuccess()) {
                return leaveApplyResult;
            }
            LeaveApplyResultDto applyResultDto = (LeaveApplyResultDto) leaveApplyResult.getData();
            LogRecordContext.putVariable("empId", leaveApplySaveDto.getEmpid());

            return Result.ok(applyResultDto.getBusinessKey());
        } catch (Exception e) {
            log.error("Save leave apply failed：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "休假附件重新保存")
    @PostMapping(value = "/saveLeaveAttachments")
    public Result<Boolean> saveLeaveAttachments(@RequestBody AttachmentDto attachmentDto) {
        try {
            leaveApplyService.saveLeaveAttachments(attachmentDto);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Save leave saveLeaveAttachments failed：{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "请假申请重新发起", tags = "v1.1")
    @PostMapping(value = "/saveLeaveReApply")
    public Result saveLeaveReApply(@RequestBody LeaveApplySaveDto leaveApplySaveDto) throws Exception {
        Result checkResult = waLeaveService.checkLeaveApplyParams(leaveApplySaveDto, true, false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Result leaveApplyResult = leaveApplyService.saveLeaveApply(leaveApplySaveDto, true);
        if (!leaveApplyResult.isSuccess()) {
            return leaveApplyResult;
        }
        LeaveApplyResultDto applyResultDto = (LeaveApplyResultDto) leaveApplyResult.getData();
        return Result.ok(applyResultDto.getBusinessKey());
    }

    @PostMapping(value = "/revokeEmpLeave")
    @ApiOperation(value = "撤销员工请假单据", tags = "v1.1")
    @LogRecordAnnotation(success = "撤销了{empName{#empId}}的{{#leaveName}}", category = "撤销", menu = "休假管理-休假记录-休假记录")
    public Result<Boolean> revokeEmpLeave(@RequestBody RevokeEmpLeaveDto dto) {
        if (StringUtils.isEmpty(dto.getRecokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, null);
        }
        if (dto.getRecokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, null);
        }
        UserInfo userInfo = this.getUserInfo();
        try {
            return leaveApplyService.revokeEmpLeave(dto, userInfo, true, true);
        } catch (Exception e) {
            log.error("LeaveApplyController.revokeEmpLeave executes exception, {}", e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_FAILED, null);
        }
    }

    @ApiOperation("查看明细")
    @GetMapping(value = "/getWfFuncDetail")
    public Result<WfDetailVo> getWfFuncDetail(@RequestParam("businessKey") String businessKey,
                                              @RequestParam(value = "nodeId", required = false) String nodeId,
                                              @RequestParam(value = "funcType", required = false) Integer funcType) throws Exception {
        WfDetailDto detailDto = wfService.getWfFuncDetail(businessKey, nodeId, funcType);
        if (businessKey != null) {
            leaveApplyService.appendHomeLeaveInfo(businessKey, detailDto);
        }
        WfDetailVo vo = ObjectConverter.convert(detailDto, WfDetailVo.class);
        vo.setData(detailDto.getItems());
        vo.getData().forEach(it->{
            if("岗位".equals(it.getText())){
                if (!"enabled".equals(postTxtShowCode)) {
                    String postTxt = (String) it.getValue();
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(postTxt) && postTxt.contains("(")) {
                        postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                        it.setValue(postTxt);
                    }
                }
            }
        });
        return ResponseWrap.wrapResult(vo);
    }

    @ApiOperation("查看流程详情")
    @GetMapping(value = "/getWfDetail")
    @LogRecordAnnotation(success = "查看了数据", category = "查看", menu = "{{#menu}}")
    public Result<WfResponseDto> getWfDetail(@RequestParam("businessKey") String businessKey) {
        UserInfo userInfo = sessionService.getUserInfo();
        if (StringUtils.isNotEmpty(businessKey)) {
            String[] split = businessKey.split("_");
            BusinessCodeEnum byCode = BusinessCodeEnum.getByCode(split[1]);
            LogRecordContext.putVariable("menu", byCode == null ? "" : byCode.getMenu());
        }
        WfResponseDto wfDetail = wfService.getWfDetail(userInfo.getTenantId(), businessKey, false);
        if (businessKey != null) {
            leaveApplyService.appendHomeLeaveInfo(businessKey, wfDetail);
        }

        return ResponseWrap.wrapResult(wfDetail);
    }

    @ApiOperation("重新发起 获取详情")
    @GetMapping(value = "/getEmpLeaveById")
    public Result getEmpLeaveById(@RequestParam("waid") Integer waid) {
        return ResponseWrap.wrapResult(leaveApplyService.getEmpLeaveById(waid));
    }

    @ApiOperation("获取假期类型下拉框")
    @GetMapping(value = "/getLeaveTypeList")
    public Result getLeaveTypeList() {
        UserInfo userInfo = this.getUserInfo();
        String belongId = userInfo.getTenantId();
        Map map = new HashMap();
        map.put("items", selectListMapper.getLeaveTypeList(belongId));
        return Result.ok(map);
    }

    @ApiOperation("获取休假状态下拉框")
    @GetMapping(value = "/getLeaveStatusList")
    public Result getLeaveStatusList() {
        Map map = new HashMap();
        map.put("items", leaveApplyService.getLeaveStatusList());
        return Result.ok(map);
    }

    @ApiOperation("获取周期下拉框")
    @GetMapping(value = "/getPeriodMonthSelect")
    public Result getPeriodMonthSelect() {
        Calendar calendar = Calendar.getInstance();
        Integer period = calendar.get(Calendar.YEAR) * 100 + 1;
        Map map = new HashMap();
        List<KeyValueIntegerVo> list = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            for (int j = 0; j < 2; j++) {
                Integer finalIJ = period - j * 100 + i;
                list.add(new KeyValueIntegerVo() {{
                    setValue(finalIJ);
                    setText(finalIJ);
                }});
            }
        }
        map.put("items", list);
        return Result.ok(map);
    }

    @ApiOperation("销假催办")
    @PostMapping(value = "/urge")
    @LogRecordAnnotation(success = "一键催办[{{#content}}]的销假申请", category = "一键催办", menu = "休假管理-休假记录-休假记录")
    public Result<Boolean> urge(@RequestBody List<Integer> leaveIds) {
        return Result.ok(leaveApplyService.urge(leaveIds));
    }

    @ApiOperation("获取产假可选时间范围")
    @PostMapping("/getMaternityLeaveRange")
    public Result<MaternityLeaveRangeVo> getMaternityLeaveRange(@RequestBody MaternityLeaveRangeDto dto) {
        try {
            return leaveApplyService.getMaternityLeaveRange(dto);
        } catch (Exception e) {
            log.error("LeaveApplyController getMaternityLeaveRange exception:{}", e.getMessage(), e);
            return Result.fail();
        }
    }

    @ApiOperation("获取探亲假可选时间范围")
    @GetMapping("/getHomeLeaveRange")
    public Result<Long> getHomeLeaveRange(@RequestParam Long leaveTypeId,
                                          @RequestParam(required = false) Long empId,
                                          @RequestParam long startTime,
                                          @RequestParam String visitingReason,
                                          @RequestParam String marriage) {
        try {
            return Result.ok(leaveApplyService.getHomeLeaveRange(leaveTypeId, empId, startTime, visitingReason, marriage));
        } catch (Exception e) {
            log.error("LeaveApplyController getMaternityLeaveRange exception:{}", e.getMessage(), e);
            return Result.fail();
        }
    }

    @ApiOperation("获取探亲假可选类型")
    @GetMapping("/getHomeLeaveType")
    public Result<List<Map<String, String>>> getHomeLeaveType() {
        return Result.ok(Lists.list(Maps.map("text", ResponseWrap.wrapResult(AttendanceCodes.VISITING_PARENT, null).getMsg(), "value", "visiting_parents"),
                Maps.map("text", ResponseWrap.wrapResult(AttendanceCodes.VISITING_SPOUSE, null).getMsg(), "value", "visiting_spouse")));
    }

    @ApiOperation("获取探亲假当前余额")
    @GetMapping("/getHomeLeaveAvailableQuota")
    public Result<String> getHomeLeaveAvailableQuota(
            @RequestParam Long leaveTypeId,
            @RequestParam(required = false) Long empId,
            @RequestParam String visitingReason,
            @RequestParam String marriage) {
        try {
            return leaveApplyService.getHomeLeaveAvailableQuotaText(leaveTypeId, empId, visitingReason, marriage);
        } catch (Exception e) {
            log.error("LeaveApplyController getHomeLeaveAvailableQuota exception:{}", e.getMessage(), e);
            return Result.fail();
        }
    }

}