<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="jdbc.properties"/>
    <context id="my_tables" targetRuntime="MyBatis3">
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 注释相关设置 -->
        <commentGenerator>
            <!-- 是否去除自动生成的注释, true: 是; false: 否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!--数据库连接的信息：驱动类、连接地址、用户名、密码 -->
        <jdbcConnection driverClass="${jdbc.driver}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.username}"
                        password="${jdbc.password}">
        </jdbcConnection>
        <!-- 默认false, 把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer; 为 true 时把 JDBC DECIMAL 和
        NUMERIC 类型解析为 java.math.BigDecimal -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- 生成POJO类的位置. targetPackage为包名, targetProject为项目位置, 采用相对路径 -->
        <javaModelGenerator targetPackage="com.caidaocloud.attendance.service.infrastructure.repository.po"
                            targetProject="src/main/java">
            <!-- enableSubPackages: 是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
            <!-- 从数据库返回的值被清理前后的空格  -->
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- mapper映射文件生成的位置 -->
        <sqlMapGenerator targetPackage="com.caidaocloud.attendance.service.infrastructure.repository.sqlmap"
                         targetProject="src/main/java">
            <!-- enableSubPackages: 是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>
        <!-- mapper接口生成的位置 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.caidaocloud.attendance.service.infrastructure.repository.mapper"
                             targetProject="src/main/java">
            <!-- enableSubPackages: 是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>
        <!-- 指定数据库表 -->
        <table tableName="wa_leave_extension"
               domainObjectName="WaLeaveExtension"
               mapperName="WaLeaveExtensionMapper"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false"
               enableSelectByExample="false"
               selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="false"/>
        </table>
    </context>
</generatorConfiguration>