package com.caidaocloud.attendance.service.interfaces.vo.overtime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量加班分页列表VO
 *
 * <AUTHOR>
 * @Date 2024/6/24
 */
@Data
@ApiModel("批量加班分页列表VO")
public class BatchOvertimePageListVo {
    @ApiModelProperty("主键ID")
    private Long batchId;

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("姓名")
    private String empName;

    @ApiModelProperty("任职组织")
    private String orgName;

    @ApiModelProperty("事件时间")
    private String timeSlot;

    @ApiModelProperty("休假申请时长")
    private String timeDuration;

    @ApiModelProperty("有效加班时长")
    private String validTimeDuration;

    @ApiModelProperty("申请日期")
    private Long createDate;

    @ApiModelProperty("审批状态")
    private Integer status;

    @ApiModelProperty("审批状态名称")
    private String statusName;

    @ApiModelProperty("审批时间")
    private Long lastApprovalTime;

    @ApiModelProperty("流程业务主键")
    private String businessKey;

    @ApiModelProperty("有效时长")
    private Float relTimeDuration;
}
