package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/2/9
 */
@Data
public class RoundShiftDto implements Serializable {
    private static final long serialVersionUID = 5633164650085549870L;
    @ApiModelProperty("排班周期班次记录ID")
    private Integer roundShiftId;

    @ApiModelProperty("排班计划ID")
    private Integer workRoundId;

    @ApiModelProperty("班次ID")
    private Integer shiftDefId;

    @ApiModelProperty("顺序")
    private Integer roundNo;

    @ApiModelProperty("班次名称")
    private String shiftDefName;

    @ApiModelProperty("用户ID")
    private Long crtuser;
}
