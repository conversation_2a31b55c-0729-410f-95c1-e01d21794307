package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserLeaveApplyListVo {
    @ApiModelProperty("假期类型ID")
    private Integer leaveId;
    @ApiModelProperty("假期类型名称")
    private String leaveName;
    @ApiModelProperty("申请时间")
    private long applyTime;
    @ApiModelProperty("状态")
    private Short status;
    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("开始时间")
    private long startTime;
    @ApiModelProperty("结束时间")
    private long endTime;

    @ApiModelProperty("申请时长")
    private Float duration;
    @ApiModelProperty("销假时长")
    private Float cancelTimeDuration;
    @ApiModelProperty("实际时长")
    private Float actualTimeDuration;
    @ApiModelProperty("休假单位")
    private String timeUnitName;

    @ApiModelProperty("撤销原因")
    private String revokeReason;

    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("查看的工作流id")
    private String businessKey;

    @ApiModelProperty("销假审批时间")
    private String leaveCancelTime;

    @ApiModelProperty("休假审批时间")
    private Long lastApprovalTime;

    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;
    @ApiModelProperty("休假事由")
    private String reason;
    @ApiModelProperty("休假时间")
    private String timeSlot;
    @ApiModelProperty("假期类型编码")
    private String leaveTypeCode;
    @ApiModelProperty("休假状态 1 销假中 2 已完成 3 未销假")
    private Integer leaveStatus;
    @ApiModelProperty("是否允许多次销假")
    private boolean allowManyLeaveCancel;
}
