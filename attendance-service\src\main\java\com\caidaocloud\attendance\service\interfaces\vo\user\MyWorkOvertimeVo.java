package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyWorkOvertimeVo {
    private Integer otId;
    @ApiModelProperty("审批状态，2：已通过;9：已撤销;0:暂存;1:审批中;3:已拒绝;4:已作废；5：已退回")
    private Short status;
    @ApiModelProperty("加班类型，1：工作日加班；2：休息日加班；3：法定假日加班；4：特殊休日加班")
    private Short dateType;
    private Integer compensateType;
    @ApiModelProperty("加班申请时间")
    private Long applyTime;
    private String timeSlot;
    @ApiModelProperty("加班类型说明")
    private String dateTypeName;
    @ApiModelProperty("加班补偿类型")
    private String compensateTypeName;
    @ApiModelProperty("审批状态说明")
    private String statusName;
    private String procInstId;
    private String businessKey;
    @ApiModelProperty("加班开始时间")
    private Long startTime;
    @ApiModelProperty("加班结束时间")
    private Long endTime;
    @ApiModelProperty("加班时长")
    private Integer otDuration;

    private String startTimeTxt;
    private String endTimeTxt;
    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;
    @ApiModelProperty("有效加班时长")
    private Float duration;
    private String reason;
    private Long revokeId;
    private Integer detailId;
    private String revokeReason;
}
