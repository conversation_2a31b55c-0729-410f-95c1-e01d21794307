package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.service.dto.EquipmentMaintainingParam;
import com.caidao1.system.mybatis.model.SysWifiInfo;
import com.caidao1.system.service.EquipmentMaintainingService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.KeywordBasePageDto;
import com.caidaocloud.attendance.service.interfaces.dto.WaClockInWifiDto;
import com.caidaocloud.attendance.service.interfaces.vo.WifiVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/attendance/wifi/v1")
@Api(value = "/api/attendance/wifi/v1", description = "wifi设置接口")
public class ClockWifiController {

    @Resource
    private EquipmentMaintainingService equipmentMaintainingService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation(value = "获取Wi-Fi列表")
    @PostMapping("/getWifiList")
    public Result<AttendancePageResult> getWifiList(@RequestBody KeywordBasePageDto dto) throws Exception {
        PageBean pageBean = PageUtil.getPageBean(dto);
        EquipmentMaintainingParam param = new EquipmentMaintainingParam();
        param.setText_query(dto.getKeyworks());
        param.setBelongOrgId(getUserInfo().getTenantId());
        List<Map> result = equipmentMaintainingService.querySysWifiInfo(param, pageBean);
        PageList<Map> pageList = (PageList<Map>) result;
        List<WifiVo> wifiVos = JSON.parseArray(JSON.toJSONString(result)).toJavaList(WifiVo.class);
        AttendancePageResult pageResult = new AttendancePageResult(wifiVos, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return Result.ok(pageResult);
    }

    @ApiOperation("wifi保存")
    @PostMapping("/saveWifiEquipment")
    public Result saveEquipment(@RequestBody WaClockInWifiDto dto) {
        SysWifiInfo sysWifiInfo = ObjectConverter.convert(dto, SysWifiInfo.class);
        try {
            equipmentMaintainingService.saveWifiEquipment(sysWifiInfo);
        } catch (Exception e) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, e.getMessage(), null);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @DeleteMapping("/delWifiEquipment")
    public Result<Boolean> delWifiEquipment(@RequestParam("wifiId") Integer wifiId) {
        SysWifiInfo sysWifiInfo = new SysWifiInfo();
        sysWifiInfo.setBelongOrgId(getUserInfo().getTenantId());
        sysWifiInfo.setWifiId(wifiId);
        try {
            equipmentMaintainingService.delWifiEquipment(sysWifiInfo);
        } catch (Exception e) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, e.getMessage(), null);
        }
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }
    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }
}
