package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidao1.ioc.util.ListHelper;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.impl.GetOrgTreeService;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.common.VerifyInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.VerifyResult;
import com.caidaocloud.attendance.service.interfaces.dto.group.*;
import com.caidaocloud.attendance.service.interfaces.vo.ChildOrgVo;
import com.caidaocloud.attendance.service.interfaces.vo.StartEndVo;
import com.caidaocloud.attendance.service.interfaces.vo.group.EmpGroupIdVo;
import com.caidaocloud.attendance.service.interfaces.vo.group.EmpGroupVo;
import com.caidaocloud.attendance.service.interfaces.vo.group.GroupInfoVo;
import com.caidaocloud.attendance.service.interfaces.vo.group.GroupVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;

@Slf4j
@RestController
@Api(value = "/api/attendance/group/v1", description = "考勤方案接口")
@RequestMapping("/api/attendance/group/v1")
public class GroupController {

    @Resource
    private IGroupService groupService;
    @Resource
    private WaAttendanceConfigService waConfigService;
    @Resource
    private GetOrgTreeService getOrgTreeService;
    @Resource
    private ISessionService sessionService;
    @Resource
    private CacheService cacheService;

    @Autowired
    private AsyncExecService asyncService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    private static final String SYN_EMP_GROUP_PROCESS = "SYN_EMP_GROUP_PROCESS_";

    private static final String SYN_EMP_GROUP_MSG_PROCESS = "SYN_EMP_GROUP_MSG_PROCESS_";

    @ApiOperation("新增编辑考勤方案")
    @PostMapping("/save")
    public Result<Boolean> saveGroup(@RequestBody GroupInfoDto dto) {
        dto.initWaGroupName();
        dto.initI18nWaGroupName();
        if (StringUtils.isNotBlank(dto.getWaGroupName()) && dto.getWaGroupName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        try {
            return groupService.saveGroup(dto);
        } catch (Exception ex) {
            log.error("GroupController.saveGroup executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation(value = "考勤方案设置-保存基本信息", tags = "v1.1")
    @PostMapping("/saveGroupInfo")
    @LogRecordAnnotation(success = "{{#operate}}了{{#name}}", category = "{{#operate}}", menu = "考勤设置-方案设置")
    public Result<Boolean> saveGroupInfo(@RequestBody PlanGroupInfoDto dto) {
        dto.initWaGroupName();
        dto.initI18nWaGroupName();
        if (StringUtils.isNotBlank(dto.getWaGroupName()) && dto.getWaGroupName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        try {
            return groupService.saveGroupInfo(dto);
        } catch (Exception ex) {
            log.error("GroupController.saveGroupInfo executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取考勤方案分页列表")
    @PostMapping("/list")
    public Result<AttendancePageResult<GroupVo>> getGroupList(@RequestBody AttendanceBasePage basePage) {
        try {
            PageBean pageBean = PageUtil.getPageBean(basePage);
            AttendancePageResult<GroupDto> result = groupService.getGroupList(pageBean);
            List<GroupVo> list = ObjectConverter.convertList(result.getItems(), GroupVo.class);
            return ResponseWrap.wrapResult(new AttendancePageResult<>(list, result.getPageNo(), result.getPageSize(), result.getTotal()));
        } catch (Exception ex) {
            log.error("GroupController.getGroupList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>());
        }
    }

    @ApiOperation("获取考勤方案详情")
    @GetMapping("/detail")
    public Result<GroupInfoVo> getGroupInfo(@RequestParam("id") Integer id) {
        try {
            GroupDetailDto dto = groupService.getGroupInfo(id);
            if (null == dto) {
                return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(dto, GroupInfoVo.class));
        } catch (Exception ex) {
            log.error("GroupController.getGroupInfo executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_DETAIL_FAILED, null);
        }
    }

    @ApiOperation("删除考勤方案")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了{{#name}}", category = "删除", menu = "考勤设置-方案设置")
    public Result<Boolean> deleteGroup(@RequestParam("id") Integer id) {
        try {
            GroupDetailDto dto = groupService.getGroupInfo(id);
            LogRecordContext.putVariable("name", dto.getWaGroupName());
            groupService.deleteGroup(id);

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("GroupController.deleteGroup executes exception, {}", ex.getMessage(), ex);
            if (ex instanceof ServerException) {
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取考勤方案下拉框选项")
    @GetMapping("/selectGroupOptions")
    public Result<ItemsResult<KeyValue>> selectGroupOptions() {

        try {
            List<Map> groups = groupService.selectGroupOptions();
            List<KeyValue> items = new ArrayList<>();
            groups.forEach(group -> items.add(new KeyValue(String.valueOf(group.get("wa_group_name")), (Integer) group.get("wa_group_id"))));
            return ResponseWrap.wrapResult(new ItemsResult<>(items));
        } catch (Exception ex) {
            log.error("GroupController.deleteGroup executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), new ItemsResult<>());
        }
    }

    @ApiOperation("取得周期 (用于账套下拉选择考勤方案的开始截止时间)")
    @GetMapping("/getGroupCycle")
    public Result<StartEndVo> getGroupCycle(@RequestParam("sysPeriodMonth") Integer sysPeriodMonth, @RequestParam("groupId") Integer groupId) {

        try {
            Map map = waConfigService.getWaGroupCycle(sysPeriodMonth, groupId);
            StartEndVo vo = JSON.parseObject(JSON.toJSONString(map), StartEndVo.class);
            return ResponseWrap.wrapResult(vo);
        } catch (Exception ex) {
            log.error("SobController.getGroupCycle executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), null);
        }
    }

    @ApiOperation("校验已选员工是否已适用某方案")
    @PostMapping("/verifySelectedEmployees")
    public Result<ItemsResult<VerifyResult>> verifySelectedEmployees(@RequestBody VerifyInfoDto dto) {
        if (CollectionUtils.isEmpty(dto.getEmpIds())) {
            return ResponseWrap.wrapResult(new ItemsResult<>());
        }
        Integer planId = dto.getEntityId() != null ? Integer.valueOf(String.valueOf(dto.getEntityId())) : null;
        return ResponseWrap.wrapResult(new ItemsResult<>(groupService.verifySelectedEmployees(planId, dto.getEmpIds())));
    }

    @ApiOperation("取得员工分组列表")
    @PostMapping("/getEmpGroupList")
    public Result<AttendancePageResult<EmpGroupVo>> getEmpGroupList(@RequestBody EmpGroupDto dto) {
        if (null == dto.getPlanId()) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "方案id为空", null);
        }
        PageBean pageBean = PageUtil.getPageBean(dto);
        List list = groupService.getEmpGroupList(dto.getPlanId(), pageBean);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseWrap.wrapResult(new AttendancePageResult<>());
        }
        PageList<Map> pageList = (PageList<Map>) list;
        List<EmpGroupVo> items = JSON.parseArray(JSON.toJSONString(pageList), EmpGroupVo.class);
        return ResponseWrap.wrapResult(new AttendancePageResult<>(items, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount()));
    }

    @ApiOperation(value = "获取部门树信息")
    @GetMapping(value = "/getSysCorpOrgTreeByTid")
    public Result<List<ChildOrgVo>> getSysCorpOrgTreeByTid() {
        List<ChildOrgVo> list = getOrgTreeService.getSysCorpOrgTreeByTid(UserContext.getCorpId());
        return Result.ok(list);
    }

    @ApiOperation("考勤方案设置-保存休假规则")
    @PostMapping("/saveLeaveRule")
    public Result<Boolean> saveGroupLeaveRule(@RequestBody GroupLeaveRuleSaveDto saveDto) {
        groupService.saveGroupLeaveRule(saveDto);
        return Result.ok();
    }

    @ApiOperation("考勤方案设置-保存加班规则")
    @PostMapping("/saveBaseInfo")
    public Result<Boolean> saveGroupBaseInfo(@RequestBody GroupBaseInfoDto dto) {
        if (dto.getOutLimitControl() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.OUT_LIMIT_CONTROL_EMPTY, Boolean.FALSE);
        }
        if (dto.getOutLimit() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.OUT_LIMIT_EMPTY, Boolean.FALSE);
        }
        if (dto.getOutLimit() > 999) {
            return ResponseWrap.wrapResult(AttendanceCodes.OUT_LIMIT_TOO_BIG, Boolean.FALSE);
        }
        try {
            Optional<GroupDetailDto> opt = Optional.ofNullable(groupService.getGroupInfo(dto.getWaGroupId()));
            if (!opt.isPresent()) {
                return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, Boolean.FALSE);
            }
            groupService.saveGroupBaseInfo(dto);
        } catch (Exception e) {
            log.error("Save group base info exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @ApiOperation("取得员工考勤方案")
    @PostMapping("/getEmpWaGroupList")
    public Result getEmpWaGroupList(@RequestBody AttEmpGroupReqDto dto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        UserInfo userInfo = this.getUserInfo();
        return Result.ok(groupService.getWaEmpGroupList(dto, userInfo));
    }

    @ApiOperation("取得员工考勤方案详情")
    @GetMapping("/getEmpWaGroupDetail")
    public Result getEmpWaGroupDetail(@RequestParam("id") Integer id) {
        return Result.ok(groupService.getWaEmpGroupById(id));
    }

    @ApiOperation("删除员工考勤方案")
    @DeleteMapping("/deleteWaEmpGroup")
    public Result<Boolean> deleteEmpWaGroup(@RequestParam("id") Integer id) {
        try {
            groupService.deleteWaEmpGroup(id);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("GroupController.deleteWaEmpGroup executes exception, {}", ex.getMessage(), ex);
            if (ex instanceof ServerException) {
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), Boolean.FALSE);
            }
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("保存员工考勤方案")
    @PostMapping("/saveEmpWaGroup")
    public Result<Boolean> saveEmpWaGroup(@RequestBody AttEmpGroupDto dto) {
        try {
            return groupService.saveWaEmpGroup(dto);
        } catch (Exception e) {
            log.error("GroupController.saveEmpWaGroup has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SAVE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("同步员工考勤方案")
    @GetMapping("/synchronize")
    public Result<Boolean> synchronizeEmpGroup(@RequestParam("progress") String progress) {
        UserInfo userInfo = getUserInfo();
        //重复生成校验
        String lockKey = MessageFormat.format("SYNEMPGROUP_LOCK_{0}", userInfo.getTenantId());
        if (cacheService.containsKey(lockKey)) {
            //return Result.fail("另一个员工考勤方案进程正在运行中，请稍候再尝试!");
            return ResponseWrap.wrapResult(AttendanceCodes.SYNC_IN_PROGRESS, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60);
        try {
            cacheService.cacheValue(SYN_EMP_GROUP_PROCESS + progress, "0.5");
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    try {
                        log.info("synchronizeEmpGroup params ... " + progress + ",user info = " + JSONUtils.ObjectToJson(userInfo));
                        groupService.synchronizeEmpGroup(userInfo.getTenantId(), userInfo.getUserId(), ConvertHelper.longConvert(userInfo.getTenantId()));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            //engineMessage.setMessage("同步考勤方案失败,请联系管理员!");
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.SYNC_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    return params;
                }

                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(SYN_EMP_GROUP_PROCESS + progress, "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        cacheService.cacheValue(SYN_EMP_GROUP_MSG_PROCESS + progress, JsonSerializeHelper.serialize(engineMessage), 5 * 60);
                    }
                    cacheService.remove(lockKey);
                }
            });
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            cacheService.remove(lockKey);
            log.error("GroupController.synchronizeEmpGroup has exception {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SYNC_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取同步考勤方案进度")
    @RequestMapping(value = "/getProgress", method = RequestMethod.GET)
    public Result getProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(SYN_EMP_GROUP_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        Double rate = Double.valueOf(cacheService.getValue(SYN_EMP_GROUP_PROCESS + progress));
        if (rate == null) {
            rate = 0.5d;
        }
        if (rate >= 1) {
            String exeResult = cacheService.getValue(SYN_EMP_GROUP_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(exeResult)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(exeResult, AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            cacheService.remove(SYN_EMP_GROUP_PROCESS + progress);
            cacheService.remove(SYN_EMP_GROUP_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation("批量删除员工考勤方案")
    @PostMapping("/deleteWaEmpGroups")
    public Result<Boolean> deleteEmpWaGroups(@RequestBody ItemsResult<Integer> dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "参数为空", Boolean.FALSE);
        }
        try {
            groupService.deleteWaEmpGroups(dto.getItems());
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception ex) {
            log.error("GroupController.deleteEmpWaGroups executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.DELETE_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("获取当前有效期内的员工考勤方案")
    @GetMapping("/getEmpGroup")
    public Result<GroupInfoVo> getEmpGroup(@RequestParam(name = "empId", required = false) Long empId) {
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            if (null == empId) {
                empId = userInfo.getStaffId();
            }
            GroupDetailDto group = groupService.getEmpGroup(userInfo.getTenantId(), empId, DateUtil.getCurrentTime(true));
            if (null == group) {
                return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convert(group, GroupInfoVo.class));
        } catch (Exception ex) {
            log.error("GroupController.getEmpGroup executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Get employee group exception", null);
        }
    }

    @ApiOperation(value = "分组条件 下拉框数据")
    @RequestMapping(value = "/selectCondList", method = RequestMethod.GET)
    public Result<Map> selectCondList(@RequestParam(value = "type", required = false) Integer type) {
        List<Map> list = new ArrayList<Map>() {{
            add(new HashMap() {{
                put("text", "专业职级");
                put("value", "job_grade");
            }});
            add(new HashMap() {{
                put("text", "账号");
                put("value", "workno");
            }});
            add(new HashMap() {{
                put("text", "合同公司");
                put("value", "company_contract_id");
            }});
            add(new HashMap() {{
                put("text", "任职组织");
                put("value", "orgid");
            }});
            add(new HashMap() {{
                put("text", "任职组织（包含下级）");
                put("value", "subOrg");
            }});
            add(new HashMap() {{
                put("text", "司龄");
                put("value", "corp_age");
            }});
            add(new HashMap() {{
                put("text", "工龄");
                put("value", "job_age");
            }});
            add(new HashMap() {{
                put("text", "工作地");
                put("value", "workplace");
            }});

            add(new HashMap() {{
                put("text", "员工类型");
                put("value", "employ_type");
            }});

            add(new HashMap() {{
                put("text", "员工状态");
                put("value", "stats");
            }});

            add(new HashMap() {{
                put("text", "社保缴纳地");
                put("value", "social_city");
            }});

            add(new HashMap() {{
                put("text", "婚姻状态");
                put("value", "marriage");
            }});
            add(new HashMap() {{
                put("text", "子女年龄");
                put("value", "parental_leave");
            }});
            add(new HashMap() {{
                put("text", "探亲事由");
                put("value", "visiting_reason");
            }});

            add(new HashMap() {{
                put("text", "入职日期");
                put("value", "hire_date");
            }});
        }};
        Map<String, List<Map<String, Object>>> map = ListHelper.convertMapList(list, new ListHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("value");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("text");
            }
        }, false);
        Map map1 = new HashMap();
        map1.put("items", map.get("options"));
        return ResponseWrap.wrapResult(map1);
    }

    @ApiOperation("获取合同公司列表")
    @GetMapping(value = "/getContractCompany")
    public Result<List<KeyValue>> getContractCompany() {
        List<KeyValue> keyValues = null;
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            keyValues = groupService.getConTractCompany(userInfo.getTenantId());
        } catch (Exception e) {
            return Result.fail("获取合同公司失败");
        }
        return Result.ok(keyValues);
    }

    @ApiOperation("获取当前有效期内的员工考勤方案id")
    @PostMapping("/getEmpGroupIdByEmpIds")
    public Result<List<EmpGroupIdVo>> getEmpGroupIdByEmpIds(@RequestBody EmpGroupIdDto dto) {
        try {
            SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
            if (CollectionUtils.isEmpty(dto.getEmpIds())) {
                dto.getEmpIds().add(userInfo.getEmpId());
            }
            if (dto.getCurrentTime() == null) {
                dto.setCurrentTime(DateUtil.getCurrentTime(true));
            }
            List<EmpGroupIdDto> list = groupService.getEmpGroupIdByEmpIds(userInfo.getTenantId(), dto.getEmpIds(), dto.getCurrentTime());
            if (null == list) {
                return ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, null);
            }
            return ResponseWrap.wrapResult(ObjectConverter.convertList(list, EmpGroupIdVo.class));
        } catch (Exception ex) {
            log.error("GroupController.getEmpGroupByEmpIds executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "Get employee group by empIds exception", null);
        }
    }
}
