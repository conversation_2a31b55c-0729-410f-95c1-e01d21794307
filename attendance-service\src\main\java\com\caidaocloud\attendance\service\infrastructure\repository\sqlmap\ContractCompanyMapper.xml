<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.ContractCompanyMapper">
    <select id="getContractCompany" resultType="map">
        select
            contract_company_id as "value",
            contract_company_name as "text"
        from sys_contract_company sc
        where sc.tenant_id = #{belongId} and deleted = 0
    </select>
</mapper>