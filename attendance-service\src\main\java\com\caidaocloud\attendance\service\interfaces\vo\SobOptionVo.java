package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SobOptionVo implements Serializable {

    @ApiModelProperty("锁定状态的值")
    private boolean isLock;

    @ApiModelProperty("锁定状态")
    private String lockTxt;

    @ApiModelProperty("考核截止日")
    private Long sobEndDate;

    @ApiModelProperty("运算状态值 0未关闭")
    private Integer status;

    @ApiModelProperty("运算状态")
    private String statusTxt;

    private Integer sysPeriodMonth;

    @ApiModelProperty("考勤分组名称")
    private String waGroupName;

    @ApiModelProperty("账套id")
    private Integer waSobId;

    @ApiModelProperty("考勤分组id")
    private Integer waGroupId;

    @ApiModelProperty(value = "考勤帐套名称")
    private String waSobName;

    @ApiModelProperty(value = "考核周期")
    private String waSobRange;
}
