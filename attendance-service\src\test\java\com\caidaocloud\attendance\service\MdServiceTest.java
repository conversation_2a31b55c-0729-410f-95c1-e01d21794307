package com.caidaocloud.attendance.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdDataQuery;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.var;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class MdServiceTest {
    @Before
    public void before() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void test1() {
        var pageResult = MdDataQuery.identifier("entity.hr.EmpWorkInfo")
                .filter(DataFilter.eq("empId","1925765963184133")
                        .andEq("deleted", Boolean.FALSE.toString()), EmpInfoEntity.class);
        List<EmpInfoEntity> items = pageResult.getItems();
        System.out.println(items);
    }
}
