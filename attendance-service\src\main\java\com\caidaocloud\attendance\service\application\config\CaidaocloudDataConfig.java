package com.caidaocloud.attendance.service.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "caidaocloud.data")
public class CaidaocloudDataConfig {

    /**
     * 清水生成配额的异动类型名称，yaml配置示例如下：
     * caidaocloud:
     *   data:
     *     change-names-for-shimz-quota:
     *       - 部署异动
     *       - 项目合同更新确认书
     */
    private List<String> changeNamesForShimzQuota = Arrays.asList("部署异动", "项目合同更新确认书");
}