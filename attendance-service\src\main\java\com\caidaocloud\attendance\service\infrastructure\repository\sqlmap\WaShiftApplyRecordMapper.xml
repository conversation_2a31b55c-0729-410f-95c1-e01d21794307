<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaShiftApplyRecordMapper">
    <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftApplyRecord">
        <id column="rec_id" property="recId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="emp_id" property="empId" jdbcType="BIGINT"/>
        <result column="work_date" property="workDate" jdbcType="BIGINT"/>
        <result column="old_shift_def_id" property="oldShiftDefId" jdbcType="INTEGER"/>
        <result column="new_shift_def_id" property="newShiftDefId" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="revoke_reason" property="revokeReason" jdbcType="VARCHAR"/>
        <result column="file_id" property="fileId" jdbcType="VARCHAR"/>
        <result column="file_name" property="fileName" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
    rec_id, tenant_id, emp_id, work_date, old_shift_def_id, new_shift_def_id,
    reason, status, revoke_reason,file_id, file_name, deleted, create_by, create_time, update_by, update_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from wa_shift_apply_record
        where rec_id = #{recId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_shift_apply_record
    where rec_id = #{recId,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftApplyRecord">
    insert into wa_shift_apply_record (rec_id, tenant_id, emp_id, 
      work_date, old_shift_def_id, new_shift_def_id, 
      reason, status,revoke_reason,
      file_id, file_name, deleted, 
      create_by, create_time, update_by, 
      update_time)
    values (#{recId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{empId,jdbcType=BIGINT}, 
      #{workDate,jdbcType=BIGINT}, #{oldShiftDefId,jdbcType=INTEGER}, #{newShiftDefId,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},#{revokeReason,jdbcType=VARCHAR},
      #{fileId,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftApplyRecord">
        insert into wa_shift_apply_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recId != null">
                rec_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="workDate != null">
                work_date,
            </if>
            <if test="oldShiftDefId != null">
                old_shift_def_id,
            </if>
            <if test="newShiftDefId != null">
                new_shift_def_id,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="revokeReason != null">
                revoke_reason,
            </if>
            <if test="fileId != null">
                file_id,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recId != null">
                #{recId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=BIGINT},
            </if>
            <if test="workDate != null">
                #{workDate,jdbcType=BIGINT},
            </if>
            <if test="oldShiftDefId != null">
                #{oldShiftDefId,jdbcType=INTEGER},
            </if>
            <if test="newShiftDefId != null">
                #{newShiftDefId,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="revokeReason != null">
                #{revokeReason,jdbcType=VARCHAR},
            </if>
            <if test="fileId != null">
                #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftApplyRecord">
        update wa_shift_apply_record
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=BIGINT},
            </if>
            <if test="workDate != null">
                work_date = #{workDate,jdbcType=BIGINT},
            </if>
            <if test="oldShiftDefId != null">
                old_shift_def_id = #{oldShiftDefId,jdbcType=INTEGER},
            </if>
            <if test="newShiftDefId != null">
                new_shift_def_id = #{newShiftDefId,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="revokeReason != null">
                revoke_reason = #{revokeReason,jdbcType=INTEGER},
            </if>
            <if test="fileId != null">
                file_id = #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
        </set>
        where rec_id = #{recId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftApplyRecord">
    update wa_shift_apply_record
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=BIGINT},
      work_date = #{workDate,jdbcType=BIGINT},
      old_shift_def_id = #{oldShiftDefId,jdbcType=INTEGER},
      new_shift_def_id = #{newShiftDefId,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      file_id = #{fileId,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where rec_id = #{recId,jdbcType=BIGINT}
   </update>

    <select id="selectByExample"  resultType="com.caidaocloud.attendance.service.domain.entity.WaShiftApplyRecordDo" parameterType="com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto">
        select
        <include refid="Base_Column_List"/>
        from wa_shift_apply_record
        <where>
            <if test="tenantId != null">
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="empId != null">
                and emp_id = #{empId}
            </if>
            <if test="workDate != null">
                and work_date = #{workDate}
            </if>
            <if test="oldShiftDefId != null">
                and old_shift_def_id = #{oldShiftDefId}
            </if>
            <if test="newShiftDefId != null">
                and new_shift_def_id = #{newShiftDefId}
            </if>
            <if test="reason != null">
                and reason = #{reason}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="revokeReason != null">
                and revoke_reason = #{reason}
            </if>
            <if test="fileId != null">
                and file_id = #{fileId}
            </if>
            <if test="fileName != null">
                and file_name = #{fileName}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>

    </select>

    <select id="selectPageList" resultType="com.caidaocloud.attendance.service.domain.entity.WaShiftApplyRecordDo">
        select * from (
        select
        b.workno as "workNo",
        b.emp_name as "empName",
        c.shortname as "orgName",
        case
        when c.full_path is not null and c.full_path != ''
        then concat_ws('/', c.full_path, c.shortname)
        else c.shortname
        end  as "fullPath",
        a.rec_id as "recId",
        a.work_date as "workDate",
        d.shift_def_name as "oldShift",
        e.shift_def_name as "newShift",
        a.create_time as "createTime",
        a.reason,
        a.status,
        a.update_time as "lastApprovalTime",
        b.orgid,
        b.employ_type
        from wa_shift_apply_record a
        join sys_emp_info b on a.emp_id = b.empid and b.deleted = 0
        left join sys_corp_org c on c.orgid = b.orgid
        left join wa_shift_def d on d.shift_def_id = a.old_shift_def_id
        left join wa_shift_def e on e.shift_def_id = a.new_shift_def_id
        <where>
            and a.deleted=0
            and b.belong_org_id = #{belongOrgId,jdbcType=VARCHAR}
            <if test="keywords != null and keywords != ''">
                and (b.emp_name like concat('%', #{keywords}, '%') or b.workno like concat('%', #{keywords}, '%'))
            </if>
            <if test="empId !=null">
                and a.emp_id = #{empId}
            </if>
            <if test="datafilter != null and datafilter != ''">
                ${datafilter}
            </if>
        </where>
        order by a.update_time desc
        ) as t
        <where>
            <if test="filter != null">
                ${filter}
            </if>
        </where>
        order by "lastApprovalTime" desc,"createTime" desc
    </select>

    <select id="getByIdAndCorp" resultType="com.caidaocloud.attendance.service.domain.entity.WaShiftApplyRecordDo">
       select b.workno                                                   as "workNo",
       b.empid                                                           as "empId",
       b.emp_name                                                        as "empName",
       b.employ_type                                                     as "employType",
       b.workplace                                                       as "workCity",
       c.shortname                                                       as "orgName",
              case
                  when c.full_path is not null and c.full_path != ''
        then concat_ws('/', c.full_path, c.shortname)
                  else c.shortname
                  end                                                       as "fullPath",
       b.hire_date                                                       as "hireDate",
       a.work_date                                                       as "workDate",
       g.shift_def_name                                                  as "oldShift",
       g.start_time                                                      as "oldStartTime",
       g.end_time                                                        as "oldEndTime",
       h.shift_def_name                                                  as "newShift",
       h.start_time                                                      as "newStartTime",
       h.end_time                                                        as "newEndTime",
       a.create_time                                                     as "createTime",
       a.status,
       case
           when a.status = 0
               then '暂存'
           when a.status = 1
               then '审批中'
           when a.status = 2
               then '已通过'
           when a.status = 3
               then '已拒绝'
           when a.status = 4
               then '已作废'
           when a.status = 5
               then '已退回'
           when a.status = 9
               then '已撤销' end                                            as "statusName",
           a.reason                                                        as "reason",
           a.file_id                                                       as "fileId",
           a.file_name                                                     as "fileName",
           a.create_by                                                     as "createBy"
       from wa_shift_apply_record a
         join sys_emp_info b on a.emp_id = b.empid and b.deleted = 0
         left join sys_corp_org c on c.orgid = b.orgid
         left join wa_shift_def g on a.old_shift_def_id = g.shift_def_id
         left join wa_shift_def h on a.new_shift_def_id = h.shift_def_id
      where rec_id =#{recId}
        <if test="corpId != null">
            and b.corpid = #{corpId}
        </if>
    </select>

    <select id="selectShiftApplyList" resultType="com.caidaocloud.attendance.service.domain.entity.WaShiftApplyRecordDo">
        select status,
        emp_id as "empId",
        old_shift_def_id as "oldShiftDefId",
        new_shift_def_id as "newShiftDefId"
        from wa_shift_apply_record
        where tenant_id=#{tenantId}
        and work_date between #{startDate} and #{endDate}
        <if test="empIds != null and empIds.size() > 0">
            and emp_id in
            <foreach collection="empIds" open="(" close=")" item="empId" separator=",">
                #{empId}
            </foreach>
        </if>
    </select>
</mapper>