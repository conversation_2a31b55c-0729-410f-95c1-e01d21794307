package com.caidaocloud.attendance.service.interfaces.dto.shift;

import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Optional;

@Data
@ApiModel("班次设置-加班时间范围内的多段休息时间段DTO")
public class OvertimeRestPeriodsDto {
    @ApiModelProperty("开始时间(单位分钟),eg:1080")
    private Integer overtimeRestStartTime;
    @ApiModelProperty("结束时间(单位分钟),eg:1110")
    private Integer overtimeRestEndTime;

    public Integer doGetRealOvertimeRestStartTime(WaShiftDo shift) {
        if (null == overtimeRestStartTime) {
            return 0;
        }
        Integer overtimeStartTime = Optional.ofNullable(shift.getOvertimeStartTime()).orElse(0);
        Integer overtimeEndTime = Optional.ofNullable(shift.getOvertimeEndTime()).orElse(0);
        // 加班时间跨夜时：检查加班休息时间是否跨夜
        if (overtimeStartTime > overtimeEndTime && overtimeRestStartTime < overtimeStartTime) {
            return overtimeRestStartTime + 1440;
        }
        return overtimeRestStartTime;
    }

    public Integer doGetRealOvertimeRestEndTime(WaShiftDo shift) {
        if (null == overtimeRestEndTime) {
            return 0;
        }
        if (overtimeRestStartTime >= overtimeRestEndTime) {
            return overtimeRestEndTime + 1440;
        }
        Integer overtimeStartTime = Optional.ofNullable(shift.getOvertimeStartTime()).orElse(0);
        Integer overtimeEndTime = Optional.ofNullable(shift.getOvertimeEndTime()).orElse(0);
        // 加班时间跨夜时：检查加班休息时间是否跨夜
        if (overtimeStartTime > overtimeEndTime && overtimeRestEndTime < overtimeStartTime) {
            return overtimeRestEndTime + 1440;
        }
        return overtimeRestEndTime;
    }
}
