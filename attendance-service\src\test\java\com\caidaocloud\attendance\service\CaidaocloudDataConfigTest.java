package com.caidaocloud.attendance.service;

import com.caidaocloud.attendance.service.application.config.CaidaocloudDataConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * CaidaocloudDataConfig 配置测试类
 *
 * <AUTHOR>
 * @Date 2024/12/17
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AttendanceApplication.class)
public class CaidaocloudDataConfigTest {

    @Autowired
    private CaidaocloudDataConfig caidaocloudDataConfig;

    /**
     * 测试默认值是否正确
     */
    @Test
    public void testDefaultValues() {
        List<String> changeNames = caidaocloudDataConfig.getChangeNamesForShimzQuota();
        System.out.println("testDefaultValues: " + changeNames);
    }
}