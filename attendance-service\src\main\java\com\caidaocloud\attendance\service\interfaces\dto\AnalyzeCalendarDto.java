package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AnalyzeCalendarDto {

    @ApiModelProperty("日期类型")
    private Integer dateType;
    @ApiModelProperty("日期")
    private String workDate;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("员工id")
    private Long empId;
    @ApiModelProperty("考勤结果")
    private List<WaResultDto> waResult;


}
