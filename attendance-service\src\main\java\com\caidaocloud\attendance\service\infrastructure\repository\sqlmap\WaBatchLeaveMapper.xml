<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaBatchLeaveMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchLeave">
    <id column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="empid" jdbcType="BIGINT" property="empid" />
    <result column="leave_type_id" jdbcType="INTEGER" property="leaveTypeId" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="end_date" jdbcType="BIGINT" property="endDate" />
    <result column="emergency_contact" jdbcType="VARCHAR" property="emergencyContact" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="time_slot" jdbcType="VARCHAR" property="timeSlot" />
    <result column="time_duration" jdbcType="REAL" property="timeDuration" />
    <result column="time_unit" jdbcType="INTEGER" property="timeUnit" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ext_custom_col" jdbcType="VARCHAR" property="extCustomCol" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="last_approval_time" jdbcType="BIGINT" property="lastApprovalTime" />
    <result column="last_empid" jdbcType="BIGINT" property="lastEmpid" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="revoke_status" jdbcType="INTEGER" property="revokeStatus" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="process_code" jdbcType="CHAR" property="processCode" />
    <result column="multi_leave_type_id" jdbcType="VARCHAR" property="multiLeaveTypeId" />
    <result column="time_duration_txt" jdbcType="VARCHAR" property="timeDurationTxt" />
  </resultMap>
  <sql id="Base_Column_List">
    batch_id, empid, leave_type_id, start_date, end_date, emergency_contact, file_path, 
    file_name, time_slot, time_duration, time_unit, business_key, reason, ext_custom_col, 
    status, last_approval_time, last_empid, revoke_reason, revoke_status, tenant_id, 
    deleted, create_by, create_time, update_by, update_time, process_code, multi_leave_type_id, 
    time_duration_txt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_batch_leave
    where batch_id = #{batchId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_batch_leave
    where batch_id = #{batchId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchLeave">
    insert into wa_batch_leave (batch_id, empid, leave_type_id, 
      start_date, end_date, emergency_contact, 
      file_path, file_name, time_slot, 
      time_duration, time_unit, business_key, 
      reason, ext_custom_col, status, 
      last_approval_time, last_empid, revoke_reason, 
      revoke_status, tenant_id, deleted, 
      create_by, create_time, update_by, 
      update_time, process_code, multi_leave_type_id, 
      time_duration_txt)
    values (#{batchId,jdbcType=BIGINT}, #{empid,jdbcType=BIGINT}, #{leaveTypeId,jdbcType=INTEGER}, 
      #{startDate,jdbcType=BIGINT}, #{endDate,jdbcType=BIGINT}, #{emergencyContact,jdbcType=VARCHAR}, 
      #{filePath,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{timeSlot,jdbcType=VARCHAR}, 
      #{timeDuration,jdbcType=REAL}, #{timeUnit,jdbcType=INTEGER}, #{businessKey,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{extCustomCol,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{lastApprovalTime,jdbcType=BIGINT}, #{lastEmpid,jdbcType=BIGINT}, #{revokeReason,jdbcType=VARCHAR}, 
      #{revokeStatus,jdbcType=INTEGER}, #{tenantId,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{processCode,jdbcType=CHAR}, #{multiLeaveTypeId,jdbcType=VARCHAR}, 
      #{timeDurationTxt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchLeave">
    insert into wa_batch_leave
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="leaveTypeId != null">
        leave_type_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="emergencyContact != null">
        emergency_contact,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="timeSlot != null">
        time_slot,
      </if>
      <if test="timeDuration != null">
        time_duration,
      </if>
      <if test="timeUnit != null">
        time_unit,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="extCustomCol != null">
        ext_custom_col,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time,
      </if>
      <if test="lastEmpid != null">
        last_empid,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="revokeStatus != null">
        revoke_status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="processCode != null">
        process_code,
      </if>
      <if test="multiLeaveTypeId != null">
        multi_leave_type_id,
      </if>
      <if test="timeDurationTxt != null">
        time_duration_txt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=BIGINT},
      </if>
      <if test="leaveTypeId != null">
        #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=BIGINT},
      </if>
      <if test="emergencyContact != null">
        #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="timeSlot != null">
        #{timeSlot,jdbcType=VARCHAR},
      </if>
      <if test="timeDuration != null">
        #{timeDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="processCode != null">
        #{processCode,jdbcType=CHAR},
      </if>
      <if test="multiLeaveTypeId != null">
        #{multiLeaveTypeId,jdbcType=VARCHAR},
      </if>
      <if test="timeDurationTxt != null">
        #{timeDurationTxt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchLeave">
    update wa_batch_leave
    <set>
      <if test="empid != null">
        empid = #{empid,jdbcType=BIGINT},
      </if>
      <if test="leaveTypeId != null">
        leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=BIGINT},
      </if>
      <if test="emergencyContact != null">
        emergency_contact = #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="timeSlot != null">
        time_slot = #{timeSlot,jdbcType=VARCHAR},
      </if>
      <if test="timeDuration != null">
        time_duration = #{timeDuration,jdbcType=REAL},
      </if>
      <if test="timeUnit != null">
        time_unit = #{timeUnit,jdbcType=INTEGER},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="extCustomCol != null">
        ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lastApprovalTime != null">
        last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      </if>
      <if test="lastEmpid != null">
        last_empid = #{lastEmpid,jdbcType=BIGINT},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="revokeStatus != null">
        revoke_status = #{revokeStatus,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="processCode != null">
        process_code = #{processCode,jdbcType=CHAR},
      </if>
      <if test="multiLeaveTypeId != null">
        multi_leave_type_id = #{multiLeaveTypeId,jdbcType=VARCHAR},
      </if>
      <if test="timeDurationTxt != null">
        time_duration_txt = #{timeDurationTxt,jdbcType=VARCHAR},
      </if>
    </set>
    where batch_id = #{batchId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaBatchLeave">
    update wa_batch_leave
    set empid = #{empid,jdbcType=BIGINT},
      leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=BIGINT},
      end_date = #{endDate,jdbcType=BIGINT},
      emergency_contact = #{emergencyContact,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      time_slot = #{timeSlot,jdbcType=VARCHAR},
      time_duration = #{timeDuration,jdbcType=REAL},
      time_unit = #{timeUnit,jdbcType=INTEGER},
      business_key = #{businessKey,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      ext_custom_col = #{extCustomCol,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      last_approval_time = #{lastApprovalTime,jdbcType=BIGINT},
      last_empid = #{lastEmpid,jdbcType=BIGINT},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      revoke_status = #{revokeStatus,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      process_code = #{processCode,jdbcType=CHAR},
      multi_leave_type_id = #{multiLeaveTypeId,jdbcType=VARCHAR},
      time_duration_txt = #{timeDurationTxt,jdbcType=VARCHAR}
    where batch_id = #{batchId,jdbcType=BIGINT}
  </update>

  <select id="selectPageList" parameterType="hashmap" resultType="hashmap">
    SELECT * from(
      SELECT leave.batch_id,
      leave.time_duration,
      leave.time_unit,
      leave.time_slot,
      leave.create_time as crttime,
      leave.last_approval_time,
      leave.status,
      leave.start_date,
      leave.end_date,
      leave.business_key,
      leave.empid,
      leave.leave_type_id,
      leave.multi_leave_type_id,
      leave.time_duration_txt,
      ei.workno,
      ei.emp_name,
      org.orgid,
      org.shortname as "orgName",
      case
      when org.full_path is not null and org.full_path != ''
      then concat_ws('/', org.full_path, org.shortname)
      else org.shortname
      end       as "fullPath"
      FROM wa_batch_leave leave
      JOIN sys_emp_info ei ON leave.empid = ei.empid AND ei.deleted = 0
      LEFT JOIN sys_corp_org org on ei.orgid = org.orgid AND org.deleted = 0
      <where>
        leave.tenant_id = #{tenantId}
        <if test="empid != null">
          AND leave.empid = #{empid}
        </if>
        <if test="keywords != null and keywords != ''">
          AND (ei.workno like concat('%', #{keywords}, '%') or ei.emp_name like concat('%', #{keywords}, '%'))
        </if>
        <if test="startDate != null and endDate != null">
          AND leave.start_date <![CDATA[<=]]> #{endDate} AND leave.end_date <![CDATA[>=]]> #{startDate}
        </if>
        <if test="datafilter != null and datafilter != ''">
          ${datafilter}
        </if>
      </where>
    ) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
  </select>
</mapper>