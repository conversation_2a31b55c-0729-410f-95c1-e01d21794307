package com.caidaocloud.attendance.service.interfaces.dto.clock;

import com.caidaocloud.dto.EmpInfoKeyValue;
import com.caidaocloud.dto.KeyValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ClockPlanPageInfoDto {

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("方案名称")
    private String planName;
    @ApiModelProperty("打卡方式：1、GPS打卡，4、蓝牙，5、WIFI，多个打卡方式以逗号分隔")
    private String clockWay;
    @ApiModelProperty("支持补卡次数")
    private Integer supplementCount;
    @ApiModelProperty("是否支持补卡")
    private Boolean isSupplement;
    @ApiModelProperty("打卡地点")
    private List<KeyValue> gps = new ArrayList<>();
    @ApiModelProperty("wifi")
    private List<KeyValue> wifi = new ArrayList<>();
    @ApiModelProperty("蓝牙")
    private List<KeyValue> bluetooth = new ArrayList<>();
    @ApiModelProperty("适用对象")
    private List<EmpInfoKeyValue> employees = new ArrayList<>();
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    /**
     * 20210830新增字段
     */
    @ApiModelProperty("是否允许外勤打卡:true/false，默认false")
    private Boolean allowFieldClockIn;
    @ApiModelProperty("外勤打卡需填写备注:true/false，默认false")
    private Boolean fieldClockInNote;
    @ApiModelProperty("外勤打卡需拍照:true/false，默认false")
    private Boolean fieldClockInEnclosure;
    /**
     * 202100907新增字段 by aaron.chen
     */
    @ApiModelProperty("打卡时间间隔")
    private Float timeInterval;
    /**
     * 202100923新增字段 by aaron.chen
     */
    @ApiModelProperty("补卡说明")
    private String description;
    @ApiModelProperty("补卡条数每次")
    private Integer supplementNumber;
}
