package com.caidaocloud.attendance.service.interfaces.vo;

import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.attendance.service.application.dto.WaCustomParseRuleDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnalysisDetailVo {
    private Integer parseGroupId;
    private String parseGroupName;
    private BigDecimal lateCount;
    private Integer lateUnit;
    private String earlyCycle;
    private BigDecimal earlyCount;
    private Integer earlyUnit;
    private Boolean otParse;
    private Boolean lvParse;
    private Integer registerMiss;
    private Integer lateAllowNumber;
    private Integer lateAllowUnit;
    private Integer earlyAllowNumber;
    private Integer earlyAllowUnit;
    private Boolean isAnalyzeLateEarly;
    private Boolean otSumParse;
    @ApiModelProperty("异常类型：1早退加迟到，2迟到或早退")
    private Integer abnormalType;
    @ApiModelProperty("旷工规则")
    private List<AbsentConditionVo> absentConditionJsonb;
    @ApiModelProperty("开启自定义分析规则开关")
    private Boolean openCustomPaseRule;
    @ApiModelProperty("自定义迟到分析规则")
    private WaCustomParseRuleDto customLatePaseRule;
    @ApiModelProperty("自定义早退分析规则")
    private WaCustomParseRuleDto customEarlyPaseRule;
    @ApiModelProperty("自定义旷工分析规则")
    private WaCustomParseRuleDto customAbsentPaseRule;
    private List<OtPaseVo> otPaseJsonb;
    @ApiModelProperty("出勤规则：1 一次卡 2 二次卡 0 不打卡")
    private Integer clockType;
    private JSONObject clockRuleDto;
    @ApiModelProperty("外勤分析规则：1 出差单 2 外勤打卡 3 出差单联动外勤打卡")
    private Integer outParseRule;
    @ApiModelProperty("弹性分析开关 1:关闭 2:开启")
    private Integer flexibleWorkSwitch;
    @ApiModelProperty("弹性分析开关 1:按弹性区间分析 2:按班次分析")
    private Integer flexibleWorkType;
    @ApiModelProperty("允许打卡日期类型：1(工作日),2(休息日),3(法定假日),4(特殊休日),多个类型逗号分隔")
    private String allowedDateType;
    @ApiModelProperty("外勤联动班次打卡")
    private Boolean fieldClockLinkShift;

    private Integer minLateTime;
    private Short minLateTimeUnit;
    private Integer minEarlyTime;
    private Short minEarlyTimeUnit;
    private Boolean leaveExemptionSwitch;

    @ApiModelProperty("统计转换类型：1、按阶梯统计，2、按比例统计，默认值1")
    private Integer statisticType;
    @ApiModelProperty("迟到早退转旷工按比例转换时长，默认1:1转换")
    private Integer convertTime;
    @ApiModelProperty("迟到早退转旷工按比例转换比例，默认1:1转换")
    private Integer convertScale;
    @ApiModelProperty("迟到早退转旷工上限，超过上限转旷工，默认1:1转换")
    private Integer convertTimeLimit;
    @ApiModelProperty("迟到早退按比例转换迟到早退，默认1:1转换")
    private Integer expConvertScale;
    @ApiModelProperty("迟到早退按比例转换旷工比例(迟到早退值)，默认1:1转换")
    private Integer convertKgScale;

    @ApiModelProperty("是否豁免迟到早退休假类型，1、正常上/下班享有，2、休半天假是享有，3、休小时假时享有，复选，多个逗号分隔")
    private String leaveExemptionType;
}
