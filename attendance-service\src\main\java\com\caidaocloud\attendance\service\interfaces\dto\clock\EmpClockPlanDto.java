package com.caidaocloud.attendance.service.interfaces.dto.clock;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/9/9
 */
@Data
public class EmpClockPlanDto {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("所属公司")
    private String corpName;
    @ApiModelProperty("所属部门")
    private String orgName;
    @ApiModelProperty("组织全路径")
    private String fullPath;
    @ApiModelProperty("打卡方案id")
    private Long planId;
    @ApiModelProperty("打卡方案名称")
    private String planName;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("操作人")
    private String updaterName;
    @ApiModelProperty("结束时间")
    private Long updateTime;

    @ApiModelProperty("员工状态")
    private Integer empStatus;
    @ApiModelProperty("员工状态名称")
    private String empStatusName;
    @ApiModelProperty("员工类型")
    private Long empStyle;
    @ApiModelProperty("员工类型名称")
    private String empStyleName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("离职日期")
    private Long terminationDate;
    private String i18nPlanName;
    @ApiModelProperty("方案状态")
    private String effectiveStatus;

    public void doSetEffectiveStatus(Long nowTime) {
        if (this.startTime == null || this.endTime == null || nowTime == null) {
            return;
        }
        if (nowTime < this.startTime) {
            // 未生效
            this.setEffectiveStatus(MessageHandler.getMessage("caidao.exception.error_202018", WebUtil.getRequest()));
        } else if (nowTime >= this.startTime && nowTime <= this.endTime) {
            // 生效中
            this.setEffectiveStatus(MessageHandler.getMessage("caidao.exception.error_202019", WebUtil.getRequest()));
        } else if (nowTime > this.endTime) {
            // 已失效
            this.setEffectiveStatus(MessageHandler.getMessage("caidao.exception.error_202020", WebUtil.getRequest()));
        }
    }
}
