package com.caidaocloud.attendance.service.interfaces.vo.quota;

import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AnnualLeaveDetailVo {

    @ApiModelProperty("余额id")
    private Integer empQuotaId;

    @ApiModelProperty("员工信息")
    private EmpInfoDTO empInfo;

    @ApiModelProperty("员工姓名")
    private String empName;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("假期类型")
    private String leaveName;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("首次参工日期")
    private Long firstWorkDate;

    @ApiModelProperty("年份")
    private Integer periodYear;

    @ApiModelProperty("发放周期开始日期unix时间戳")
    private Long disCycleStart;

    @ApiModelProperty("发放周期结束日期unix时间戳")
    private Long disCycleEnd;

    @ApiModelProperty("有效期")
    private Float validityDuration;

    @ApiModelProperty("有效期单位：1 天、2 月、3 年")
    private Integer validityUnit;

    @ApiModelProperty("生效日期：开始时间")
    private Long startDate;

    @ApiModelProperty("生效日期：结束时间")
    private Long lastDate;

    @ApiModelProperty("时间单位|单位")
    private String timeUnitName;

    @ApiModelProperty("本年额度")
    private Float quotaDay;

    @ApiModelProperty("当前额度")
    private Float nowQuota;

    @ApiModelProperty("本年已用")
    private Float usedDay;

    @ApiModelProperty("可否预支：0 不可预支 、 1 可预支")
    private Integer ifAdvance;

    @ApiModelProperty("本年已用")
    private Float usedYear;

    @ApiModelProperty("本年可用")
    private Float availableYear;

    @ApiModelProperty("调整额度")
    private Float adjustQuota;

    @ApiModelProperty("单位、时间单位：1、天，2、小时")
    private Integer acctTimeType;

    @ApiModelProperty("调整已使用额度|调整本年已用|固定已使用")
    private Float fixUsedDay;

    @ApiModelProperty("预支年假")
    private Float advanceAnnualLeave;

    @ApiModelProperty("当前剩余｜本年余额")
    private Float curRemain;

    @ApiModelProperty("状态：true 生效，false 失效")
    private Boolean annualLeaveStatus;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("在途额度、流程中")
    private Float inTransitQuota;

    @ApiModelProperty("假期类型id")
    private Integer leaveTypeId;

    @ApiModelProperty("结转额度")
    private Float remainDay;

    @ApiModelProperty("离职日期")
    private Long terminationDate;

    @ApiModelProperty("额度ID")
    private Long configId;

    private String quotaName;
}
