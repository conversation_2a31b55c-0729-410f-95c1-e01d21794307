package com.caidaocloud.attendance.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OvertimeApplyVo {
    @ApiModelProperty("主键id")
    private Integer waid;

    @ApiModelProperty("员工工号 = 账号")
    private String workno;

    @ApiModelProperty("员工姓名")
    private String empName;

    @ApiModelProperty("任职组织")
    private String shortname;

    @ApiModelProperty("加班类型 1:工作日加班 2休息日加班，3法定假日加班,4特殊日期加班")
    private Integer dateType;

    @ApiModelProperty("补偿方式 1加班费 2调休 4其他")
    private Integer compensateType;

    @ApiModelProperty("补偿方式 1加班费 2调休 4其他")
    private String compensateTypeTxt;

    @ApiModelProperty("申请时长")
    private Float duration;

    @ApiModelProperty("有效时长")
    private Float relTimeDuration;

    @ApiModelProperty("加班单位")
    private String timeUnitName;

    @ApiModelProperty("加班事件时间")
    private String timeSlot;

    @ApiModelProperty("加班事由")
    private String reason;

    @ApiModelProperty("附件名称")
    private String fileName;

    @ApiModelProperty("申请日期")
    private Long crttime;

    @ApiModelProperty("审批状态")
    private String statusName;

    @ApiModelProperty("审批日期")
    private Long lastApprovalTime;

    @ApiModelProperty("申请加班开始时间")
    private Long startTime;

    @ApiModelProperty("申请加班结束时间")
    private Long endTime;

    @ApiModelProperty("查看的工作流id")
    private String businessKey;

    @ApiModelProperty("审批状态 0暂存 1审批中 2审批通过 3审批不通过 4作废 5已退回 8撤销中 9已撤销")
    private Integer status;

    @ApiModelProperty("审批流程唯一标识")
    private Integer funcType;

    private String fullPath;

    @ApiModelProperty("加班类型")
    private String typeName;

    @ApiModelProperty("转换时长")
    private Float transferDuration;
    @ApiModelProperty("转换单位:1天，2小时")
    private Integer transferUnit;
    @ApiModelProperty("主键id")
    private Integer detailId;
}
