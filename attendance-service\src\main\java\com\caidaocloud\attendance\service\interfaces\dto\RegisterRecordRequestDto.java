package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/28
 */
@Data
public class RegisterRecordRequestDto extends ExportBasePage {
    @ApiModelProperty("开始日期")
    private Integer startDate;

    @ApiModelProperty("结束日期")
    private Integer endDate;

    @ApiModelProperty("搜索关键字")
    private String keywords;

    @ApiModelProperty("打卡类型")
    private List<Integer> types;

    @ApiModelProperty("数据显示类型 1 只显示当天最早的签到和最晚的签退")
    private Integer showType;

    private Long corpId;

    private String belongOrgId;

    private boolean isAnalyze;

    @ApiModelProperty("是否展示所有有效的打卡记录")
    private Boolean ifShowAll;

    @ApiModelProperty("是否查询审批通过的补打卡记录")
    private Boolean queryApprovalBdk;
}
