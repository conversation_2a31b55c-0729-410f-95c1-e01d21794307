package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.service.IWorkRoundService;
import com.caidao1.ioc.util.ListsHelper;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.WorkRoundDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkRoundGridDto;
import com.caidaocloud.attendance.service.interfaces.vo.RoundShiftVo;
import com.caidaocloud.attendance.service.interfaces.vo.WorkRoundVo;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/2/8
 */
@Slf4j
@RestController
@RequestMapping("/api/attendance/workround/v1")
@Api(value = "/api/attendance/workround/v1", description = "排班计划")
public class WorkRoundController {
    @Autowired
    private IWorkRoundService workRoundService;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private RedisTemplate redisTemplate;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @ApiOperation("获取排班计划分页列表")
    @PostMapping(value = "/list")
    public Result<AttendancePageResult<WorkRoundGridDto>> getWorkRoundPageList(@RequestBody AttendanceBasePage basePage) throws Exception {
        PageBean pageBean = PageUtil.getPageBean(basePage);
        return ResponseWrap.wrapResult(workRoundService.getWorkRoundPageList(pageBean));
    }

    @ApiOperation(value = "查询排班计划详情")
    @GetMapping(value = "/detail")
    public Result<WorkRoundVo> getRounfShiftInfoByWorkRoundId(@RequestParam("id") Integer id) throws Exception {
        UserInfo userInfo = this.getUserInfo();
        WorkRoundDto workRoundDto = workRoundService.getRounfShiftInfoByWorkRoundId(userInfo.getTenantId(), id);
        WorkRoundVo round = ObjectConverter.convert(workRoundDto, WorkRoundVo.class);
        if (CollectionUtils.isNotEmpty(workRoundDto.getRoundShiftList())) {
            List<RoundShiftVo> list = ObjectConverter.convertList(workRoundDto.getRoundShiftList(), RoundShiftVo.class);
            round.setRoundShiftList(list);
        }
        return ResponseWrap.wrapResult(round);
    }

    @ApiOperation(value = "新增排班计划")
    @PostMapping(value = "/save")
    public Result<Boolean> saveWorkRoundShift(@RequestBody WorkRoundDto workRoundDto) {
        workRoundDto.initRoundName();
        workRoundDto.initI18nRoundName();
        if (StringUtils.isNotBlank(workRoundDto.getRoundName()) && workRoundDto.getRoundName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        UserInfo userInfo = getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("SAVE_WORKROUNDSHIFT_LOCK_{0}_{1}", userInfo.getTenantId(), workRoundDto.getRoundName());
        if (redisTemplate.hasKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        redisTemplate.opsForValue().set(lockKey, 1, 60, TimeUnit.SECONDS);
        try {
            workRoundService.saveOrUpdateWorkRoundShift(workRoundDto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        } finally {
            //执行结束，释放key值
            redisTemplate.delete(lockKey);
        }
    }

    @ApiOperation(value = "修改排班计划")
    @PostMapping(value = "/update")
    public Result<Boolean> updateWorkRoundShift(@RequestBody WorkRoundDto workRoundDto) {
        workRoundDto.initRoundName();
        workRoundDto.initI18nRoundName();
        if (StringUtils.isNotBlank(workRoundDto.getRoundName()) && workRoundDto.getRoundName().length() > 30) {
            return ResponseWrap.wrapResult(ErrorCodes.NAME_LENGTH_TOO_LONG, Boolean.FALSE);
        }
        UserInfo userInfo = getUserInfo();
        //重复提交校验
        String lockKey = MessageFormat.format("UPDATE_WORKROUNDSHIFT_LOCK_{0}_{1}", userInfo.getTenantId(), workRoundDto.getWorkRoundId());
        if (redisTemplate.hasKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        redisTemplate.opsForValue().set(lockKey, 1, 60, TimeUnit.SECONDS);
        try {
            workRoundService.saveOrUpdateWorkRoundShift(workRoundDto);
            return Result.ok(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            throw new RuntimeException(e);
        } finally {
            //执行结束，释放key值
            redisTemplate.delete(lockKey);
        }
    }

    @ApiOperation(value = "删除排班计划")
    @DeleteMapping(value = "/delete")
    public Result<Boolean> deleteWorkRound(@RequestParam("id") Integer id) {
        UserInfo userInfo = this.getUserInfo();
        List<String> list = workRoundService.getWorktimeNameList(userInfo.getTenantId(), id);
        if (CollectionUtils.isNotEmpty(list)) {
            return ResponseWrap.wrapResult(AttendanceCodes.SCHEDULED_DELETE_NOT_ALLOW, Boolean.FALSE);
        }
        workRoundService.deleteWorkRound(id);
        return ResponseWrap.wrapResult(CommonConstant.TRUE);
    }

    @ApiOperation(value = "期间工作计划下拉列表")
    @RequestMapping(value = "/selectWorkRoundList")
    public Result selectWorkRoundList() throws Exception {
        List<Map> list = waConfigService.getWorkRoundList(new PageBean(true));
        Map<String, List<Map<String, Object>>> listMap = ListsHelper.convertMapList(list, new ListsHelper.LabelValueBeanCreator<Map>() {
            @Override
            public Object getValue(Map t) {
                return t.get("work_round_id");
            }

            @Override
            public String getLabel(Map t) {
                return (String) t.get("round_name");
            }
        }, false);
        Map<String, Object> map = new HashMap<>();
        map.put("items", listMap.get("options"));
        return ResponseWrap.wrapResult(map);
    }
}
