<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.LeaveTypeDefMapper">

    <select id="getWaLeaveTypeDefList" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDefDo">
        SELECT leave_type_def_code,
               leave_type_def_id,
               leave_type_def_name,
               status,
               belong_orgid,
               deleted,
               crttime,
               crtuser,
               updtime,
               upduser,
               leave_type_def_lang,
               i18n_leave_type_def_name
        FROM wa_leave_type_def
        WHERE deleted = 0
          AND (belong_orgid = '0' OR belong_orgid = #{belongOrgId,jdbcType=VARCHAR})
        ORDER BY leave_type_def_code
    </select>

    <select id="getWaLeaveTypeDefByData" resultType="com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDefDo">
        SELECT
            leave_type_def_code,
            leave_type_def_id,
            leave_type_def_name,
            i18n_leave_type_def_name
        FROM wa_leave_type_def
        WHERE deleted = 0
          AND (belong_orgid = '0' OR belong_orgid = #{belongOrgId,jdbcType=VARCHAR})
          AND leave_type_def_name = #{name}
        <if test="id != null and id != ''">
            AND leave_type_def_id != #{id}
        </if>
    </select>
</mapper>