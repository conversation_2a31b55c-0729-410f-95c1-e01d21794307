package com.caidaocloud.attendance.service.interfaces.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MyLeaveTimeVo {
    @ApiModelProperty("单据ID")
    private Integer leaveId;
    @ApiModelProperty("假期名称")
    private String leaveName;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态文本")
    private String statusName;
    @ApiModelProperty("休假时间文本")
    private String leaveTimeTxt;
    @ApiModelProperty("审批流程key")
    private String businessKey;
    @ApiModelProperty("审批流程ID")
    private String procInstId;
    @ApiModelProperty("申请时间")
    private Long applyTime;
    private Integer funcType;
    @ApiModelProperty("时长文本")
    private String timeDurationTxt;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("申请类型")
    private String applyName;
    @ApiModelProperty("假期类型编码")
    private String leaveTypeCode;


}
