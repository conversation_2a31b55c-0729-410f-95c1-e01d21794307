package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.interfaces.dto.QuotaEmpGroupDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by lufangge on 2021/1/31.
 */
@Data
public class LeaveQuotaVo {
    //员工分组设置
    List<QuotaEmpGroupDto> quotaGroups;
    //配额设置
    @ApiModelProperty("假期类型ID")
    private Integer leaveTypeId;

    @ApiModelProperty("配额类型ID")
    private Integer quotaSettingId;

    @ApiModelProperty("配额名称")
    private String quotaSettingName;

    @ApiModelProperty("配额多语言名称")
    private Object quotaSettingNameLang;

    @ApiModelProperty("配额周期")
    private Integer quotaPeriodType;

    @ApiModelProperty("配额开始日期")
    private Long startDate;

    @ApiModelProperty("配额结束日期")
    private Long endDate;

    @ApiModelProperty("试用期不累计")
    private Boolean isCountInProbation;

    @ApiModelProperty("结转规则 1 结转 2 付现 3 作废")
    private Integer carryOverType;

    @ApiModelProperty("结转有效时长")
    private Integer carryOverTimeNum;

    @ApiModelProperty("结转有效期单位")
    private Integer carryOverTimeUnit;

    @ApiModelProperty("最大结转时长")
    private Integer maxCarryOverDay;

    @ApiModelProperty("未结转时长处理方式")
    private Integer noCarryOverHandle;

    @ApiModelProperty("额度扣减顺序")
    private Integer quotaSortNo;

    @ApiModelProperty("员工端是否显示")
    private Boolean isEmpShow;

//    @ApiModelProperty("试用期冻结")
//    private Boolean isTrialFreeze;

    @ApiModelProperty("冻结规则")
    private Short freezingRules;

    @ApiModelProperty("试用期额度折算")
    private Boolean isTrialConvert;

//    @ApiModelProperty("是否折算")
//    private Boolean ifConvert;

    @ApiModelProperty("备注")
    private String rmk;
}
