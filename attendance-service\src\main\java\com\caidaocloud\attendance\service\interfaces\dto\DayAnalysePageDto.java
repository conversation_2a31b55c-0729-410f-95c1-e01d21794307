package com.caidaocloud.attendance.service.interfaces.dto;

import com.caidao1.report.dto.FilterBean;
import com.caidaocloud.attendance.service.infrastructure.common.ExportBasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DayAnalysePageDto extends ExportBasePage implements Serializable {

    @ApiModelProperty("开始日期")
    private Integer startDate;

    @ApiModelProperty("结束日期")
    private Integer endDate;

    @ApiModelProperty("考勤账套id，多选，以','分隔")
    private String sobId;

    @ApiModelProperty("是否原始：false/true")
    private boolean isOrigin;

    @ApiModelProperty("过滤条件：lat迟到,early早退,kg旷工,leavetime请假,overtime加班")
    private String[] statusOptions;

    @ApiModelProperty("筛选条件")
    private List<FilterBean> filterList;

    @ApiModelProperty("考勤方案")
    private List<Integer> waGroupIds;

    private Boolean notOrderBy;

    private Boolean isCount;


    private List<Long> empIds;
}
