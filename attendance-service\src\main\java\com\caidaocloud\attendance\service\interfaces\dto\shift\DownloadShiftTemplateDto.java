package com.caidaocloud.attendance.service.interfaces.dto.shift;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/11/26 14:03
 * @Description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadShiftTemplateDto {

    @ApiModelProperty("排班周期年月，格式：202111")
    private String yearMonth;
    @ApiModelProperty("员工id")
    private List<Long> empIds;

    private Map rows;
}
