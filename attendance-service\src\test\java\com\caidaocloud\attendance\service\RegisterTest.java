package com.caidaocloud.attendance.service;

import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.application.dto.AnalyzeResultCalculateDto;
import com.caidaocloud.attendance.service.application.service.impl.AnalyzeResultCalculateService;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RunWith(SpringRunner.class)
@SpringBootTest
public class RegisterTest {

    @Autowired
    private AnalyzeResultCalculateService analyzeResultCalculateService;

    @Test
    public void register_test() throws Exception {
        AnalyzeResultCalculateDto dto = new AnalyzeResultCalculateDto();
        dto.setBelongid("56594");
        dto.setEmpids(new Long[]{4L, 5L, 6L, 8L, 9L, 10L, 13L, 14L, 15L});
        dto.setStartDate(1546272000L);
        dto.setEndDate(1643039999L);
        List<WaRegisterRecordDo> list = new ArrayList<>();//analyzeResultCalculateService.getAllRegisterRecordList(dto);
//        Map<Integer, List<Integer>> listMap = list.stream().collect(Collectors.groupingBy(WaRegisterRecordDo::getEmpid,
//                Collectors.mapping(WaRegisterRecordDo::getShiftDefId, Collectors.toList())));
        System.out.println("list: " + JSONUtils.ObjectToJson(list));
//        System.out.println("listMap: " + JSONUtils.ObjectToJson(listMap));
        Map<Integer, List<WaRegisterRecordDo>> recordMap = list.stream().collect(Collectors.groupingBy(WaRegisterRecordDo::getRecordId));
        for (Integer key : recordMap.keySet()) {
            if (recordMap.get(key).size() > 1) {
                System.out.println("recordMap1: " + JSONUtils.ObjectToJson(recordMap.get(key)));
            }
        }
        System.out.println("recordMap: " + JSONUtils.ObjectToJson(recordMap));
    }
}
