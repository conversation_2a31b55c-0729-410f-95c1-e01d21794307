package com.caidaocloud.attendance.service.infrastructure.util;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.cglib.beans.BeanMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BeanMapUtils {
    private static final char UNDERLINE = '_';

    /**
     * 将对象属性转化为map结合
     */
    public static <T> Map<String, Object> beanToMap(T bean) {
        Map<String, Object> map = new HashMap<>(16);
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                map.put(key + "", beanMap.get(key));
            }
        }
        return map;
    }

    /**
     * 将map集合中的数据转化为指定对象的同名属性中
     */
    public static <T> T mapToBean(Map<String, Object> map, Class<T> clazz) throws Exception {
        T bean = clazz.newInstance();
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }

    /**
     * 将map集合中的数据转化为指定对象的同名属性中
     */
    public static <T> List<T> mapToBean(List<Map> mapList, Class<T> clazz) {
        if(null == mapList || mapList.isEmpty()){
            return new ArrayList<>();
        }

        List<T> dataList = mapList.stream().map(map -> {
            T bean = null;
            try {
                bean = clazz.newInstance();
            } catch (Exception e) {
                e.printStackTrace();
            }
            BeanMap beanMap = BeanMap.create(bean);
            beanMap.putAll(map);
            return bean;
        }).collect(Collectors.toList());
        return dataList;
    }

    /**
     * 将List中map的key值命名方式格式化为驼峰
     *
     * @param list
     * @return
     */
    public static List<Map<String, Object>> formatHumpNameForList(List<Map<String, Object>> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(row -> result.add(formatHumpName(row)));
        }
        return result;
    }

    /**
     * 将Map中的key由下划线转换为驼峰
     *
     * @param map
     * @return
     */
    public static Map<String, Object> formatHumpName(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            result.put(toFormatCol(entry.getKey()), entry.getValue());
        }
        return result;
    }

    private static String toFormatCol(String colName) {
        if (!colName.contains(String.valueOf(UNDERLINE))) {
            return colName;
        }
        StringBuilder sb = new StringBuilder();
        String[] str = colName.toLowerCase().split(String.valueOf(UNDERLINE));
        int i = 0;
        for (String s : str) {
            if (s.length() == 1) {
                s = s.toUpperCase();
            }
            i++;
            if (i == 1) {
                sb.append(s);
                continue;
            }
            if (s.length() > 0) {
                sb.append(s.substring(0, 1).toUpperCase());
                sb.append(s.substring(1));
            }
        }
        return sb.toString();
    }

//    private static String underlineToCamel(String param) {
//        if (param == null || "".equals(param.trim())) {
//            return "";
//        }
//        int len = param.length();
//        StringBuilder sb = new StringBuilder(len);
//        for (int i = 0; i < len; i++) {
//            char c = param.charAt(i);
//            if (c == UNDERLINE) {
//                if (++i < len) {
//                    sb.append(Character.toUpperCase(param.charAt(i)));
//                }
//            } else {
//                sb.append(Character.toLowerCase(param.charAt(i)));
//            }
//        }
//        return sb.toString();
//    }
}
