package com.caidaocloud.attendance.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class QuotaEmpGroupDto {

    @ApiModelProperty("员工分组名称")
    private String empGroupName;

    @ApiModelProperty("员工分组表达式")
    private String groupExp;

    @ApiModelProperty("员工分组描述")
    private String groupNote;

    @ApiModelProperty("额度")
    private Integer quotaVal;

}
