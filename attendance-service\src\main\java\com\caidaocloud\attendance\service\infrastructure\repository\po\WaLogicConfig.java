package com.caidaocloud.attendance.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 考勤逻辑配置表
 *
 * <AUTHOR>
 * @Date 2024/2/4
 */
@Data
@TableName(value = "wa_logic_config")
public class WaLogicConfig {
    private Long id;
    private String logicName;
    private String logicCode;
    private String logicType;
    private String logicExp;
    private String logicParams;
    private String tenantId;
    private Integer deleted;
    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
}
