package com.caidaocloud.attendance.service.application.dto;

import com.caidaocloud.attendance.service.application.enums.ValidStatusEnum;
import lombok.Data;

/**
 * 考勤分析-判断外出单是否可以进行抵扣结果
 */
@Data
public class CheckTravelIfValidResultDto {
    /**
     * 外出单有效状态 默认有效
     */
    private Integer validStatus = ValidStatusEnum.VALID.getIndex();

    /**
     * 外出单是否可以进行抵扣 true 可以 false 不可以
     */
    private boolean ifCalculateKgForTravel;
}
