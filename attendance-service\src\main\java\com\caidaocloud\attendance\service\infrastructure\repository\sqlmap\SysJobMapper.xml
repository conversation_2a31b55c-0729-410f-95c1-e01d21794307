<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.SysJobMapper" >
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.SysJobPo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="deleted" property="deleted" jdbcType="INTEGER" />
    <result column="path_name" property="pathName" jdbcType="VARCHAR" />
    <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
    <result column="path" property="path" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="layer_type" property="layerType" jdbcType="VARCHAR" />
    <result column="third_id" property="thirdId" jdbcType="VARCHAR" />
    <result column="third_part" property="thirdPart" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, name, parent_id, deleted, path_name, tenant_id, path, type, layer_type, third_id,
    third_part
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.impl.SysJobRepository" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_hierarchy_setting_${sysTenantId}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from sys_hierarchy_setting_${sysTenantId}
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from sys_hierarchy_setting_${sysTenantId}
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.impl.SysJobRepository" >
    delete from sys_hierarchy_setting_${sysTenantId}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.SysJobPo" >
    insert into sys_hierarchy_setting_${sysTenantId} (id, name, parent_id,
      deleted, path_name, tenant_id,
      path, type, layer_type,
      third_id, third_part)
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},
      #{deleted,jdbcType=INTEGER}, #{pathName,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR},
      #{path,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{layerType,jdbcType=VARCHAR},
      #{thirdId,jdbcType=VARCHAR}, #{thirdPart,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.SysJobPo" >
    insert into sys_hierarchy_setting_${sysTenantId}
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="pathName != null" >
        path_name,
      </if>
      <if test="tenantId != null" >
        tenant_id,
      </if>
      <if test="path != null" >
        path,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="layerType != null" >
        layer_type,
      </if>
      <if test="thirdId != null" >
        third_id,
      </if>
      <if test="thirdPart != null" >
        third_part,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="pathName != null" >
        #{pathName,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="path != null" >
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="layerType != null" >
        #{layerType,jdbcType=VARCHAR},
      </if>
      <if test="thirdId != null" >
        #{thirdId,jdbcType=VARCHAR},
      </if>
      <if test="thirdPart != null" >
        #{thirdPart,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.impl.SysJobRepository" resultType="java.lang.Integer" >
    select count(*) from sys_hierarchy_setting_${sysTenantId}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update sys_hierarchy_setting_${sysTenantId}
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null" >
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null" >
        deleted = #{record.deleted,jdbcType=INTEGER},
      </if>
      <if test="record.pathName != null" >
        path_name = #{record.pathName,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null" >
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.path != null" >
        path = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.layerType != null" >
        layer_type = #{record.layerType,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdId != null" >
        third_id = #{record.thirdId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdPart != null" >
        third_part = #{record.thirdPart,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update sys_hierarchy_setting_${sysTenantId}
    set id = #{record.id,jdbcType=INTEGER},
    name = #{record.name,jdbcType=VARCHAR},
    parent_id = #{record.parentId,jdbcType=BIGINT},
    deleted = #{record.deleted,jdbcType=INTEGER},
    path_name = #{record.pathName,jdbcType=VARCHAR},
    tenant_id = #{record.tenantId,jdbcType=VARCHAR},
    path = #{record.path,jdbcType=VARCHAR},
    type = #{record.type,jdbcType=VARCHAR},
    layer_type = #{record.layerType,jdbcType=VARCHAR},
    third_id = #{record.thirdId,jdbcType=VARCHAR},
    third_part = #{record.thirdPart,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.SysJobPo" >
    update sys_hierarchy_setting_${sysTenantId}
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="pathName != null" >
        path_name = #{pathName,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null" >
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="path != null" >
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="layerType != null" >
        layer_type = #{layerType,jdbcType=VARCHAR},
      </if>
      <if test="thirdId != null" >
        third_id = #{thirdId,jdbcType=VARCHAR},
      </if>
      <if test="thirdPart != null" >
        third_part = #{thirdPart,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.SysJobPo" >
    update sys_hierarchy_setting_${sysTenantId}
    set name = #{name,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      path_name = #{pathName,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      layer_type = #{layerType,jdbcType=VARCHAR},
      third_id = #{thirdId,jdbcType=VARCHAR},
      third_part = #{thirdPart,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="checkTableExist" resultType="int">
    select count(*) from information_schema.TABLES where table_name=#{tableName}
  </select>
</mapper>