<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="apply_id" jdbcType="BIGINT" property="applyId" />
    <result column="quota_id" jdbcType="BIGINT" property="quotaId" />
    <result column="quota_unit" jdbcType="INTEGER" property="quotaUnit" />
    <result column="quota_day" jdbcType="REAL" property="quotaDay" />
    <result column="start_date" jdbcType="BIGINT" property="startDate" />
    <result column="end_date" jdbcType="BIGINT" property="endDate" />
    <result column="apply_duration" jdbcType="REAL" property="applyDuration" />
    <result column="time_unit" jdbcType="INTEGER" property="timeUnit" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, apply_id, quota_id, quota_unit, quota_day, start_date, end_date, apply_duration,
    deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_emp_compensatory_case
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_emp_compensatory_case
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase">
    insert into wa_emp_compensatory_case (id, tenant_id, apply_id,
      quota_id, quota_unit, quota_day, 
      start_date, end_date, apply_duration, deleted,
      create_by, create_time, update_by, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{applyId,jdbcType=BIGINT},
      #{quotaId,jdbcType=BIGINT}, #{quotaUnit,jdbcType=INTEGER}, #{quotaDay,jdbcType=REAL}, 
      #{startDate,jdbcType=BIGINT}, #{endDate,jdbcType=BIGINT}, #{applyDuration,jdbcType=REAL},
      #{deleted,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT},
      #{updateBy,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase">
    insert into wa_emp_compensatory_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="applyId != null">
        apply_id,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="quotaUnit != null">
        quota_unit,
      </if>
      <if test="quotaDay != null">
        quota_day,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="applyDuration != null">
        apply_duration,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="applyId != null">
        #{applyId,jdbcType=BIGINT},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="quotaUnit != null">
        #{quotaUnit,jdbcType=INTEGER},
      </if>
      <if test="quotaDay != null">
        #{quotaDay,jdbcType=REAL},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=BIGINT},
      </if>
      <if test="applyDuration != null">
        #{applyDuration,jdbcType=REAL},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase">
    update wa_emp_compensatory_case
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="applyId != null">
        apply_id = #{applyId,jdbcType=BIGINT},
      </if>
      <if test="quotaId != null">
        quota_id = #{quotaId,jdbcType=BIGINT},
      </if>
      <if test="quotaUnit != null">
        quota_unit = #{quotaUnit,jdbcType=INTEGER},
      </if>
      <if test="quotaDay != null">
        quota_day = #{quotaDay,jdbcType=REAL},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=BIGINT},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=BIGINT},
      </if>
      <if test="applyDuration != null">
        apply_duration = #{applyDuration,jdbcType=REAL},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase">
    update wa_emp_compensatory_case
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      apply_id = #{applyId,jdbcType=BIGINT},
      quota_id = #{quotaId,jdbcType=BIGINT},
      quota_unit = #{quotaUnit,jdbcType=INTEGER},
      quota_day = #{quotaDay,jdbcType=REAL},
      start_date = #{startDate,jdbcType=BIGINT},
      end_date = #{endDate,jdbcType=BIGINT},
      apply_duration = #{applyDuration,jdbcType=REAL},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getDetailById" resultType="com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseDo">
    select ecc.*,
           sei.workno workNo,
           sei.emp_name empName,
           coalesce(sei2.emp_name, sui.empname) initiator,
           sei2.workno initiatorWorkNo
    from wa_emp_compensatory_case ecc
           left join sys_emp_info sei on sei.empid=ecc.emp_id
           left join sys_user_info sui on ecc.create_by=sui.userid
           left join sys_emp_info sei2 on sui.empid=sei2.empid
    where ecc.tenant_id=#{tenantId,jdbcType=VARCHAR} and id=#{id}
  </select>

  <select id="queryCompensatoryRecordList" parameterType="hashmap" resultType="com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryCaseDo">
    select * from(
    SELECT a.id,
    b.empid empId,
    b.workno workNo,
    b.emp_name empName,
    b.orgid,
    a.apply_duration applyDuration,
    a.valid_duration validDuration,
    a.time_unit timeUnit,
    a.create_time createTime,
    a.status,
    a.last_approval_time lastApprovalTime,
    a.note,a.start_date,a.end_date,
    lqc.rule_name quotaName
    FROM wa_emp_compensatory_case a
    JOIN sys_emp_info b ON a.emp_id = b.empid AND b.deleted = 0
    JOIN wa_emp_compensatory_quota c ON c.quota_id=a.quota_id
    LEFT JOIN sys_corp_org sco on b.orgid = sco.orgid AND sco.deleted = 0
    LEFT JOIN wa_leave_quota_config lqc ON lqc.config_id = c.config_id
    <where>
      AND a.tenant_id=#{tenantId} AND a.deleted=0
      <if test="dataFilter != null and dataFilter != ''">
        ${dataFilter}
      </if>
      <if test="status != null">
        AND a.status = #{status}
      </if>
      <if test="empId != null">
        AND a.emp_id = #{empId}
      </if>
      <if test="empIds != null and empIds.size() > 0">
        AND a.emp_id in
        <foreach collection="empIds" item="empIdItem" open="(" close=")" separator=",">
          #{empIdItem}
        </foreach>
      </if>
      <if test="startDate != null and endDate != null">
        AND a.last_approval_time between #{startDate} AND #{endDate}
      </if>
      <if test="keywords != null and keywords != ''">
        AND (b.workno like concat('%', #{keywords}, '%') or b.emp_name like concat('%', #{keywords}, '%'))
      </if>
    </where>) as t
    <where>
      <if test="filter != null">
        ${filter}
      </if>
    </where>
    order by createTime desc
  </select>

  <select id="batchInsert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase">
    <foreach collection="records" item="record" separator=";">
      insert into wa_emp_compensatory_case (id, tenant_id, apply_id, quota_id, quota_unit, quota_day, start_date, end_date, apply_duration,
      deleted, create_by, create_time, update_by, update_time)
      values (#{record.id,jdbcType=BIGINT}, #{record.tenantId,jdbcType=VARCHAR}, #{record.applyId,jdbcType=BIGINT},
      #{record.quotaId,jdbcType=BIGINT}, #{record.quotaUnit,jdbcType=INTEGER}, #{record.quotaDay,jdbcType=REAL},
      #{record.startDate,jdbcType=BIGINT}, #{record.endDate,jdbcType=BIGINT}, #{record.applyDuration,jdbcType=REAL},
      #{record.deleted,jdbcType=INTEGER},
      #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT}, #{record.updateBy,jdbcType=BIGINT},
      #{record.updateTime,jdbcType=BIGINT})
    </foreach>
  </select>
</mapper>