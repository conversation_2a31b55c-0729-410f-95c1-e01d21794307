package com.caidaocloud.attendance.service.interfaces.shimz.facade;

import com.caidaocloud.attendance.service.application.shimz.service.DataMigrationService;
import com.caidaocloud.attendance.service.interfaces.shimz.dto.DataMigrationDto;
import com.caidaocloud.attendance.service.interfaces.shimz.dto.EmpOvertimeDetailDto;
import com.caidaocloud.attendance.service.interfaces.shimz.dto.EmpOvertimeUpdateDto;
import com.caidaocloud.attendance.service.interfaces.shimz.dto.OvertimeDetailVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/attendance/dataMigration/v1")
@Api(value = "/api/attendance/dataMigration/v1", description = "考勤数据迁移接口")
public class DataMigrationController {

    @Autowired
    private DataMigrationService dataMigrationService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation("获取统计考勤汇总数据")
    @PostMapping("/clearData")
    public Result<?> clearAnalyzeData(@RequestBody DataMigrationDto dto) {
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            dataMigrationService.clearAnalyzeData(dto, userInfo);
            return Result.success();
        } catch (Exception e) {
            log.error("clearData failed : {}", e.getMessage(), e);
            return Result.fail();
        }
    }

    @ApiOperation("更新加班数据")
    @PostMapping("/updateEmpOvertimeData")
    public Result<?> updateEmpOvertimeData(@RequestBody EmpOvertimeUpdateDto dto) {
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            if (null == userInfo) {
                return Result.fail("更新数据失败");
            }
            dataMigrationService.updateEmpOvertimeData(dto, userInfo);
            return Result.success();
        } catch (Exception e) {
            log.error("DataMigrationController.updateEmpOvertimeData failed : {}", e.getMessage(), e);
            return Result.fail("更新数据失败");
        }
    }

    @ApiOperation("查询加班详情")
    @PostMapping("/getEmpOvertimeDetailData")
    public Result<ItemsResult<OvertimeDetailVo>> getEmpOvertimeDetailData(@RequestBody EmpOvertimeDetailDto dto) {
        try {
            UserInfo userInfo = sessionService.getUserInfo();
            if (null == userInfo) {
                return Result.fail("账号不存在");
            }
            return Result.ok(new ItemsResult<>(dataMigrationService.getEmpOvertimeDetailData(dto, userInfo)));
        } catch (Exception e) {
            log.error("DataMigrationController.getEmpOvertimeDetailData failed : {}", e.getMessage(), e);
            return Result.fail("查询数据失败");
        }
    }
}
