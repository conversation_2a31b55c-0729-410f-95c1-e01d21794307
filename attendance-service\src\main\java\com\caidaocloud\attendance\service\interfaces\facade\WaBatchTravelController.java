package com.caidaocloud.attendance.service.interfaces.facade;

import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelDto;
import com.caidaocloud.attendance.service.application.dto.travel.BatchTravelImportResult;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchTravelService;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.RevokeEmpTraveDto;
import com.caidaocloud.attendance.service.interfaces.vo.EmpInfoForTravelVo;
import com.caidaocloud.attendance.service.interfaces.vo.EmpTravelVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/attendance/batch/travel/v1")
@Api(value = "/api/attendance/batch/travel/v1", description = "批量出差")
public class WaBatchTravelController {
    private static final String LOCK_KEY_OF_CHECK = "BATCH_TRAVEL_CHECK_LOCK_{0}_{1}";
    private static final String LONK_KEY_OF_SAVE = "BATCH_TRAVEL_SAVE_LOCK_{0}_{1}";
    private static final String IMPORT_BATCH_TRAVEL_MSG_PROCESS = "IMPORT_BATCH_TRAVEL_MSG_PROCESS_";

    @Autowired
    private CacheService cacheService;
    @Autowired
    private WaBatchTravelService waBatchTravelService;
    @Autowired
    private AsyncExecService asyncService;

    @ApiOperation(value = "获取出差时长")
    @PostMapping(value = "/getTimeDuration")
    public Result getTimeDuration(@RequestBody BatchTravelDto dto) {
        if (dto.getEmpId() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_EMPLOYEE_SELECTED, Boolean.FALSE);
        }
        UserInfo userInfo = UserContext.preCheckUser();
        String lockKey = MessageFormat.format(LOCK_KEY_OF_CHECK, userInfo.getUserId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return waBatchTravelService.getTimeDuration(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation(value = "保存出差申请单")
    @PostMapping(value = "/save")
    public Result save(@RequestBody BatchTravelDto dto) {
        if (dto.getEmpId() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.NO_EMPLOYEE_SELECTED, Boolean.FALSE);
        }
        UserInfo userInfo = UserContext.preCheckUser();
        String lockKey = MessageFormat.format(LONK_KEY_OF_SAVE, userInfo.getUserId(), dto.getEmpId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return waBatchTravelService.save(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("同步导入批量出差单据")
    @PostMapping(value = "/syncImport")
    public Result<BatchTravelImportResult> syncImport(@RequestParam("file") MultipartFile file) {
        return Result.ok(waBatchTravelService.importTravel(file, null, false));
    }

    @ApiOperation("异步导入批量出差单据")
    @PostMapping(value = "/asyncImport")
    public Result<Boolean> asyncImport(@RequestParam("file") MultipartFile file, @RequestParam("progress") String progress) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        // 重复提交校验
        String lockKey = MessageFormat.format("IMPORT_BATCH_TRAVEL_LOCK_{0}", userInfo.getTenantId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 60 * 60);// 单位秒
        try {
            cacheService.cacheValue(WaBatchTravelService.IMPORT_BATCH_TRAVEL_PROCESS + progress, "0.01");
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    BatchTravelImportResult importResult = null;
                    try {
                        log.info("importTravel async execution start, progress: {}, userInfo: {}", progress, FastjsonUtil.toJsonStr(userInfo));
                        importResult = waBatchTravelService.asyncImportTravel(file, userInfo, progress, true);
                        if (!importResult.isSuccess()) {
                            engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null).getMsg());
                        }
                    } catch (Exception e) {
                        log.error("导入批量出差单据异步执行失败: {}", e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException || e instanceof ServerException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage.setMessage(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, null).getMsg());
                        }
                    }
                    Map params = new HashMap();
                    params.put("result", engineMessage);
                    params.put("importResult", importResult);
                    return params;
                }

                @Override
                public void callback(Map params) throws Exception {
                    cacheService.cacheValue(WaBatchTravelService.IMPORT_BATCH_TRAVEL_PROCESS + progress, "1");
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    BatchTravelImportResult importResult = (BatchTravelImportResult) params.get("importResult");

                    // 缓存导入结果和执行消息
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("engineMessage", engineMessage);
                    resultMap.put("importResult", importResult);

                    cacheService.cacheValue(IMPORT_BATCH_TRAVEL_MSG_PROCESS + progress, JsonSerializeHelper.serialize(resultMap), 5 * 60);
                }
            });
            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("启动导入批量出差单据异步任务失败: {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FAILED, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    @ApiOperation("获取异步导入进度")
    @RequestMapping(value = "/getAsyncImportProgress", method = RequestMethod.GET)
    public Result getAsyncImportProgress(@RequestParam("progress") String progress) {
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!cacheService.containsKey(WaBatchTravelService.IMPORT_BATCH_TRAVEL_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        //进度
        Double rate = Double.valueOf(cacheService.getValue(WaBatchTravelService.IMPORT_BATCH_TRAVEL_PROCESS + progress));
        if (rate == null) {
            rate = 0.5d;
        }

        BatchTravelImportResult importResult = null;
        if (rate >= 1) {
            //执行结果
            String analyzeMsg = cacheService.getValue(IMPORT_BATCH_TRAVEL_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(analyzeMsg)) {
                try {
                    Map<String, Object> resultMap = JsonSerializeHelper.deserialize(analyzeMsg, Map.class);
                    if (resultMap != null) {
                        AttendanceEngineMessage engineMessageForCache = FastjsonUtil.convertObject(resultMap.get("engineMessage"), AttendanceEngineMessage.class);
                        if (engineMessageForCache != null) {
                            engineMessage = engineMessageForCache;
                        }
                        // 获取导入结果
                        importResult = FastjsonUtil.convertObject(resultMap.get("importResult"), BatchTravelImportResult.class);
                    }
                } catch (Exception e) {
                    log.error("解析导入结果失败: {}", e.getMessage(), e);
                    engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                    engineMessage.setMessage("解析导入结果失败");
                }
            }
            //删除缓存
            cacheService.remove(WaBatchTravelService.IMPORT_BATCH_TRAVEL_PROCESS + progress);
            cacheService.remove(IMPORT_BATCH_TRAVEL_MSG_PROCESS + progress);
        }
        engineMessage.setProcess(rate);

        // 如果执行完成且成功，返回包含导入结果的响应
        if (rate >= 1 && engineMessage.getCode() == 0 && importResult != null) {
            Map<String, Object> response = new HashMap<>();
            response.put("engineMessage", engineMessage);
            response.put("importResult", importResult);
            return Result.ok(response);
        }

        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    @ApiOperation(value = "获取出差记录分页列表")
    @PostMapping("/list")
    public Result<PageResult<EmpTravelVo>> getPageList(@RequestBody EmpTravelReqDto dto, HttpServletRequest request) {
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dto.setDataScope(orgDataScope);
        }
        return ResponseWrap.wrapResult(waBatchTravelService.getPageList(dto, request));
    }

    @ApiOperation("导出")
    @PostMapping(value = "/export")
    public Result<TaskDto> export(@RequestBody EmpTravelReqDto requestDto, HttpServletRequest request) {
        String dataScope = SpringUtil.getBean(ISessionService.class).getAndDataScope(AuthDataScopeCode.BATCH_EMP_TRAVEL_LIST, "sei");
        requestDto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            requestDto.setDataScope(requestDto.getDataScope() + orgDataScope);
        }
        return ResponseWrap.wrapResult(waBatchTravelService.export(requestDto, request));
    }

    @PostMapping(value = "/revoke")
    @ApiOperation(value = "撤销")
    public Result revokeEmpTravel(@RequestBody RevokeEmpTraveDto dto) {
        if (StringUtils.isEmpty(dto.getRecokeReason())) {
            return ResponseWrap.wrapResult(AttendanceCodes.REASON_EMPTY, Boolean.FALSE);
        }
        if (dto.getRecokeReason().length() >= 100) {
            return ResponseWrap.wrapResult(AttendanceCodes.REVOKE_REASON_LIMIT100, Boolean.FALSE);
        }
        try {
            return waBatchTravelService.revokeEmpTravel(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof CDException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_REVOKE_FAILED, Boolean.FALSE);
        }
    }

    @GetMapping("/emp")
    @ApiOperation(value = "查询员工信息")
    public Result<EmpInfoForTravelVo> getEmpInfo(@RequestParam(value = "empId") String empId) {
        Map<String, Object> empInfo = waBatchTravelService.getEmpInfo(empId);
        return Result.ok(FastjsonUtil.convertObject(empInfo, EmpInfoForTravelVo.class));
    }
}
