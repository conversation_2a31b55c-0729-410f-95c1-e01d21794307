<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpApplyRecordMapper">
  <resultMap id="BaseResultMap" type="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpApplyRecordPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="entity_id" jdbcType="BIGINT" property="entityId" />
    <result column="emp_id" jdbcType="BIGINT" property="empId" />
    <result column="apply_time" jdbcType="BIGINT" property="applyTime" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="replace_time" jdbcType="BIGINT" property="replaceTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, module, entity_id, emp_id, apply_time, business_key, start_time, end_time, 
    replace_time, status, deleted, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wa_emp_apply_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wa_emp_apply_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpApplyRecordPo">
    insert into wa_emp_apply_record (id, tenant_id, module, 
      entity_id, emp_id, apply_time, 
      business_key, start_time, end_time, 
      replace_time, status, deleted, 
      create_by, create_time, update_by, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{module,jdbcType=VARCHAR},
      #{entityId,jdbcType=BIGINT}, #{empId,jdbcType=BIGINT}, #{applyTime,jdbcType=BIGINT},
      #{businessKey,jdbcType=VARCHAR}, #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, 
      #{replaceTime,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}, 
      #{createBy,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpApplyRecordPo">
    insert into wa_emp_apply_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="module != null">
        module,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="replaceTime != null">
        replace_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=BIGINT},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=BIGINT},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="replaceTime != null">
        #{replaceTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpApplyRecordPo">
    update wa_emp_apply_record
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=BIGINT},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=BIGINT},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="replaceTime != null">
        replace_time = #{replaceTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpApplyRecordPo">
    update wa_emp_apply_record
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      module = #{module,jdbcType=VARCHAR},
      entity_id = #{entityId,jdbcType=BIGINT},
      emp_id = #{empId,jdbcType=BIGINT},
      apply_time = #{applyTime,jdbcType=BIGINT},
      business_key = #{businessKey,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      replace_time = #{replaceTime,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryByModuleAndEntityId" resultMap="BaseResultMap">
    select * from wa_emp_apply_record
    where module = #{module} and entity_id = #{entityId} and deleted = 0
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=VARCHAR}
    </if>
  </select>

  <delete id="deleteByModuleAndEntityId">
    delete from wa_emp_apply_record
    where module = #{module} and entity_id = #{entityId}
    <if test="tenantId != null">
      and tenant_id = #{tenantId,jdbcType=VARCHAR}
    </if>
  </delete>

  <update id="clearEmpApplyRecords">
    truncate table wa_emp_apply_record
  </update>
</mapper>