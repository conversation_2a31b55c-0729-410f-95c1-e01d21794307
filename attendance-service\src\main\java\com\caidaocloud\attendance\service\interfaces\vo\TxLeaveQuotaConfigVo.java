package com.caidaocloud.attendance.service.interfaces.vo;

import com.caidaocloud.attendance.service.application.enums.ApplyQuotaEnum;
import com.caidaocloud.attendance.service.interfaces.dto.QuotaEmpGroupDto;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TxLeaveQuotaConfigVo {
    @ApiModelProperty("余额规则配置主键")
    private Long configId;
    @ApiModelProperty("发放周期：1 自然年、2 入职年、3 自定义周期")
    private Integer distributionCycle;
    @ApiModelProperty("发放周期开始日")
    private Long disCycleStart;
    @ApiModelProperty("发放周期结束日")
    private Long disCycleEnd;
    @ApiModelProperty("有效期类型：1 限制有效期 、2 不限制有效期")
    private Integer validityPeriodType;
    @ApiModelProperty("有效期")
    private Float validityDuration;
    @ApiModelProperty("有效期单位：1 天、2 月、3 ")
    private Integer validityUnit;
    @ApiModelProperty("调休有效期对应的开始日期计算规则：1 当年1月1号、2 加班开始日期、3 加班开始月")
    private Integer validityStartType;
    @ApiModelProperty("本年额度发放规则：1 按全年额度发放、2 按入职日比例发放、3 按入职月比例发放")
    private Integer quotaDistributeRule;
    @ApiModelProperty("本年额度舍位规则，当发放规则为2时才有效，值为：1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5")
    private Integer quotaRoundingRule;
    @ApiModelProperty("当前额度发放规则：1 按天")
    private Integer nowDistributeRule;
    @ApiModelProperty("当前额度舍位规则，值为：1 四舍五入保留整数、2 向上取整1、3 向下取整1、4 向上取整0.5、5 向下取整0.5")
    private Integer nowRoundingRule;
    @ApiModelProperty("可否预支：0 不可预支 、 1 可预支")
    private Integer ifAdvance;
    @ApiModelProperty("过期规则： 1 付现、2 作废")
    private Integer expirationRule;
    @ApiModelProperty("结转至，要结转的假期类型ID")
    private Long carryOverTo;
    @ApiModelProperty("结转有效期开始日期类型：1 原配额生效日期 、2 原配额失效日期后一天、3 原配额失效日期")
    private Integer carryOverStartType;
    @ApiModelProperty("结转有效期")
    private Float carryOverValidityDuration;
    @ApiModelProperty("结转有效期单位：1 天、2 月、3 ")
    private Integer carryOverValidityUnit;
    @ApiModelProperty("配额规则")
    private List<QuotaEmpGroupDto> quotaGroups;
    @ApiModelProperty("配额折算规则：1 生成时折算、2 到期时折算")
    private Integer convertRule;
    @ApiModelProperty("育儿假生成规则 1 按子女个数生成  2 按子女个数累加生成  3 仅生成一条")
    private Integer childRule;
    @ApiModelProperty("失效日期类型：1 固定有效期 2 当年失效 3 次年失效")
    private Integer invalidType;
    @ApiModelProperty("失效日期")
    private String invalidDate;
    @ApiModelProperty("失效日期是否延长至当月月底")
    private Boolean validityExtension;
    @ApiModelProperty("额度说明")
    private String description;
    @ApiModelProperty("额度规则名称")
    private String ruleName;
    @ApiModelProperty("额度规则有效期生效日期")
    private Long ruleStartDate;
    @ApiModelProperty("额度规则有效期失效日期")
    private Long ruleEndDate;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("分组表达式")
    private String groupExp;
    @ApiModelProperty("分组表达式描述")
    private String groupNote;
    @ApiModelProperty("是否允许员工申请调休转付现,0关闭,1开启,默认关闭")
    private Integer applyCompensatoryCash;
    @ApiModelProperty("过期处理,1、作废,2、付现")
    private Integer expireHandle;
    @ApiModelProperty(value = "申请额度类型", name = "INVALID 失效额度;EFFICIENT 有效额度")
    private Set<ApplyQuotaEnum> applyTypes;
    @ApiModelProperty("有效额度申请上限")
    private Float validQuotaLimit;

    @ApiModelProperty("假期延期申请开关, true/允许，false/不允许，默认false")
    private Boolean leaveExtension;
    @ApiModelProperty("延期申请时间单位, 1天、2月")
    private Integer extensionUnit;
    @ApiModelProperty("延期申请最大申请次数")
    private Integer maxExtension;
    @ApiModelProperty("延期申请时间, 正整数")
    private Integer extensionTime;
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nRuleName;
    @ApiModelProperty("本年额度发放规则按照入职月比例发放时的临界日期")
    private Integer dayOfHireMonthDist;
    @ApiModelProperty("结转配额显示方式，1、结转单独生成额度 2、结转额度和当年额度合并显示")
    private Integer carryToType;
    @ApiModelProperty("自定义表达式条件")
    private ConditionTree groupExpCondition;
    @ApiModelProperty("结转类型：1结转全部过期额度/2结转部分过期额度,默认1")
    private Integer transferType;
    @ApiModelProperty("最多结转额度")
    private Float maxTransferQuota;
}