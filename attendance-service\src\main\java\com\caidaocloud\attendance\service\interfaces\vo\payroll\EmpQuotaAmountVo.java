package com.caidaocloud.attendance.service.interfaces.vo.payroll;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EmpQuotaAmountVo {
    @ApiModelProperty(value = "员工ID")
    private Long empid;

    @ApiModelProperty(value = "假期类型ID")
    private Integer leaveTypeId;

    @ApiModelProperty("调整余额")
    private Float quotaDay;

    @ApiModelProperty("当前剩余")
    private Float curRemain;
}
