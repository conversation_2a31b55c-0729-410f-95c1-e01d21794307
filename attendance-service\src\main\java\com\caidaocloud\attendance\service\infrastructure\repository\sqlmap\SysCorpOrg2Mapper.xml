<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.caidaocloud.attendance.service.infrastructure.repository.mapper.SysCorpOrg2Mapper">
    <sql id="Base_Column_List">
        shortname as "title",orgid as "value",'org' as "type"
    </sql>

    <select id="getFirstNode" parameterType="java.lang.Integer"
            resultType="com.caidaocloud.attendance.service.interfaces.vo.OrgTreeVo">
        select
        <include refid="Base_Column_List" />
        from sys_corp_org where corpid =#{corpId} and belong_org_id = '0'
    </select>

    <select id="getOrgTreeDetail" parameterType="java.lang.Integer"
            resultType="com.caidaocloud.attendance.service.interfaces.vo.ChildOrgVo">
        select shortname as "title",orgid as "value",forgid as "forgId",'org' as "type"
        from sys_corp_org where forgid = #{orgId}
    </select>
</mapper>