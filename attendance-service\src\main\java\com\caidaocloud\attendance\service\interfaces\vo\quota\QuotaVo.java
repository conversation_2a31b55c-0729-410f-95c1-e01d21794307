package com.caidaocloud.attendance.service.interfaces.vo.quota;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QuotaVo {

    @ApiModelProperty("假期配额id")
    private Integer empQuotaId;

    @ApiModelProperty("余额类型id")
    private Integer quotaSettingId;

    @ApiModelProperty("年份")
    private Short periodYear;

    @ApiModelProperty("生效日期：开始时间")
    private Long startDate;

    @ApiModelProperty("生效日期：结束时间")
    private Long lastDate;

    @ApiModelProperty("员工id")
    private Long empid;

    @ApiModelProperty("上年结转额度")
    private Float remainDay;

    @ApiModelProperty("上年结转有效期至")
    private Long remainValidDate;

    @ApiModelProperty("本年扣减")
    private Float deductionDay;

    @ApiModelProperty("本年额度")
    private Float quotaDay;

    @ApiModelProperty("本年已用")
    private Float usedDay;

    @ApiModelProperty("备注")
    private String remarks;
}
